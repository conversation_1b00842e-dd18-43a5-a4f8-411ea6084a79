import { AngleTimeReportSandboxService } from '../app/service/sandbox/index.js';
import dbSandbox from '../app/models/sandbox.js';
import db from '../app/models/index.js';
import { BaseRepository } from '../app/repository/v2/base-repository.js';

const id = '';

const angleTimeReportSandboxService = new AngleTimeReportSandboxService({
	kinebot_repository: new BaseRepository(db),
	sandbox_repository: new BaseRepository(dbSandbox)
});

await angleTimeReportSandboxService.sendReport(id);
