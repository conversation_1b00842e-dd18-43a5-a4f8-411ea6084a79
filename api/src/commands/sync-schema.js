import { ScriptDatabaseSchemaSync } from '../app/models/scripts/script_database_schema_sync.js';
import { logger } from '../app/helpers/index.js';

async function syncSchema() {
	try {
		logger.info('Starting database synchronization...');

		await ScriptDatabaseSchemaSync.copyAllSandboxTableSchemas();

		logger.info('Database synchronization completed successfully');
		process.exit(0);
	} catch (error) {
		logger.error('Failed to synchronize database:', error);
		process.exit(1);
	}
}

syncSchema();
