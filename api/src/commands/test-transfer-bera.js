import { BeraReportSandboxService } from '../app/service/sandbox/bera_report_sandbox.js';
import dbSandbox from '../app/models/sandbox.js';
import db from '../app/models/index.js';
import { BaseRepository } from '../app/repository/v2/base-repository.js';

const id = '';

const beraReportSandboxService = new BeraReportSandboxService({
	kinebot_repository: new BaseRepository(db),
	sandbox_repository: new BaseRepository(dbSandbox)
});

await beraReportSandboxService.sendReport(id);
