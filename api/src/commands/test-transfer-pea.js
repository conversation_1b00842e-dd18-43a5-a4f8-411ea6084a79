import { PeaReportSandboxService } from '../app/service/sandbox/pea_report_sandbox.js';
import dbSandbox from '../app/models/sandbox.js';
import db from '../app/models/index.js';
import { BaseRepository } from '../app/repository/v2/base-repository.js';

const id = 'd27e8400-56c8-11f0-9030-09bd465bb518';

const peaReportSandboxService = new PeaReportSandboxService({
	kinebot_repository: new BaseRepository(dbSandbox),
	sandbox_repository: new BaseRepository(db)
});

await peaReportSandboxService.sendReport(id);
