import { SuperPeaReportSandboxService } from '../app/service/sandbox/super_pea_report_sandbox.js';
import dbSandbox from '../app/models/sandbox.js';
import db from '../app/models/index.js';
import { BaseRepository } from '../app/repository/v2/base-repository.js';

const id = '';

const superPeaReportSandboxService = new SuperPeaReportSandboxService({
	kinebot_repository: new BaseRepository(db),
	sandbox_repository: new BaseRepository(dbSandbox)
});

await superPeaReportSandboxService.sendReport(id);
