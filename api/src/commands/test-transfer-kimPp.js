import { KimPpReportSandboxService } from '../app/service/sandbox/index.js';
import dbSandbox from '../app/models/sandbox.js';
import db from '../app/models/index.js';
import { BaseRepository } from '../app/repository/v2/base-repository.js';

const id = '';

const kimPpReportSandboxService = new KimPpReportSandboxService({
	kinebot_repository: new BaseRepository(db),
	sandbox_repository: new BaseRepository(dbSandbox)
});

await kimPpReportSandboxService.sendReport(id);
