import { CustomReportSandboxService } from '../app/service/sandbox/custom_report_sandbox.js';
import dbSandbox from '../app/models/sandbox.js';
import db from '../app/models/index.js';
import { BaseRepository } from '../app/repository/v2/base-repository.js';

const id = 'f00bdf80-56d8-11f0-a3ad-7ddd6b243192';

const customReportSandboxService = new CustomReportSandboxService({
	kinebot_repository: new BaseRepository(dbSandbox),
	sandbox_repository: new BaseRepository(db)
});

await customReportSandboxService.sendReport(id);
