export const DASHBOARD_DEFAULT_INITIAL_DATE = '2020-01-01';
export const DASHBOARD_INITIAL_MONTH_FISCAL_YEAR = 11;
export const DASHBOARD_GENDER_NEUTRAL_TEXT = 'GNS';
export const DASHBOARD_JANUARY_INDEX_MONTH = 1;
export const REBA_RISK_NAME = 'REBA';

export const DASHBOARD_GRANULARITY_VALUES = {
	SEMESTER: 'semester',
	QUARTER: 'quarter',
	MONTH: 'month',
	YEAR: 'year',
	WEEK: 'week',
	DAY: 'day'
};

export const BERA_SIX_TOO = {
	TOO_HIGH: 'too_high',
	TOO_MANY: 'too_many',
	TOO_MUCH: 'too_much',
	TOO_LONG: 'too_long',
	TOO_FAR: 'too_far',
	TOO_LOW: 'too_low'
};

export const SERA_CATEGORIES = {
	ERGONOMIC: 'Ergonomic - Organizational',
	PSYCHOSOCIAL_AND_COGNITIVE: 'Ergonomic - Psychosocial and cognitive',
	ENVIRONMENTAL: 'Ergonomic - Environmental',
	BIOMECHANICAL: 'Ergonomic - Biomechanical',
	FURNITURE_AND_EQUIPMENT: 'Ergonomic - Furniture and equipment'
};

export const SERA_CATEGORIES_ORDER = [
	SERA_CATEGORIES.ERGONOMIC,
	SERA_CATEGORIES.PSYCHOSOCIAL_AND_COGNITIVE,
	SERA_CATEGORIES.ENVIRONMENTAL,
	SERA_CATEGORIES.BIOMECHANICAL,
	SERA_CATEGORIES.FURNITURE_AND_EQUIPMENT
];

export const ACTION_PLAN_TABLE_NAME = 'action_plans_v2';

export const ACTION_PLAN_HISTORY_TYPES = {
	DELETED: 'action_plan_deleted',
	CREATE: 'action_plan_created',
	DESCRIPTION_CHANGED: 'description_changed',
	RESPONSIBLE_CHANGED: 'responsible_changed',
	ATTACHMENT_REMOVED: 'attachment_removed',
	ATTACHMENT_ADDED: 'attachment_added',
	TASK_REMOVED: 'task_removed',
	EVIDENCE_REMOVED: 'evidence_removed',
	STATUS_CHANGED: 'status_changed',
	DUE_DATE_CHANGED: 'due_date_changed',
	PRIORITY_CHANGED: 'priority_changed',
	TITLE_CHANGED: 'title_changed',
	INVESTMENT_CHANGED: 'investment_changed',
	TASK_ADDED: 'task_added',
	TASK_COMPLETED: 'task_completed',
	TASK_UNCOMPLETED: 'task_uncompleted',
	TASK_CHANGED: 'task_changed',
	EVIDENCE_ADDED: 'evidence_added',
	LINKED_TOOL: 'linked_tool',
	UNLINKED_TOOL: 'unlinked_tool',
	COMMENT_ADDED: 'comment_added',
	COMMENT_CHANGED: 'comment_changed',
	COMMENT_REMOVED: 'comment_removed'
};

export const NOTIFICATION_TYPES = {
	ACTION_PLAN: {
		AUTHOR_STATUS_CHANGED: 'author_action_plan_status_changed',
		AUTHOR_TASK_COMPLETED: 'author_action_plan_task_completed',
		AUTHOR_DUE_DATE_NEAREST: 'author_action_plan_due_date_nearest',
		RESPONSIBLE_CREATED: 'create_action',
		RESPONSIBLE_DUE_DATE_NEAREST: 'responsible_action_plan_due_date_nearest'
	}
};

export const NOTIFICATION_METHODS = {
	EMAIL: 'email',
	WEB: 'web'
};

export const ACTION_PLAN_PDF_SECTIONS = {
	RESPONSIBLE: 'responsible',
	DUE_DATE: 'due_date',
	PRIORITY: 'priority',
	INVESTMENT: 'investment',
	IMAGE_ATTACHMENTS: 'image_attachments',
	OTHER_ATTACHMENTS: 'other_attachments',
	COMPLETED_TASKS: 'completed_tasks',
	UNCOMPLETED_TASKS: 'uncompleted_tasks',
	HISTORY: 'history',
	COMMENTS: 'comments'
};

export const ACTION_PLAN_MAX_ATTACHMENTS = 4;

export const ACTION_PLAN_SUPPORT_MIME_TYPES = [
	'text/csv',
	'image/jpg',
	'image/jpeg',
	'image/png',
	'application/pdf',
	'application/msword',
	'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
	'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
];

const MB = 1024 * 1024;

export const ACTION_PLAN_ATTACHMENT_MAX_SIZE = MB * 15;

export const PDF_FUNCTION_NAME = 'pdf_container';

export const ACTION_PLAN_ORIGIN_TABLE_NAME = {
	SERA_SUMMARY: 'sera_summaries',
	SERA_SUMMARY_REVIEW: 'sera_summary_reviews',
	CUSTOM_REPORT_RESULT: 'custom_report_results',
	CUSTOM_REPORT_STEP_KEY: 'custom_report_step_keys',
	CUSTOM_REPORT_SUB_STEP_KEY: 'custom_report_sub_step_keys'
};

export const RISK_RPN_COLORS = {
	DEFAULT: '#2F54EB',
	ACCEPTABLE: '#2CC852',
	MODERATE: '#F8D627',
	HIGH: '#F78A38',
	VERY_HIGH: '#E74150',
	SERIOUS: '#9B54E2'
};

export const RISK_RPN_MAX = {
	ACCEPTABLE: 7,
	MODERATE: 35,
	HIGH: 124,
	VERY_HIGH: 179,
	SERIOUS: 216
};

export const SANDBOX_REPORT_TYPE = [
	'BERA',
	'CUSTOM',
	'PEA',
	'SUPER_PEA',
	'NIOSH',
	'LIBERTY_MUTUAL',
	'BACK_COMPRESSIVE',
	'ANGLE_TIME',
	'KIM_MHO',
	'KIM_PP',
	'REBA',
	'RECOVERY',
	'STRAIN_INDEX',
	'SERA',
	'ACTION_PLAN'
];
