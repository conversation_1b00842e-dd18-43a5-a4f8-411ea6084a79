import { StorageContext } from '../utils/storage_context.js';
import { logger } from '../helpers/logger.js';
export class SandboxEnvironmentMiddleware {
	static async setEnvironment(req, _, next) {
		try {
			const environment = req.headers['x-environment'];
			const user_id = req.user.id;

			StorageContext.run({ environment, user_id }, () => {
				next();
			});
		} catch (error) {
			logger.error('[SandboxMiddleware] Error:', error);
			next();
		}
	}
}
