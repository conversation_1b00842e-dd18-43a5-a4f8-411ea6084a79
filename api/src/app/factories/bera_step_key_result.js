import { ControllerProxy } from '../controllers/controller-proxy.js';
import { BeraStepKeyResultService } from '../service/bera_step_key_result.js';
import { BeraStepKeyResultRepository } from '../repository/bera_step_key_result.js';
import { BeraStepKeyResultController } from '../controllers/bera_step_key_result.js';
import { BeraStepKeyResultMiddleware } from '../middlewares/bera_step_key_result.js';

export class BeraStepKeyResultFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: BeraStepKeyResultController,
			Service: BeraStepKeyResultService,
			Repositories: {
				repository: BeraStepKeyResultRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new BeraStepKeyResultMiddleware(new BeraStepKeyResultRepository(database));

		return { middleware, controller };
	}
}
