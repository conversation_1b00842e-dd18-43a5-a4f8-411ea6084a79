import { SystemOfUnitsService } from '../service/system_of_units.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { SystemOfUnitsRepository } from '../repository/system_of_units.js';
import { SystemOfUnitsController } from '../controllers/system_of_units.js';
import { SystemOfUnitsMiddleware } from '../middlewares/system_of_units.js';

export class SystemOfUnitsFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: SystemOfUnitsController,
			Service: SystemOfUnitsService,
			Repositories: {
				repository: SystemOfUnitsRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new SystemOfUnitsMiddleware(new SystemOfUnitsRepository(database));

		return { middleware, controller };
	}
}
