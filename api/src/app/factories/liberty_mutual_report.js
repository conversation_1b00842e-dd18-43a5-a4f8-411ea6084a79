import { UserRepository } from '../repository/v2/user.js';
import { LibertyMutualSchema } from '../schemas/liberty_mutual.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { LibertyMutualReportMiddleware } from '../middlewares/index.js';
import { LibertyMutualReportService } from '../service/liberty_mutual_report.js';
import { LibertyMutualReportController } from '../controllers/liberty_mutual_report.js';
import {
	FileRepository,
	WorkstationRepository,
	SystemOfUnitsRepository,
	LibertyMutualReportRepository,
	LibertyMutualReportInputRepository
} from '../repository/index.js';

export class LibertyMutualReportFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: LibertyMutualReportController,
			Service: LibertyMutualReportService,
			Repositories: {
				repository: LibertyMutualReportRepository,
				file_repository: FileRepository,
				user_repository: UserRepository,
				system_of_units_repository: SystemOfUnitsRepository,
				liberty_mutual_report_input_repository: LibertyMutualReportInputRepository,
				workstation_repository: WorkstationRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new LibertyMutualReportMiddleware(new LibertyMutualReportRepository(database));
		const schema = new LibertyMutualSchema();

		return { schema, middleware, controller };
	}
}
