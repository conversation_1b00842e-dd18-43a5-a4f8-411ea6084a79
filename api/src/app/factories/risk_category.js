import { RiskCategoryService } from '../service/risk_category.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { RiskCategoryController } from '../controllers/risk_category.js';
import { RiskCategoryMiddleware } from '../middlewares/risk_category.js';
import { RiskCategoryRepository } from '../repository/risk_category.js';

export class RiskCategoryFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: RiskCategoryController,
			Service: RiskCategoryService,
			Repositories: {
				repository: RiskCategoryRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new RiskCategoryMiddleware(new RiskCategoryRepository(database));

		return { middleware, controller };
	}
}
