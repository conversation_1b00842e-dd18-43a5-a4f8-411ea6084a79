import { SuperPEAReportService } from '../service/super_pea.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { SuperPEAReportController } from '../controllers/super_pea.js';
import { SuperPEAReportMiddleware } from '../middlewares/super_pea.js';
import { SuperPEAReportRepository } from '../repository/super_pea.js';

export class SuperPEAReportFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: SuperPEAReportController,
			Service: SuperPEAReportService,
			Repositories: {
				repository: SuperPEAReportRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new SuperPEAReportMiddleware(new SuperPEAReportRepository(database));

		return { middleware, controller };
	}
}
