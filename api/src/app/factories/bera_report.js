import { BeraReportSchema } from '../schemas/index.js';
import { BeraReportService } from '../service/bera_report.js';
import { BeraReportMiddleware } from '../middlewares/index.js';
import { BeraReportController } from '../controllers/bera_report.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import {
	BeraWeightedAverageRepository,
	BeraStepKeyResultRepository,
	CustomReportStepRepository,
	BeraJobSummaryRepository,
	CustomReportRepository,
	WorkstationRepository,
	BeraReportRepository,
	TaskRepository,
	FileRepository
} from '../repository/index.js';

export class BeraReportFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: BeraReportController,
			Service: BeraReportService,
			Repositories: {
				repository: BeraReportRepository,
				file_repository: FileRepository,
				task_repository: TaskRepository,
				bera_job_repository: BeraJobSummaryRepository,
				workstation_repository: WorkstationRepository,
				custom_report_repository: CustomReportRepository,
				custom_report_step_repository: CustomReportStepRepository,
				bera_step_key_result_repository: BeraStepKeyResultRepository,
				bera_weighted_average_repository: BeraWeightedAverageRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new BeraReportMiddleware(new BeraReportRepository(database));
		const schema = new BeraReportSchema();

		return { schema, middleware, controller };
	}
}
