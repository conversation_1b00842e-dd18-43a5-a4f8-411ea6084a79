import { BeraJobSummaryMiddleware } from '../middlewares/index.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { BeraJobSummaryService } from '../service/bera_job_summary.js';
import { BeraJobSummaryController } from '../controllers/bera_job_summary.js';
import {
	BeraReportRepository,
	BeraJobSummaryRepository,
	BeraJobSummaryFilesRepository,
	BeraWeightedAverageRepository
} from '../repository/index.js';

export class BeraJobSummaryFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: BeraJobSummaryController,
			Service: BeraJobSummaryService,
			Repositories: {
				repository: BeraJobSummaryRepository,
				bera_weighted_average_repository: BeraWeightedAverageRepository,
				bera_report_repository: BeraReportRepository,
				bera_job_summary_files_repository: BeraJobSummaryFilesRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new BeraJobSummaryMiddleware(new BeraJobSummaryRepository(database));

		return { middleware, controller };
	}
}
