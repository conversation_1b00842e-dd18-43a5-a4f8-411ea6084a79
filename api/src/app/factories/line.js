import { LineService } from '../service/line.js';
import { LineController } from '../controllers/line.js';
import { LineMiddleware } from '../middlewares/index.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import {
	FileRepository,
	LineRepository,
	WorkstationRepository,
	PreliminaryAnalysisRepository
} from '../repository/index.js';

export class LineFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: LineController,
			Service: LineService,
			Repositories: {
				repository: LineRepository,
				file_repository: FileRepository,
				workstation_repository: WorkstationRepository,
				preliminary_analysis_repository: PreliminaryAnalysisRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new LineMiddleware(new LineRepository(database));

		return { middleware, controller };
	}
}
