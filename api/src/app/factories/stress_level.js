import { StressLevelService } from '../service/stress_level.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { StressLevelRepository } from '../repository/stress_level.js';
import { StressLevelController } from '../controllers/stress_level.js';
import { StressLevelMiddleware } from '../middlewares/stress_level.js';
import { CustomReportStepKeyRepository } from '../repository/custom_report_step_key.js';

export class StressLevelFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: StressLevelController,
			Service: StressLevelService,
			Repositories: {
				repository: StressLevelRepository,
				custom_report_step_key_repository: CustomReportStepKeyRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new StressLevelMiddleware(new StressLevelRepository(database));

		return { middleware, controller };
	}
}
