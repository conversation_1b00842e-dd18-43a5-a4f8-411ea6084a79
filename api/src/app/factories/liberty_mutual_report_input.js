import { UserRepository } from '../repository/v2/user.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { LibertyMutualReportInputMiddleware } from '../middlewares/index.js';
import { LibertyMutualReportInputService } from '../service/liberty_mutual_report_input.js';
import { LibertyMutualReportInputController } from '../controllers/liberty_mutual_report_input.js';
import {
	FileRepository,
	SystemOfUnitsRepository,
	LibertyMutualTaskRepository,
	LibertyMutualReportInputRepository
} from '../repository/index.js';

export class LibertyMutualReportInputFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: LibertyMutualReportInputController,
			Service: LibertyMutualReportInputService,
			Repositories: {
				repository: LibertyMutualReportInputRepository,
				user_repository: UserRepository,
				file_repository: FileRepository,
				task_repository: LibertyMutualTaskRepository,
				system_of_units_repository: SystemOfUnitsRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new LibertyMutualReportInputMiddleware(new LibertyMutualReportInputRepository(database));

		return { middleware, controller };
	}
}
