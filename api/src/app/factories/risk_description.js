import { ControllerProxy } from '../controllers/controller-proxy.js';
import { RiskDescriptionService } from '../service/risk_description.js';
import { RiskDescriptionController } from '../controllers/risk_description.js';
import { RiskDescriptionMiddleware } from '../middlewares/risk_description.js';
import { RiskDescriptionRepository } from '../repository/risk_description.js';

export class RiskDescriptionFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: RiskDescriptionController,
			Service: RiskDescriptionService,
			Repositories: {
				repository: RiskDescriptionRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new RiskDescriptionMiddleware(new RiskDescriptionRepository(database));

		return { middleware, controller };
	}
}
