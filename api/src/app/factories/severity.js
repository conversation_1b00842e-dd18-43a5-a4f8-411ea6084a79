import { SeverityService } from '../service/severity.js';
import { SeverityRepository } from '../repository/severity.js';
import { SeverityController } from '../controllers/severity.js';
import { SeverityMiddleware } from '../middlewares/severity.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';

export class SeverityFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: SeverityController,
			Service: SeverityService,
			Repositories: {
				repository: SeverityRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new SeverityMiddleware(new SeverityRepository(database));

		return { middleware, controller };
	}
}
