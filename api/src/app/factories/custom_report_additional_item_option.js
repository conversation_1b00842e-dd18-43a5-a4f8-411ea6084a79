import { ControllerProxy } from '../controllers/controller-proxy.js';
import { CustomReportAdditionalItemOptionService } from '../service/index.js';
import { CustomReportAdditionalItemOptionController } from '../controllers/index.js';
import { CustomReportAdditionalItemOptionMiddleware } from '../middlewares/index.js';
import { CustomReportAdditionalItemOptionRepository, CustomReportStepKeyRepository } from '../repository/index.js';

export class CustomReportAdditionalItemOptionFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: CustomReportAdditionalItemOptionController,
			Service: CustomReportAdditionalItemOptionService,
			Repositories: {
				repository: CustomReportAdditionalItemOptionRepository,
				custom_report_step_key_repository: CustomReportStepKeyRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new CustomReportAdditionalItemOptionMiddleware(
			new CustomReportAdditionalItemOptionRepository(database)
		);

		return { middleware, controller };
	}
}
