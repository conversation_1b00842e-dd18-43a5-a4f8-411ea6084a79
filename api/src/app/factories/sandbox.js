import { SandboxSchema } from '../schemas/sandbox.js';
import { SandboxService } from '../service/sandbox.js';
import { SandboxController } from '../controllers/sandbox.js';
import { SandboxMiddleware } from '../middlewares/sandbox.js';
import { BaseRepository } from '../repository/v2/base-repository.js';

export class SandboxFactory {
	static createInstance(database, database_sandbox) {
		const kinebot_repository = new BaseRepository(database);
		const sandbox_repository = new BaseRepository(database_sandbox);
		const service = new SandboxService({ kinebot_repository, sandbox_repository });
		const controller = new SandboxController(service);
		const middleware = new SandboxMiddleware(kinebot_repository);
		const schema = new SandboxSchema();

		return { schema, middleware, controller };
	}
}
