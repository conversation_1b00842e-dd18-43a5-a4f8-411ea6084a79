import { KimManualHandlingSchema } from '../schemas/kim_mho.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { KimManualHandlingReportService } from '../service/kim_mho.js';
import { KimManualHandlingReportRepository } from '../repository/kim_mho.js';
import { KimManualHandlingReportController } from '../controllers/kim_mho.js';
import { KimManualHandlingReportMiddleware } from '../middlewares/kim_mho.js';

export class KimManualHandlingReportFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: KimManualHandlingReportController,
			Service: KimManualHandlingReportService,
			Repositories: {
				repository: KimManualHandlingReportRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new KimManualHandlingReportMiddleware(new KimManualHandlingReportRepository(database));
		const schema = new KimManualHandlingSchema();

		return { schema, middleware, controller };
	}
}
