import { UserRepository } from '../repository/v2/user.js';
import { SeraSummaryService } from '../service/sera_summary.js';
import { SeraSummaryMiddleware } from '../middlewares/index.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { SeraSummaryController } from '../controllers/sera_summary.js';
import { ActionPlanV2Repository } from '../repository/v2/action_plan.js';
import { SeraSummaryReviewRepository, SeraSummaryRepository, FileRepository } from '../repository/index.js';

export class SeraSummaryFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: SeraSummaryController,
			Service: SeraSummaryService,
			Repositories: {
				repository: SeraSummaryRepository,
				sera_summary_review_repository: SeraSummaryReviewRepository,
				user_repository: UserRepository,
				file_repository: FileRepository,
				action_plan_repository: ActionPlanV2Repository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new SeraSummaryMiddleware(new SeraSummaryRepository(database));

		return { middleware, controller };
	}
}
