import { TaskService } from '../service/task.js';
import { TaskController } from '../controllers/task.js';
import { TaskMiddleware } from '../middlewares/index.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { CycleRepository, TaskRepository, FileRepository } from '../repository/index.js';

export class TaskFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: TaskController,
			Service: TaskService,
			Repositories: {
				repository: TaskRepository,
				cycle_repository: CycleRepository,
				file_repository: FileRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new TaskMiddleware(new TaskRepository(database));

		return { middleware, controller };
	}
}
