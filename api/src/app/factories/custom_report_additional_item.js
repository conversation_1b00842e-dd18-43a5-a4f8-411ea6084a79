import { ControllerProxy } from '../controllers/controller-proxy.js';
import { CustomReportAdditionalItemService } from '../service/index.js';
import { CustomReportAdditionalItemRepository } from '../repository/index.js';
import { CustomReportAdditionalItemController } from '../controllers/index.js';
import { CustomReportAdditionalItemMiddleware } from '../middlewares/index.js';

export class CustomReportAdditionalItemFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: CustomReportAdditionalItemController,
			Service: CustomReportAdditionalItemService,
			Repositories: {
				repository: CustomReportAdditionalItemRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new CustomReportAdditionalItemMiddleware(new CustomReportAdditionalItemRepository(database));

		return { middleware, controller };
	}
}
