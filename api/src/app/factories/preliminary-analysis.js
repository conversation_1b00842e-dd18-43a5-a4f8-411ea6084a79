import { ControllerProxy } from '../controllers/controller-proxy.js';
import { PreliminaryAnalysisService } from '../service/preliminary-analysis.js';
import { PreliminaryAnalysisController } from '../controllers/preliminary-analysis.js';
import { PreliminaryAnalysisMiddleware } from '../middlewares/preliminary-analysis.js';
import { PreliminaryAnalysisRepository } from '../repository/preliminary-analysis.js';
import { BusinessInformationRepository } from '../repository/business_information.js';

export class PreliminaryAnalysisFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: PreliminaryAnalysisController,
			Service: PreliminaryAnalysisService,
			Repositories: {
				repository: PreliminaryAnalysisRepository,
				business_information_repository: BusinessInformationRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new PreliminaryAnalysisMiddleware(new PreliminaryAnalysisRepository(database));

		return { middleware, controller };
	}
}
