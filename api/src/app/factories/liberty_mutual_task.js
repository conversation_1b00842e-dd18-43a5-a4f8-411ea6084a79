import { ControllerProxy } from '../controllers/controller-proxy.js';
import { LibertyMutualTaskService } from '../service/liberty_mutual_task.js';
import { LibertyMutualTaskRepository } from '../repository/liberty_mutual_task.js';
import { LibertyMutualTaskMiddleware } from '../middlewares/liberty_mutual_task.js';
import { LibertyMutualTaskController } from '../controllers/liberty_mutual_task.js';

export class LibertyMutualTaskFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: LibertyMutualTaskController,
			Service: LibertyMutualTaskService,
			Repositories: {
				repository: LibertyMutualTaskRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new LibertyMutualTaskMiddleware(new LibertyMutualTaskRepository(database));

		return { middleware, controller };
	}
}
