import { EvaluatorService } from '../service/evaluator.js';
import { EvaluatorRepository } from '../repository/evaluator.js';
import { EvaluatorController } from '../controllers/evaluator.js';
import { EvaluatorMiddleware } from '../middlewares/evaluator.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';

export class EvaluatorFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: EvaluatorController,
			Service: EvaluatorService,
			Repositories: {
				repository: EvaluatorRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new EvaluatorMiddleware(new EvaluatorRepository(database));

		return { middleware, controller };
	}
}
