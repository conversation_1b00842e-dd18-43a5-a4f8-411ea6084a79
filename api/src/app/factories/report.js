import { ReportSchema } from '../middlewares/schema/reports.js';
import { ReportMiddleware } from '../middlewares/report.js';
import { ReportController } from '../controllers/reports.js';
import { ReportService } from '../service/report.js';
import { ReportRepository } from '../repository/report.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import CompanyRepository from '../repository/companyRepository.js';
import ReportRepository2 from '../repository/reportRepository.js';
import FileRepository from '../repository/fileRepository.js';
import ReportServiceOld from '../service/report.js';
import ReportControllerDeprecated from '../controllers/reports.js';

export class ReportFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: ReportController,
			Service: ReportService,
			Repositories: {
				repository: ReportRepository
			}
		});

		const proxy_old = new ControllerProxy({
			Controller: ReportControllerDeprecated,
			Service: ReportServiceOld,
			Repositories: {
				repository: ReportRepository,
				company_repository: CompanyRepository,
				file_repository: FileRepository,
				report_repository: ReportRepository2
			}
		});

		const newController = proxy.createInstance(database);
		const oldController = proxy_old.createInstance(database);
		const middleware = new ReportMiddleware(new ReportRepository(database));
		const schema = new ReportSchema();

		return { schema, middleware, newController, controller: oldController };
	}
}
