import { ActionPlanSchema } from '../schemas/action_plan.js';
import { ActionPlanService } from '../service/action_plan.js';
import { ActionPlanController } from '../controllers/action_plan.js';
import { ActionPlanMiddleware } from '../middlewares/action_plan.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import {
	ActionPlanRepository,
	ActionPlanTaskRepository,
	CustomReportStepKeyRepository,
	CustomReportSubStepKeyRepository,
	PreliminaryAnalysisStepRepository,
	CustomReportRepository
} from '../repository/index.js';

export class ActionPlanFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: ActionPlanController,
			Service: ActionPlanService,
			Repositories: {
				repository: ActionPlanRepository,
				action_plan_task_repository: ActionPlanTaskRepository,
				custom_report_step_key_repository: CustomReportStepKeyRepository,
				preliminary_analysis_step_repository: PreliminaryAnalysisStepRepository,
				custom_report_sub_step_key_repository: CustomReportSubStepKeyRepository,
				custom_report_repository: CustomReportRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new ActionPlanMiddleware({
			repository: new ActionPlanRepository(database),
			custom_report_repository: new CustomReportRepository(database)
		});
		const schema = new ActionPlanSchema();

		return { schema, middleware, controller };
	}
}
