import { WorkstationService } from '../service/workstation.js';
import { WorkstationMiddleware } from '../middlewares/index.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { WorkstationController } from '../controllers/workstation.js';
import { PreliminaryAnalysisRepository, WorkstationRepository, FileRepository } from '../repository/index.js';

export class WorkstationFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: WorkstationController,
			Service: WorkstationService,
			Repositories: {
				repository: WorkstationRepository,
				file_repository: FileRepository,
				preliminary_analysis_repository: PreliminaryAnalysisRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new WorkstationMiddleware(new WorkstationRepository(database));

		return { middleware, controller };
	}
}
