import { RebaReportService } from '../service/reba.js';
import { RebaReportSchema } from '../schemas/index.js';
import { RebaReportController } from '../controllers/reba.js';
import { RebaReportMiddleware } from '../middlewares/index.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { RebaReportRepository, FileRepository, RangeRiskRepository } from '../repository/index.js';

export class RebaReportFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: RebaReportController,
			Service: RebaReportService,
			Repositories: {
				repository: RebaReportRepository,
				file_repository: FileRepository,
				range_risk_repository: RangeRiskRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new RebaReportMiddleware(new RebaReportRepository(database));
		const schema = new RebaReportSchema(new RebaReportRepository(database));

		return { schema, middleware, controller };
	}
}
