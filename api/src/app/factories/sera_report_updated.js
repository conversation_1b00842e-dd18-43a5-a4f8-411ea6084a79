import { ControllerProxy } from '../controllers/controller-proxy.js';
import { SeraReportUpdatedService } from '../service/sera_report_updated.js';
import { SeraReportUpdatedController } from '../controllers/sera_report_updated.js';
import { SeraReportUpdatedMiddleware } from '../middlewares/sera_report_updated.js';
import { SeraReportUpdatedRepository } from '../repository/sera_report_updated.js';

export class SeraReportUpdatedUpdatedFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: SeraReportUpdatedController,
			Service: SeraReportUpdatedService,
			Repositories: {
				repository: SeraReportUpdatedRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new SeraReportUpdatedMiddleware(new SeraReportUpdatedRepository(database));

		return { middleware, controller };
	}
}
