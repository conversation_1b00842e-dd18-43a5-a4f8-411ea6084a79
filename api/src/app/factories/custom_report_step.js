import { CustomReportStepController } from '../controllers/custom_report_step.js';
import { CustomReportStepMiddleware } from '../middlewares/custom_report_step.js';
import { CustomReportStepRepository } from '../repository/custom_report_step.js';
import { CustomReportStepService } from '../service/custom_report_step.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';

export class CustomReportStepFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: CustomReportStepController,
			Service: CustomReportStepService,
			Repositories: {
				repository: CustomReportStepRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new CustomReportStepMiddleware(new CustomReportStepRepository(database));

		return { middleware, controller };
	}
}
