import { WorkerSelfEvaluationService } from '../service/index.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { WorkerSelfEvaluationRepository } from '../repository/index.js';
import { WorkerSelfEvaluationController } from '../controllers/index.js';
import { WorkerSelfEvaluationMiddleware } from '../middlewares/index.js';

export class WorkerSelfEvaluationFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: WorkerSelfEvaluationController,
			Service: WorkerSelfEvaluationService,
			Repositories: {
				repository: WorkerSelfEvaluationRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new WorkerSelfEvaluationMiddleware(new WorkerSelfEvaluationRepository(database));

		return { middleware, controller };
	}
}
