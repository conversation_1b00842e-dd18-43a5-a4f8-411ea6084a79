import { FileRepository } from '../repository/file.js';
import { UserRepository } from '../repository/v2/user.js';
import { WorkstationRepository } from '../repository/workstation.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { SystemOfUnitsRepository } from '../repository/system_of_units.js';
import { BusinessInformationRepository } from '../repository/business_information.js';
import { BackCompressiveForceEstimationService } from '../service/back_compressive_force_estimation_report.js';
import { BackCompressiveForceEstimationReportRepository } from '../repository/back_compressive_force_estimation_report.js';
import { BackCompressiveForceEstimationReportMiddleware } from '../middlewares/back_compressive_force_estimation_report.js';
import { BackCompressiveForceEstimationReportController } from '../controllers/back_compressive_force_estimation_report.js';

export class BackCompressiveForceEstimationReportFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: BackCompressiveForceEstimationReportController,
			Service: BackCompressiveForceEstimationService,
			Repositories: {
				repository: BackCompressiveForceEstimationReportRepository,
				user_repository: UserRepository,
				file_repository: FileRepository,
				workstation_repository: WorkstationRepository,
				system_of_units_repository: SystemOfUnitsRepository,
				business_information_repository: BusinessInformationRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new BackCompressiveForceEstimationReportMiddleware(
			new BackCompressiveForceEstimationReportRepository(database)
		);

		return { middleware, controller };
	}
}
