import { VulnerabilityService } from '../service/vulnerability.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { VulnerabilityRepository } from '../repository/vulnerability.js';
import { VulnerabilityController } from '../controllers/vulnerability.js';
import { VulnerabilityMiddleware } from '../middlewares/vulnerability.js';

export class VulnerabilityFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: VulnerabilityController,
			Service: VulnerabilityService,
			Repositories: {
				repository: VulnerabilityRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new VulnerabilityMiddleware(new VulnerabilityRepository(database));

		return { middleware, controller };
	}
}
