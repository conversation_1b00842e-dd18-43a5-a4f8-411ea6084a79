import { UploadSchema } from '../schemas/upload.js';
import { UploadService } from '../service/upload.js';
import { UploadRepository } from '../repository/upload.js';
import { UploadController } from '../controllers/upload.js';
import { UploadMiddleware } from '../middlewares/v2/upload.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { UploadMiddleware as UploadMiddlewareDeprecado } from '../middlewares/upload.js';

export class UploadFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: UploadController,
			Service: UploadService,
			Repositories: {
				repository: UploadRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware2 = new UploadMiddlewareDeprecado(new UploadRepository(database));
		const middleware = new UploadMiddleware(new UploadRepository(database));
		const schema = new UploadSchema();

		return { schema, middleware2, middleware, controller };
	}
}
