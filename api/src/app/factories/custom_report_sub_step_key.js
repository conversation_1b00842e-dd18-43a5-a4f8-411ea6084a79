import { ControllerProxy } from '../controllers/controller-proxy.js';
import { CustomReportSubStepKeyService } from '../service/index.js';
import { CustomReportSubStepKeyController } from '../controllers/index.js';
import { CustomReportSubStepKeyMiddleware } from '../middlewares/index.js';
import { CustomReportSubStepKeysDefaultRiskRepository, CustomReportSubStepKeyRepository } from '../repository/index.js';

export class CustomReportSubStepKeyFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: CustomReportSubStepKeyController,
			Service: CustomReportSubStepKeyService,
			Repositories: {
				repository: CustomReportSubStepKeyRepository,
				custom_report_sub_step_key_default_risk: CustomReportSubStepKeysDefaultRiskRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new CustomReportSubStepKeyMiddleware(new CustomReportSubStepKeyRepository(database));

		return { middleware, controller };
	}
}
