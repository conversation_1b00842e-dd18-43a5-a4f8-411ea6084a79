import {
	SeverityRepository,
	ExposureRepository,
	RiskDamageRepository,
	ActionPlanV2Repository,
	RiskCategoryRepository,
	VulnerabilityRepository,
	RiskDescriptionRepository,
	CustomReportStepRepository,
	CustomReportResultRepository,
	CustomReportStepKeyRepository,
	CustomReportSubStepKeyRepository,
	CustomReportStepKeyResultRepository,
	CustomReportSubStepKeyResultRepository,
	CustomReportAdditionalItemOptionRepository,
	CustomReportStepKeyAdditionalItemRepository,
	CustomReportStepKeyAdditionalItemResultRepository
} from '../repository/index.js';
import { CustomReportStepKeyResultService } from '../service/custom_report_step_key_result.js';
import { CustomReportStepKeyResultMiddleware } from '../middlewares/custom_report_step_key_result.js';
import { CustomReportStepKeyResultController } from '../controllers/custom_report_step_key_result.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';

export class CustomReportStepKeyResultFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: CustomReportStepKeyResultController,
			Service: CustomReportStepKeyResultService,
			Repositories: {
				repository: CustomReportStepKeyResultRepository,
				custom_report_step_key_additional_item_results_repository:
					CustomReportStepKeyAdditionalItemResultRepository,
				custom_report_step_keys_additional_items_repository: CustomReportStepKeyAdditionalItemRepository,
				custom_report_additional_item_option_repository: CustomReportAdditionalItemOptionRepository,
				custom_report_sub_step_key_result_repository: CustomReportSubStepKeyResultRepository,
				custom_report_sub_step_key_repository: CustomReportSubStepKeyRepository,
				custom_report_step_key_repository: CustomReportStepKeyRepository,
				custom_report_result_repository: CustomReportResultRepository,
				custom_report_step_repository: CustomReportStepRepository,
				risk_description_repository: RiskDescriptionRepository,
				vulnerability_repository: VulnerabilityRepository,
				risk_category_repository: RiskCategoryRepository,
				repository: CustomReportStepKeyResultRepository,
				risk_damage_repository: RiskDamageRepository,
				action_plan_repository: ActionPlanV2Repository,
				severity_repository: SeverityRepository,
				exposure_repository: ExposureRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new CustomReportStepKeyResultMiddleware(new CustomReportStepKeyResultRepository(database));

		return { middleware, controller };
	}
}
