import { FileRepository } from '../repository/file.js';
import { StrainIndexSchema } from '../schemas/strain_index.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { WorkstationRepository } from '../repository/workstation.js';
import { StrainIndexReportService } from '../service/strain_index.js';
import { StrainIndexReportRepository } from '../repository/strain_index.js';
import { StrainIndexReportController } from '../controllers/strain_index.js';
import { StrainIndexReportMiddleware } from '../middlewares/strain_index.js';

export class StrainIndexReportFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: StrainIndexReportController,
			Service: StrainIndexReportService,
			Repositories: {
				repository: StrainIndexReportRepository,
				file_repository: FileRepository,
				workstation_repository: WorkstationRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new StrainIndexReportMiddleware(new StrainIndexReportRepository(database));
		const schema = new StrainIndexSchema();

		return { schema, middleware, controller };
	}
}
