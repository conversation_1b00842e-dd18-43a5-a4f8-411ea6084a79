import { UserRepository } from '../../repository/user.js';
import { FileRepository } from '../../repository/file.js';
import { ActionPlanSchema } from '../../schemas/action_plan.js';
import { ActionPlanV2Service } from '../../service/v2/action_plan.js';
import { ControllerProxy } from '../../controllers/controller-proxy.js';
import { NotificationRepository } from '../../repository/notification.js';
import { ActionPlanV2Repository } from '../../repository/v2/action_plan.js';
import { ActionPlanV2Controller } from '../../controllers/v2/action_plan.js';
import { ActionPlanV2Middleware } from '../../middlewares/v2/action_plan.js';
import { ErgonomicToolRepository } from '../../repository/ergonomic_tool.js';
import { BusinessInformationRepository } from '../../repository/business_information.js';
import { CustomReportStepKeyResultRepository } from '../../repository/custom_report_step_key_result.js';
import { CustomReportSubStepKeyResultRepository } from '../../repository/custom_report_sub_step_key_result.js';
import { ActionPlanTaskRepository } from '../../repository/action_plan_task.js';
import { CustomReportStepKeyRepository } from '../../repository/custom_report_step_key.js';
import { PreliminaryAnalysisStepRepository } from '../../repository/preliminary_analysis_step.js';
import { CustomReportSubStepKeyRepository } from '../../repository/custom_report_sub_step_key.js';
import { CustomReportRepository } from '../../repository/custom_report.js';

export class ActionPlanV2Factory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: ActionPlanV2Controller,
			Service: ActionPlanV2Service,
			Repositories: {
				repository: ActionPlanV2Repository,
				user_repository: UserRepository,
				file_repository: FileRepository,
				notification_repository: NotificationRepository,
				ergonomic_tool_repository: ErgonomicToolRepository,
				business_information_repository: BusinessInformationRepository,
				custom_report_step_key_result_repository: CustomReportStepKeyResultRepository,
				custom_report_sub_step_key_result_repository: CustomReportSubStepKeyResultRepository,
				action_plan_task_repository: ActionPlanTaskRepository,
				custom_report_step_key_repository: CustomReportStepKeyRepository,
				preliminary_analysis_step_repository: PreliminaryAnalysisStepRepository,
				custom_report_sub_step_key_repository: CustomReportSubStepKeyRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new ActionPlanV2Middleware({
			repository: new ActionPlanV2Repository(database),
			custom_report_repository: new CustomReportRepository(database)
		});
		const schema = new ActionPlanSchema();

		return { schema, middleware, controller };
	}
}
