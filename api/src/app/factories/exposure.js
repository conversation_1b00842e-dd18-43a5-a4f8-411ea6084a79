import { ExposureService } from '../service/exposure.js';
import { ExposureRepository } from '../repository/exposure.js';
import { ExposureController } from '../controllers/exposure.js';
import { ExposureMiddleware } from '../middlewares/exposure.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';

export class ExposureFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: ExposureController,
			Service: ExposureService,
			Repositories: {
				repository: ExposureRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new ExposureMiddleware(new ExposureRepository(database));

		return { middleware, controller };
	}
}
