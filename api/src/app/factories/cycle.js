import { CycleService } from '../service/cycle.js';
import { CycleRepository } from '../repository/cycle.js';
import { CycleMiddleware } from '../middlewares/cycle.js';
import { CycleController } from '../controllers/cycle.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';

export class CycleFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: CycleController,
			Service: CycleService,
			Repositories: { repository: CycleRepository }
		});
		const controller = proxy.createInstance(database);
		const middleware = new CycleMiddleware(new CycleRepository(database));

		return { middleware, controller };
	}
}
