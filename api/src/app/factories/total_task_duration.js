import { ControllerProxy } from '../controllers/controller-proxy.js';
import { TotalTaskDurationService } from '../service/total_task_duration.js';
import { TotalTaskDurationController } from '../controllers/total_task_duration.js';
import { TotalTaskDurationMiddleware } from '../middlewares/total_task_duration.js';
import { TotalTaskDurationRepository } from '../repository/total_task_duration.js';

export class TotalTaskDurationFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: TotalTaskDurationController,
			Service: TotalTaskDurationService,
			Repositories: {
				repository: TotalTaskDurationRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new TotalTaskDurationMiddleware(new TotalTaskDurationRepository(database));

		return { middleware, controller };
	}
}
