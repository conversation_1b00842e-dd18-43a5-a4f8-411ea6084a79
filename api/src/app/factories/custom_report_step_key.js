import { ControllerProxy } from '../controllers/controller-proxy.js';
import { CustomReportStepKeyService } from '../service/custom_report_step_key.js';
import { CustomReportStepKeyController } from '../controllers/custom_report_step_key.js';
import { CustomReportStepKeyMiddleware } from '../middlewares/custom_report_step_key.js';
import { CustomReportStepKeysDefaultRiskRepository, CustomReportStepKeyRepository } from '../repository/index.js';

export class CustomReportStepKeyFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: CustomReportStepKeyController,
			Service: CustomReportStepKeyService,
			Repositories: {
				repository: CustomReportStepKeyRepository,
				custom_report_step_key_default_risk: CustomReportStepKeysDefaultRiskRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new CustomReportStepKeyMiddleware(new CustomReportStepKeyRepository(database));

		return { middleware, controller };
	}
}
