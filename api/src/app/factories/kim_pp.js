import { KimPushPullReportService } from '../service/kim_pp.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { <PERSON><PERSON>ushPullReportController } from '../controllers/kim_pp.js';
import { KimPushPullReportMiddleware } from '../middlewares/kim_pp.js';
import { KimPushPullReportRepository } from '../repository/kim_pp.js';

export class KimPushPullReportFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: KimPushPullReportController,
			Service: KimPushPullReportService,
			Repositories: {
				repository: KimPushPullReportRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new KimPushPullReportMiddleware(new KimPushPullReportRepository(database));

		return { middleware, controller };
	}
}
