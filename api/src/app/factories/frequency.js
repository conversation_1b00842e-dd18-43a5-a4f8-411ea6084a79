import { FrequencyService } from '../service/frequency.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { FrequencyController } from '../controllers/frequency.js';
import { FrequencyMiddleware } from '../middlewares/frequency.js';
import { FrequencyRepository } from '../repository/frequency.js';

export class FrequencyFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: FrequencyController,
			Service: FrequencyService,
			Repositories: {
				repository: FrequencyRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new FrequencyMiddleware(new FrequencyRepository(database));

		return { middleware, controller };
	}
}
