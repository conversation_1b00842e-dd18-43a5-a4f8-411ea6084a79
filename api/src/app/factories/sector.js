import { SectorService } from '../service/index.js';
import { SectorMiddleware } from '../middlewares/v2/sector.js';
import { SectorSchema } from '../middlewares/schema/index.js';
import { SectorController } from '../controllers/index.js';
import { FileRepository } from '../repository/file.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import SectorController2 from '../controllers/sector.js';
import SectorServiceOld from '../service/sector.js';
import SectorRepositoryOld from '../repository/sectorRepository.js';

// OBS: REALIZAR A COLETA DE LOGS DOS SERVIÇOS REFATORADOS!
import { SectorMiddleware as SectorMiddlewareDeprecado } from '../middlewares/index.js';
import {
	LineRepository,
	SectorRepository,
	WorkstationRepository,
	PreliminaryAnalysisRepository
} from '../repository/index.js';

export class SectorFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: SectorController,
			Service: SectorService,
			Repositories: {
				repository: SectorRepository,
				line_repository: LineRepository,
				file_repository: FileRepository,
				workstation_repository: WorkstationRepository,
				preliminary_analysis_repository: PreliminaryAnalysisRepository
			}
		});

		const proxy_old = new ControllerProxy({
			Controller: SectorController2,
			Service: SectorServiceOld,
			Repositories: {
				repository: SectorRepositoryOld
			}
		});

		const controller = proxy.createInstance(database);

		const schema = new SectorSchema(); // Deprecado
		const middleware_DEPRECATED = new SectorMiddlewareDeprecado(database); // Deprecado
		const middleware = new SectorMiddleware(new SectorRepository(database));
		const sectorController = proxy_old.createInstance(database);

		return { middleware, schema, middleware_DEPRECATED, controller, sectorController };
	}
}
