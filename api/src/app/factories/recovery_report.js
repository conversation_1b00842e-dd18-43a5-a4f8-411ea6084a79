import { ControllerProxy } from '../controllers/controller-proxy.js';
import { RecoveryReportService } from '../service/recovery_report.js';
import { RecoveryReportRepository } from '../repository/recovery_report.js';
import { RecoveryReportController } from '../controllers/recovery_report.js';
import { RecoveryReportMiddleware } from '../middlewares/recovery_report.js';
import { RecoveryReportSchema } from '../middlewares/schema/recovery_report.js';

export class RecoveryReportFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: RecoveryReportController,
			Service: RecoveryReportService,
			Repositories: {
				repository: RecoveryReportRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new RecoveryReportMiddleware(new RecoveryReportRepository(database));
		const schema = new RecoveryReportSchema();

		return { schema, middleware, controller };
	}
}
