import * as Repo from '../repository/index.js';
import { CustomReportResultSchema } from '../schemas/custom_report_result.js';
import { UserRepository, CompanyRepository } from '../repository/v2/index.js';
import { CustomReportResultService } from '../service/custom_report_result.js';
import { CustomReportResultController } from '../controllers/custom_report_result.js';
import { CustomReportResultMiddleware } from '../middlewares/custom_report_result.js';

export class CustomReportResultFactory {
	static createInstance(database) {
		const custom_report_step_key_additional_item_result_repository =
			new Repo.CustomReportStepKeyAdditionalItemResultRepository(database);
		const custom_report_sub_step_key_result_repository = new Repo.CustomReportSubStepKeyResultRepository(database);
		const custom_report_result_actions_log_repository = new Repo.CustomReportResultActionLogRepository(database);
		const custom_report_step_key_result_repository = new Repo.CustomReportStepKeyResultRepository(database);
		const characteristic_result_repository = new Repo.CharacteristicResultRepository(database);
		const work_condition_result_repository = new Repo.WorkConditionResultRepository(database);
		const business_information_repository = new Repo.BusinessInformationRepository(database);
		const custom_report_review_repository = new Repo.CustomReportReviewRepository(database);
		const custom_report_step_repository = new Repo.CustomReportStepRepository(database);
		const custom_report_repository = new Repo.CustomReportRepository(database);
		const organization_repository = new Repo.OrganizationRepository(database);
		const workstation_repository = new Repo.WorkstationRepository(database);
		const action_plan_repository = new Repo.ActionPlanV2Repository(database);
		const evaluator_repository = new Repo.EvaluatorRepository(database);
		const repository = new Repo.CustomReportResultRepository(database);
		const company_repository = new CompanyRepository(database);
		const file_repository = new Repo.FileRepository(database);
		const upload_repository = new Repo.UploadRepository(database);
		const user_repository = new UserRepository(database);
		const line_repository = new Repo.LineRepository(database);
		const sector_repository = new Repo.SectorRepository(database);

		const service = new CustomReportResultService({
			repository,
			file_repository,
			upload_repository,
			user_repository,
			company_repository,
			evaluator_repository,
			action_plan_repository,
			workstation_repository,
			organization_repository,
			custom_report_repository,
			custom_report_step_repository,
			custom_report_review_repository,
			business_information_repository,
			characteristic_result_repository,
			work_condition_result_repository,
			custom_report_step_key_result_repository,
			custom_report_result_actions_log_repository,
			custom_report_sub_step_key_result_repository,
			custom_report_step_key_additional_item_result_repository,
			line_repository,
			sector_repository
		});
		const controller = new CustomReportResultController(service);
		const middleware = new CustomReportResultMiddleware({ repository, custom_report_repository, user_repository });
		const schema = new CustomReportResultSchema();
		return { schema, middleware, controller };
	}
}
