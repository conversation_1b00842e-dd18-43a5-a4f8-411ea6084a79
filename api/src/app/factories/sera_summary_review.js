import { ControllerProxy } from '../controllers/controller-proxy.js';
import { SeraSummaryReviewMiddleware } from '../middlewares/index.js';
import { SeraSummaryReviewService } from '../service/sera_summary_review.js';
import { SeraSummaryReviewController } from '../controllers/sera_summary_review.js';
import { SeraSummaryReviewRepository, CustomReportRepository } from '../repository/index.js';

export class SeraSummaryReviewFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: SeraSummaryReviewController,
			Service: SeraSummaryReviewService,
			Repositories: {
				repository: SeraSummaryReviewRepository,
				custom_report_repository: CustomReportRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new SeraSummaryReviewMiddleware(new SeraSummaryReviewRepository(database));

		return { middleware, controller };
	}
}
