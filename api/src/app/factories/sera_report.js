import { SeraReportSchema } from '../schemas/index.js';
import { SeraReportService } from '../service/sera_report.js';
import { SeraReportMiddleware } from '../middlewares/index.js';
import { SeraReportController } from '../controllers/sera_report.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import {
	SeraReportRepository,
	SeraSummaryRepository,
	ActionPlanV2Repository,
	SeraReportUpdatedRepository,
	SeraSummaryReviewRepository,
	SeraReviewSelectorRepository,
	SeraReviewTasksResultRepository
} from '../repository/index.js';

export class SeraReportFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: SeraReportController,
			Service: SeraReportService,
			Repositories: {
				repository: SeraReportRepository,
				action_plan_repository: ActionPlanV2Repository,
				sera_summary_repository: SeraSummaryRepository,
				sera_summary_review_repository: SeraSummaryReviewRepository,
				sera_report_updated_repository: SeraReportUpdatedRepository,
				sera_review_selector_repository: SeraReviewSelectorRepository,
				sera_review_tasks_result_repository: SeraReviewTasksResultRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new SeraReportMiddleware(new SeraReportRepository(database));
		const schema = new SeraReportSchema();

		return { schema, middleware, controller };
	}
}
