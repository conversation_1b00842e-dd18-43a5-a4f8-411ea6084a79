import { AngleTimeReportService } from '../service/angle_time.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { AngleTimeReportRepository } from '../repository/angle_time.js';
import { AngleTimeReportController } from '../controllers/angle_time.js';
import { AngleTimeReportMiddleware } from '../middlewares/angle_time.js';

export class AngleTimeFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: AngleTimeReportController,
			Service: AngleTimeReportService,
			Repositories: {
				repository: AngleTimeReportRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new AngleTimeReportMiddleware(new AngleTimeReportRepository(database));

		return { middleware, controller };
	}
}
