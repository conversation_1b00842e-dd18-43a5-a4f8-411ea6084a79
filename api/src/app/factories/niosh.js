import { NioshReportService } from '../service/niosh.js';
import { NioshReportRepository } from '../repository/index.js';
import { NioshReportMiddleware } from '../middlewares/index.js';
import { NioshReportController } from '../controllers/niosh.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';

export class NioshReportFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: NioshReportController,
			Service: NioshReportService,
			Repositories: {
				repository: NioshReportRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new NioshReportMiddleware(new NioshReportRepository(database));

		return { middleware, controller };
	}
}
