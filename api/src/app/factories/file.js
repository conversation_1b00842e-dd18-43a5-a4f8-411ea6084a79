import { FileController } from '../controllers/index.js';
import { FileSchema } from '../middlewares/schema/index.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { FileMiddlewareOld, FileMiddleware } from '../middlewares/file.js';
import { FileRepository, UserRepository } from '../repository/index.js';
import FileRepositoryOld from '../repository/fileRepository.js';
import FileServiceOld, { FileService } from '../service/file.js';

export class FileFactory {
	static createInstance(database) {
		const repository = new FileRepository(database);
		const proxy = new ControllerProxy({
			Controller: FileController,
			Service: FileService,
			Repositories: {
				fileRepository: FileRepository,
				userRepository: UserRepository
			}
		});

		const proxy_old = new ControllerProxy({
			Controller: FileController,
			Service: FileServiceOld,
			Repositories: {
				repository: FileRepositoryOld
			}
		});
		const controller = proxy.createInstance(database);
		const controller_old = proxy_old.createInstance(database);
		const middleware = new FileMiddleware(repository);
		const schema = new FileSchema();
		const oldMiddleware = new FileMiddlewareOld(database);

		return { schema, oldMiddleware, middleware, controller, repository, controllerOld: controller_old };
	}
}
