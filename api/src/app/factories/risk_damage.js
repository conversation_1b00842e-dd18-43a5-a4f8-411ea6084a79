import { RiskDamageService } from '../service/risk_damage.js';
import { SeverityRepository } from '../repository/severity.js';
import { RiskDamageController } from '../controllers/risk_damage.js';
import { RiskDamageMiddleware } from '../middlewares/risk_damage.js';
import { RiskDamageRepository } from '../repository/risk_damage.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';

export class RiskDamageFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: RiskDamageController,
			Service: RiskDamageService,
			Repositories: {
				repository: RiskDamageRepository,
				severity_repository: SeverityRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new RiskDamageMiddleware(new RiskDamageRepository(database));

		return { middleware, controller };
	}
}
