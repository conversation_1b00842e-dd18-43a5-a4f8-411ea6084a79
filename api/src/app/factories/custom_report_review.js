import * as Repositories from '../repository/index.js';
import { CustomReportReviewService } from '../service/index.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';
import { CustomReportReviewMiddleware } from '../middlewares/index.js';
import { CustomReportReviewController } from '../controllers/index.js';

export class CustomReportReviewFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: CustomReportReviewController,
			Service: CustomReportReviewService,
			Repositories: {
				repository: Repositories.CustomReportReviewRepository,
				file_repository: Repositories.FileRepository,
				upload_repository: Repositories.UploadRepository,
				line_repository: Repositories.LineRepository,
				sector_repository: Repositories.SectorRepository,
				action_plan_repository: Repositories.ActionPlanRepository,
				workstation_repository: Repositories.WorkstationRepository,
				custom_report_repository: Repositories.CustomReportRepository,
				action_plan_task_repository: Repositories.ActionPlanTaskRepository,
				custom_report_step_repository: Repositories.CustomReportStepRepository,
				custom_report_result_repository: Repositories.CustomReportResultRepository,
				business_information_repository: Repositories.BusinessInformationRepository,
				custom_report_step_key_result_repository: Repositories.CustomReportStepKeyResultRepository,
				custom_report_result_actions_log_repository: Repositories.CustomReportResultActionLogRepository,
				custom_report_sub_step_key_result_repository: Repositories.CustomReportSubStepKeyResultRepository,
				custom_report_step_key_additional_item_result_repository:
					Repositories.CustomReportStepKeyAdditionalItemResultRepository,
				custom_report_step_key_result_repository: Repositories.CustomReportStepKeyResultRepository,
				business_information_repository: Repositories.BusinessInformationRepository,
				custom_report_result_repository: Repositories.CustomReportResultRepository,
				custom_report_step_repository: Repositories.CustomReportStepRepository,
				custom_report_repository: Repositories.CustomReportRepository,
				organization_repository: Repositories.OrganizationRepository,
				workstation_repository: Repositories.WorkstationRepository,
				action_plan_repository: Repositories.ActionPlanV2Repository,
				evaluator_repository: Repositories.EvaluatorRepository,
				company_repository: Repositories.CompanyRepository,
				user_repository: Repositories.UserRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new CustomReportReviewMiddleware(new Repositories.CustomReportReviewRepository(database));

		return { middleware, controller };
	}
}
