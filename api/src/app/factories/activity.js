import { ActivityService } from '../service/index.js';
import { ActivityRepository } from '../repository/index.js';
import { ActivityMiddleware } from '../middlewares/index.js';
import { ActivityController } from '../controllers/index.js';
import { WorkstationRepository } from '../repository/workstation.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';

export class ActivityFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: ActivityController,
			Service: ActivityService,
			Repositories: {
				repository: ActivityRepository,
				workstation_repository: WorkstationRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new ActivityMiddleware(new ActivityRepository(database));

		return { middleware, controller };
	}
}
