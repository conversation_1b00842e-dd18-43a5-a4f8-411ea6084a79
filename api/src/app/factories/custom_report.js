import * as Factory from './index.js';
import * as Repo from '../repository/index.js';
import { CustomReportService } from '../service/index.js';
import { CustomReportSchema } from '../schemas/index.js';
import { CustomReportController } from '../controllers/index.js';
import { CustomReportMiddleware } from '../middlewares/index.js';
import { UserRepository, CompanyRepository } from '../repository/v2/index.js';
import { ControllerProxy } from '../controllers/controller-proxy.js';

export class CustomReportFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: CustomReportController,
			Service: CustomReportService,
			Repositories: {
				custom_report_step_key_additional_item_result_repository:
					Repo.CustomReportStepKeyAdditionalItemResultRepository,
				custom_report_sub_step_key_result_repository: Repo.CustomReportSubStepKeyResultRepository,
				custom_report_step_key_result_repository: Repo.CustomReportStepKeyResultRepository,
				worker_self_evaluation_repository: Repo.WorkerSelfEvaluationRepository,
				characteristic_result_repository: Repo.CharacteristicResultRepository,
				work_condition_result_repository: Repo.WorkConditionResultRepository,
				custom_report_result_repository: Repo.CustomReportResultRepository,
				custom_report_review_repository: Repo.CustomReportReviewRepository,
				custom_report_step_repository: Repo.CustomReportStepRepository,
				bera_job_summary_repository: Repo.BeraJobSummaryRepository,
				organization_repository: Repo.OrganizationRepository,
				repository: Repo.CustomReportRepository,
				company_repository: CompanyRepository,
				file_repository: Repo.FileRepository,
				user_repository: UserRepository
			}
		});

		const customReportSubStepKeyFactory = Factory.CustomReportSubStepKeyFactory.createInstance(database);
		const customReportStepKeyFactory = Factory.CustomReportStepKeyFactory.createInstance(database);
		const totalTaskDurationFactory = Factory.TotalTaskDurationFactory.createInstance(database);
		const customReportStepFactory = Factory.CustomReportStepFactory.createInstance(database);
		const riskDescriptionFactory = Factory.RiskDescriptionFactory.createInstance(database);
		const vulnerabilityFactory = Factory.VulnerabilityFactory.createInstance(database);
		const riskCategoryFactory = Factory.RiskCategoryFactory.createInstance(database);
		const stressLevelFactory = Factory.StressLevelFactory.createInstance(database);
		const riskDamageFactory = Factory.RiskDamageFactory.createInstance(database);
		const frequencyFactory = Factory.FrequencyFactory.createInstance(database);
		const exposureFactory = Factory.ExposureFactory.createInstance(database);
		const severityFactory = Factory.SeverityFactory.createInstance(database);
		const controller = proxy.createInstance(database);
		const middleware = new CustomReportMiddleware({
			repository: new Repo.CustomReportRepository(database),
			user_repository: new UserRepository(database)
		});
		const schema = new CustomReportSchema();

		return {
			schema,
			middleware,
			controller,
			severityFactory,
			exposureFactory,
			frequencyFactory,
			riskDamageFactory,
			stressLevelFactory,
			riskCategoryFactory,
			vulnerabilityFactory,
			riskDescriptionFactory,
			customReportStepFactory,
			totalTaskDurationFactory,
			customReportStepKeyFactory,
			customReportSubStepKeyFactory
		};
	}
}
