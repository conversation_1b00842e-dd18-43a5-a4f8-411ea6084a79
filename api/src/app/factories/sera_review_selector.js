import { ControllerProxy } from '../controllers/controller-proxy.js';
import { SeraReviewSelectorRepository } from '../repository/index.js';
import { SeraReviewSelectorMiddleware } from '../middlewares/index.js';
import { SeraReviewSelectorService } from '../service/sera_review_selector.js';
import { SeraReviewSelectorController } from '../controllers/sera_review_selector.js';

export class SeraReviewSelectorFactory {
	static createInstance(database) {
		const proxy = new ControllerProxy({
			Controller: SeraReviewSelectorController,
			Service: SeraReviewSelectorService,
			Repositories: {
				repository: SeraReviewSelectorRepository
			}
		});
		const controller = proxy.createInstance(database);
		const middleware = new SeraReviewSelectorMiddleware(new SeraReviewSelectorRepository(database));

		return { middleware, controller };
	}
}
