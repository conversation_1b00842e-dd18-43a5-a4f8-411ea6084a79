import { logger } from '../helpers/index.js';
import { BaseRepository } from './v2/base-repository.js';

export class KimManualHandlingReportRepository extends BaseRepository {
	constructor(database) {
		super(database);
	}

	async countAllByRisk(params) {
		logger.info('[KimManualHandlingReport] repository - countAllByRisk init', { params });
		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date,
			user_id
		} = params;
		try {
			const query = `
				SELECT
					COUNT(CASE WHEN kim_mho.risk_score < 20 THEN kim_mho.id END) AS low,
					COUNT(CASE WHEN kim_mho.risk_score >= 20 AND kim_mho.risk_score < 50 THEN kim_mho.id END) AS slightly_increased ,
					COUNT(CASE WHEN kim_mho.risk_score >= 50 AND kim_mho.risk_score < 100 THEN kim_mho.id END) AS substantially_increased,
					COUNT(CASE WHEN kim_mho.risk_score >= 100 THEN kim_mho.id END) AS high
				FROM kim_mho_reports as kim_mho
					INNER JOIN files ON kim_mho.file_id = files.id AND files.is_active = 1
					INNER JOIN workstations ON workstations.id = files.workstation_id
					INNER JOIN lines AS l ON l.id = workstations.line_id
					INNER JOIN sectors ON sectors.id = l.sector_id
				WHERE files.organization_id = :organization_id
					AND kim_mho.is_active = 1
					AND workstations.deleted_at IS NULL
					AND l.deleted_at IS NULL
					AND sectors.is_active = 1
					AND files.is_active = 1
					${company_id ? 'AND files.company_id = :company_id' : 'AND files.company_id IN(:companies_with_user_access)'}
					${sector_id ? 'AND sectors.id = :sector_id' : ''}
					${line_id ? 'AND l.id = :line_id' : ''}
					${workstation_id ? 'AND workstations.id = :workstation_id' : ''}
					${start_date ? 'AND DATE(kim_mho.created_at) >= DATE(:start_date)' : ''}
					${end_date ? 'AND DATE(kim_mho.created_at) <= DATE(:end_date)' : ''}
					${user_id ? 'AND kim_mho.report_user_id = :user_id' : ''}
			`;

			const config = {
				nest: true,
				replacements: {
					organization_id,
					company_id,
					sector_id,
					line_id,
					workstation_id,
					companies_with_user_access,
					start_date,
					end_date,
					user_id
				}
			};

			const [data] = await this.db.sequelize.query(query, config);

			logger.info('[KimManualHandlingReport] repository - countAllByRisk success');
			return [data, null];
		} catch (error) {
			logger.error('[KimManualHandling] repository - countAllByRisk error');
			return [null, error];
		} finally {
			logger.info('[KimManualHandling] repository - countAllByRisk finish');
		}
	}
}
