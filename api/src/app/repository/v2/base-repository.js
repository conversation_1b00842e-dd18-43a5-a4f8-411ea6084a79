import { logger } from '../../helpers/index.js';

export class BaseRepository {
	constructor(database) {
		this.db = database;
	}

	#innerJoinCheckingACL({ companyId }) {
		let join_company = '';
		if (companyId) {
			join_company = `INNER JOIN companies AS company ON acl.company_id = company.id`;
		}
		return { joinCompany: join_company };
	}

	#whereCheckingACL({ companyId }) {
		let where_company = '';
		if (companyId) {
			where_company = `&& company.id = :companyId AND company.is_active = 1`;
		}
		return { companyId: where_company };
	}

	async checkingACL(attributes) {
		const { companyId } = this.#whereCheckingACL(attributes);
		const { joinCompany } = this.#innerJoinCheckingACL(attributes);

		const query = `
			SELECT
				acl.*
			FROM users AS user
				INNER JOIN user_accesses AS acl ON
					user.id = acl.user_id
				INNER JOIN organizations AS organization ON
					acl.organization_id = organization.id
				${joinCompany}
			WHERE(
				user.id = :userId  AND user.is_active = 1
					&&
				organization.id = :organizationId AND organization.is_active = 1
				${companyId}
			)
		`;

		const [result] = await this.db.sequelize.query(query, {
			replacements: attributes
		});

		return result;
	}

	async checkingHierarchyACL(hierarchy) {
		logger.info('[BaseRepository] checkingHierarchyACL init');
		try {
			const query = `
			SELECT
				COUNT(*) as total
			FROM users AS user
				INNER JOIN user_accesses AS acl ON
					user.id = acl.user_id
				INNER JOIN organizations AS organization ON
					acl.organization_id = organization.id
				${hierarchy.company_id ? 'INNER JOIN companies AS company ON acl.company_id = company.id' : ''}
				${hierarchy.sector_id ? 'INNER JOIN sectors AS sector ON sector.company_id = company.id' : ''}
				${hierarchy.line_id ? 'INNER JOIN lines AS line ON line.sector_id = sector.id' : ''}
				${hierarchy.workstation_id ? 'INNER JOIN workstations AS workstation ON workstation.line_id = line.id' : ''}
			WHERE(
				user.id = :user_id  AND user.is_active = 1
					&&
				organization.id = :organization_id AND organization.is_active = 1
				${hierarchy.company_id ? '&& company.id = :company_id AND company.is_active = 1' : ''}
				${hierarchy.sector_id ? '&& sector.id = :sector_id AND sector.is_active = 1' : ''}
				${hierarchy.line_id ? '&& line.id = :line_id AND line.deleted_at IS NULL' : ''}
				${hierarchy.workstation_id ? '&& workstation.id = :workstation_id AND workstation.deleted_at IS NULL' : ''}
			)
		`;

			const result = await this.db.sequelize.query(query, {
				replacements: hierarchy,
				plain: true
			});

			logger.info('[BaseRepository] checkingHierarchyACL success');
			return [result?.total, null];
		} catch (error) {
			logger.error('[BaseRepository] checkingHierarchyACL error');
			return [null, error];
		} finally {
			logger.info('[BaseRepository] checkingHierarchyACL finish');
		}
	}

	async checkingUserAccess(attributes) {
		const { userId } = attributes;
		const query = `
			SELECT
				acl.company_id AS id
			FROM user_accesses AS acl
				INNER JOIN organizations AS organization ON acl.organization_id = organization.id
				INNER JOIN companies AS company ON acl.company_id = company.id
			WHERE
				acl.user_id = :user_id
				AND acl.is_active = 1
				AND acl.company_id IS NOT NULL
				AND organization.is_active = 1
				AND company.is_active = 1
			GROUP BY id
		`;

		const [result] = await this.db.sequelize.query(query, {
			replacements: {
				user_id: userId
			}
		});

		return result;
	}
}
