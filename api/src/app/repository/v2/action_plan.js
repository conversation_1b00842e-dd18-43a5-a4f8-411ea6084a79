import { logger } from '../../helpers/logger.js';
import { BaseRepository } from './base-repository.js';
import { GetHistorySQL } from '../../mappers/ActionPlan/GetHistorySQL.js';
import { GetCommentsSQL } from '../../mappers/ActionPlan/GetCommentsSQL.js';
import { GetFindByPkSQL } from '../../mappers/ActionPlan/GetFindByPkSQL.js';
import { GetActionPlansSQL } from '../../mappers/ActionPlan/GetActionPlansSQL.js';
import { GetRelatedToolsSQL } from '../../mappers/ActionPlan/GetRelatedToolsSQL.js';
import { GetReportOriginSQL } from '../../mappers/ActionPlan/GetReportOriginSQL.js';
import { GetOriginOptionsSQL } from '../../mappers/ActionPlan/GetOriginOptionsSQL.js';
import { GetReportOriginsSQL } from '../../mappers/ActionPlan/GetReportOriginsSQL.js';
import { UpdateReportScoreSQL } from '../../mappers/ActionPlan/UpdateReportScoreSQL.js';
import { DeleteActionPlansSQL } from '../../mappers/ActionPlan/DeleteActionPlansSQL.js';
import { GetActionPlansForBoardSQL } from '../../mappers/ActionPlan/GetActionPlansForBoardSQL.js';
import { GetCountActionPlansDelayedSQL } from '../../mappers/ActionPlan/GetCountActionPlansDelayedSQL.js';
import { GetCountActionPlansByStatusSQL } from '../../mappers/ActionPlan/GetCountActionPlansByStatusSQL.js';
import { GetListCountsActionPlansSQL } from '../../mappers/action-plans/v2/GetListCountsActionPlansSQL.js';
import { GetListActionPlansOriginCountSQL } from '../../mappers/action-plans/v2/GetListActionPlansOriginCountSQL.js';

export class ActionPlanV2Repository extends BaseRepository {
	constructor(database) {
		super(database);
		this.action_plan = this.db.ActionPlanV2;
		this.action_plan_attachment = this.db.ActionPlanAttachment;
		this.action_plan_comment = this.db.ActionPlanComment;
		this.action_plan_history = this.db.ActionPlanHistoryV2;
		this.action_plan_history_type = this.db.ActionPlanHistoryType;
		this.action_plan_origin = this.db.ActionPlanOrigin;
		this.action_plan_related_report = this.db.ActionPlanRelatedReport;
		this.action_plan_task = this.db.ActionPlanTask;
		this.action_plan_task_attachment = this.db.ActionPlanTaskAttachment;
		this.action_plan_column_field = this.db.ActionPlanColumnField;
		this.action_plan_user_preference = this.db.ActionPlanUserPreference;
	}

	async findAllForBoard(params) {
		logger.info('[ActionPlan] repository - findAllForBoard init', { params });
		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			activity_id,
			title,
			status,
			responsible_id,
			start_date,
			end_date,
			priority,
			investment_range,
			due_date_start,
			due_date_end,
			origin_name,
			sort,
			limit,
			offset,
			user_id
		} = params;
		try {
			const payload = {
				organization_id,
				company_id,
				companies_with_user_access,
				sector_id,
				line_id,
				workstation_id,
				activity_id,
				title,
				priority,
				investment_range,
				responsible_id,
				start_date,
				end_date,
				due_date_start,
				due_date_end,
				origin_name,
				sort,
				limit,
				offset,
				status,
				user_id
			};

			const mapper = new GetActionPlansForBoardSQL(payload);
			const query = mapper.getActionPlansForBoardQuery();

			const config = { nest: true, replacements: payload };
			const data = await this.db.sequelize.query(query, config);

			logger.info('[ActionPlan] repository - findAllForBoard success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - findAllForBoard error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - findAllForBoard finish');
		}
	}

	async create(params, options) {
		logger.info('[ActionPlan] repository - create init');
		try {
			const data = await this.action_plan.create(params, options);
			logger.info('[ActionPlan] repository - create success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - create error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - create finish');
		}
	}

	async findOne(params, options) {
		logger.info('[ActionPlan] repository - findOne init');
		try {
			const data = await this.action_plan.findOne({
				where: params,
				...options
			});
			logger.info('[ActionPlan] repository - findOne success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - findOne error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - findOne finish');
		}
	}

	async findByPk(id, { company_id, organization_id }) {
		logger.info('[ActionPlan] repository - findByPk init');
		try {
			const query = new GetFindByPkSQL().getQuery();
			const config = { plain: true, replacements: { id, company_id, organization_id } };
			const data = await this.db.sequelize.query(query, config);
			logger.info('[ActionPlan] repository - findByPk success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - findByPk error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - findByPk finish');
		}
	}

	async index(params) {
		logger.info('[ActionPlan] repository - index init');
		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			activity_id,
			title,
			status,
			responsible_id,
			start_date,
			end_date,
			priority,
			investment_range,
			due_date_start,
			due_date_end,
			origin_name,
			sort,
			limit,
			offset
		} = params;
		try {
			const payload = {
				organization_id,
				company_id,
				companies_with_user_access,
				sector_id,
				line_id,
				workstation_id,
				activity_id,
				title,
				priority,
				investment_range,
				responsible_id,
				start_date,
				end_date,
				due_date_start,
				due_date_end,
				origin_name,
				sort,
				limit,
				offset,
				status
			};

			const mapper = new GetActionPlansSQL(payload);

			const query = mapper.getIndexQuery();
			const config = { nest: true, replacements: payload };

			const data = await this.db.sequelize.query(query, config);

			logger.info('[ActionPlan] repository - index success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - index error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - index finish');
		}
	}

	async countAll(params) {
		logger.info('[ActionPlan] repository - countAll init');
		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			activity_id,
			title,
			status,
			responsible_id,
			start_date,
			end_date,
			priority,
			investment_range,
			due_date_start,
			due_date_end,
			origin_name
		} = params;
		try {
			const payload = {
				organization_id,
				company_id,
				companies_with_user_access,
				sector_id,
				line_id,
				workstation_id,
				activity_id,
				title,
				status,
				priority,
				investment_range,
				responsible_id,
				start_date,
				end_date,
				due_date_start,
				due_date_end,
				origin_name
			};
			const mapper = new GetActionPlansSQL(payload);
			const query = mapper.getCountAllQuery();

			const config = { nest: true, replacements: payload };
			const [{ total }] = await this.db.sequelize.query(query, config);

			logger.info('[ActionPlan] repository - countAll success');
			return [total, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - countAll error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - countAll finish');
		}
	}

	async createOrigin(params, options) {
		logger.info('[ActionPlan] repository - createOrigin init');
		try {
			const data = await this.action_plan_origin.findOrCreate({
				where: params,
				defaults: params,
				...options
			});
			logger.info('[ActionPlan] repository - createOrigin success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - createOrigin error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - createOrigin finish');
		}
	}

	async getTask(params, options = {}) {
		logger.info('[ActionPlan] repository - getTask init');
		try {
			const data = await this.action_plan_task.findOne({
				where: params,
				...options
			});

			logger.info('[ActionPlan] repository - getTask success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - getTask error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - getTask finish');
		}
	}

	async getTasks(params, options = {}) {
		logger.info('[ActionPlan] repository - getTasks init', { params });
		const { action_plan_id, is_completed } = params;
		try {
			const where = { action_plan_id };

			if (is_completed != null) {
				where.is_completed = is_completed;
			}

			const data = await this.action_plan_task.findAll({
				...options,
				where
			});
			logger.info('[ActionPlan] repository - getTasks success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - getTasks error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - getTasks finish');
		}
	}

	async createTask(params, options) {
		logger.info('[ActionPlan] repository - createTask init');
		try {
			const data = await this.action_plan_task.create(params, options);
			logger.info('[ActionPlan] repository - createTask success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - createTask error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - createTask finish');
		}
	}

	async getHistoryTypeByName(name, options = {}) {
		logger.info('[ActionPlan] repository - getHistoryTypeByName init');
		try {
			const data = await this.action_plan_history_type.findOne({
				where: {
					name
				},
				...options
			});
			logger.info('[ActionPlan] repository - getHistoryTypeByName success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - getHistoryTypeByName error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - getHistoryTypeByName finish');
		}
	}

	async getHistory(params) {
		logger.info('[ActionPlan] repository - getHistory init', { params });
		const { company_id, action_plan_id } = params;
		try {
			const mapper = new GetHistorySQL();
			const query = mapper.getQuery();

			const config = {
				nest: true,
				replacements: {
					action_plan_id,
					company_id
				}
			};

			const data = await this.db.sequelize.query(query, config);

			logger.info('[ActionPlan] repository - getHistory success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - getHistory error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - getHistory finish');
		}
	}

	async createHistory(params, options = {}) {
		logger.info('[ActionPlan] repository - createHistory init');
		const { action_plan_history_type_id, action_plan_id, description, user_id } = params;
		try {
			const data = await this.action_plan_history.create(
				{
					action_plan_history_type_id,
					action_plan_id,
					description,
					user_id
				},
				options
			);
			logger.info('[ActionPlan] repository - createHistory success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - createHistory error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - createHistory finish');
		}
	}

	async getAttachment(params, options = {}) {
		logger.info('[ActionPlan] repository - getAttachment init');
		try {
			const data = await this.action_plan_attachment.findOne({
				where: params,
				...options
			});

			logger.info('[ActionPlan] repository - getAttachment success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - getAttachment error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - getAttachment finish');
		}
	}

	async getAttachments(params, options = {}) {
		logger.info('[ActionPlan] repository - getAttachments init');
		try {
			const data = await this.action_plan_attachment.findAll({
				where: params,
				...options
			});

			logger.info('[ActionPlan] repository - getAttachments success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - getAttachments error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - getAttachments finish');
		}
	}

	async countAttachments(params) {
		logger.info('[ActionPlan] repository - countAttachments init');
		try {
			const data = await this.action_plan_attachment.count({
				where: params
			});

			logger.info('[ActionPlan] repository - countAttachments success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - countAttachments error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - countAttachments finish');
		}
	}

	async updateAttachments(params, where, options = {}) {
		logger.info('[ActionPlan] repository - updateAttachments init');
		try {
			const [data] = await this.action_plan_attachment.update(params, {
				where,
				...options
			});

			logger.info('[ActionPlan] repository - updateAttachments success');
			return [!!data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - updateAttachments error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - updateAttachments finish');
		}
	}

	async getTaskAttachments(params, options = {}) {
		logger.info('[ActionPlan] repository - getTaskAttachments init');
		try {
			const data = await this.action_plan_task_attachment.findAll({
				where: params,
				...options
			});

			logger.info('[ActionPlan] repository - getTaskAttachments success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - getTaskAttachments error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - getTaskAttachments finish');
		}
	}

	async getTaskAttachment(params, options = {}) {
		logger.info('[ActionPlan] repository - getTaskAttachment init');
		try {
			const data = await this.action_plan_task_attachment.findOne({
				where: params,
				...options
			});

			logger.info('[ActionPlan] repository - getTaskAttachment success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - getTaskAttachment error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - getTaskAttachment finish');
		}
	}

	async countTaskAttachments(params) {
		logger.info('[ActionPlan] repository - countTaskAttachments init');
		try {
			const data = await this.action_plan_task_attachment.count({
				where: params
			});

			logger.info('[ActionPlan] repository - countTaskAttachments success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - countTaskAttachments error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - countTaskAttachments finish');
		}
	}

	async updateTaskAttachments(params, where, options = {}) {
		logger.info('[ActionPlan] repository - updateTaskAttachments init');
		try {
			const [data] = await this.action_plan_task_attachment.update(params, {
				where,
				...options
			});

			logger.info('[ActionPlan] repository - updateTaskAttachments success');
			return [!!data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - updateTaskAttachments error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - updateTaskAttachments finish');
		}
	}

	async createRelatedReport(params, options = {}) {
		logger.info('[ActionPlan] repository - createRelatedReport init');
		try {
			const data = await this.action_plan_related_report.create(params, options);

			logger.info('[ActionPlan] repository - createRelatedReport success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - createRelatedReport error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - createRelatedReport finish');
		}
	}

	async createAttachment(params, options = {}) {
		logger.info('[ActionPlan] repository - createAttachment init');
		try {
			const data = await this.action_plan_attachment.create(params, options);

			logger.info('[ActionPlan] repository - createAttachment success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - createAttachment error');
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - createAttachment finish');
		}
	}

	async createTaskAttachment(params, options = {}) {
		logger.info('[ActionPlan] repository - createTaskAttachment init');
		try {
			const data = await this.action_plan_task_attachment.create(params, options);

			logger.info('[ActionPlan] repository - createTaskAttachment success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - createTaskAttachment error');
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - createTaskAttachment finish');
		}
	}

	async getRelatedTools({ table_name, column_id }) {
		logger.info('[ActionPlan] repository - getRelatedTools init');
		try {
			const is_custom_report_result = table_name === this.db.CustomReportResult.tableName;
			const get_related_tools_sql = new GetRelatedToolsSQL({ is_custom_report_result });
			const query = get_related_tools_sql.getQuery();

			const data = await this.db.sequelize.query(query, {
				replacements: {
					column_id
				},
				nest: true
			});

			logger.info('[ActionPlan] repository - getRelatedTools success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - getRelatedTools error');
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - getRelatedTools finish');
		}
	}

	async getRelatedReports({ action_plan_id }, options = {}) {
		logger.info('[ActionPlan] repository - getRelatedReports init');
		try {
			const data = await this.action_plan_related_report.findAll({
				where: { action_plan_id },
				...options
			});

			logger.info('[ActionPlan] repository - getRelatedReports success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - getRelatedReports error');
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - getRelatedReports finish');
		}
	}

	async getReportOrigin(params) {
		logger.info('[ActionPlan] repository - getReportOrigin init', { params });
		const { table_name, column_id, file_id } = params;
		try {
			const mapper = new GetReportOriginSQL();
			const query = mapper.getQuery({ table_name });

			const data = await this.db.sequelize.query(query, {
				replacements: { column_id, file_id },
				plain: true
			});

			logger.info('[ActionPlan] repository - getReportOrigins success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - getReportOrigins error');
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - getReportOrigins finish');
		}
	}

	async getReportOrigins(params) {
		logger.info('[ActionPlan] repository - getReportOrigins init', { params });
		const { workstation_id, report_name, limit, offset } = params;
		try {
			const get_report_origins_sql = new GetReportOriginsSQL({ report_name });
			const query = get_report_origins_sql.getQuery();

			const rows = await this.db.sequelize.query(query, {
				replacements: { workstation_id, report_name, limit, offset: limit * (offset - 1) },
				nest: true
			});

			const data = {
				rows,
				limit,
				offset,
				total: rows?.[0]?.total ?? 0
			};

			logger.info('[ActionPlan] repository - getReportOrigins success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - getReportOrigins error');
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - getReportOrigins finish');
		}
	}

	async updateReportScoreOrigin(params, options = {}) {
		logger.info('[ActionPlan] repository - updateReportScoreOrigin init');
		const {
			task_id,
			table_name,
			custom_report_result_id,
			custom_report_step_key_id,
			custom_report_sub_step_key_id,
			sera_summary_review_id
		} = params;
		try {
			const mapper = new UpdateReportScoreSQL();
			const query = mapper.getQuery({ table_name });

			const replacements = {
				task_id,
				table_name,
				sera_summary_review_id,
				custom_report_result_id,
				custom_report_step_key_id,
				custom_report_sub_step_key_id
			};

			const [data] = await this.db.sequelize.query(query, { replacements, ...options });

			logger.info('[ActionPlan] repository - updateReportScoreOrigin success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - updateReportScoreOrigin error');
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - updateReportScoreOrigin finish');
		}
	}

	async getFistActionPlan(status) {
		logger.info('[ActionPlan] repository - getFistActionPlan init');
		try {
			const where = {};

			if (status) {
				where.status = status;
			}

			const data = await this.action_plan.findOne({
				where,
				attributes: ['id', 'workstation_id', 'lexo_rank'],
				order: [['lexo_rank', 'ASC']]
			});

			logger.info('[ActionPlan] repository - getFistActionPlan success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - getFistActionPlan error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - getFistActionPlan finish');
		}
	}

	async updateRank(params, options = {}) {
		logger.info('[ActionPlan] repository - updateRank init', { params });
		const { lexo_rank, status, id } = params;
		try {
			const [updated] = await this.action_plan.update({ lexo_rank, status }, { where: { id }, ...options });
			logger.info('[ActionPlan] repository - updateRank success');
			return [updated, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - updateRank error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - updateRank finish');
		}
	}

	async delete(params, options = {}) {
		logger.info('[ActionPlan] repository - delete init');
		const { ids, user_id } = params;
		try {
			const mapper = new DeleteActionPlansSQL();
			const query = mapper.getQuery();

			const [data] = await this.db.sequelize.query(query, {
				replacements: { ids, user_id },
				...options
			});

			logger.info('[ActionPlan] repository - delete success');
			return [data.affectedRows, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - delete error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - delete finish');
		}
	}

	async findCommentByPk(id) {
		logger.info('[ActionPlan] repository - findCommentByPk init');
		try {
			const data = await this.action_plan_comment.findByPk(id);
			logger.info('[ActionPlan] repository - getTask success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - findCommentByPk error');
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - getComments finish');
		}
	}

	async getComments(params) {
		logger.info('[ActionPlan] repository - getComments init', { params });
		const { company_id, action_plan_id, limit, offset } = params;
		try {
			const mapper = new GetCommentsSQL({ action_plan_id, limit, offset });
			const query = mapper.getIndexQuery();

			const data = await this.db.sequelize.query(query, {
				replacements: { company_id, action_plan_id, limit, offset },
				nest: true
			});

			logger.info('[ActionPlan] repository - getComments success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - getComments error');
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - getComments finish');
		}
	}

	async createComment(params, options) {
		logger.info('[ActionPlan] repository - createComment init');
		const { organization_id, company_id, action_plan_id, description, user_id } = params;
		try {
			const parameters = { organization_id, company_id, action_plan_id, description, user_id };
			const data = await this.action_plan_comment.create(parameters, options);
			logger.info('[ActionPlan] repository - createComment success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - createComment error');
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - createComment finish');
		}
	}

	async countAllComments(params) {
		logger.info('[ActionPlan] repository - countAllComments init');
		const { company_id, action_plan_id } = params;
		try {
			const mapper = new GetCommentsSQL({ action_plan_id });
			const query = mapper.getCountAllQuery();

			const [{ total }] = await this.db.sequelize.query(query, {
				replacements: { company_id, action_plan_id },
				nest: true
			});

			logger.info('[ActionPlan] repository - countAllComments success');
			return [total, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - countAllComments error');
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - countAllComments finish');
		}
	}

	async getOriginOptions() {
		logger.info('[ActionPlan] repository - getOriginOptions init');
		try {
			const query = new GetOriginOptionsSQL().getQuery();
			const data = await this.db.sequelize.query(query, { nest: true });

			logger.info('[ActionPlan] repository - getOriginOptions success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - getOriginOptions error');
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - getOriginOptions finish');
		}
	}

	async getUserPreferenceColumns(user_id) {
		logger.info('[ActionPlan] repository - getUserPreferenceColumns init');
		try {
			const parameters = {
				raw: true,
				nest: true,
				attributes: [],
				where: { user_id },
				include: {
					association: 'column_field',
					attributes: ['name']
				}
			};

			logger.info('[ActionPlan] repository - getUserPreferenceColumns parameters', { parameters });

			const data = await this.action_plan_user_preference.findAll(parameters);
			const column_names = data.map((item) => item.column_field.name);

			logger.info('[ActionPlan] repository - getUserPreferenceColumns success');
			return [column_names, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - getUserPreferenceColumns error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - getUserPreferenceColumns finish');
		}
	}

	async updateUserPreferenceColumns(params, options = {}) {
		logger.info('[ActionPlan] repository - updateUserPreferenceColumns init');
		const { records_to_create, user_id } = params;
		try {
			await this.action_plan_user_preference.destroy({
				...options,
				where: { user_id }
			});

			const updated_rows = await this.action_plan_user_preference.bulkCreate(records_to_create, {
				...options,
				returning: true
			});

			logger.info('[ActionPlan] repository - updateUserPreferenceColumns success');
			return [updated_rows, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - updateUserPreferenceColumns error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - updateUserPreferenceColumns finish');
		}
	}

	async createUserPreferenceColumns(params, options = {}) {
		logger.info('[ActionPlan] repository - createUserPreferenceColumns init');
		const { records_to_create } = params;
		try {
			const data = await this.action_plan_user_preference.bulkCreate(records_to_create, options);
			logger.info('[ActionPlan] repository - createUserPreferenceColumns success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - createUserPreferenceColumns error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - createUserPreferenceColumns finish');
		}
	}

	async getColumnFieldsByNames(column_names, options = {}) {
		logger.info('[ActionPlan] repository - getColumnFieldsByNames init');
		try {
			const data = await this.action_plan_column_field.findAll({
				...options,
				where: {
					name: {
						[this.db.Sequelize.Op.in]: column_names
					}
				}
			});

			logger.info('[ActionPlan] repository - getColumnFieldsByNames success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - getColumnFieldsByNames error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - getColumnFieldsByNames finish');
		}
	}

	async bulkUpdate(params, options = {}) {
		logger.info('[ActionPlan] repository - bulkUpdate init');
		try {
			const { ids, field } = params;
			const { name, value } = field;

			const [affected_rows] = await this.action_plan.update(
				{ [name]: value, updated_at: new Date() },
				{
					where: {
						id: ids
					},
					...options
				}
			);

			logger.info('[ActionPlan] repository - bulkUpdate success');
			return [affected_rows, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - bulkUpdate error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - bulkUpdate finish');
		}
	}

	async findAll(params, options = {}) {
		logger.info('[ActionPlan] repository - findAll init');
		try {
			const data = await this.action_plan.findAll({ where: params, ...options });
			logger.info('[ActionPlan] repository - findAll success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - findAll error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - findAll finish');
		}
	}

	async countAllFromReports(params) {
		logger.info('[ActionPlan] repository - countAllFromReports init', { params });
		const {
			companies_with_user_access,
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date
		} = params;
		try {
			const mapper = new GetCountActionPlansDelayedSQL(params);
			const query = mapper.getQuery();
			const config = {
				nest: true,
				replacements: {
					organization_id,
					company_id,
					companies_with_user_access,
					sector_id,
					line_id,
					workstation_id,
					start_date,
					end_date,
					current_date: new Date()
				}
			};
			const [{ total }] = await this.db.sequelize.query(query, config);
			logger.info('[ActionPlan] repository - countAllFromReports success');
			return [total, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - countAllFromReports error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - countAllFromReports finish');
		}
	}

	async countAllFromReportsByStatus(params) {
		logger.info('[ActionPlan] repository - countAllFromReportsByStatus init', { params });
		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date
		} = params;
		try {
			const mapper = new GetCountActionPlansByStatusSQL(params);
			const query = mapper.getQuery();
			const config = {
				nest: true,
				plain: true,
				replacements: {
					organization_id,
					company_id,
					companies_with_user_access,
					sector_id,
					line_id,
					workstation_id,
					start_date,
					end_date,
					current_date: new Date()
				}
			};
			const data = await this.db.sequelize.query(query, config);
			logger.info('[ActionPlan] repository - countAllFromReportsByStatus success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - countAllFromReportsByStatus error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - countAllFromReportsByStatus finish');
		}
	}

	async getReportsActionPlansCount(params) {
		logger.info('[ActionPlan] repository - getReportsActionPlansCount init', { params });
		try {
			const mapper = new GetListCountsActionPlansSQL(params);
			const { query, replacements } = mapper.getQuery();

			const [data] = await this.db.sequelize.query(query, {
				replacements,
				type: this.db.sequelize.QueryTypes.SELECT
			});

			logger.info('[ActionPlan] repository - getReportsActionPlansCount success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - getReportsActionPlansCount error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - getReportsActionPlansCount finish');
		}
	}

	async getReportsActionPlansOriginCount(params) {
		logger.info('[ActionPlan] repository - getReportsActionPlansOriginCount init', { params });
		try {
			const mapper = new GetListActionPlansOriginCountSQL(params);
			const { query, replacements } = mapper.getQuery();

			const data = await this.db.sequelize.query(query, {
				replacements,
				type: this.db.sequelize.QueryTypes.SELECT
			});

			logger.info('[ActionPlan] repository - getReportsActionPlansOriginCount success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - getReportsActionPlansOriginCount error', { error });
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - getReportsActionPlansOriginCount finish');
		}
	}
}
