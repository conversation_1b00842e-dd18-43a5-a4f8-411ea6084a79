export class HooksRepository {
	constructor(db) {
		this.db = db;
	}

	async showEvent(customerId) {
		const query = `
			SHOW EVENTS WHERE name = :customer_id
		`;
		const config = {
			nest: true,
			replacements: {
				customer_id: customerId
			}
		};
		const [result] = await this.db.sequelize.query(query, config);
		return result;
	}

	async createEvent(customerId) {
		const query = `
			CREATE EVENT ${customerId}
				ON SCHEDULE EVERY 1 MONTH
				STARTS CURRENT_TIMESTAMP + INTERVAL 1 MONTH
				ENDS CURRENT_TIMESTAMP + INTERVAL 1 YEAR
				DO UPDATE customer_informations SET expiration_plan = DATE_ADD(NOW(), INTERVAL 1 MONTH)
				WHERE(id = ${customerId});
		`;
		const result = await this.db.sequelize.query(query);
		return result;
	}

	async dropEvent(customerId) {
		const query = `DROP EVENT ${customerId}`;
		const result = await this.db.sequelize.query(query);
		return result;
	}
}
