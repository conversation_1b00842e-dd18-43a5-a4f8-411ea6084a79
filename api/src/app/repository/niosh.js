import { logger } from '../helpers/logger.js';
import { BaseRepository } from './v2/base-repository.js';

export class NioshReportRepository extends BaseRepository {
	constructor(database) {
		super(database);
	}

	async countAllByRisk(params) {
		logger.info('[NioshReport] repository - countAllByRisk init');
		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date,
			user_id
		} = params;
		try {
			const query = `
				SELECT
					COUNT(DISTINCT CASE WHEN niosh.risk = 'VERY_LOW' THEN niosh.id END) AS very_low,
					COUNT(DISTINCT CASE WHEN niosh.risk = 'LOW' THEN niosh.id END) AS low,
					COUNT(DISTINCT CASE WHEN niosh.risk = 'MODERATE' THEN niosh.id END) AS moderate,
					COUNT(DISTINCT CASE WHEN niosh.risk = 'HIGH' THEN niosh.id END) AS high,
					COUNT(DISTINCT CASE WHEN niosh.risk = 'VERY_HIGH' THEN niosh.id END) AS very_high
				FROM niosh_reports AS niosh
					INNER JOIN files ON niosh.file_id = files.id
					INNER JOIN workstations ON workstations.id = files.workstation_id
					INNER JOIN lines AS l ON l.id = workstations.line_id
					INNER JOIN sectors ON sectors.id = l.sector_id
				WHERE files.organization_id = :organization_id
					AND niosh.is_active = true
					AND workstations.deleted_at IS NULL
					AND l.deleted_at IS NULL
					AND sectors.is_active = 1
					AND files.is_active = 1
					${company_id ? 'AND files.company_id = :company_id' : 'AND files.company_id IN(:companies_with_user_access)'}
					${sector_id ? 'AND sectors.id = :sector_id' : ''}
					${line_id ? 'AND l.id = :line_id' : ''}
					${workstation_id ? 'AND workstations.id = :workstation_id' : ''}
					${start_date ? 'AND DATE(niosh.created_at) >= DATE(:start_date)' : ''}
					${end_date ? 'AND DATE(niosh.created_at) <= DATE(:end_date)' : ''}
					${user_id ? 'AND niosh.report_user_id = :user_id' : ''}
			`;

			const config = {
				nest: true,
				plain: true,
				replacements: {
					organization_id,
					company_id,
					companies_with_user_access,
					sector_id,
					line_id,
					workstation_id,
					start_date,
					end_date
				}
			};

			const data = await this.db.sequelize.query(query, config);

			logger.info('[NioshReport] repository - countAllByRisk success');
			return [data, null];
		} catch (error) {
			logger.error('[NioshReport] repository - countAll error');
			return [null, error];
		} finally {
			logger.info('[NioshReport] repository - countAllByRisk finish');
		}
	}
}
