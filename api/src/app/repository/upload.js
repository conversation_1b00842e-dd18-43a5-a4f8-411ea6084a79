import { RESPONSE_ERROR_STATUS } from '../helpers/constants.js';
import { FileFiltersMapper } from '../entities/File/index.js';
import { BaseRepository } from './v2/base-repository.js';
import { AppError } from '../helpers/errors.js';
import { logger } from '../helpers/logger.js';
import { GetUploadWithCustomReportSQL } from '../entities/Upload/Mappers/GetUploadWithCustomReportSQL.js';

const { DATABASE_FAILED_PERFORM_QUERY } = RESPONSE_ERROR_STATUS;

export class UploadRepository extends BaseRepository {
	#filter_mapper;
	constructor(database) {
		super(database);
		this.File = this.db.File;
		this.#filter_mapper = new FileFiltersMapper();
	}

	async checkingCredits(customerId) {
		const query = `
			SELECT
				credits.minutes as credits_minutes
			FROM
				customer_informations AS customer
					LEFT JOIN
				customer_credits AS credits ON customer.id = credits.customer_id
			WHERE
				customer_id = :customer_id
			`;

		const [data] = await this.db.sequelize.query(query, {
			nest: true,
			replacements: {
				customer_id: customerId
			}
		});

		return data?.credits_minutes || null;
	}

	async detailsPlanConsumed(customer_id) {
		const context = {
			_service: 'upload',
			_layer: 'repository',
			_meta: 'detailsPlanConsumed'
		};

		logger.child({ context, params: { customer_id } }).info('Init');

		try {
			const query = `
				SELECT
				IFNULL(
					(SELECT
						CAST(SUM(duration) AS UNSIGNED) AS duration
					FROM
						files AS file
							INNER JOIN
						users AS user ON file.user_id = user.id
							INNER JOIN
						customer_informations AS customer ON user.customer_information_id = customer.id
					WHERE
							(file.user_id IN (SELECT
									id
								FROM
									users AS user
								WHERE
									(user.customer_information_id = :customer_id
										&& is_active = 1))
								AND file.created_at <= customer.expiration_plan
								AND file.status = 'PROCESSED'
								AND file.created_at >= DATE_SUB(customer.expiration_plan,
								INTERVAL 30 DAY))),
					0) AS total_seconds,
				(SELECT
						plan.max_minutes + COALESCE(credits.minutes, 0) AS max_minutes
					FROM
						customer_informations AS customer
							LEFT JOIN
						customer_credits AS credits ON customer.id = credits.customer_id
							INNER JOIN
						customer_plans AS cs_plan ON customer.id = cs_plan.customer_id
							INNER JOIN
						plans AS plan ON cs_plan.plan_id = plan.id
							INNER JOIN
						users AS user ON customer.id = user.customer_information_id
							LEFT JOIN
						files AS file ON file.user_id = user.id
					WHERE EXISTS
						(SELECT
							file.user_id IN ((SELECT
								GROUP_CONCAT(id SEPARATOR ', ')
							FROM
								users
							WHERE
								(user.customer_information_id = :customer_id
									&& is_active = 1 )))) AND customer.id = :customer_id
					GROUP BY plan.max_minutes , credits.minutes) AS max_minutes
				FROM DUAL
			`;

			const config = {
				nest: true,
				replacements: {
					customer_id: customer_id
				}
			};

			const [result] = await this.db.sequelize.query(query, config);

			logger.child({ context, params: { customer_id } }).info('Success');
			return result;
		} catch (error) {
			logger.child({ context }).error(error);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		} finally {
			logger.child({ context, params: { customer_id } }).info('Finish');
		}
	}

	async findAllWithHierarchy(parameters) {
		logger.info('[File] repository - findAllWithHierarchy init');
		const filter_options = this.#filter_mapper.setFilter(parameters);
		const files = await this.File.findAll(filter_options);
		logger.info('[File] repository - findAllWithHierarchy finish');
		return files;
	}

	async findAndCountAllWithHierarchy(parameters) {
		logger.info('[File] repository - findAndCountAllWithHierarchy init');
		const filter_options = this.#filter_mapper.setFilter(parameters);
		const data = await this.File.findAndCountAll({
			...filter_options,
			limit: parameters.limit,
			offset: parameters.offset * parameters.limit
		});
		logger.info('[File] repository - findAndCountAllWithHierarchy finish');
		return data;
	}

	async findAndCountAll(params) {
		logger.info('[File] repository - findAndCountAll init');
		const files = await this.File.findAndCountAll({
			...params
		});
		logger.info('[File] repository - findAndCountAll finish');
		return files;
	}

	async findOne(params) {
		logger.info('[File] repository - findOne init');
		const file = await this.File.findOne(params);
		logger.info('[File] repository - findOne finish');
		return file;
	}

	async findAndCountAllRawQuery(params) {
		logger.info('[Upload] repository - findAndCountAllRawQuery init', { params });
		const { file_ids, limit, offset, user_id } = params;

		const mapper = new GetUploadWithCustomReportSQL({ file_ids, limit, offset, user_id });
		const rows_query = mapper.getRows();
		const count_query = mapper.getCount();

		const config = { nest: true };

		const [rows, { count }] = await Promise.all([
			this.db.sequelize.query(rows_query, config),
			this.db.sequelize.query(count_query, { ...config, plain: true })
		]);

		logger.info('[Upload] repository - findAndCountAllRawQuery finish');
		return { count, rows };
	}
}
