import { BaseRepository } from './baseRepository/index.js';
import { logger } from '../helpers/logger.js';

export class CompanyRepository extends BaseRepository {
	constructor(database) {
		super(database);
	}

	async index({
		where,
		limit = 100,
		order = { field: 'name', type: 'ASC' },
		include = [
			{
				association: 'sector',
				attributes: ['id'],
				where: {
					is_active: true
				}
			}
		]
	}) {
		logger.info('[Company] repository - index init');
		const companies = await this.db.Company.findAll({
			where,
			limit,
			include,
			order: [[`${order.field}`, `${order.type}`]]
		});
		logger.info('[Company] repository - index finish');
		return companies;
	}

	async hasPermissionOrganization(parameters) {
		const { organization_id, user_id } = parameters;

		const query = `
			SELECT
				acl.*
		  	FROM
				user_accesses AS acl
					INNER JOIN users AS usr
						ON usr.id = acl.user_id
							INNER JOIN organizations AS org
								ON acl.organization_id = org.id
			WHERE (
				org.id =:organization_id
					AND
				acl.user_id =:user_id
					AND
				org.is_active = 1
					AND
				acl.is_active = 1
			);
		`;

		const config = {
			nest: true,
			replacements: { user_id, organization_id }
		};

		const [result] = await this.db.sequelize.query(query, config);

		return result;
	}

	async statusRisk(parameters) {
		const { organization_id, company_id } = parameters;

		const query = `
		  	SELECT
				AVG(rula_score) AS risk
		  	FROM
				files AS f
		  	WHERE (
				f.organization_id =:organization_id
			  		AND
				f.company_id =:company_id
			  		AND
				f.status='PROCESSED'
					AND
				sector_id IS NOT NULL
					AND
				workstation IS NOT NULL
					AND
				f.is_active=1
			)
		`;

		const config = { nest: true, replacements: { organization_id, company_id } };
		const [result] = await this.db.sequelize.query(query, config);

		return result;
	}
}
