import { BaseRepository } from './v2/base-repository.js';
import { logger } from '../helpers/logger.js';

export class SectorRepository extends BaseRepository {
	constructor(database) {
		super(database);
		this.sector = this.db.Sector;
	}

	async findByCompanyAndOrganization({
		where,
		attributes,
		limit = 100,
		order = { field: 'name', type: 'ASC' },
		include = []
	}) {
		logger.info('[Sector] repository - findByCompanyAndOrganization init');
		const sectors = await this.sector.findAll({
			where,
			attributes,
			include,
			limit,
			order: [[`${order.field}`, `${order.type}`]]
		});
		logger.info('[Sector] repository - findByCompanyAndOrganization finish');
		return sectors;
	}

	async findByPk(id, params) {
		logger.info('[Sector] repository - findByPk init');
		const sectors = await this.sector.findByPk(id, params);
		logger.info('[Sector] repository - findByPk finish');
		return sectors;
	}

	async findAllByForeignKey(params) {
		logger.info('[Sector] repository - findAllByForeignKey init');
		const sectors = await this.sector.findAll({
			...params
		});
		logger.info('[Sector] repository - findAllByForeignKey finish');
		return sectors;
	}

	async update(params, options) {
		logger.info('[Sector] repository - update init');
		const sector = await this.sector.update(params, options);
		logger.info('[Sector] repository - update finish');
		return sector;
	}

	async findAllWithWorstScore(company_id) {
		logger.info('[Sector] repository - findAllWithWorstScore init');
		const sectors = await this.sector.findAll({
			where: {
				company_id,
				is_active: true
			},
			attributes: ['id', 'name'],
			required: false,
			include: [
				{
					association: 'line',
					attributes: ['id'],
					required: false,
					include: [
						{
							association: 'workstation',
							attributes: ['id'],
							required: false,
							include: [
								{
									association: 'file',
									attributes: ['id'],
									required: false,
									where: {
										is_active: true
									},
									include: [
										{
											association: 'preliminary_analysis',
											required: false,
											where: {
												consolidated: true,
												is_active: true
											},
											attributes: ['worst_score']
										}
									]
								}
							]
						}
					]
				}
			],
			order: [['name', 'ASC']],
			raw: true
		});
		logger.info('[Sector] repository - findAllWithWorstScore finish');
		return sectors;
	}

	async create(params, transaction) {
		logger.info('[Sector] repository - create init');
		const sector = await this.sector.create(params, transaction);
		logger.info('[Sector] repository - create finish');
		return sector;
	}

	// Deprecated
	async index(params) {
		return new Promise(async (resolve, _) => {
			const { organization_id, company_id } = params;
			try {
				const query = `
					SELECT
						str.id,
						str.name
					FROM
						organizations AS org
							INNER JOIN
						companies AS cmp ON org.id = cmp.organization_id
							INNER JOIN
						sectors AS str ON cmp.id = str.company_id
					WHERE(
						org.id = :organization_id
							&&
						cmp.id = :company_id
							&&
						str.is_active = 1
					);
				`;

				const config = {
					nest: true,
					replacements: {
						organization_id: organization_id,
						company_id: company_id
					}
				};
				const result = await this.db.sequelize.query(query, config);
				resolve([result, null]);
			} catch (error) {
				resolve([null, error]);
			}
		});
	}

	async getCritical(parameters) {
		const { organization_id, company_id } = parameters;
		const query = `
			SELECT
				sector.name AS sector,
				AVG(file.rula_score) AS score
			FROM
				files AS file
					INNER JOIN
						sectors AS sector ON file.sector_id = sector.id
			WHERE
				(file.organization_id = :organization_id
					&& file.company_id = :company_id
					&& file.status = 'PROCESSED'
					&& file.is_active = 1)
			GROUP BY sector
			ORDER BY score DESC
			LIMIT 1
		`;
		const config = {
			nest: true,
			replacements: { organization_id, company_id }
		};

		const [result] = await this.db.sequelize.query(query, config);
		return result;
	}

	async countSector(parameters) {
		const { organization_id, company_id } = parameters;
		const query = `
			SELECT
				COUNT(sector.id) AS total
			FROM
				organizations AS organization
					INNER JOIN
						companies AS company
							ON organization.id = company.organization_id
					INNER JOIN
						sectors AS sector
							ON company.id = sector.company_id
			WHERE(
				organization.id = :organization_id
					&&
				organization.is_active = 1
					&&
				company.id = :company_id
					&&
				company.is_active = 1
					&&
				sector.is_active = 1
			)
		`;
		const config = {
			nest: true,
			replacements: { organization_id, company_id }
		};

		const [result] = await this.db.sequelize.query(query, config);
		return result;
	}

	async getSectorCritical(params) {
		const { organization_id, company_id } = params;

		const query = `
			SELECT
				sectors.name as sector,
				AVG(files.rula_score) AS score
			FROM files AS files
				INNER JOIN sectors AS sectors
					ON files.sector_id = sectors.id
			WHERE(
				files.organization_id = :organization_id
					&&
				files.company_id = :company_id
					&&
				files.status = 'PROCESSED'
					&&
				files.is_active = 1
			)
			GROUP BY sector
			ORDER BY score DESC LIMIT 1;
		`;

		const config = {
			nest: true,
			replacements: { organization_id, company_id }
		};

		const [result] = await this.db.sequelize.query(query, config);

		return result;
	}
}
