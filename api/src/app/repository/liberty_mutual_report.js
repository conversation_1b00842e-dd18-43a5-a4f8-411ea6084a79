import { logger } from '../helpers/logger.js';
import { BaseRepository } from './v2/base-repository.js';

export class LibertyMutualReportRepository extends BaseRepository {
	constructor(database) {
		super(database);
		this.liberty_mutual_report = this.db.LibertyMutualReport;
	}

	async create(params, transaction) {
		logger.info('[LibertyMutualReport] repository - create init');
		const liberty_mutual_report = await this.liberty_mutual_report.create(params, transaction);
		logger.info('[LibertyMutualReport] repository - create finish');
		return liberty_mutual_report;
	}

	async update(params, options) {
		logger.info('[LibertyMutualReport] repository - update init');
		const liberty_mutual_report = await this.liberty_mutual_report.update(params, options);
		logger.info('[LibertyMutualReport] repository - update finish');
		return liberty_mutual_report;
	}

	async findByPk(id, params) {
		logger.info('[LibertyMutualReport] repository - findByPk init');
		const liberty_mutual_report = await this.liberty_mutual_report.findByPk(id, params);
		logger.info('[LibertyMutualReport] repository - findByPk finish');
		return liberty_mutual_report;
	}

	async findOne(params) {
		logger.info('[LibertyMutualReport] repository - findOne init');
		const liberty_mutual_reports = await this.liberty_mutual_report.findOne({
			...params
		});
		logger.info('[LibertyMutualReport] repository - findOne finish');
		return liberty_mutual_reports;
	}

	async findAllByForeignKey(params) {
		logger.info('[LibertyMutualReport] repository - findAllByForeignKey init');
		const liberty_mutual_reports = await this.liberty_mutual_report.findAll({
			...params
		});
		logger.info('[LibertyMutualReport] repository - findAllByForeignKey finish');
		return liberty_mutual_reports;
	}

	getPercentileByGenderFilterParams(params) {
		logger.info('[LibertyMutualReportInput] repository - getPercentileByGenderFilterParams init', { params });
		const {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_ids,
			start_date,
			end_date,
			user_id
		} = params;

		let filters = { organization_id };

		if (sector_id) {
			filters.sector_id = sector_id;
		}

		if (company_id) {
			filters.company_id = company_id;
		}

		if (companies_ids) {
			filters.companies_ids = companies_ids;
		}

		if (line_id) {
			filters.line_id = line_id;
		}

		if (workstation_id) {
			filters.workstation_id = workstation_id;
		}

		if (start_date && end_date) {
			filters.start_date = start_date;
			filters.end_date = end_date;
		}

		if (user_id) {
			filters.user_id = user_id;
		}

		logger.info('[LibertyMutualReportInput] repository - getPercentileByGenderFilterParams finish');
		return filters;
	}

	getPercentileByGenderFilterQuery(params) {
		logger.info('[LibertyMutualReportInput] repository - getPercentileByGenderFilterQuery init', { params });
		const { company_id, sector_id, line_id, workstation_id, companies_ids, start_date, end_date, user_id } = params;

		const query = `
			SELECT
				COALESCE(MIN(percentile_man), 0) as man, COALESCE(MIN(percentile_woman), 0) as woman
			FROM
				liberty_mutual_reports as report
				INNER JOIN files as file on file.id = report.file_id
				INNER JOIN workstations as workstation on workstation.id = file.workstation_id
				INNER JOIN lines as line on line.id = workstation.line_id
				INNER JOIN sectors as sector on sector.id = line.sector_id
				INNER JOIN liberty_mutual_report_inputs as report_inputs on report_inputs.liberty_mutual_report_id = report.id
			WHERE (
				report.deleted_at IS NULL
				AND file.is_active = 1
				AND sector.is_active = 1
				AND line.deleted_at IS NULL
				AND workstation.deleted_at IS NULL
				${company_id ? `AND file.organization_id = :organization_id AND file.company_id = :company_id` : ''}
				${!company_id && companies_ids ? `AND file.company_id IN (:companies_ids)` : ''}
				${sector_id ? `AND file.sector_id = :sector_id` : ''}
				${line_id ? `AND line.id = :line_id` : ''}
				${workstation_id ? `AND file.workstation_id = :workstation_id` : ''}
				${start_date && end_date ? `AND DATE(report_inputs.created_at) BETWEEN DATE (:start_date) AND DATE(:end_date)` : ''}
				${user_id ? `AND report.report_user_id = :user_id` : ''}
			)
		`;

		logger.info('[LibertyMutualReportInput] repository - getPercentileByGenderFilterQuery finish');
		return query;
	}

	async getPercentileByGender(params) {
		logger.info('[LibertyMutualReportInput] repository - getPercentileByGender init', { params });
		try {
			const filter = this.getPercentileByGenderFilterParams(params);
			const query = this.getPercentileByGenderFilterQuery(params);

			const config = {
				plain: true,
				replacements: filter,
				type: this.db.sequelize.QueryTypes.SELECT
			};

			const data = await this.db.sequelize.query(query, config);

			logger.info('[LibertyMutualReportInput] repository - getPercentileByGender success');
			return [data, null];
		} catch (error) {
			logger.error('[LibertyMutualReportInput] repository - getPercentileByGender error');
			return [null, error];
		} finally {
			logger.info('[LibertyMutualReportInput] repository - getPercentileByGender finish');
		}
	}
}
