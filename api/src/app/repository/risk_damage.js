import { BaseRepository } from './v2/base-repository.js';
import { logger } from '../helpers/logger.js';

export class RiskDamageRepository extends BaseRepository {
	constructor(database) {
		super(database);
		this.risk_damage = this.db.RiskDamage;
	}

	async findByPk(id, params) {
		logger.info('[RiskDamage] repository - findByPk init', { params: { id, ...params } });
		const data = await this.risk_damage.findByPk(id, params);
		logger.info('[RiskDamage] repository - findByPk success');
		return data;
	}

	async create(params, transaction) {
		logger.info('[RiskDamage] repository - create init', { params });
		const data = await this.risk_damage.create(params, transaction);
		logger.info('[RiskDamage] repository - create success');
		return data;
	}

	async update(params, options) {
		logger.info('[RiskDamage] repository - update init', { params });
		const data = await this.risk_damage.update(params, options);
		logger.info('[RiskDamage] repository - update success');
		return data;
	}

	async delete(params, transaction) {
		logger.info('[RiskDamage] repository - delete init', { params });
		const data = await this.risk_damage.destroy(params, transaction);
		logger.info('[RiskDamage] repository - delete success');
		return data;
	}

	async index(params) {
		logger.info('[RiskDamage] repository - index init', { params });
		const data = await this.risk_damage.findAll({
			...params
		});
		logger.info('[RiskDamage] repository - index success');
		return data;
	}

	async findAllByForeignKey(params) {
		logger.info('[RiskDamage] repository - findAllByForeignKey init', { params });
		const data = await this.risk_damage.findAll({
			...params,
			distinct: true
		});
		logger.info('[RiskDamage] repository - findAllByForeignKey success');
		return data;
	}
}
