import { GetEwaCustomReportResultsSQL } from '../mappers/getEwaCustomReportResultsSQL.js';
import { DASHBOARD_GRANULARITY_VALUES } from '../utils/constants.js';
import { BaseRepository } from './v2/base-repository.js';
import { logger } from '../helpers/logger.js';
import { ListAllResultsByReportTypeSQL } from '../mappers/custom_reports/result/ListAllResultsByReportTypeSQL.js';
import { CountAllResultsByReportTypeSQL } from '../mappers/custom_reports/result/CountAllResultsByReportTypeSQL.js';
import { ListAllBeraReportsSQL } from '../mappers/bera_reports/ListAllBeraReportsSQL.js';
import { CountAllBeraReportsSQL } from '../mappers/bera_reports/CountAllBeraReportsSQL.js';
import { ListAllSeraReportsSQL } from '../mappers/sera_reports/ListAllSeraReportsSQL.js';
import { CountAllSeraReportsSQL } from '../mappers/sera_reports/CountAllSeraReportsSQL.js';
import { CUSTOM_REPORT_NAMES } from '../util/constants-custom-report.js';

export class CustomReportResultRepository extends BaseRepository {
	constructor(database) {
		super(database);
		this.custom_report_result = this.db.CustomReportResult;
	}

	async create(params, transaction) {
		logger.info('[CustomReportResult] repository - create init');
		const custom_report_result = await this.custom_report_result.create(params, transaction);
		logger.info('[CustomReportResult] repository - create finish');
		return custom_report_result;
	}

	async findByPk(id, params) {
		logger.info('[CustomReportResult] repository - findByPk init');
		const custom_report_result = await this.custom_report_result.findByPk(id, params);
		logger.info('[CustomReportResult] repository - findByPk finish');
		return custom_report_result;
	}

	async update(params, options) {
		logger.info('[CustomReportResult] repository - update init');
		const custom_report_result = await this.custom_report_result.update(params, options);
		logger.info('[CustomReportResult] repository - update finish');
		return custom_report_result;
	}

	async delete(params, transaction) {
		logger.info('[CustomReportResult] repository - delete init');
		const custom_report_result = await this.custom_report_result.destroy(params, transaction);
		logger.info('[CustomReportResult] repository - delete finish');
		return custom_report_result;
	}

	async findOne(params) {
		logger.info('[CustomReportResult] repository - findOne init');
		const custom_report_results = await this.custom_report_result.findOne({
			...params
		});
		logger.info('[CustomReportResult] repository - findOne finish');
		return custom_report_results;
	}

	async findAllByForeignKey(params) {
		logger.info('[CustomReportResult] repository - findAllByForeignKey init');
		const custom_report_results = await this.custom_report_result.findAll({
			...params
		});
		logger.info('[CustomReportResult] repository - findAllByForeignKey finish');
		return custom_report_results;
	}

	async findAndCountAll(params) {
		logger.info('[CustomReportResult] repository - findAndCountAll init');
		const custom_report_results = await this.custom_report_result.findAndCountAll({
			...params
		});
		logger.info('[CustomReportResult] repository - findAndCountAll finish');
		return custom_report_results;
	}

	async countCreatedReportResultsByPeriod(params) {
		logger.info('[CustomReportResult] repository - countCreatedReportResultsByPeriod init');
		const {
			year_start_month = 1,
			created_at_start,
			organization_id,
			created_at_end,
			workstation_id,
			period_group,
			has_reviews,
			company_id,
			sector_id,
			line_id,
			user_id
		} = params;
		try {
			const { fields, group_by, order_by } = this.getPeriodGroupQuery({ year_start_month, period_group });
			const review_join = this.getReportReviewJoin(has_reviews);

			const query = `
				SELECT
					COUNT(*) as count,
					${fields}
				FROM
					custom_report_results
					INNER JOIN files ON files.id = custom_report_results.file_id
					INNER JOIN workstations ON workstations.id = files.workstation_id
					INNER JOIN \`lines\` AS l ON l.id = workstations.line_id
					INNER JOIN sectors ON sectors.id = l.sector_id
					${review_join}
				WHERE files.organization_id = :organization_id
					AND custom_report_results.custom_report_id = :custom_report_id
					AND custom_report_results.consolidated = 1
					AND custom_report_results.deleted_at IS NULL
					AND sectors.is_active = 1
					AND l.deleted_at IS NULL
					AND workstations.deleted_at IS NULL
					AND files.is_active = 1
					${
						created_at_start && created_at_end
							? `AND DATE(custom_report_results.created_at) BETWEEN DATE(:created_at_start) AND DATE(:created_at_end)`
							: ''
					}
					${company_id ? `AND files.company_id = :company_id` : 'AND files.company_id IN(:companies_ids)'}
					${sector_id ? `AND files.sector_id = :sector_id` : ''}
					${line_id ? `AND workstations.line_id = :line_id` : ''}
					${workstation_id ? `AND files.workstation_id = :workstation_id` : ''}
					${user_id ? `AND custom_report_results.created_by_user_id = :user_id` : ''}
				GROUP BY ${group_by}
				ORDER BY ${order_by}
			`;

			const custom_report_results = await this.db.sequelize.query(query, {
				replacements: params,
				type: this.db.sequelize.QueryTypes.SELECT
			});

			logger.info('[CustomReportResult] repository - countCreatedReportResultsByPeriod finish');
			return [custom_report_results, null];
		} catch (error) {
			logger.error('[CustomReportResult] repository - countCreatedReportResultsByPeriod error');
			return [null, error];
		} finally {
			logger.info('[CustomReportResult] repository - countCreatedReportResultsByPeriod finish');
		}
	}

	getReportReviewJoin(has_reviews) {
		if (has_reviews) {
			return `
				INNER JOIN (
					SELECT crr.*
					FROM custom_report_reviews crr
					INNER JOIN (
						SELECT original_custom_report_result_id, MAX(version) AS max_version
						FROM custom_report_reviews crr
						INNER JOIN custom_report_results crr_result ON crr.custom_report_result_id = crr_result.id
						WHERE crr_result.consolidated = 1
						GROUP BY original_custom_report_result_id
					) latest_reviews ON crr.original_custom_report_result_id = latest_reviews.original_custom_report_result_id
					AND crr.version = latest_reviews.max_version
				) custom_report_reviews ON custom_report_reviews.custom_report_result_id = custom_report_results.id
			`;
		}

		return '';
	}

	getYearExpression(year_start_month) {
		if (year_start_month === 1) {
			return `YEAR(custom_report_results.created_at) as year`;
		}

		return `
			YEAR(custom_report_results.created_at) +
			IF(MONTH(custom_report_results.created_at) >= ${year_start_month}, 1, 0) as year
		`;
	}

	getMonthExpression(year_start_month = 1) {
		if (year_start_month === 1) {
			return `MONTH(custom_report_results.created_at) as month`;
		}

		return `1 + MOD(MONTH(custom_report_results.created_at) - ${year_start_month} + 12, 12) as month`;
	}

	getQuarterExpression(year_start_month) {
		const month_expression = this.getMonthExpression(year_start_month).replace('as month', '');
		return `CEIL((${month_expression}) / 3) as quarter`;
	}

	getSemesterExpression(year_start_month) {
		const month_expression = this.getMonthExpression(year_start_month).replace('as month', '');
		return `CEIL((${month_expression}) / 6) as semester`;
	}

	getPeriodGroupQuery({ period_group, year_start_month }) {
		const year_expression = this.getYearExpression(year_start_month, period_group);
		const semester_expression = this.getSemesterExpression(year_start_month);
		const quarter_expression = this.getQuarterExpression(year_start_month);
		const month_expression = this.getMonthExpression();

		let group_by = 'year';
		let order_by = 'year';
		let fields = year_expression;

		if (period_group === DASHBOARD_GRANULARITY_VALUES.DAY) {
			group_by = 'day, month, year';
			order_by = 'year, month, day';
			fields = `
				DAY(custom_report_results.created_at) as day,
				${month_expression},
				${year_expression}
			`;
		}

		if (period_group === DASHBOARD_GRANULARITY_VALUES.WEEK) {
			group_by = 'week, month, year';
			order_by = 'year, month, week';
			fields = `
				WEEK(custom_report_results.created_at) as week,
				${month_expression},
				${year_expression}
			`;
		}

		if (period_group === DASHBOARD_GRANULARITY_VALUES.MONTH) {
			group_by = 'month, year';
			order_by = 'year, month';
			fields = `
				${month_expression},
				${year_expression}
			`;
		}

		if (period_group === DASHBOARD_GRANULARITY_VALUES.QUARTER) {
			group_by = 'quarter, year';
			order_by = 'year, quarter';
			fields = `
				${quarter_expression},
				${year_expression}
			`;
		}

		if (period_group === DASHBOARD_GRANULARITY_VALUES.SEMESTER) {
			group_by = 'semester, year';
			order_by = 'year, semester';
			fields = `
            	${semester_expression},
            	${year_expression}
        	`;
		}

		return { group_by, order_by, fields };
	}

	async countAllByRisk(params) {
		logger.info('[CustomReportResult] repository - countAllByRisk init');
		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date,
			custom_report_id,
			has_reviews
		} = params;

		try {
			let reviews_inner_join = '';

			if (has_reviews) {
				reviews_inner_join = `
					INNER JOIN (
						SELECT cr_reviews.*
						FROM custom_report_reviews cr_reviews
						INNER JOIN (
							SELECT original_custom_report_result_id, MAX(version) AS max_version
							FROM custom_report_reviews cr_reviews
							INNER JOIN custom_report_results cr_results ON cr_reviews.custom_report_result_id = cr_results.id
							WHERE cr_results.consolidated = 1
							GROUP BY original_custom_report_result_id
						) latest_reviews ON cr_reviews.original_custom_report_result_id = latest_reviews.original_custom_report_result_id
						AND cr_reviews.version = latest_reviews.max_version
					) cr_reviews ON cr_reviews.custom_report_result_id = cr_results.id
				`;
			}

			const query = `
				SELECT
					COUNT(DISTINCT CASE WHEN cr_results.result  = 1 THEN cr_results.id END) AS acceptable_risk,
					COUNT(DISTINCT CASE WHEN cr_results.result  = 2 THEN cr_results.id END) AS moderate_risk,
					COUNT(DISTINCT CASE WHEN cr_results.result  = 3 THEN cr_results.id END) AS high_risk,
					COUNT(DISTINCT CASE WHEN cr_results.result  = 4 THEN cr_results.id END) AS very_high_risk,
					COUNT(DISTINCT CASE WHEN cr_results.result  = 5 THEN cr_results.id END) AS serious_and_imminent_risk
				FROM custom_report_results AS cr_results
					INNER JOIN files ON cr_results.file_id = files.id
					INNER JOIN workstations ON workstations.id = files.workstation_id
					INNER JOIN \`lines\` AS l ON l.id = workstations.line_id
					INNER JOIN sectors ON sectors.id = l.sector_id
					${reviews_inner_join}
				WHERE files.organization_id = :organization_id
					AND cr_results.custom_report_id = :custom_report_id
					AND cr_results.deleted_at IS NULL
					AND cr_results.consolidated IS TRUE
					AND sectors.is_active = 1
					AND l.deleted_at IS NULL
					AND workstations.deleted_at IS NULL
					AND files.is_active = 1
					${company_id ? 'AND files.company_id = :company_id' : 'AND files.company_id IN(:companies_with_user_access)'}
					${sector_id ? 'AND sectors.id = :sector_id' : ''}
					${line_id ? 'AND l.id = :line_id' : ''}
					${workstation_id ? 'AND workstations.id = :workstation_id' : ''}
					${start_date ? 'AND DATE(cr_results.created_at) >= DATE(:start_date)' : ''}
					${end_date ? 'AND DATE(cr_results.created_at) <= DATE(:end_date)' : ''}
			`;

			const replacements = {
				organization_id,
				company_id,
				companies_with_user_access,
				sector_id,
				line_id,
				workstation_id,
				start_date,
				end_date,
				custom_report_id
			};

			const [total] = await this.db.sequelize.query(query, { nest: true, replacements });

			logger.info('[CustomReportResult] repository - countAllByRisk finish');
			return [total, null];
		} catch (error) {
			logger.error('[CustomReportResult] repository - countAllByRisk error');
			return [null, error];
		} finally {
			logger.info('[CustomReportResult] repository - countAllByRisk finish');
		}
	}

	#getHierarchyQuery({ company_id, sector_id, line_id, workstation_id }) {
		if (!company_id) {
			return {
				select: `companies.id, companies.name, MAX(cr_results.worst_score) AS worst_score`,
				group_by: `companies.name, companies.id`
			};
		}

		if (!sector_id) {
			return {
				select: `sectors.id, sectors.name, MAX(cr_results.worst_score) AS worst_score`,
				group_by: `sectors.name, sectors.id`
			};
		}

		if (!line_id) {
			return {
				select: `l.id, l.name, MAX(cr_results.worst_score) AS worst_score`,
				group_by: `l.name, l.id`
			};
		}

		if (!workstation_id) {
			return {
				select: `workstations.id, workstations.name, MAX(cr_results.worst_score) AS worst_score`,
				group_by: `workstations.name, workstations.id`
			};
		}

		return {
			select: `files.id, files.original_name as name, MAX(cr_results.worst_score) AS worst_score`,
			group_by: `files.original_name, files.id`
		};
	}

	async hierarchyWorstScores(params) {
		logger.info('[CustomReportResult] repository - hierarchyWorstScores init');
		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date,
			custom_report_id,
			limit
		} = params;

		try {
			const { select, group_by } = this.#getHierarchyQuery({
				organization_id,
				company_id,
				sector_id,
				line_id,
				workstation_id
			});

			const query = `
				SELECT
					${select}
				FROM custom_report_results AS cr_results
					INNER JOIN files ON cr_results.file_id = files.id
					INNER JOIN workstations ON workstations.id = files.workstation_id
					INNER JOIN \`lines\` AS l ON l.id = workstations.line_id
					INNER JOIN sectors ON sectors.id = l.sector_id
					INNER JOIN companies ON companies.id = sectors.company_id
					INNER JOIN organizations ON organizations.id = companies.organization_id
				WHERE cr_results.custom_report_id = :custom_report_id
					AND cr_results.consolidated IS TRUE
					AND cr_results.deleted_at IS NULL
					AND files.organization_id = :organization_id
					AND sectors.is_active = 1
					AND l.deleted_at IS NULL
					AND workstations.deleted_at IS NULL
					AND files.is_active = 1
					${company_id ? 'AND files.company_id = :company_id' : 'AND companies.id IN(:companies_with_user_access)'}
					${sector_id ? 'AND sectors.id = :sector_id' : ''}
					${line_id ? 'AND l.id = :line_id' : ''}
					${workstation_id ? 'AND workstations.id = :workstation_id' : ''}
					${start_date ? 'AND DATE(cr_results.created_at) >= DATE(:start_date)' : ''}
					${end_date ? 'AND DATE(cr_results.created_at) <= DATE(:end_date)' : ''}
				GROUP BY ${group_by}
				ORDER BY worst_score DESC
				LIMIT :limit
			`;

			const config = {
				nest: true,
				replacements: {
					organization_id,
					company_id,
					companies_with_user_access,
					sector_id,
					line_id,
					workstation_id,
					start_date,
					end_date,
					custom_report_id,
					limit
				}
			};

			const data = await this.db.sequelize.query(query, config);

			logger.info('[CustomReportResult] repository - hierarchyWorstScores finish');
			return [data, null];
		} catch (error) {
			logger.error('[CustomReportResult] repository - hierarchyWorstScores error');
			return [null, error];
		} finally {
			logger.info('[CustomReportResult] repository - hierarchyWorstScores finish');
		}
	}

	async findAllEwa(params) {
		logger.info('[CustomReportResult] repository - findAllEwa init');
		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			evaluator_id,
			collection_date_start,
			collection_date_end,
			created_at_start,
			created_at_end,
			file_name,
			user_id
		} = params;

		try {
			const mapper = new GetEwaCustomReportResultsSQL({
				organization_id,
				company_id,
				companies_with_user_access,
				sector_id,
				line_id,
				workstation_id,
				evaluator_id,
				collection_date_start,
				collection_date_end,
				created_at_start,
				created_at_end,
				file_name,
				user_id
			});

			const query = mapper.getIndexQuery();

			const config = { nest: true, replacements: params };
			const data = await this.db.sequelize.query(query, config);

			logger.info('[CustomReportResult] repository - findAllEwa finish');
			return [data, null];
		} catch (error) {
			logger.error('[CustomReportResult] repository - findAllEwa error');
			return [null, error];
		} finally {
			logger.info('[CustomReportResult] repository - findAllEwa finish');
		}
	}

	async countAllEwa(params) {
		logger.info('[CustomReportResult] repository - countAllEwa init');
		const {
			company_id,
			sector_id,
			line_id,
			workstation_id,
			evaluator_id,
			collection_date_start,
			collection_date_end,
			created_at_start,
			created_at_end,
			file_name,
			user_id
		} = params;

		try {
			const mapper = new GetEwaCustomReportResultsSQL({
				company_id,
				sector_id,
				line_id,
				workstation_id,
				evaluator_id,
				collection_date_start,
				collection_date_end,
				created_at_start,
				created_at_end,
				file_name,
				user_id
			});

			const countQuery = mapper.getCountAllQuery();

			const config = { nest: true, replacements: params };
			const [{ total }] = await this.db.sequelize.query(countQuery, config);

			logger.info('[CustomReportResult] repository - countAllEwa finish');
			return [total, null];
		} catch (error) {
			logger.error('[CustomReportResult] repository - countAllEwa error');
			return [null, error];
		} finally {
			logger.info('[CustomReportResult] repository - countAllEwa finish');
		}
	}

	async findResultsByReportType(params) {
		logger.info('[CustomReportResult] repository - findResultsByReportType init', { params });
		const { report_type } = params;
		try {
			const strategies = {
				default: {
					rows_mapper: new ListAllResultsByReportTypeSQL(params),
					count_mapper: new CountAllResultsByReportTypeSQL(params)
				},
				[CUSTOM_REPORT_NAMES.SERA]: {
					rows_mapper: new ListAllSeraReportsSQL(params),
					count_mapper: new CountAllSeraReportsSQL(params)
				},
				[CUSTOM_REPORT_NAMES.BERA]: {
					rows_mapper: new ListAllBeraReportsSQL(params),
					count_mapper: new CountAllBeraReportsSQL(params)
				}
			};

			const { rows_mapper, count_mapper } = strategies[report_type] || strategies['default'];

			const rows_query = rows_mapper.getQuery();
			const count_query = count_mapper.getQuery();

			const rows_config = { nest: true, replacements: rows_mapper.replacements };
			const count_config = { nest: true, replacements: count_mapper.replacements };

			const [rows, [count]] = await Promise.all([
				this.db.sequelize.query(rows_query, rows_config),
				this.db.sequelize.query(count_query, count_config)
			]);

			const data = { rows, count: count?.total || 0 };

			logger.info('[CustomReportResult] repository - findResultsByReportType success');
			return [data, null];
		} catch (error) {
			logger.error('[CustomReportResult] repository - findResultsByReportType error', { error });
			return [null, error];
		} finally {
			logger.info('[CustomReportResult] repository - findResultsByReportType finish');
		}
	}
}
