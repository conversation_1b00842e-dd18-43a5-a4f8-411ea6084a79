import { BaseRepository } from './baseRepository/index.js';

export class UserManagementRepository extends BaseRepository {
	constructor({ database }) {
		super(database);
	}
	async hasPermissionsUser(userId) {
		const query = `
			SELECT
				org.id as organization_id,
				org.name as organization_name,
				cmp.id as company_id,
				cmp.name as company_name
			FROM users as user
				INNER JOIN user_accesses as usr_access ON
					user.id = usr_access.user_id
				INNER JOIN organizations as org ON
					usr_access.organization_id = org.id
				INNER JOIN companies as cmp ON
					usr_access.company_id = cmp.id
			WHERE(
				user.id = :user_id
					&&
				user.is_active = 1
					&&
				usr_access.is_active = 1
					&&
				org.is_active = 1 && cmp.is_active = 1
			)
		`;
		const config = {
			nest: true,
			replacements: { user_id: userId }
		};
		const result = await this.db.sequelize.query(query, config);
		return result;
	}
}
