import { logger } from '../helpers/logger.js';
import { BaseRepository } from './v2/base-repository.js';

export class StrainIndexReportRepository extends BaseRepository {
	constructor(database) {
		super(database);
		this.strain_index = this.db.StrainIndexReport;
	}

	async create(params, transaction) {
		logger.info('[StrainIndexReport] repository - create init');
		const strain_index = await this.strain_index.create(params, transaction);
		logger.info('[StrainIndexReport] repository - create finish');
		return strain_index;
	}

	async findByPk(id, params) {
		logger.info('[StrainIndexReport] repository - findByPk init');
		const strain_index = await this.strain_index.findByPk(id, params);
		logger.info('[StrainIndexReport] repository - findByPk finish');
		return strain_index;
	}

	async update(params, options) {
		logger.info('[StrainIndexReport] repository - update init');
		const strain_index = await this.strain_index.update(params, options);
		logger.info('[StrainIndexReport] repository - update finish');
		return strain_index;
	}

	async delete(params, transaction) {
		logger.info('[StrainIndexReport] repository - delete init');
		const strain_index = await this.strain_index.destroy(params, transaction);
		logger.info('[StrainIndexReport] repository - delete finish');
		return strain_index;
	}

	async findOne(params) {
		logger.info('[StrainIndexReport] repository - findOne init');
		const strain_index = await this.strain_index.findOne({
			...params
		});
		logger.info('[StrainIndexReport] repository - findOne finish');
		return strain_index;
	}

	async findAll(params) {
		logger.info('[StrainIndexReport] repository - findAll init');
		const strain_index = await this.strain_index.findAll({
			...params
		});
		logger.info('[StrainIndexReport] repository - findAll finish');
		return strain_index;
	}

	async findAllByForeignKey(params) {
		logger.info('[StrainIndexReport] repository - findAllByForeignKey init');
		const strain_index = await this.strain_index.findAll({
			...params
		});
		logger.info('[StrainIndexReport] repository - findAllByForeignKey finish');
		return strain_index;
	}

	getGreatestScoreRsiFilterParams(params) {
		logger.info('[StrainIndexReport] repository - getGreatestScoreRsiFilterParams init', { params });
		const {
			organization_id,
			companies_ids,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date,
			user_id
		} = params;

		let filters = {
			organization_id
		};

		if (company_id) {
			filters.company_id = company_id;
		}

		if (companies_ids) {
			filters.companies_ids = companies_ids;
		}

		if (sector_id) {
			filters.sector_id = sector_id;
		}

		if (line_id) {
			filters.line_id = line_id;
		}

		if (workstation_id) {
			filters.workstation_id = workstation_id;
		}

		if (start_date && end_date) {
			filters.start_date = start_date;
			filters.end_date = end_date;
		}

		if (user_id) {
			filters.user_id = user_id;
		}

		logger.info('[StrainIndexReport] repository - getGreatestScoreRsiFilterParams finish');
		return filters;
	}

	getGreatestScoreRsiFilterQuery(params) {
		logger.info('[StrainIndexReport] repository - getGreatestScoreRsiFilterQuery init', { params });
		const { company_id, sector_id, line_id, workstation_id, companies_ids, start_date, end_date, user_id } = params;

		const sub_query = `
			SELECT
                report.id,
                COALESCE(GREATEST(MAX(report.score_left_rsi), MAX(report.score_right_rsi)), 0) AS greatest_score
            FROM
                strain_index_reports AS report
                INNER JOIN files AS file ON file.id = report.file_id
                INNER JOIN workstations AS workstation ON workstation.id = file.workstation_id
                INNER JOIN lines AS line ON line.id = workstation.line_id
                INNER JOIN sectors AS sector ON sector.id = line.sector_id
			WHERE
				report.is_active = 1
				AND file.organization_id = :organization_id
				AND file.is_active = 1
				AND sector.is_active = 1
				AND line.deleted_at IS NULL
				AND workstation.deleted_at IS NULL
				${company_id ? `AND file.company_id = :company_id` : ''}
				${!company_id && companies_ids ? `AND file.company_id IN(:companies_ids)` : ''}
				${workstation_id ? `AND workstation.id = :workstation_id` : ''}
				${sector_id ? `AND file.id = :sector_id` : ''}
				${line_id ? `AND line.id = :line_id` : ''}
				${start_date && end_date ? `AND DATE(report.created_at) BETWEEN DATE (:start_date) AND DATE(:end_date)` : ''}
				${user_id ? `AND report.report_user_id = :user_id` : ''}
			GROUP BY report.id
		`;

		const query = `
			SELECT
				COUNT(CASE WHEN greatest_score <= 10 THEN 1 END) AS less_or_equal_than_ten,
				COUNT(CASE WHEN greatest_score > 10 THEN 1 END) AS greater_than_ten
			FROM
				(${sub_query}) AS rsi_risk
		`;

		logger.info('[StrainIndexReport] repository - getGreatestScoreRsiFilterQuery finish');
		return query;
	}

	async getGreatestScoreRsi(params) {
		logger.info('[StrainIndexReport] repository - getGreatestScoreRsi init', { params });
		try {
			const filter = this.getGreatestScoreRsiFilterParams(params);
			const query = this.getGreatestScoreRsiFilterQuery(params);

			const config = {
				plain: true,
				replacements: filter,
				type: this.db.sequelize.QueryTypes.SELECT
			};

			const data = await this.db.sequelize.query(query, config);

			logger.info('[StrainIndexReport] repository - getGreatestScoreRsi success');
			return [data, null];
		} catch (error) {
			logger.error('[StrainIndexReport] repository - getGreatestScoreRsi error');
			return [null, error];
		} finally {
			logger.info('[StrainIndexReport] repository - getGreatestScoreRsi finish');
		}
	}
}
