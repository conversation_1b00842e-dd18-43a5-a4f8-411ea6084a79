import { logger } from '../helpers/logger.js';
import { BaseRepository } from './v2/base-repository.js';

export class RebaReportRepository extends BaseRepository {
	constructor(database) {
		super(database);
	}

	async findFilesActionPlans(payload) {
		logger.info('[RebaReport] repository - findFilesActionPlans init');
		const query = `
			SELECT
				org.name AS Empresa,
				cmp.name AS Unidade,
				sct.name AS Setor,
				files.original_name AS 'Nome Arquivo',
				files.id AS file_id,
				apt.description AS "Tarefa",
				ap.title AS 'Título Plano',
				ap.description AS 'Descrição',
				pas.score AS Score,
				pa.self_evaluation as 'Nível de estresse',
				ap.deadline AS Prazo,
				users.name AS 'Responsável'
			FROM
				action_plans AS ap
			INNER JOIN
				preliminary_analysis_steps AS pas ON
				ap.step_id = pas.id
			INNER JOIN
				preliminary_analyzes pa ON
				pas.preliminary_analysis_id = pa.id
			LEFT JOIN
				action_plans_tasks AS apt ON
				ap.id = apt.action_plan_id
			INNER JOIN
				files AS files ON
				ap.file_id = files.id
			INNER JOIN
				organizations AS org ON
				files.organization_id = org.id
			INNER JOIN
				companies AS cmp ON
				files.company_id = cmp.id
			INNER JOIN
				sectors AS sct ON
				files.sector_id = sct.id
			LEFT JOIN
				users AS users ON
				ap.responsible_user_id = users.id
			WHERE
				(
					org.id = :organization_id
					AND cmp.id = :company_id
					AND ap.is_active = 1
					AND pas.is_active = 1
					AND pa.is_active = 1
					AND pa.consolidated = 1
					AND files.is_active = 1
					AND org.is_active = 1
					AND cmp.is_active = 1
					${
						payload.sector_id
							? `AND sct.id = :sector_id
							   AND sct.is_active = 1`
							: ''
					}
				)
		`;

		const config = {
			nest: true,
			replacements: payload
		};

		const result = await this.db.sequelize.query(query, config);

		logger.info('[RebaReport] repository - findFilesActionPlans finish');
		return result;
	}

	async getExposuresScore(params) {
		logger.info('[RebaReport] repository - getExposuresScore init', { params });
		const {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_ids,
			range_risk_id,
			start_date,
			end_date,
			user_id
		} = params;
		try {
			const where_filters = this.getExposureScoreFilter({
				organization_id,
				company_id,
				sector_id,
				line_id,
				workstation_id,
				companies_ids,
				start_date,
				end_date,
				user_id: user_id || ''
			});

			const query = `
				SELECT
					risk,
					SUM(percentage * files.duration) / 100 as seconds,
					b.name as body_part
				FROM
					files
					INNER JOIN file_risk_results ON file_risk_results.file_id = files.id
					INNER JOIN body_parts b ON file_risk_results.body_part_id = b.id
					INNER JOIN reba_reports r ON files.id = r.file_id
					INNER JOIN workstations w ON files.workstation_id = w.id
					INNER JOIN lines AS l ON l.id = w.line_id
					INNER JOIN sectors s ON s.id = l.sector_id
				WHERE
					files.is_active = 1
					AND r.is_active = 1
					AND file_risk_results.range_risk_id = :range_risk_id
					AND file_risk_results.deleted_at IS NULL
					AND w.deleted_at IS NULL
					AND l.deleted_at IS NULL
					AND s.is_active = 1
					${where_filters}
				GROUP BY body_part, risk
				ORDER BY body_part, risk
			`;

			const data = await this.db.sequelize.query(query, {
				replacements: {
					organization_id,
					company_id,
					sector_id,
					line_id,
					workstation_id,
					companies_ids,
					range_risk_id,
					start_date,
					end_date,
					user_id
				},
				type: this.db.sequelize.QueryTypes.SELECT
			});

			logger.info('[RebaReport] repository - getExposuresScore finish');
			return [data, null];
		} catch (error) {
			logger.error('[RebaReport] repository - getExposuresScore error');
			return [null, error];
		} finally {
			logger.info('[RebaReport] repository - getExposuresScore finish');
		}
	}

	async getBodySideExposuresScore(params) {
		logger.info('[RebaReport] repository - getBodySideExposuresScore init', { params });
		const {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_ids,
			start_date,
			end_date,
			range_risk_id,
			user_id
		} = params;

		try {
			const where_filters = this.getExposureScoreFilter({
				organization_id,
				company_id,
				sector_id,
				line_id,
				workstation_id,
				companies_ids,
				start_date,
				end_date,
				user_id: user_id || ''
			});

			const query = `
				SELECT
					risk,
					SUM(percentage * files.duration) / 100 as seconds,
					CASE
						WHEN b.name LIKE '%right%' THEN 'right_side'
						ELSE 'left_side'
					END as body_side
				FROM
					files
					INNER JOIN file_risk_results ON file_risk_results.file_id = files.id
					INNER JOIN body_parts b ON file_risk_results.body_part_id = b.id
					INNER JOIN reba_reports r ON files.id = r.file_id
					INNER JOIN workstations w ON files.workstation_id = w.id
					INNER JOIN lines AS l ON l.id = w.line_id
					INNER JOIN sectors s ON s.id = l.sector_id
				WHERE
					files.is_active = 1
					AND file_risk_results.deleted_at IS NULL
					AND r.is_active = 1
					AND w.deleted_at IS NULL
					AND l.deleted_at IS NULL
					AND s.is_active = 1
					AND file_risk_results.range_risk_id = :range_risk_id
					AND b.name LIKE '%left%' OR b.name LIKE '%right%'
					${where_filters}
				GROUP BY body_side, risk
				ORDER BY body_side, risk
			`;

			const data = await this.db.sequelize.query(query, {
				type: this.db.sequelize.QueryTypes.SELECT,
				replacements: {
					organization_id,
					company_id,
					sector_id,
					line_id,
					workstation_id,
					companies_ids,
					start_date,
					end_date,
					range_risk_id,
					user_id
				}
			});

			logger.info('[RebaReport] repository - getBodySideExposuresScore finish');
			return [data, null];
		} catch (error) {
			logger.error('[RebaReport] repository - getBodySideExposuresScore error');
			return [null, error];
		} finally {
			logger.info('[RebaReport] repository - getBodySideExposuresScore finish');
		}
	}

	getExposureScoreFilter(params) {
		const {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_ids,
			start_date,
			end_date,
			user_id
		} = params;

		const where_filters = `
			${start_date && end_date ? 'AND DATE(r.created_at) BETWEEN DATE(:start_date) AND DATE(:end_date)' : ''}
			${organization_id ? 'AND files.organization_id = :organization_id' : ''}
			${company_id ? 'AND files.company_id = :company_id' : ''}
			${!company_id && companies_ids ? 'AND files.company_id IN(:companies_ids)' : ''}
			${line_id ? 'AND w.line_id = :line_id' : ''}
			${sector_id ? 'AND files.sector_id = :sector_id' : ''}
			${workstation_id ? 'AND files.workstation_id = :workstation_id' : ''}
			${user_id ? 'AND r.report_user_id = :user_id' : ''}
		`;

		return where_filters;
	}
}
