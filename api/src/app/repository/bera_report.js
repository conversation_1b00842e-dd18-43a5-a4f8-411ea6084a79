import { logger } from '../helpers/logger.js';
import { BaseRepository } from './v2/base-repository.js';
import { DASHBOARD_GRANULARITY_VALUES } from '../utils/constants.js';

export class BeraReportRepository extends BaseRepository {
	constructor(database) {
		super(database);
		this.bera_report = this.db.BeraReport;
	}

	async create(params, transaction) {
		logger.info('[BeraReport] repository - create init');
		const bera_report = await this.bera_report.create(params, transaction);
		logger.info('[BeraReport] repository - create finish');
		return bera_report;
	}

	async update(params, options) {
		logger.info('[BeraReport] repository - update init');
		const bera_report = await this.bera_report.update(params, options);
		logger.info('[BeraReport] repository - update finish');
		return bera_report;
	}

	async delete(params, transaction) {
		logger.info('[BeraReport] repository - delete init');
		const bera_report = await this.bera_report.destroy(params, transaction);
		logger.info('[BeraReport] repository - delete finish');
		return bera_report;
	}

	async findByPk(id, params) {
		logger.info('[BeraReport] repository - findByPk init');
		const bera_report = await this.bera_report.findByPk(id, params);
		logger.info('[BeraReport] repository - findByPk finish');
		return bera_report;
	}

	async findOne(params) {
		logger.info('[BeraReport] repository - findOne init');
		const bera_reports = await this.bera_report.findOne({
			...params
		});
		logger.info('[BeraReport] repository - findOne finish');
		return bera_reports;
	}

	async findAllByForeignKey(params) {
		logger.info('[BeraReport] repository - findAllByForeignKey init');
		const bera_reports = await this.bera_report.findAll({
			...params
		});
		logger.info('[BeraReport] repository - findAllByForeignKey finish');
		return bera_reports;
	}

	async genderNeutralCount(params) {
		logger.info('[BeraReport] repository - genderNeutralCount init', { params });
		const {
			six_too_tasks_neutral_with_rpn_one,
			is_gender_neutral,
			year_start_month,
			organization_id,
			workstation_id,
			companies_ids,
			period_group,
			fiscal_year,
			company_id,
			start_date,
			sector_id,
			end_date,
			line_id
		} = params;

		try {
			const last_reviews_query = this.getQueryLastReviews({
				organization_id,
				workstation_id,
				companies_ids,
				company_id,
				start_date,
				sector_id,
				end_date,
				line_id
			});
			const { fields, group_by, order_by } = this.getPeriodGroupQuery({
				period_group,
				year_start_month
			});

			const query = `
				WITH bera_last_reviews AS (${last_reviews_query})
				SELECT
					COUNT(DISTINCT bera.id) as count,
					${fields}
				FROM bera_reports bera
				INNER JOIN bera_last_reviews blr ON bera.bera_job_summary_id = blr.bera_job_summary_id AND bera.created_at = blr.last_created_at
				WHERE bera.id ${is_gender_neutral ? 'NOT IN' : 'IN'} (
					SELECT
						DISTINCT bera_report_id
					FROM
						bera_step_key_results bskr
					WHERE
						(bskr.task_rpn > 3 AND bskr.custom_report_step_key_id NOT IN (:six_too_tasks_neutral_with_rpn_one))
						OR (bskr.task_rpn > 1 AND bskr.custom_report_step_key_id IN (:six_too_tasks_neutral_with_rpn_one))
				)
				GROUP BY ${group_by}
				ORDER BY ${order_by}
			`;

			const data = await this.db.sequelize.query(query, {
				replacements: {
					six_too_tasks_neutral_with_rpn_one,
					is_gender_neutral,
					year_start_month,
					organization_id,
					workstation_id,
					companies_ids,
					period_group,
					fiscal_year,
					company_id,
					start_date,
					sector_id,
					end_date,
					line_id
				},
				type: this.db.sequelize.QueryTypes.SELECT
			});

			logger.info('[BeraReport] repository - genderNeutral success');
			return [data, null];
		} catch (error) {
			logger.error('[BeraReport] repository - genderNeutral error');
			return [null, error];
		} finally {
			logger.info('[BeraReport] repository - genderNeutral finish');
		}
	}

	async sumRPN(params) {
		logger.info('[BeraReport] repository - sumRPN init', { params });
		const {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_ids,
			start_date,
			end_date,
			year_start_month,
			user_id
		} = params;
		try {
			const last_reviews_query = this.getQueryLastReviews({
				organization_id,
				company_id,
				sector_id,
				line_id,
				workstation_id,
				companies_ids,
				start_date,
				end_date
			});

			const query = `
				WITH bera_last_reviews AS (${last_reviews_query})
				SELECT COALESCE(SUM(bera.rpn), 0) as sum_rpn
				FROM bera_reports bera
					INNER JOIN bera_last_reviews blr ON bera.bera_job_summary_id = blr.bera_job_summary_id AND bera.created_at = blr.last_created_at
			`;

			const data = await this.db.sequelize.query(query, {
				plain: true,
				replacements: {
					organization_id,
					company_id,
					sector_id,
					line_id,
					workstation_id,
					companies_ids,
					start_date,
					end_date,
					year_start_month
				},
				type: this.db.sequelize.QueryTypes.SELECT
			});

			logger.info('[BeraReport] repository - sumRPN success');
			return [data, null];
		} catch (error) {
			logger.error('[BeraReport] repository - sumRPN error');
			return [null, error];
		} finally {
			logger.info('[BeraReport] repository - sumRPN finish');
		}
	}

	async hierarchySumRPN(params) {
		logger.info('[BeraReport] repository - hierarchySumRPN init', { params });
		const {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_ids,
			start_date,
			end_date,
			limit,
			offset,
			user_id
		} = params;
		try {
			const last_reviews_query = this.getQueryLastReviews({
				organization_id,
				company_id,
				sector_id,
				line_id,
				workstation_id,
				companies_ids,
				start_date,
				end_date,
				select_with_full_hierarchy: true,
				user_id
			});

			const hierarchy_rpn_query = this.getHierarchySumRPNQuery({
				organization_id,
				company_id,
				sector_id,
				line_id,
				workstation_id,
				companies_ids,
				start_date,
				end_date
			});

			const query = `
				WITH bera_last_reviews AS (${last_reviews_query})
				${hierarchy_rpn_query}
				ORDER BY total_rpn DESC
				LIMIT :limit
				OFFSET :offset
			`;

			const data = await this.db.sequelize.query(query, {
				replacements: {
					organization_id,
					company_id,
					sector_id,
					line_id,
					workstation_id,
					companies_ids,
					start_date,
					end_date,
					limit,
					offset: (offset - 1) * limit,
					user_id
				},
				type: this.db.sequelize.QueryTypes.SELECT
			});

			logger.info('[BeraReport] repository - hierarchySumRPN success');
			return [data, null];
		} catch (error) {
			logger.error('[BeraReport] repository - hierarchySumRPN error');
			return [null, error];
		} finally {
			logger.info('[BeraReport] repository - hierarchySumRPN finish');
		}
	}

	async sixTooSumRPN(params) {
		logger.info('[BeraReport] repository - sixTooSumRPN init', { params });
		const {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_ids,
			custom_report_step_ids,
			start_date,
			end_date,
			user_id
		} = params;
		try {
			const last_reviews_query = this.getQueryLastReviews({
				organization_id,
				company_id,
				sector_id,
				line_id,
				workstation_id,
				companies_ids,
				start_date,
				end_date,
				user_id
			});

			const query = `
				WITH bera_last_reviews AS (${last_reviews_query})
				SELECT
					COALESCE(SUM(task_rpn), 0) as sum_rpn,
					cpsk.custom_report_step_id
				FROM bera_reports bera
					INNER JOIN bera_last_reviews blr ON bera.bera_job_summary_id = blr.bera_job_summary_id AND bera.created_at = blr.last_created_at
					INNER JOIN bera_step_key_results bskr ON bskr.bera_report_id = bera.id
					INNER JOIN custom_report_step_keys cpsk ON cpsk.id = bskr.custom_report_step_key_id
				WHERE
					cpsk.custom_report_step_id IN (:custom_report_step_ids)
				GROUP BY cpsk.custom_report_step_id
			`;

			const data = await this.db.sequelize.query(query, {
				replacements: {
					organization_id,
					company_id,
					sector_id,
					line_id,
					workstation_id,
					companies_ids,
					custom_report_step_ids,
					start_date,
					end_date,
					user_id
				},
				type: this.db.sequelize.QueryTypes.SELECT
			});

			logger.info('[BeraReport] repository - sixTooSumRPN success');
			return [data, null];
		} catch (error) {
			logger.error('[BeraReport] repository - sixTooSumRPN error');
			return [null, error];
		} finally {
			logger.info('[BeraReport] repository - sixTooSumRPN finish');
		}
	}

	getHierarchySumRPNQuery(params) {
		const { workstation_id, line_id, sector_id, company_id } = params;

		let fields = 'companies.id, companies.name';

		let table = `
			companies
			INNER JOIN bera_last_reviews blr ON companies.id = blr.company_id
		`;

		if (company_id) {
			fields = 'sectors.id, sectors.name';
			table = `
				sectors
				INNER JOIN bera_last_reviews blr ON sectors.id = blr.sector_id
			`;
		}

		if (sector_id) {
			fields = 'lines.id, lines.name';
			table = `
				lines
				INNER JOIN bera_last_reviews blr ON lines.id = blr.line_id
			`;
		}

		if (line_id) {
			fields = 'workstations.id, workstations.name';
			table = `
				workstations
				INNER JOIN bera_last_reviews blr ON workstations.id = blr.workstation_id
			`;
		}

		if (workstation_id) {
			fields = 'tasks.id, tasks.name';
			table = `
				tasks
				INNER JOIN bera_last_reviews blr ON tasks.id = blr.task_id
			`;
		}

		return `
			SELECT
				${fields}, COALESCE(SUM(blr.rpn), 0) AS total_rpn
			FROM
				${table}
			GROUP BY ${fields}
		`;
	}

	getQueryLastReviews(params) {
		logger.info('[BeraReport] repository - getQueryLastReviews init');
		const {
			select_with_full_hierarchy,
			organization_id,
			workstation_id,
			companies_ids,
			company_id,
			start_date,
			sector_id,
			end_date,
			line_id,
			user_id
		} = params;

		let additional_fields = '';

		if (select_with_full_hierarchy) {
			additional_fields = `
				bera_reports.rpn,
				bera_reports.task_id,
				files.organization_id,
				c.id as company_id,
				files.sector_id,
				${line_id || sector_id ? 'workstations.line_id,' : ''}
				files.workstation_id,
			`;
		}

		const last_reviews_query = `
				SELECT
					bera_reports.bera_job_summary_id,
					${additional_fields}
					MAX(bera_reports.created_at) AS last_created_at
				FROM
					bera_reports
					INNER JOIN files ON files.id = bera_reports.file_id
					INNER JOIN workstations ON workstations.id = files.workstation_id  AND workstations.deleted_at IS NULL
					INNER JOIN lines AS l ON l.id = workstations.line_id AND l.deleted_at IS NULL
					INNER JOIN sectors s ON s.id = l.sector_id AND s.is_active = 1
					INNER JOIN bera_job_summaries bjs ON bjs.id = bera_reports.bera_job_summary_id
					INNER JOIN companies c ON c.id = s.company_id AND c.is_active = 1
				WHERE
					files.is_active = 1
					AND bera_reports.deleted_at IS NULL
					AND bera_reports.consolidated = 1
					${start_date && end_date ? 'AND DATE(bera_reports.created_at) BETWEEN DATE(:start_date) AND DATE(:end_date)' : ''}
					${organization_id ? 'AND files.organization_id = :organization_id' : ''}
					${company_id ? 'AND c.id = :company_id' : ''}
					${!company_id && companies_ids ? 'AND c.id IN(:companies_ids)' : ''}
					${line_id ? 'AND workstations.line_id = :line_id' : ''}
					${sector_id ? 'AND s.id = :sector_id' : ''}
					${workstation_id ? 'AND files.workstation_id = :workstation_id' : ''}
					${user_id ? 'AND bjs.user_id = :user_id' : ''}
				GROUP BY bera_reports.bera_job_summary_id
		`;

		logger.info('[BeraReport] repository - getQueryLastReviews finish');
		return last_reviews_query.trim();
	}

	getPeriodGroupQuery({ period_group, year_start_month }) {
		const year_expression = this.getYearExpression(year_start_month, period_group);
		const semester_expression = this.getSemesterExpression(year_start_month);
		const quarter_expression = this.getQuarterExpression(year_start_month);
		const month_expression = this.getMonthExpression();

		let group_by = 'year';
		let order_by = 'year';
		let fields = year_expression;

		if (period_group === DASHBOARD_GRANULARITY_VALUES.DAY) {
			fields = `
				DAY(bera.created_at) as day,
				${month_expression},
				${year_expression}
			`;
			group_by = 'day, month, year';
			order_by = 'year, month, day';
		}

		if (period_group === DASHBOARD_GRANULARITY_VALUES.WEEK) {
			fields = `
				WEEK(bera.created_at) as week,
				${month_expression},
				${year_expression}
			`;

			group_by = 'week, month, year';
			order_by = 'year, month, week';
		}

		if (period_group === DASHBOARD_GRANULARITY_VALUES.MONTH) {
			fields = `
				${month_expression},
				${year_expression}
			`;
			group_by = 'month, year';
			order_by = 'year, month';
		}

		if (period_group === DASHBOARD_GRANULARITY_VALUES.QUARTER) {
			fields = `
				${quarter_expression},
				${year_expression}
			`;
			group_by = 'quarter, year';
			order_by = 'year, quarter';
		}

		if (period_group === DASHBOARD_GRANULARITY_VALUES.SEMESTER) {
			fields = `
            	${semester_expression},
            	${year_expression}
        	`;
			group_by = 'semester, year';
			order_by = 'year, semester';
		}

		return { group_by, order_by, fields };
	}

	getYearExpression(year_start_month) {
		if (year_start_month === 1) {
			return `YEAR(bera.created_at) as year`;
		}

		return `
			YEAR(bera.created_at) + IF(MONTH(bera.created_at) >= ${year_start_month}, 1, 0) as year
		`;
	}

	getMonthExpression(year_start_month = 1) {
		if (year_start_month === 1) {
			return `MONTH(bera.created_at) as month`;
		}

		return `1 + MOD(MONTH(bera.created_at) - ${year_start_month} + 12, 12) as month`;
	}

	getQuarterExpression(year_start_month) {
		const month_expression = this.getMonthExpression(year_start_month).replace('as month', '');
		return `CEIL((${month_expression}) / 3) as quarter`;
	}

	getSemesterExpression(year_start_month) {
		const month_expression = this.getMonthExpression(year_start_month).replace('as month', '');
		return `CEIL((${month_expression}) / 6) as semester`;
	}
}
