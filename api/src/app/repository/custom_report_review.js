import moment from 'moment';
import sequelize from 'sequelize';

import { AppError, RESPONSE_ERROR_ENTITIES } from '../helpers/index.js';
import { BaseRepository } from './v2/base-repository.js';
import { logger } from '../helpers/logger.js';

const { CUSTOM_REPORT_REVIEW } = RESPONSE_ERROR_ENTITIES;

const OPERATOR_TYPES = {
	GT: 'gt',
	GTE: 'gte',
	LT: 'lt',
	LTE: 'lte'
};

const OPERATORS = {
	[OPERATOR_TYPES.GT]: '>',
	[OPERATOR_TYPES.GTE]: '>=',
	[OPERATOR_TYPES.LT]: '<',
	[OPERATOR_TYPES.LTE]: '<='
};

export class CustomReportReviewRepository extends BaseRepository {
	constructor(database) {
		super(database);
		this.custom_report_review = this.db.CustomReportReview;
	}

	async create(params, transaction) {
		logger.info('[CustomReportReview] repository - create init');
		const custom_report_review = await this.custom_report_review.create(params, transaction);
		logger.info('[CustomReportReview] repository - create finish');
		return custom_report_review;
	}

	async findByPk(id, params) {
		logger.info('[CustomReportReview] repository - findByPk init');
		const custom_report_review = await this.custom_report_review.findByPk(id, params);
		logger.info('[CustomReportReview] repository - findByPk finish');
		return custom_report_review;
	}

	async findOne(params) {
		logger.info('[CustomReportReview] repository - findOne init');
		const custom_report_reviews = await this.custom_report_review.findOne({
			...params
		});
		logger.info('[CustomReportReview] repository - findOne finish');
		return custom_report_reviews;
	}

	async findAll(params) {
		logger.info('[CustomReportReview] repository - findAll init');
		const custom_report_reviews = await this.custom_report_review.findAll({
			...params
		});
		logger.info('[CustomReportReview] repository - findAll finish');
		return custom_report_reviews;
	}

	async update(params, options) {
		logger.info('[CustomReportReview] repository - update init');
		const custom_report_review = await this.custom_report_review.update(params, options);
		logger.info('[CustomReportReview] repository - update finish');
		return custom_report_review;
	}

	async delete(params, transaction) {
		logger.info('[CustomReportReview] repository - delete init');
		const custom_report_review = await this.custom_report_review.destroy(params, transaction);
		logger.info('[CustomReportReview] repository - delete finish');
		return custom_report_review;
	}

	async findCustomReportResultLastReviews(payload, offset = 0, limit = 10) {
		logger.info('[CustomReportReview] repository - findCustomReportResultLastReview init');

		const { filters } = payload;

		const sub_query = this.#subQueryToGetLastResultReviews();
		const inner_joins = this.#innerJoinsToGetLastResultReviews();
		const where = this.#setFilters(filters);

		const query = `
			SELECT
				reviews.id,
				reviews.version,
				reviews.created_at,
				results.id as result_id,
				results.name as result_name,
				results.result,
				results.file_id,
				results.custom_report_id,
				workstations.name as workstation_name,
				original_results.id as original_result_id,
				original_results.created_at as original_created_at
			FROM (${sub_query}) AS last_reviews
				${inner_joins}
			WHERE
				reviews.deleted_at IS NULL
				${where}
			LIMIT :limit
			OFFSET :offset
		`;

		const config = {
			nest: true,
			replacements: {
				...filters,
				limit,
				offset: limit * offset
			}
		};

		const result = await this.db.sequelize.query(query, config);

		logger.info('[CustomReportReview] repository - findCustomReportResultLastReview finish');
		return result;
	}

	async countCustomReportResultLastReviews(payload) {
		logger.info('[CustomReportReview] repository - countCustomReportResultLastReviews init');

		const { filters } = payload;

		const sub_query = this.#subQueryToGetLastResultReviews();
		const inner_joins = this.#innerJoinsToGetLastResultReviews();
		const where = this.#setFilters(filters);

		const query = `
			SELECT
				COUNT(*) as count
			FROM (${sub_query}) AS last_reviews
				${inner_joins}
			WHERE
				reviews.deleted_at IS NULL
				${where}
		`;

		const config = {
			nest: true,
			replacements: {
				...filters
			}
		};

		const [result] = await this.db.sequelize.query(query, config);

		logger.info('[CustomReportReview] repository - countCustomReportResultLastReviews finish');

		return result;
	}

	async findLastReviewByOriginalResultId(original_custom_report_result_id) {
		const find_last_review_version = await this.custom_report_review.findOne({
			attributes: [
				'original_custom_report_result_id',
				[sequelize.fn('MAX', sequelize.col('version')), 'max_version']
			],
			where: {
				original_custom_report_result_id
			},
			group: ['original_custom_report_result_id']
		});

		if (!find_last_review_version) {
			throw new AppError(CUSTOM_REPORT_REVIEW.NOT_FOUND);
		}

		const last_review_data = find_last_review_version.get({ plain: true });

		const last_review = await this.custom_report_review.findOne({
			where: {
				original_custom_report_result_id,
				version: last_review_data.max_version
			}
		});

		return last_review;
	}

	#subQueryToGetLastResultReviews() {
		return `
			SELECT
				MAX(version) AS last_version,
				original_custom_report_result_id
			FROM
				custom_report_reviews
			WHERE
				original_custom_report_result_id IN (:original_custom_report_result_ids)
				AND deleted_at IS NULL
			GROUP BY
				original_custom_report_result_id
		`;
	}

	#innerJoinsToGetLastResultReviews() {
		return `
			INNER JOIN custom_report_reviews reviews
				ON reviews.original_custom_report_result_id = last_reviews.original_custom_report_result_id
				AND reviews.version = last_reviews.last_version
			INNER JOIN custom_report_results results
				ON results.id = reviews.custom_report_result_id
			INNER JOIN custom_report_results original_results
				ON original_results.id = reviews.original_custom_report_result_id
			INNER JOIN files files
				ON files.id = results.file_id
			INNER JOIN workstations workstations
				ON workstations.id = files.workstation_id
		`;
	}

	#setFilters(filters) {
		const { evaluator_id, type_rpn, reviewed_start_date, reviewed_end_date } = filters;

		let query_where = '';

		if (evaluator_id) {
			query_where = `${query_where} AND reviews.evaluator_id = :evaluator_id`;
		}

		if (type_rpn) {
			query_where = `${query_where} ${this.#setRPNFilter(filters)}`;
		}

		if (reviewed_start_date && reviewed_end_date) {
			const formatted_reviewed_start_date = this.#formatDate(reviewed_start_date);
			const end_date_plus_one_day = moment(reviewed_end_date).add(1, 'days');
			const formatted_reviewed_end_date = this.#formatDate(end_date_plus_one_day);

			query_where = `${query_where} AND reviews.version > 1`;
			query_where = `${query_where} AND reviews.created_at BETWEEN '${formatted_reviewed_start_date}' AND '${formatted_reviewed_end_date}'`;
		}

		return query_where;
	}

	#setRPNFilter(filters) {
		const { min_rpn, max_rpn, type_rpn } = filters;

		if (min_rpn && max_rpn) {
			return `AND results.worst_score BETWEEN :min_rpn AND :max_rpn`;
		}

		return `AND results.worst_score ${OPERATORS[type_rpn]} ${min_rpn ? ':min_rpn' : ':max_rpn'}`;
	}

	#formatDate(date) {
		return moment(date).format('YYYY-MM-DD');
	}
}
