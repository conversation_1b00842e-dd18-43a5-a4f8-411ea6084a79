import {
	NIOSH_QUERY_JOIN,
	ACTION_PLAN_LEFT_JOIN,
	ACTION_PLAN_INNER_JOIN,
	REBA_REPORTS_QUERY_JOIN,
	SOME_REPORTS_QUERY_JOIN,
	STRAIN_INDEX_QUERY_JOIN,
	PREL<PERSON><PERSON>ARY_ANALYSIS_QUERY,
	WORK_ORGANIZATION_QUERY_JOIN,
	WORK_ORGANIZATION_QUERY_REBA_JOIN
} from '../util/constants-dashboard.js';
import { logger } from '../helpers/logger.js';
import { BaseRepository } from './v2/base-repository.js';

export class DashboardRepository extends BaseRepository {
	constructor(database) {
		super(database);
	}

	async countNotAnalyzedFiles(payload) {
		logger.info('[Dashboard] repository - countNotAnalyzedFiles init');
		const { filters, work_organization } = payload;
		const query = `
			SELECT
				COUNT(DISTINCT (f.id)) as total_files_not_analyzed
			FROM
				files f
			LEFT JOIN custom_reports_files crf
				ON crf.file_id = f.id
			LEFT JOIN (
				SELECT
					p_a.id, p_a.file_id, p_a.consolidated
				FROM
					preliminary_analyzes p_a
				WHERE p_a.is_active = TRUE
					AND p_a.consolidated = TRUE
			) as consolidated_analyzes
				ON consolidated_analyzes.file_id = f.id
			LEFT JOIN workstations w
				ON f.workstation_id = w.id
			LEFT JOIN (
				${PRELIMINARY_ANALYSIS_QUERY}
			) as results
				ON results.id = consolidated_analyzes.id
			LEFT JOIN preliminary_analyzes pa
				ON pa.file_id = f.id
			${STRAIN_INDEX_QUERY_JOIN}
			${NIOSH_QUERY_JOIN}
			LEFT JOIN reba_reports AS rr
				ON rr.file_id = f.id
			LEFT JOIN action_plans ap
				ON ap.file_id = f.id
			${WORK_ORGANIZATION_QUERY_JOIN}
			WHERE f.company_id = :company_id
				AND crf.id IS NULL
				AND f.organization_id = :organization_id
				AND f.is_active = TRUE
				AND consolidated_analyzes.id IS NULL
				${work_organization ? `AND work_results.score = ${Number(work_organization)}` : ''}
				${filters}
		`;

		const config = {
			nest: true,
			replacements: payload
		};

		const [result] = await this.db.sequelize.query(query, config);

		logger.info('[Dashboard] repository - countNotAnalyzedFiles finish');
		return result;
	}

	async countAnalyzedFiles(payload) {
		logger.info('[Dashboard] repository - countAnalyzedFiles init');
		const { filters, work_organization, action_plan } = payload;
		const query = `
			SELECT
				COUNT(DISTINCT f.id) as total_files_analyzed
			FROM
				files f
			LEFT JOIN custom_reports_files crf
				ON crf.file_id = f.id
			INNER JOIN workstations w
				ON f.workstation_id = w.id
			INNER JOIN preliminary_analyzes pa
				ON pa.file_id = f.id
			${STRAIN_INDEX_QUERY_JOIN}
			${NIOSH_QUERY_JOIN}
			${action_plan ? ACTION_PLAN_INNER_JOIN : ACTION_PLAN_LEFT_JOIN}
			INNER JOIN reba_reports AS rr
				ON rr.file_id = f.id
			LEFT JOIN action_plans ap
				ON ap.file_id = f.id
			${WORK_ORGANIZATION_QUERY_JOIN}
			WHERE f.company_id = :company_id
				AND crf.id IS NULL
				AND f.organization_id = :organization_id
				AND pa.consolidated = TRUE
				AND f.is_active = TRUE
				AND pa.is_active = TRUE
				AND rr.is_active = TRUE
				${work_organization ? `AND work_results.score = ${Number(work_organization)}` : ''}
				${filters}
			`;

		const config = {
			nest: true,
			replacements: payload
		};

		const [result] = await this.db.sequelize.query(query, config);

		logger.info('[Dashboard] repository - countAnalyzedFiles finish');
		return result;
	}

	async findEachScoreAmount(payload) {
		logger.info('[Dashboard] repository - findEachScoreAmount init');
		const { filters, work_organization, action_plan } = payload;
		const query = `
			SELECT
				pa.worst_score as score,
				COUNT(pa.worst_score) as amount
			FROM
				files f
			LEFT JOIN custom_reports_files crf
				ON crf.file_id = f.id
			INNER JOIN workstations w
				ON f.workstation_id = w.id
			INNER JOIN preliminary_analyzes pa
				ON pa.file_id = f.id
			${STRAIN_INDEX_QUERY_JOIN}
			${NIOSH_QUERY_JOIN}
			INNER JOIN reba_reports AS rr
				ON rr.file_id = f.id
			${action_plan ? ACTION_PLAN_INNER_JOIN : ACTION_PLAN_LEFT_JOIN}
			${WORK_ORGANIZATION_QUERY_JOIN}
			WHERE f.company_id = :company_id
				AND crf.id IS NULL
				AND f.organization_id = :organization_id
				AND pa.is_active = TRUE
				AND f.is_active = TRUE
				AND pa.consolidated = TRUE
				AND rr.is_active = TRUE
				${work_organization ? `AND work_results.score = ${Number(work_organization)}` : ''}
				${filters}
			GROUP BY
				pa.worst_score
		`;

		const config = {
			nest: true,
			replacements: payload
		};

		const result = await this.db.sequelize.query(query, config);

		logger.info('[Dashboard] repository - findEachScoreAmount finish');
		return result;
	}

	async findNioshReports(payload) {
		logger.info('[Dashboard] repository - findNioshReports init');
		const { filters, work_organization } = payload;
		const query = `
			SELECT
				DISTINCT(nr.id) as niosh_report,
				f.id as file,
				nr.risk
			FROM
				files f
			LEFT JOIN custom_reports_files crf
				ON crf.file_id = f.id
			INNER JOIN workstations w
				ON f.workstation_id = w.id
			INNER JOIN preliminary_analyzes pa
				ON pa.file_id = f.id
			INNER JOIN (
				${PRELIMINARY_ANALYSIS_QUERY}
			) as results
				ON results.id = pa.id
			INNER JOIN niosh_reports nr
				ON nr.file_id = f.id
			LEFT JOIN strain_index_reports sir
				ON sir.file_id = f.id
			LEFT JOIN reba_reports AS rr
				ON rr.file_id = f.id
			LEFT JOIN action_plans ap
				ON ap.file_id = f.id
			${WORK_ORGANIZATION_QUERY_JOIN}
			WHERE f.company_id = :company_id
				AND crf.id IS NULL
				AND f.organization_id = :organization_id
				AND pa.is_active = TRUE
				AND pa.consolidated = TRUE
				AND f.is_active = TRUE
				AND nr.is_active = TRUE
				${work_organization ? `AND work_results.score = ${Number(work_organization)}` : ''}
				${filters}
		`;

		const config = {
			nest: true,
			replacements: payload
		};

		const result = await this.db.sequelize.query(query, config);

		logger.info('[Dashboard] repository - findNioshReports finish');
		return result;
	}

	async findStrainIndexReports(payload) {
		logger.info('[Dashboard] repository - findStrainIndexReports init');
		const { filters, work_organization } = payload;
		const query = `
			SELECT
				DISTINCT(sir.id) as strain_index_report,
				f.id as file,
				GREATEST(sir.score_left_rsi, sir.score_right_rsi) as worst_score
			FROM
				files f
			LEFT JOIN custom_reports_files crf
				ON crf.file_id = f.id
			INNER JOIN workstations w
				ON f.workstation_id = w.id
			INNER JOIN preliminary_analyzes pa
				ON pa.file_id = f.id
			INNER JOIN (
				${PRELIMINARY_ANALYSIS_QUERY}
			) as results
				ON results.id = pa.id
			INNER JOIN strain_index_reports sir
				ON f.id = sir.file_id
			LEFT JOIN niosh_reports nr
				ON nr.file_id = f.id
			LEFT JOIN reba_reports AS rr
				ON rr.file_id = f.id
			LEFT JOIN action_plans ap
				ON ap.file_id = f.id
			${WORK_ORGANIZATION_QUERY_JOIN}
			WHERE (f.company_id = :company_id
				AND crf.id IS NULL
				AND f.organization_id = :organization_id
				AND pa.is_active = TRUE
				AND pa.consolidated = 1
				AND f.is_active = TRUE
				AND sir.is_active = TRUE
				${work_organization ? `AND work_results.score = ${Number(work_organization)}` : ''}
				${filters}
			)
		`;

		const config = {
			nest: true,
			replacements: payload
		};

		const result = await this.db.sequelize.query(query, config);

		logger.info('[Dashboard] repository - findStrainIndexReports finish');
		return result;
	}

	async findWorkOrganizationReports(payload) {
		logger.info('[Dashboard] repository - findWorkOrganizationReports init');
		const { filters, work_organization } = payload;
		const subQuery = `
			SELECT
				pa.id as pa_id,
				MAX(pas.result) AS score,
				COUNT(DISTINCT(pa.id)) AS amount
			FROM
				preliminary_analysis_steps pas
			INNER JOIN preliminary_analyzes pa
				ON pa.id = pas.preliminary_analysis_id
			INNER JOIN files f
				ON f.id = pa.file_id
			LEFT JOIN custom_reports_files crf
				ON crf.file_id = f.id
			INNER JOIN workstations w
				ON f.workstation_id = w.id
			INNER JOIN preliminary_analysis_step_keys pask
				ON pas.preliminary_analysis_step_key_id = pask.id
			INNER JOIN (
				${PRELIMINARY_ANALYSIS_QUERY}
			) as results
				ON results.id = pa.id
			LEFT JOIN strain_index_reports sir
				ON sir.file_id = f.id
			LEFT JOIN niosh_reports nr
				ON nr.file_id = f.id
			LEFT JOIN reba_reports AS rr
				ON rr.file_id = f.id
			LEFT JOIN action_plans ap
				ON ap.file_id = f.id
			WHERE pask.sequence IN (0, 49, 50, 51, 52, 53, 54)
				AND crf.id IS NULL
				AND pas.is_active = TRUE
				AND pa.is_active = TRUE
				AND pa.consolidated = TRUE
				AND f.is_active = TRUE
				AND pask.is_active = TRUE
				${filters}
			GROUP BY
				pa.id
			ORDER BY
				MAX(pas.result) ASC`;

		let query = `SELECT pa_id, score AS score, amount FROM (${subQuery}) AS temp_table WHERE score = ${work_organization};`;

		if (!work_organization) {
			query = subQuery;
		}

		const config = {
			nest: true,
			replacements: payload
		};

		const result = await this.db.sequelize.query(query, config);

		logger.info('[Dashboard] repository - findWorkOrganizationReports finish');
		return result;
	}

	async findRebaReports(payload) {
		logger.info('[Dashboard] repository - findRebaReports init');
		const { filters, work_organization } = payload;
		const query = `
			SELECT
				CONCAT('{"bodyPart": "', response.bodyPart, '", "scores": [', GROUP_CONCAT(response.scoreJSON), ']}') AS reba_reports
			FROM (
				SELECT
					'trunk' AS bodyPart,
					COALESCE(rr.trunk, 0) AS 'score',
					JSON_OBJECT('scale', COALESCE(rr.trunk, 0), 'amount', COUNT(DISTINCT(rr.file_id))) AS scoreJSON
				FROM
					reba_reports AS rr
				${REBA_REPORTS_QUERY_JOIN}
				${WORK_ORGANIZATION_QUERY_REBA_JOIN}
				INNER JOIN workstations w
					ON f.workstation_id = w.id
				WHERE rr.is_active = TRUE
					${work_organization ? `AND work_results.scores = ${Number(work_organization)}` : ''}
					AND crf.id IS NULL
					AND f.is_active = TRUE
					AND pa.is_active = TRUE
					AND pa.consolidated = TRUE
					AND f.company_id = :company_id
					AND f.organization_id = organization_id
					${filters}
				GROUP BY
					score
				UNION ALL
				SELECT
					'neck' AS bodyPart,
					COALESCE(rr.neck, 0) as 'score',
					JSON_OBJECT('scale', COALESCE(rr.neck, 0), 'amount', COUNT(DISTINCT(rr.file_id))) AS scoreJSON
				FROM
					reba_reports AS rr
				${REBA_REPORTS_QUERY_JOIN}
				${WORK_ORGANIZATION_QUERY_REBA_JOIN}
				INNER JOIN workstations w
					ON f.workstation_id = w.id
				WHERE rr.is_active = TRUE
					${work_organization ? `AND work_results.scores = ${Number(work_organization)}` : ''}
					AND crf.id IS NULL
					AND f.is_active = TRUE
					AND pa.is_active = TRUE
					AND pa.consolidated = TRUE
					AND f.company_id = :company_id
					AND f.organization_id = organization_id
					${filters}
				GROUP BY
					score
				UNION ALL
				SELECT
					'left_lower_arm' AS bodyPart,
					COALESCE(rr.left_lower_arm, 0) as 'score',
					JSON_OBJECT('scale', COALESCE(rr.left_lower_arm, 0), 'amount', COUNT(DISTINCT(rr.file_id))) AS scoreJSON
				FROM
					reba_reports AS rr
				${REBA_REPORTS_QUERY_JOIN}
				${WORK_ORGANIZATION_QUERY_REBA_JOIN}
				INNER JOIN workstations w
					ON f.workstation_id = w.id
				WHERE rr.is_active = TRUE
					${work_organization ? `AND work_results.scores = ${Number(work_organization)}` : ''}
					AND crf.id IS NULL
					AND f.is_active = TRUE
					AND pa.is_active = TRUE
					AND pa.consolidated = TRUE
					AND f.company_id = :company_id
					AND f.organization_id = organization_id
					${filters}
				GROUP BY
					score
				UNION ALL
				SELECT
					'right_lower_arm' AS bodyPart,
					COALESCE(rr.right_lower_arm, 0) as 'score',
					JSON_OBJECT('scale', COALESCE(rr.right_lower_arm, 0), 'amount', COUNT(DISTINCT(rr.file_id))) AS scoreJSON
				FROM
					reba_reports AS rr
				${REBA_REPORTS_QUERY_JOIN}
				${WORK_ORGANIZATION_QUERY_REBA_JOIN}
				INNER JOIN workstations w
					ON f.workstation_id = w.id
				WHERE rr.is_active = TRUE
					${work_organization ? `AND work_results.scores = ${Number(work_organization)}` : ''}
					AND crf.id IS NULL
					AND f.is_active = TRUE
					AND pa.is_active = TRUE
					AND pa.consolidated = TRUE
					AND f.company_id = :company_id
					AND f.organization_id = organization_id
					${filters}
				GROUP BY
					score
				UNION ALL
				SELECT
					'left_upper_arm' AS bodyPart,
					COALESCE(rr.left_upper_arm, 0) as 'score',
					JSON_OBJECT('scale', COALESCE(rr.left_upper_arm, 0), 'amount', COUNT(DISTINCT(rr.file_id))) AS scoreJSON
				FROM
					reba_reports AS rr
				${REBA_REPORTS_QUERY_JOIN}
				${WORK_ORGANIZATION_QUERY_REBA_JOIN}
				INNER JOIN workstations w
					ON f.workstation_id = w.id
				WHERE rr.is_active = TRUE
					${work_organization ? `AND work_results.scores = ${Number(work_organization)}` : ''}
					AND crf.id IS NULL
					AND f.is_active = TRUE
					AND pa.is_active = TRUE
					AND pa.consolidated = TRUE
					AND f.company_id = :company_id
					AND f.organization_id = organization_id
					${filters}
				GROUP BY
					score
				UNION ALL
				SELECT
					'right_upper_arm' AS bodyPart,
					COALESCE(rr.right_upper_arm, 0) as 'score',
					JSON_OBJECT('scale', COALESCE(rr.right_upper_arm, 0), 'amount', COUNT(DISTINCT(rr.file_id))) AS scoreJSON
				FROM
					reba_reports AS rr
				${REBA_REPORTS_QUERY_JOIN}
				${WORK_ORGANIZATION_QUERY_REBA_JOIN}
				INNER JOIN workstations w
					ON f.workstation_id = w.id
				WHERE rr.is_active = TRUE
					${work_organization ? `AND work_results.scores = ${Number(work_organization)}` : ''}
					AND crf.id IS NULL
					AND f.is_active = TRUE
					AND pa.is_active = TRUE
					AND pa.consolidated = TRUE
					AND f.company_id = :company_id
					AND f.organization_id = organization_id
					${filters}
				GROUP BY
					score
				UNION ALL
				SELECT
					'right_knee' AS bodyPart,
					COALESCE(rr.right_knee, 0) as 'score',
					JSON_OBJECT('scale', COALESCE(rr.right_knee, 0), 'amount', COUNT(DISTINCT(rr.file_id))) AS scoreJSON
				FROM
					reba_reports AS rr
				${REBA_REPORTS_QUERY_JOIN}
				${WORK_ORGANIZATION_QUERY_REBA_JOIN}
				INNER JOIN workstations w
					ON f.workstation_id = w.id
				WHERE rr.is_active = TRUE
					${work_organization ? `AND work_results.scores = ${Number(work_organization)}` : ''}
					AND crf.id IS NULL
					AND f.is_active = TRUE
					AND pa.is_active = TRUE
					AND pa.consolidated = TRUE
					AND f.company_id = :company_id
					AND f.organization_id = organization_id
					${filters}
				GROUP BY
					score
				UNION ALL
				SELECT
					'left_knee' AS bodyPart,
					COALESCE(rr.left_knee, 0) as 'score',
					JSON_OBJECT('scale', COALESCE(rr.left_knee, 0), 'amount', COUNT(DISTINCT(rr.file_id))) AS scoreJSON
				FROM
					reba_reports AS rr
				${REBA_REPORTS_QUERY_JOIN}
				${WORK_ORGANIZATION_QUERY_REBA_JOIN}
				INNER JOIN workstations w
					ON f.workstation_id = w.id
				WHERE rr.is_active = TRUE
					${work_organization ? `AND work_results.scores = ${Number(work_organization)}` : ''}
					AND crf.id IS NULL
					AND f.is_active = TRUE
					AND pa.is_active = TRUE
					AND pa.consolidated = TRUE
					AND f.company_id = :company_id
					AND f.organization_id = organization_id
					${filters}
				GROUP BY
					score
				) as response group by response.bodyPart
		`;

		const config = {
			nest: true,
			replacements: payload
		};

		const results = await this.db.sequelize.query(query, config);

		logger.info('[Dashboard] repository - findRebaReports finish');
		return results.map((result) => JSON.parse(result.reba_reports));
	}

	async findEachWorkstationScoreAndAnalyzes(payload, offset = 0, limit = 10) {
		logger.info('[Dashboard] repository - findEachWorkstationScoreAndAnalyzes init');
		const { filters, work_organization } = payload;
		const query = `
			SELECT
				w.name AS workstation,
				w.id AS workstation_id,
				pa.worst_score AS result,
				pa.score_sum AS score,
				COUNT(DISTINCT(pa.id)) AS amount
			FROM
				files f
			LEFT JOIN custom_reports_files crf
				ON crf.file_id = f.id
			INNER JOIN workstations w
				ON f.workstation_id = w.id
			INNER JOIN preliminary_analyzes pa
				ON pa.file_id = f.id
			LEFT JOIN action_plans ap
				ON ap.file_id = f.id
			${SOME_REPORTS_QUERY_JOIN}
			${WORK_ORGANIZATION_QUERY_JOIN}
			WHERE f.company_id = :company_id
				AND crf.id IS NULL
				AND f.organization_id = :organization_id
				AND pa.consolidated = TRUE
				AND pa.is_active = TRUE
				AND f.is_active = TRUE
				${work_organization ? `AND work_results.score = ${Number(work_organization)}` : ''}
				${filters}
			GROUP BY
				f.workstation,
				pa.id
			ORDER BY
				score DESC
			LIMIT
				${limit}
			OFFSET
				${offset}
		`;

		const config = {
			nest: true,
			replacements: payload
		};

		const result = await this.db.sequelize.query(query, config);

		logger.info('[Dashboard] repository - findEachWorkstationScoreAndAnalyzes finish');
		return result;
	}

	async countTotalActionPlans(payload) {
		logger.info('[Dashboard] repository - countTotalActionPlans init');
		const { filters } = payload;
		const query = `
			SELECT
				COUNT(DISTINCT ap.id) AS total_action_plans
			FROM
				files AS f
					LEFT JOIN 
				custom_reports_files crf ON crf.file_id = f.id
					INNER JOIN
				workstations w ON f.workstation_id = w.id
					INNER JOIN
				preliminary_analyzes AS pa ON f.id = pa.file_id
					INNER JOIN
				preliminary_analysis_steps AS pea_step ON pa.id = pea_step.preliminary_analysis_id
					INNER JOIN
				action_plans AS ap ON pea_step.id = ap.step_id
					LEFT JOIN
				action_plans_tasks apt ON apt.action_plan_id = ap.id
					${SOME_REPORTS_QUERY_JOIN}
			WHERE(f.organization_id = :organization_id
					AND crf.id IS NULL
					AND f.company_id = :company_id
					AND f.is_active = TRUE
					AND ap.is_active = TRUE
					AND pa.consolidated = TRUE
					AND pa.is_active = TRUE
					${filters})
			ORDER BY ap.deadline DESC
		`;

		const config = {
			nest: true,
			replacements: payload
		};

		const [result] = await this.db.sequelize.query(query, config);

		logger.info('[Dashboard] repository - countTotalActionPlans finish');
		return result;
	}

	async findActionPlanStatuses(payload, offset = 0, limit = 10) {
		logger.info('[Dashboard] repository - findActionPlanStatuses init');
		const { filters } = payload;
		const query = `
			SELECT
				ap.board,
				COUNT(DISTINCT ap.id) AS amount
			FROM
				files AS f
					LEFT JOIN 
				custom_reports_files crf ON crf.file_id = f.id
					INNER JOIN
				workstations w ON f.workstation_id = w.id
					INNER JOIN
				preliminary_analyzes AS pa ON f.id = pa.file_id
					INNER JOIN
				preliminary_analysis_steps AS pea_step ON pa.id = pea_step.preliminary_analysis_id
					INNER JOIN
				action_plans AS ap ON pea_step.id = ap.step_id
					LEFT JOIN
				action_plans_tasks apt ON apt.action_plan_id = ap.id
					LEFT JOIN
				strain_index_reports sir ON sir.file_id = f.id
					LEFT JOIN
				niosh_reports nr ON nr.file_id = f.id
					LEFT JOIN
				reba_reports AS rr ON rr.file_id = f.id
			WHERE
				(f.organization_id = :organization_id
					AND crf.id IS NULL
					AND f.company_id = :company_id
					AND f.is_active = TRUE
					AND ap.is_active = TRUE
					AND pa.consolidated = TRUE
					AND pa.is_active = TRUE
					${filters}
				)
			GROUP BY ap.board;
		`;

		const config = {
			nest: true,
			replacements: payload
		};

		const result = await this.db.sequelize.query(query, config);

		logger.info('[Dashboard] repository - findActionPlanStatuses finish');
		return result;
	}

	async findActionPlanCards(payload, offset = 0, limit = 10) {
		logger.info('[Dashboard] repository - findActionPlanCards init');
		const { filters } = payload;
		const query = `
			SELECT
				ap.id,
				ap.title AS card,
				ap.deadline,
				apt.is_completed AS completed,
				pea_step.result AS result
			FROM
				files AS f
					LEFT JOIN 
				custom_reports_files crf ON crf.file_id = f.id
					INNER JOIN
				workstations w ON f.workstation_id = w.id
					INNER JOIN
				preliminary_analyzes AS pa ON f.id = pa.file_id
					INNER JOIN
				preliminary_analysis_steps AS pea_step ON pa.id = pea_step.preliminary_analysis_id
					INNER JOIN
				action_plans AS ap ON pea_step.id = ap.step_id
					LEFT JOIN
				action_plans_tasks apt ON apt.action_plan_id = ap.id
					${SOME_REPORTS_QUERY_JOIN}
			WHERE(f.organization_id = :organization_id
					AND crf.id IS NULL
					AND f.company_id = :company_id
					AND f.is_active = TRUE
					AND pa.is_active = TRUE
					AND ap.is_active = TRUE
					AND pa.consolidated = TRUE
					AND pa.is_active = TRUE
					${filters})
			GROUP BY ap.id
			ORDER BY ap.deadline DESC
			LIMIT
				${limit}
			OFFSET
				${offset}
		`;

		const config = {
			nest: true,
			replacements: payload
		};

		const result = await this.db.sequelize.query(query, config);

		logger.info('[Dashboard] repository - findActionPlanCards finish');
		return result;
	}
}
