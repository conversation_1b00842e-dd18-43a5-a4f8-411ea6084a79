import BaseRepository from './base/index.js';
import _ from 'lodash';

class FileRepository extends BaseRepository {
	constructor(database) {
		super(database);
	}

	async getTotalUplodsAndMinutesCustomer(usersId = null, expirationPlan = null) {
		const query = `
			SELECT
				COUNT(*) as total_uploads,
				SUM(file.duration) as total_seconds
			FROM
				files as file
					INNER JOIN users as usr ON file.user_id = usr.id
			WHERE file.user_id IN (:usersId)
				AND
					file.created_at <= :expirationPlan
				AND
					file.created_at >= DATE_SUB(:expirationPlan, INTERVAL 30 DAY);
		`;
		const config = {
			nest: true,
			replacements: {
				usersId: usersId,
				expirationPlan: expirationPlan
			}
		};
		const [result] = await this.sequelize.query(query, config);
		return result;
	}

	async getUsersByCustomer(id) {
		const query = `
			SELECT
				users.id,
				customer_informations.expiration_plan
			FROM
				customer_informations
					INNER JOIN users ON customer_informations.id = users.customer_information_id
			WHERE
				customer_informations.id IN (
					SELECT
						users.customer_information_id
					FROM
						users
					WHERE (users.id =:userId)) AND users.is_active = 1;
		`;
		const config = { nest: true, replacements: { userId: id } };
		const result = await this.sequelize.query(query, config);
		return result;
	}

	async getTotalUplodsAndMinutes(id) {
		const query = `
		SELECT COUNT(*) AS total_uploads, SUM(file.duration) as total_seconds FROM files as file WHERE (
			file.user_id IN (
				SELECT
					usr.id
						FROM users as usr WHERE(
							usr.customer_information_id = (
								SELECT
									usr.customer_information_id
								FROM users as usr
								WHERE(usr.id =:userId)
							)
						)
				)
		);
	`;

		const config = { nest: true, replacements: { userId: id } };

		const [result] = await this.sequelize.query(query, config);

		return result;
	}

	async getLimitUploadUser(id) {
		const query = `
		SELECT plan.max_upload, plan.max_minutes FROM plans as plan WHERE(
			plan.id = (
				SELECT cs_plans.plan_id FROM customer_plans AS cs_plans WHERE(
					cs_plans.customer_id = (SELECT usr.customer_information_id FROM users as usr WHERE(usr.id =:userId))
				)
			)
		);
	`;

		const config = {
			nest: true,
			replacements: {
				userId: id
			}
		};

		const [result] = await this.sequelize.query(query, config);
		return result;
	}

	async findOneFile(parameters) {
		return await this.db.File.findOne(parameters);
	}

	async riskByTime(parameters) {
		const { organization_id, company_id } = parameters;

		const dateNow = new Date();
		const month = dateNow.getUTCMonth() + 1;
		const year = dateNow.getUTCFullYear();

		const query = `
      SELECT
        f.sector_id,
        f.rula_score,
        f.created_at
      FROM files as f
        WHERE (
          organization_id =:organization_id
            AND
          company_id =:company_id
            AND
          status='PROCESSED'
            AND
          is_active=1
            AND
          rula_score IS NOT NULL AND rula_score !=0
            AND
          MONTH(created_at) =:month AND YEAR(created_at) =:year
        )
    `;

		const config = {
			nest: true,
			replacements: {
				organization_id: organization_id,
				company_id: company_id,
				month,
				year
			}
		};

		const result = await this.sequelize.query(query, config);

		return result;
	}

	async videoBySector(parameters) {
		const { organization_id, company_id } = parameters;

		const query = `
      SELECT
        f.id, s.name, f.rula_score
      FROM files as f
	      INNER JOIN
		      sectors as s ON f.sector_id = s.id
	      WHERE (
          f.organization_id =:organization_id
            AND
          f.company_id =:company_id
            AND
          f.status='PROCESSED'
            AND
          f.is_active=1
            AND
          rula_score IS NOT NULL AND rula_score !=0
        )
    `;

		const config = {
			nest: true,
			replacements: {
				organization_id: organization_id,
				company_id: company_id
			}
		};

		const result = await this.sequelize.query(query, config);

		return result;
	}

	async percentRisk(parameters) {
		/* Refactor please ! */

		const { organization_id, company_id } = parameters;

		const query = `
      SELECT
        f.rula_score,
        f.sector_id
      FROM files as f
        WHERE (
          organization_id =:organization_id
            AND
          company_id =:company_id
            AND
          status='PROCESSED'
            AND
          is_active=1
            AND
          rula_score IS NOT NULL AND rula_score !=0
        )
    `;

		const config = {
			nest: true,
			replacements: {
				organization_id: organization_id,
				company_id: company_id
			}
		};

		const result = await this.sequelize.query(query, config);

		return result;
	}

	async durationVideoTotal(parameters) {
		const { organization_id, company_id } = parameters;
		const query = `
      SELECT
        SUM(duration) AS total
      FROM
        files
      WHERE (
        organization_id =:organization_id
          AND
        company_id =:company_id
          AND
        duration > 0 AND status='PROCESSED'
      );
    `;
		const config = {
			nest: true,
			replacements: { organization_id, company_id }
		};
		const [result] = await this.sequelize.query(query, config);
		return result;
	}

	async findAllFilter({ sector, ...params }, orderBy) {
		try {
			const body = {
				where: params,
				include: [
					{
						association: 'sector',
						attributes: ['name']
					}
				]
			};

			if (sector) {
				_.set(body.include[0], 'where', { id: sector });
			}

			let defaultOrderBy = [['createdAt', 'DESC']];

			const checkOrderBy = !!orderBy > 0 ? orderBy : defaultOrderBy;

			_.set(body, 'order', checkOrderBy);

			const result = await this.db.File.findAll(body);

			return result;
		} catch (err) {
			throw err;
		}
	}

	async counterVideoProcessed(parameters) {
		const { organization_id, company_id } = parameters;

		const config = {
			where: {
				company_id,
				organization_id,
				isActive: true,
				status: 'PROCESSED'
			}
		};

		const result = await this.db.File.count(config);

		return result;
	}
}

export default FileRepository;
