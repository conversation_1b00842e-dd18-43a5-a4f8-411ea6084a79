import BaseRepository from './base/index.js';

class SectorRepository extends BaseRepository {
	constructor(database) {
		super(database);
	}

	async counterTotalCriticalSectors(parameters) {
		const { organization_id, company_id } = parameters;

		const query = `
      SELECT
        COUNT(DISTINCT s.id) AS result
      FROM
        files AS f
          INNER JOIN
          sectors AS s ON f.sector_id = s.id
      WHERE (
        f.organization_id =:organization_id
          AND
        f.company_id =:company_id
          AND
        f.status='PROCESSED'
          AND
        f.is_active=1 AND s.is_active=1
          AND
        rula_score IS NOT NULL AND rula_score > 6
      );
    `;

		const config = {
			nest: true,
			replacements: {
				organization_id: organization_id,
				company_id: company_id
			}
		};

		const [result] = await this.sequelize.query(query, config);

		return result;
	}

	async totalByCompany(parameters) {
		const { organization_id, company_id } = parameters;
		const query = `
      SELECT
        COUNT(sct.id) AS result
      FROM
        companies AS cmp
          INNER JOIN
            organizations AS org
              ON cmp.organization_id = org.id
                INNER JOIN sectors AS sct
                  ON cmp.id = sct.company_id
      WHERE (
        org.id =:organization_id AND cmp.id =:company_id
          AND
        org.is_active=1 AND cmp.is_active=1 and sct.is_active=1
      );
    `;

		const config = {
			nest: true,
			replacements: {
				organization_id: organization_id,
				company_id: company_id
			}
		};

		const [result] = await this.sequelize.query(query, config);

		return result;
	}

	async findSectorCritical(parameters) {
		const { organization_id, company_id } = parameters;
		const queryString = `
      SELECT
        s.name as sector,
        AVG(f.rula_score) AS score
      FROM files as f
        INNER JOIN
          sectors as s ON f.sector_id = s.id
      WHERE (
        f.organization_id = :organization_id
          AND
        f.company_id = :company_id
          AND
        f.status='PROCESSED'
          AND
        f.is_active=1
      )
      GROUP BY sector
      ORDER BY score DESC LIMIT 1
    `;
		const config = {
			nest: true,
			replacements: { organization_id, company_id }
		};
		const [result] = await this.sequelize.query(queryString, config);
		return result;
	}

	async usageCheck(sectorId, companyId) {
		const body = {
			include: [
				{
					association: 'sector',
					where: {
						id: sectorId,
						isActive: true
					},
					include: [
						{
							association: 'company',
							where: {
								id: companyId,
								isActive: true
							}
						}
					]
				}
			]
		};

		const response = await this.db.RecoveryReport.findAndCountAll(body);

		if (!response) {
			throw Error('Internal error');
		}

		return response;
	}
}

export default SectorRepository;
