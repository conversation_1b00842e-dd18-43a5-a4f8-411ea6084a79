import { logger } from '../helpers/logger.js';
import { BaseRepository } from './v2/base-repository.js';
import { FileFiltersMapper } from '../entities/index.js';
import { GetReportsAndToolsSQL } from '../mappers/file/GetReportsAndToolsSQL.js';

export class FileRepository extends BaseRepository {
	#filter_mapper;
	constructor(database) {
		super(database);
		this.#filter_mapper = new FileFiltersMapper();
		this.file = this.db.File;
		this.file_risk_result = this.db.FileRiskResult;
	}

	async findByPk(id, params) {
		logger.info('[File] repository - findByPk init');
		const file = await this.file.findByPk(id, params);
		logger.info('[File] repository - findByPk finish');
		return file;
	}

	async findOne(id, params) {
		logger.info('[File] repository - findOne init');
		const file = await this.file.findOne(id, params);
		logger.info('[File] repository - findOne finish');
		return file;
	}

	async findAll(params) {
		logger.info('[File] repository - findAll init');
		const file = await this.file.findAll(params);
		logger.info('[File] repository - findAll finish');
		return file;
	}

	async findAllByForeignKey(params) {
		logger.info('[File] repository - findAllByForeignKey init');
		const file = await this.file.findAll({
			...params
		});
		logger.info('[File] repository - findAllByForeignKey finish');
		return file;
	}

	async update(params, options) {
		logger.info('[File] repository - update init');
		const file = await this.file.update(params, options);
		logger.info('[File] repository - update finish');
		return file;
	}

	async findAllWithHierarchy(parameters) {
		logger.info('[File] repository - findAllWithHierarchy init');
		const filter_options = this.#filter_mapper.setFilter(parameters);
		const files = await this.file.findAll(filter_options);
		logger.info('[File] repository - findAllWithHierarchy finish');
		return files;
	}

	async findAndCountAll(params) {
		logger.info('[File] repository - findAndCountAll init');
		const files = await this.file.findAndCountAll({
			...params
		});
		logger.info('[File] repository - findAndCountAll finish');
		return files;
	}

	async minutesProcessed(parameters) {
		const { organization_id, company_id, users, start, end } = parameters;

		const query = `
			SELECT
				SUM(file.duration) as total
			FROM
				files AS file
			WHERE
				file.user_id IN (:users)
					AND
				file.created_at between :start AND :end
					AND
				file.organization_id = :organization_id
					AND
				file.company_id = :company_id
					AND
				file.status = 'PROCESSED'
					AND
				file.is_active = 1
		`;

		const config = {
			nest: true,
			replacements: { organization_id, company_id, users, start, end }
		};

		const [result] = await this.db.sequelize.query(query, config);
		return result;
	}

	async totalMinutesProcessed(params) {
		const { user_list_id, date, organization_id, company_id } = params;
		const query = `
			SELECT
				SUM(file.duration) as total_seconds
			FROM
				files as file
					INNER JOIN users as usr ON file.user_id = usr.id
			WHERE file.user_id IN (:users_id)
				AND
					file.created_at <= :expiration_plan
				AND
					file.created_at >= DATE_SUB(:expiration_plan, INTERVAL 30 DAY)
				AND
					file.organization_id = :organization_id
				AND
					file.company_id = :company_id
				AND
					file.status = 'PROCESSED'
		`;

		const config = {
			nest: true,
			replacements: {
				organization_id: organization_id,
				company_id: company_id,
				users_id: user_list_id,
				expiration_plan: date
			}
		};
		const [result] = await this.db.sequelize.query(query, config);
		return result;
	}

	async totalUploadPerCustomer(usersId, expirationPlan) {
		const query = `
			SELECT
				COUNT(*) as total_uploads,
				SUM(file.duration) as total_seconds
			FROM
				files as file
					INNER JOIN users as usr ON file.user_id = usr.id
			WHERE file.user_id IN (:usersId)
				AND
					file.created_at <= :expirationPlan
				AND
					file.created_at >= DATE_SUB(:expirationPlan, INTERVAL 30 DAY);
		`;
		const config = {
			nest: true,
			replacements: {
				usersId: usersId,
				expirationPlan: expirationPlan
			}
		};
		const [result] = await this.db.sequelize.query(query, config);
		return result;
	}

	async setRiskResults(risk_percentages, options) {
		logger.info('[File] repository - setRiskResults init');
		try {
			const data = await this.file_risk_result.bulkCreate(risk_percentages, options);
			logger.info('[File] repository - setRiskResults success');
			return [data, null];
		} catch (error) {
			logger.error('[File] repository - setRiskResults error', { error });
			return [null, error];
		} finally {
			logger.info('[File] repository - setRiskResults finish');
		}
	}

	async getReportsAndTools(params) {
		logger.info('[File] repository - getReportsAndTools init', { params });
		const { file_id, user_id } = params;
		try {
			const mapper = new GetReportsAndToolsSQL({ file_id, user_id });
			const { query, replacements } = mapper.getQuery();
			const [result] = await this.db.sequelize.query(query, {
				nest: true,
				replacements,
				type: this.db.sequelize.QueryTypes.SELECT
			});
			logger.info('[File] repository - getReportsAndTools success');
			return [result, null];
		} catch (error) {
			logger.error('[File] repository - getReportsAndTools error', { error });
			return [null, error];
		} finally {
			logger.info('[File] repository - getReportsAndTools finish');
		}
	}
}
