import BaseRepository from './base/index.js';

class ReportRepository extends BaseRepository {
	constructor(database) {
		super(database);
	}

	async countReportExtractedByCompany(parameters) {
		const { organization_id, company_id } = parameters;
		const query = `
      SELECT
        COUNT(id) AS result
      FROM
        files AS f
      WHERE (
        f.organization_id =:organization_id
          AND
        f.company_id =:company_id
          AND
        f.status='PROCESSED'
          AND
        sector_id IS NOT NULL
          AND
        workstation IS NOT NULL
          AND
        f.is_active=1
      )
    `;
		const config = {
			nest: true,
			replacements: {
				organization_id,
				company_id
			}
		};
		const [result] = await this.sequelize.query(query, config);
		return result;
	}

	async checkExistsUpload(parameters) {
		const { organization_id, company_id, upload_id } = parameters;

		const config = {
			where: {
				id: upload_id,
				company_id,
				organization_id,
				isActive: true
			}
		};

		const file = await await this.db.File.findOne(config);

		return file;

		// Deprecado!
		// const { organization_id, company_id, upload_id } = parameters;
		// try {
		//   const file = await this.db.File.findOne({
		//     where: { id: upload_id, company_id, organization_id, isActive: true }
		//   });
		//   return file;
		// } catch (error) {
		//   throw error;
		// }
	}
}

export default ReportRepository;
