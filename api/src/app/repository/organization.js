import _ from 'lodash';
import Sequelize from 'sequelize';

import { logger } from '../helpers/logger.js';
import { BaseRepository } from './v2/base-repository.js';

const { Op } = Sequelize;

export class OrganizationRepository extends BaseRepository {
	constructor(database) {
		super(database);
		this.organization = this.db.Organization;
	}

	async create(params, transaction) {
		logger.info('[Organization] repository - create init');
		const organization = await this.organization.create(params, transaction);
		logger.info('[Organization] repository - create finish');
		return organization;
	}

	async update(params, transaction) {
		logger.info('[Organization] repository - update init');
		const organization = await this.organization.update(params, transaction);
		logger.info('[Organization] repository - update finish');
		return organization;
	}

	async findByPk(id, params) {
		logger.info('[Organization] repository - findByPk init');
		const organization = await this.organization.findByPk(id, params);
		logger.info('[Organization] repository - findByPk finish');
		return organization;
	}

	async findAndCountAll(params) {
		logger.info('[Organization] repository - findAndCountAll init');
		const { user_id, limit, offset, name, is_active, created_start_date, created_end_date } = params;
		const find_all_options = {
			where: {
				is_active: is_active ?? true
			},
			include: [
				{
					association: 'UserAccess',
					required: true,
					attributes: [],
					where: {
						user_id,
						is_active: true
					}
				},
				{
					association: 'company',
					required: false,
					where: {
						is_active: true
					},
					include: [
						{
							association: 'UserAccess',
							required: false,
							attributes: ['id'],
							where: {
								user_id,
								is_active: true
							}
						}
					]
				}
			],
			limit,
			distinct: true,
			offset: limit * offset
		};

		if (is_active === 'all') {
			_.set(find_all_options, 'where', {
				[Op.or]: [
					{
						is_active: true
					},
					{
						is_active: false
					}
				]
			});
		}

		if (name) {
			_.set(find_all_options, 'where', {
				...find_all_options.where,
				name: {
					[Op.like]: `%${name}%`
				}
			});
		}

		if (created_start_date && created_end_date) {
			_.set(find_all_options, 'where', {
				...find_all_options.where,
				created_at: {
					[Op.between]: [created_start_date, created_end_date]
				}
			});
		}

		const organizations = await this.organization.findAndCountAll({
			...find_all_options
		});
		logger.info('[Organization] repository - findAndCountAll finish');
		return organizations;
	}

	async findAll(params) {
		logger.info('[Organization] repository - findAll init');
		const organizations = await this.organization.findAll({
			...params
		});
		logger.info('[Organization] repository - findAll finish');
		return organizations;
	}

	async findOne(params) {
		logger.info('[Organization] repository - findOne init');
		const organization = await this.organization.findOne({
			...params
		});
		logger.info('[Organization] repository - findOne finish');
		return organization;
	}

	async index({ user_id }) {
		const query = `
			SELECT DISTINCT
				org.id,
				org.name,
				org.url_logo
			FROM
				users AS u
			INNER JOIN
				user_accesses AS acl ON u.id = acl.user_id
			INNER JOIN
				organizations AS org ON acl.organization_id = org.id
			WHERE
				(u.id = :id
				AND u.is_active = 1
				AND acl.is_active = 1
				AND org.is_active = 1)
			ORDER BY org.name ASC
	  `;

		const config = {
			nest: true,
			replacements: {
				id: user_id
			}
		};

		const result = await this.db.sequelize.query(query, config);
		return result;
	}

	async companiesByOrganization({ organization_id, user_id }) {
		const query = `
			SELECT
				DISTINCT comp.*
		  	FROM
			  companies as comp
			INNER JOIN 	user_accesses AS acl
			INNER JOIN users AS usr
				ON usr.id = acl.user_id
			INNER JOIN organizations AS org
			WHERE (
				comp.organization_id = :organization_id
				AND comp.is_active = 1
				AND acl.user_id =:user_id
				AND org.is_active = 1
				AND acl.is_active = 1
			);
		`;

		const config = {
			nest: true,
			replacements: { user_id, organization_id }
		};

		const result = await this.db.sequelize.query(query, config);
		return result;
	}
}
