import { BaseRepository } from './v2/base-repository.js';
import { logger } from '../helpers/logger.js';

export class ActionPlanRepository extends BaseRepository {
	constructor(database) {
		super(database);
		this.action_plan = this.db.ActionPlan;
	}

	report_type_to_filter_mapper = {
		EWA: 'step_id',
		SERA: 'sera_summary_review_id'
	};

	async findByPk(params) {
		logger.info('[ActionPlan] repository - findByPk init');
		const action_plan = await this.action_plan.findByPk({
			...params
		});
		logger.info('[ActionPlan] repository - findByPk finish');
		return action_plan;
	}

	async findOne(params) {
		logger.info('[ActionPlan] repository - findOne init');
		const action_plan = await this.action_plan.findOne({
			...params
		});
		logger.info('[ActionPlan] repository - findOne finish');
		return action_plan;
	}

	async findAll(params) {
		logger.info('[ActionPlan] repository - findAll init');
		const action_plan = await this.action_plan.findAll({
			...params
		});
		logger.info('[ActionPlan] repository - findAll finish');
		return action_plan;
	}

	async create(params, transaction) {
		logger.info('[ActionPlan] repository - create init');
		const action_plan = await this.action_plan.create(params, transaction);
		logger.info('[ActionPlan] repository - create finish');
		return action_plan;
	}

	async bulkCreate(params, options) {
		logger.info('[ActionPlan] repository - bulkCreate init');
		const action_plan = await this.action_plan.bulkCreate(params, options);
		logger.info('[ActionPlan] repository - bulkCreate finish');
		return action_plan;
	}

	whereActionPlansFilter({
		id,
		title,
		responsibleUserId,
		board,
		deadlineStatus,
		deadlineDate,
		score,
		companyId,
		sectorId,
		lineId,
		workstationId,
		fileId,
		isActive,
		lexoRank,
		report_type,
		offset,
		limit
	}) {
		const whereClause = {
			id: '',
			title: '',
			responsibleUserId: '',
			deadlineStatus: '',
			deadlineDate,
			score,
			board: '',
			lexoRank: '',
			isActive: '',
			companyId: '',
			sectorId: '',
			lineId: '',
			workstationId: '',
			fileId: '',
			report_type: '',
			offset,
			limit,
			order: ''
		};

		if (report_type) {
			whereClause.report_type = `&& ap.${this.report_type_to_filter_mapper[report_type]} is NOT NULL`;
		}

		if (!score && !deadlineDate) {
			whereClause.order = 'ORDER BY BINARY ap.lexo_rank';
		}

		if (score && deadlineDate) {
			whereClause.order = `ORDER BY preliminary_steps.score ${score}, ap.deadline ${deadlineDate}`;
		}

		if (score && !deadlineDate) {
			whereClause.order = `ORDER BY preliminary_steps.score ${score}`;
		}

		if (deadlineDate && !score) {
			whereClause.order = `ORDER BY ap.deadline ${deadlineDate}`;
		}

		if (id) {
			whereClause.id = `&& ap.id = :id`;
		}

		if (title) {
			whereClause.title = `&& ap.title LIKE '%${title}%'`;
		}

		if (responsibleUserId) {
			whereClause.responsibleUserId = '&& ap.responsible_user_id = :responsibleUserId';
		}

		if (deadlineStatus) {
			const deadlineWhere = {
				ON_TIME: `&& ap.deadline >= CURDATE()`,
				OVERDUE: `&& ap.deadline < CURDATE() && ap.board <> 'DONE'`,
				THIS_WEEK: `&& ap.deadline BETWEEN CURDATE() AND date_add(CURDATE(), interval 1 week) && ap.board <> 'DONE'`
			};
			whereClause.deadlineStatus = deadlineWhere[deadlineStatus];
		}

		if (board) {
			whereClause.board = '&& ap.board = :board';
		}

		if (lexoRank) {
			whereClause.lexoRank = '&& ap.lexo_rank LIKE :lexoRank';
		}

		if (isActive) {
			whereClause.isActive = `&& ap.is_active = ${isActive ? 1 : 0}`;
		}

		if (companyId) {
			whereClause.companyId = '&& company.id = :companyId';
		}

		if (companyId && sectorId) {
			whereClause.sectorId = '&& sector.id = :sectorId && sector.is_active = TRUE';
		}

		if (companyId && sectorId && lineId) {
			whereClause.lineId = '&& line.id = :lineId && line.sector_id = :sectorId';
		}

		if (companyId && sectorId && lineId && workstationId) {
			whereClause.workstationId = '&& workstation.id = :workstationId && workstation.line_id = :lineId';
		}

		if (fileId) {
			whereClause.fileId = '&& file.id = :fileId';
		}

		return { ...whereClause };
	}

	async actionPlansFilter(attributes) {
		logger.info('[ActionPlan] repository - actionPlansFilter init');
		const {
			id,
			title,
			responsibleUserId,
			deadlineStatus,
			board,
			companyId,
			sectorId,
			lineId,
			workstationId,
			fileId,
			isActive,
			lexoRank,
			report_type,
			offset,
			limit,
			order
		} = this.whereActionPlansFilter(attributes);

		const query = `
			SELECT
				DISTINCT ap.id,
				ap.title,
				ap.description,
				ap.deadline,
				ap.responsible_user_id,
				ap.board,
				ap.is_active,
				ap.created_at,
				ap.updated_at,
				ap.lexo_rank,
				ap.file_id,
				file.sector_id,
				file.original_name,
				file.company_id,
				file.organization_id,
				file.workstation_id,
				preliminary.analyst_name,
				preliminary.activity_name,
				workstation.name as workstation_name,
				line.name as line_name,
				sector.name as sector_name,
				preliminary_steps.score
			FROM
				action_plans AS ap
					INNER JOIN
						files AS file
							ON file.id = ap.file_id
					INNER JOIN
						workstations AS workstation
							ON workstation.id = file.workstation_id
					INNER JOIN
						lines AS line
							ON line.id = workstation.line_id
					INNER JOIN
						sectors AS sector
							ON sector.id = line.sector_id
					INNER JOIN
						companies AS company
							ON sector.company_id = company.id
					INNER JOIN
						organizations AS organization
							ON company.organization_id = organization.id
					LEFT JOIN
						preliminary_analyzes AS preliminary
							ON preliminary.file_id = ap.file_id
					LEFT JOIN
						preliminary_analysis_steps AS preliminary_steps
							ON preliminary_steps.id = ap.step_id
			WHERE (
				organization.id = :organizationId
				AND file.is_active = TRUE
				${id}
				${deadlineStatus}
				${companyId}
				${sectorId}
				${lineId}
				${workstationId}
				${fileId}
				${isActive}
				${title}
				${responsibleUserId}
				${board}
				${lexoRank}
				${report_type}
			)
			${order}
			LIMIT
				${limit}
			OFFSET
				${offset}
		`;

		const result = await this.db.sequelize.query(query, {
			replacements: attributes,
			model: this.db.ActionPlan,
			mapToModel: true
		});

		logger.info('[ActionPlan] repository - actionPlansFilter finish');
		return result;
	}

	async countActionPlans(attributes) {
		logger.info('[ActionPlan] repository - countActionPlans init');
		const {
			id,
			title,
			responsibleUserId,
			deadlineStatus,
			board,
			companyId,
			sectorId,
			lineId,
			workstationId,
			fileId,
			isActive,
			lexoRank,
			report_type
		} = this.whereActionPlansFilter(attributes);

		const query = `
			SELECT
				COUNT(DISTINCT ap.id) AS total_action_plans
			FROM
				action_plans AS ap
				INNER JOIN
				files AS file
					ON file.id = ap.file_id
					INNER JOIN
						workstations AS workstation
							ON workstation.id = file.workstation_id
					INNER JOIN
						lines AS line
							ON line.id = workstation.line_id
					INNER JOIN
						sectors AS sector
							ON sector.id = line.sector_id
					INNER JOIN
						companies AS company
							ON sector.company_id = company.id
					INNER JOIN
						organizations AS organization
							ON company.organization_id = organization.id
					LEFT JOIN
						preliminary_analyzes AS preliminary
							ON preliminary.file_id = ap.file_id
			WHERE (
				organization.id = :organizationId
				AND file.is_active = TRUE
				${id}
				${deadlineStatus}
				${companyId}
				${sectorId}
				${lineId}
				${workstationId}
				${fileId}
				${isActive}
				${title}
				${responsibleUserId}
				${board}
				${lexoRank}
				${report_type}
			)
		`;

		const [result] = await this.db.sequelize.query(query, {
			replacements: attributes,
			model: this.db.ActionPlan,
			mapToModel: true,
			nest: true
		});

		logger.info('[ActionPlan] repository - countActionPlans finish');
		return result;
	}

	async getLastLexoRank({ organizationId }) {
		logger.info('[ActionPlan] repository - getLastLexoRank init');

		const query = `
			SELECT DISTINCT
				ap.lexo_rank
			FROM
				action_plans AS ap
					INNER JOIN
						files AS file
							ON file.id = ap.file_id
					INNER JOIN
						sectors AS sector
							ON file.sector_id = sector.id
					INNER JOIN
						companies AS company
							ON sector.company_id = company.id
					INNER JOIN
						organizations AS organization
							ON company.organization_id = organization.id
			WHERE (organization.id = :organizationId AND ap.lexo_rank IS NOT NULL)
			ORDER BY BINARY ap.lexo_rank DESC
			LIMIT 1
		`;

		const [result] = await this.db.sequelize.query(query, {
			replacements: { organizationId },
			type: this.db.sequelize.QueryTypes.SELECT
		});

		logger.info('[ActionPlan] repository - getLastLexoRank finish');
		return result?.lexo_rank ?? null;
	}

	#getDelayedFilter(delayed) {
		if (delayed) {
			return `
				AND
					ap.deadline < :current_date AND NOT ap.board = "DONE"
						OR
					ap.board = "DONE" AND ap.deadline <= ap.completed_at
			`;
		}

		return '';
	}

	async countAllFromReports(params) {
		logger.info('[ActionPlan] repository - countAllFromReports init', { params });
		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date,
			delayed,
			user_id
		} = params;
		try {
			const query = `
				SELECT COUNT(DISTINCT ap.id) AS total
				FROM action_plans ap
				INNER JOIN files ON ap.file_id = files.id
				INNER JOIN workstations ON workstations.id = files.workstation_id
				INNER JOIN \`lines\` AS l ON l.id = workstations.line_id
				INNER JOIN sectors ON sectors.id = l.sector_id
				WHERE files.organization_id = :organization_id
					AND ap.is_active = 1
					AND ap.custom_report_step_key_id IS NOT NULL
					AND files.is_active = 1
					AND workstations.deleted_at IS NULL
					AND l.deleted_at IS NULL
					AND sectors.is_active = 1
					${company_id ? 'AND files.company_id = :company_id' : 'AND files.company_id IN(:companies_with_user_access)'}
					${sector_id ? 'AND sectors.id = :sector_id' : ''}
					${line_id ? 'AND l.id = :line_id' : ''}
					${workstation_id ? 'AND workstations.id = :workstation_id' : ''}
					${start_date ? 'AND DATE(ap.created_at) >= :start_date' : ''}
					${end_date ? 'AND DATE(ap.created_at) <= :end_date' : ''}
					${delayed ? `AND ap.deadline < :current_date AND NOT ap.board = "DONE"` : ''}
					${user_id ? `AND ap.user_id = :user_id` : ''}
			`;

			const config = {
				nest: true,
				replacements: {
					organization_id,
					company_id,
					companies_with_user_access,
					sector_id,
					line_id,
					workstation_id,
					start_date,
					end_date,
					user_id,
					current_date: new Date()
				}
			};

			const [{ total }] = await this.db.sequelize.query(query, config);

			logger.info('[ActionPlan] repository - countAllFromReports success');
			return [total, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - countAllFromReports error');
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - countAllFromReports finish');
		}
	}

	async countAllFromReportsByStatus(params) {
		logger.info('[ActionPlan] repository - countAllFromReportsByStatus init', { params });
		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date,
			delayed,
			user_id
		} = params;
		try {
			const delayed_filter = this.#getDelayedFilter(delayed);

			const query = `
				SELECT
					COUNT(DISTINCT CASE WHEN ap.board = 'TO DO' THEN ap.id END) AS to_do,
					COUNT(DISTINCT CASE WHEN ap.board  = 'DOING' THEN ap.id END) AS doing,
					COUNT(DISTINCT CASE WHEN ap.board  = 'DONE' THEN ap.id END) AS done
				FROM action_plans ap
					INNER JOIN files ON ap.file_id = files.id AND files.is_active = 1
					INNER JOIN workstations ON workstations.id = files.workstation_id  AND workstations.deleted_at IS NULL
					INNER JOIN \`lines\` AS l ON l.id = workstations.line_id AND l.deleted_at IS NULL
					INNER JOIN sectors ON sectors.id = l.sector_id AND sectors.is_active = 1
				WHERE files.organization_id = :organization_id
					AND ap.is_active = 1
					AND ap.custom_report_step_key_id IS NOT NULL
					${company_id ? 'AND files.company_id = :company_id' : 'AND files.company_id IN(:companies_with_user_access)'}
					${sector_id ? 'AND sectors.id = :sector_id' : ''}
					${line_id ? 'AND l.id = :line_id' : ''}
					${workstation_id ? 'AND workstations.id = :workstation_id' : ''}
					${start_date ? 'AND DATE(ap.created_at) >= :start_date' : ''}
					${end_date ? 'AND DATE(ap.created_at) <= :end_date' : ''}
					${delayed_filter}
					${user_id ? `AND ap.user_id = :user_id` : ''}
			`;

			const config = {
				nest: true,
				plain: true,
				replacements: {
					organization_id,
					company_id,
					companies_with_user_access,
					sector_id,
					line_id,
					workstation_id,
					start_date,
					end_date,
					user_id,
					current_date: new Date()
				}
			};

			const data = await this.db.sequelize.query(query, config);

			logger.info('[ActionPlan] repository - countAllFromReportsByStatus success');
			return [data, null];
		} catch (error) {
			logger.error('[ActionPlan] repository - countAllFromReportsByStatus error');
			return [null, error];
		} finally {
			logger.info('[ActionPlan] repository - countAllFromReportsByStatus finish');
		}
	}
}
