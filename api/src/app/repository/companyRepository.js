import { BaseRepository } from './userRepository.js';

export default class CompanyRepository extends BaseRepository {
	constructor(database) {
		super(database);
	}

	async checkSectorExists(parameters) {
		const { organization_id, company_id, sector_id } = parameters;

		const query = `
      SELECT sctr.id FROM
        companies AS cmp
          INNER JOIN organizations AS org
            ON cmp.organization_id = org.id
          INNER JOIN sectors AS sctr
            ON sctr.company_id = cmp.id
      WHERE(
        org.id =:organization_id
          &&
        org.is_active = 1
          &&
        cmp.id =:company_id
          &&
        cmp.is_active = 1
          &&
        sctr.id =:sector_id
          &&
        sctr.is_active = 1
      );
    `;

		const config = {
			nest: true,
			replacements: {
				organization_id,
				company_id,
				sector_id
			}
		};

		const [result] = await this.db.sequelize.query(query, config);

		return result;
	}

	async statusRisk(parameters) {
		const { organization_id, company_id } = parameters;
		const query = `
      SELECT
        AVG(rula_score) AS risk
      FROM
        files AS f
      WHERE (
        f.organization_id =:organization_id
          AND
        f.company_id =:company_id
          AND
        f.status='PROCESSED'
          AND
        sector_id IS NOT NULL
          AND
        workstation IS NOT NULL
          AND
        f.is_active=1
      )
    `;

		const config = {
			nest: true,
			replacements: {
				organization_id,
				company_id
			}
		};

		const [result] = await this.db.sequelize.query(query, config);

		return result;
	}

	async userList({ organization_id, user_id }) {
		const query = `
      SELECT
        cmp.*
      FROM
        user_accesses AS acl
          INNER JOIN users AS usr
            ON usr.id = acl.user_id
              INNER JOIN organizations AS org
                ON acl.organization_id = org.id
                  INNER JOIN companies AS cmp
                    ON acl.company_id = cmp.id
      WHERE (
        acl.user_id =:user_id
          AND
        org.id =:organization_id
          AND
        org.is_active = 1
          AND
        acl.is_active = 1
          AND
        acl.company_id IS NOT NULL
      )
		ORDER BY cmp.name ASC;
    `;

		const config = {
			nest: true,
			replacements: { user_id, organization_id }
		};

		const result = await this.db.sequelize.query(query, config);

		return result;
	}
}
