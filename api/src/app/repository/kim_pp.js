import { logger } from '../helpers/logger.js';
import { BaseRepository } from './v2/base-repository.js';

export class KimPushPullReportRepository extends BaseRepository {
	constructor(database) {
		super(database);
	}

	async countAllByRisk(params) {
		logger.info('[KimPushPull] repository - countAllByRisk init', { params });
		const {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_with_user_access,
			start_date,
			end_date,
			user_id
		} = params;
		try {
			const query = `
				SELECT
					COUNT(CASE WHEN kim_pp.score < 20 THEN kim_pp.id END) AS low,
					COUNT(CASE WHEN kim_pp.score >= 20 AND kim_pp.score < 50 THEN kim_pp.id END) AS moderate,
					COUNT(CASE WHEN kim_pp.score >= 50 AND kim_pp.score < 100 THEN kim_pp.id END) AS high,
					COUNT(CASE WHEN kim_pp.score >= 100 THEN kim_pp.id END) AS very_high
				FROM kim_push_pull_reports as kim_pp
					INNER JOIN files ON kim_pp.file_id = files.id AND files.is_active = 1
					INNER JOIN workstations ON workstations.id = files.workstation_id
					INNER JOIN lines AS l ON l.id = workstations.line_id
					INNER JOIN sectors ON sectors.id = l.sector_id
				WHERE files.organization_id = :organization_id
					AND kim_pp.is_active = 1
					AND workstations.deleted_at IS NULL
					AND l.deleted_at IS NULL
					AND sectors.is_active = 1
					AND files.is_active = 1
					${company_id ? 'AND files.company_id = :company_id' : 'AND files.company_id IN(:companies_with_user_access)'}
					${sector_id ? 'AND sectors.id = :sector_id' : ''}
					${line_id ? 'AND l.id = :line_id' : ''}
					${workstation_id ? 'AND workstations.id = :workstation_id' : ''}
					${start_date ? 'AND DATE(kim_pp.created_at) >= DATE(:start_date)' : ''}
					${end_date ? 'AND DATE(kim_pp.created_at) <= DATE(:end_date)' : ''}
					${user_id ? 'AND kim_pp.report_user_id = :user_id' : ''}
			`;

			const config = {
				nest: true,
				replacements: {
					organization_id,
					company_id,
					sector_id,
					line_id,
					workstation_id,
					companies_with_user_access,
					start_date,
					end_date,
					user_id
				}
			};

			const [data] = await this.db.sequelize.query(query, config);

			logger.info('[KimPushPull] repository - countAllByRisk finish');
			return [data, null];
		} catch (error) {
			logger.error('[KimPushPull] repository - countAllByRisk error');
			return [null, error];
		} finally {
			logger.info('[KimPushPull] repository - countAllByRisk finish');
		}
	}
}
