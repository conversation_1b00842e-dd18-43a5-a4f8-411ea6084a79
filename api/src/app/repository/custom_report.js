import { logger } from '../helpers/logger.js';
import { BaseRepository } from './v2/base-repository.js';
import { CUSTOM_REPORT_NAMES } from '../util/constants-custom-report.js';
import { GetAllCustomReportTypesSQL } from '../mappers/custom_reports/GetAllCustomReportTypesSQL.js';
import { GetAllBeraReportCountSQL } from '../mappers/bera_reports/GetAllBeraReportCountSQL.js';
import { GetAllSeraReportCountSQL } from '../mappers/sera_reports/GetAllSeraReportCountSQL.js';

export class CustomReportRepository extends BaseRepository {
	constructor(database) {
		super(database);
		this.custom_report = this.db.CustomReport;
	}

	async findByPk(id, params) {
		logger.info('[CustomReport] repository - findByPk init');
		const custom_report = await this.custom_report.findByPk(id, params);
		logger.info('[CustomReport] repository - findByPk finish');
		return custom_report;
	}

	async findOne(params) {
		logger.info('[CustomReport] repository - findOne init');
		const custom_reports = await this.custom_report.findOne({
			...params
		});
		logger.info('[CustomReport] repository - findOne finish');
		return custom_reports;
	}

	async findAll(params) {
		logger.info('[CustomReport] repository - findAll init');
		const custom_report_files = await this.custom_report.findAll({
			...params
		});
		logger.info('[CustomReport] repository - findAll finish');
		return custom_report_files;
	}

	async findAllByForeignKey(params) {
		logger.info('[CustomReport] repository - findAllByForeignKey init');
		const custom_report_files = await this.custom_report.findAll({
			...params
		});
		logger.info('[CustomReport] repository - findAllByForeignKey finish');
		return custom_report_files;
	}

	async getHierarchyWorstScore(params) {
		logger.info('[CustomReport] repository - getHierarchyWorstScore init', { params });
		const { limit, offset, custom_report_name, created_at_start, created_at_end } = params;
		try {
			const task_table_name = this.getReportTaskTableName(custom_report_name);
			const entity = this.#getWorstScoreEntity({
				filters: params,
				task_table_name
			});

			const score_field = this.getWorstScoreField({
				task_table_name,
				custom_report_name
			});
			const report_joins = this.getReportJoins({
				custom_report_name,
				task_table_name,
				filters: {
					created_at_start,
					created_at_end
				}
			});

			const rows_query = this.getWorstScoreQuery({
				custom_report_name,
				filters: params,
				task_table_name,
				score_field,
				report_joins
			});
			const rows_query_count = this.getWorstScoreQueryCount({
				custom_report_name,
				filters: params,
				task_table_name,
				score_field,
				report_joins
			});

			const [rows, count] = await Promise.all([
				this.db.sequelize.query(rows_query, {
					replacements: {
						...params,
						offset: limit * (offset - 1),
						limit
					},
					type: this.db.sequelize.QueryTypes.SELECT
				}),
				this.db.sequelize.query(rows_query_count, {
					plain: true,
					replacements: params,
					type: this.db.sequelize.QueryTypes.SELECT
				})
			]);

			logger.info('[CustomReport] repository - getHierarchyWorstScore finish');
			const result = {
				rows,
				limit,
				entity,
				offset,
				total: count?.total
			};
			return [result, null];
		} catch (error) {
			logger.error('[CustomReport] repository - getHierarchyWorstScore error');
			return [null, error];
		} finally {
			logger.info('[CustomReport] repository - getHierarchyWorstScore finish');
		}
	}

	getWorstScoreQuery(params) {
		const { custom_report_name, filters, task_table_name, score_field, report_joins } = params;
		const { workstation_id, line_id, sector_id, company_id } = filters;

		if (workstation_id && task_table_name) {
			const query = `
				SELECT
					${score_field}
					tasks.id as id,
					tasks.name as name
				FROM
					tasks
					INNER JOIN tasks_files on tasks_files.task_id = tasks.id
					INNER JOIN files on files.id = tasks_files.file_id and files.is_active = 1
					LEFT JOIN ${task_table_name} on ${task_table_name}.task_id = tasks.id and ${task_table_name}.deleted_at IS NULL
				WHERE
					files.workstation_id = :workstation_id
					AND tasks.deleted_at IS NULL
				GROUP BY
					id,
					name
				LIMIT :limit
				OFFSET :offset
			`;

			return query;
		}

		if (workstation_id) {
			const query = `
				SELECT
					${score_field}
					files.id as id,
					files.original_name as name
				FROM
					files
					${report_joins}
				WHERE
					files.workstation_id = :workstation_id
					AND files.is_active = 1
				GROUP BY
					id,
					name
				LIMIT :limit
				OFFSET :offset
			`;

			return query;
		}

		if (line_id) {
			const query = `
				SELECT
					${score_field}
					workstations.id as id,
					workstations.name as name
				FROM
					workstations
					LEFT JOIN files on files.workstation_id = workstations.id and files.is_active = 1
					${report_joins}
				WHERE
					workstations.line_id = :line_id
					AND workstations.deleted_at IS NULL
				GROUP BY
					id,
					name
				LIMIT :limit
				OFFSET :offset
			`;

			return query;
		}

		if (sector_id) {
			const query = `
				SELECT
					${score_field}
					lines.id as id,
					lines.name as name
				FROM
					lines
					INNER JOIN workstations on workstations.line_id = lines.id and workstations.deleted_at IS NULL
					LEFT JOIN files on files.workstation_id = workstations.id and files.is_active = 1
					${report_joins}
				WHERE
					lines.sector_id = :sector_id
					AND lines.deleted_at IS NULL
				GROUP BY
					id,
					name
				LIMIT :limit
				OFFSET :offset
			`;

			return query;
		}

		if (company_id) {
			const query = `
				SELECT
					${score_field}
					sectors.id as id,
					sectors.name as name
				FROM
					sectors
					LEFT JOIN files on files.sector_id = sectors.id and files.is_active = 1
					${report_joins}
				WHERE
					sectors.company_id = :company_id
					AND sectors.is_active = 1
				GROUP BY
					id,
					name
				LIMIT :limit
				OFFSET :offset
			`;

			return query;
		}

		const query = `
				SELECT
					${score_field}
					companies.id as id,
					companies.name as name
				FROM
					companies
					LEFT JOIN files on files.company_id = companies.id and files.is_active = 1
					${report_joins}
				WHERE
					companies.organization_id = :organization_id
					AND companies.is_active = 1
					AND companies.id IN(:companies_ids)
				GROUP BY
					id,
					name
				LIMIT :limit
				OFFSET :offset
		`;

		return query;
	}

	getWorstScoreQueryCount(params) {
		const { filters, task_table_name, report_joins } = params;
		const { workstation_id, line_id, sector_id, company_id } = filters;

		if (workstation_id && task_table_name) {
			const query = `
				SELECT
					COUNT(DISTINCT tasks.id) as total
				FROM
					tasks
					INNER JOIN tasks_files on tasks_files.task_id = tasks.id
					INNER JOIN files on files.id = tasks_files.file_id and files.is_active = 1
					LEFT JOIN ${task_table_name} on ${task_table_name}.task_id = tasks.id and ${task_table_name}.deleted_at IS NULL
				WHERE
					files.workstation_id = :workstation_id
					AND tasks.deleted_at IS NULL
			`;

			return query;
		}

		if (workstation_id) {
			const query = `
				SELECT
					COUNT(DISTINCT files.id) as total
				FROM
					files
					${report_joins}
				WHERE
					files.workstation_id = :workstation_id
					AND files.is_active = 1
			`;

			return query;
		}

		if (line_id) {
			const query = `
				SELECT
					COUNT(DISTINCT workstations.id) as total
				FROM
					workstations
					LEFT JOIN files on files.workstation_id = workstations.id and files.is_active = 1
					${report_joins}
				WHERE
					workstations.line_id = :line_id
					AND workstations.deleted_at IS NULL
			`;

			return query;
		}

		if (sector_id) {
			const query = `
				SELECT
					COUNT(DISTINCT lines.id) as total
				FROM
					lines
					INNER JOIN workstations on workstations.line_id = lines.id and workstations.deleted_at IS NULL
					LEFT JOIN files on files.workstation_id = workstations.id and files.is_active = 1
					${report_joins}
				WHERE
					lines.sector_id = :sector_id
					AND lines.deleted_at IS NULL
			`;

			return query;
		}

		if (company_id) {
			const query = `
				SELECT
					COUNT(DISTINCT sectors.id) as total
				FROM
					sectors
					LEFT JOIN files on files.sector_id = sectors.id and files.is_active = 1
					${report_joins}
				WHERE
					sectors.company_id = :company_id
					AND sectors.is_active = 1
			`;

			return query;
		}

		const query = `
				SELECT
					COUNT(DISTINCT companies.id) as total
				FROM
					companies
					LEFT JOIN files on files.company_id = companies.id and files.is_active = 1
					${report_joins}
				WHERE
					companies.organization_id = :organization_id
					AND companies.is_active = 1
		`;

		return query;
	}

	getReportJoins({ task_table_name, filters, custom_report_name }) {
		let report_joins = `
			LEFT JOIN custom_report_results on custom_report_results.file_id = files.id
			AND custom_report_results.consolidated = 1
			AND custom_report_results.deleted_at IS NULL
			AND custom_report_results.custom_report_id = :custom_report_id
			${
				filters.created_at_start && filters.created_at_end
					? `AND DATE(custom_report_results.created_at) BETWEEN DATE(:created_at_start) AND DATE(:created_at_end)`
					: ''
			}
		`;

		if (this.#isD86(custom_report_name)) {
			report_joins += `
				LEFT JOIN (
					SELECT crr.*
					FROM custom_report_reviews crr
					INNER JOIN (
						SELECT original_custom_report_result_id, MAX(version) AS max_version
						FROM custom_report_reviews crr
						INNER JOIN custom_report_results crr_result ON crr.custom_report_result_id = crr_result.id
						WHERE crr_result.consolidated = 1
						GROUP BY original_custom_report_result_id
					) latest_reviews ON crr.original_custom_report_result_id = latest_reviews.original_custom_report_result_id
					AND crr.version = latest_reviews.max_version
				) custom_report_reviews ON custom_report_reviews.custom_report_result_id = custom_report_results.id
			`;
		}

		if (task_table_name) {
			const has_consolidated_result = this.#isBera(custom_report_name) ? 'AND bera_reports.consolidated = 1' : '';

			report_joins = `
				LEFT JOIN tasks_files on tasks_files.file_id = files.id
				LEFT JOIN tasks on tasks_files.task_id = tasks.id
				LEFT JOIN ${task_table_name} on ${task_table_name}.task_id = tasks.id and ${task_table_name}.deleted_at IS NULL ${has_consolidated_result}
				${
					filters.created_at_start && filters.created_at_end
						? `AND DATE(${task_table_name}.created_at) BETWEEN DATE(:created_at_start) AND DATE(:created_at_end)`
						: ''
				}
			`;
		}

		return report_joins;
	}

	getReportTaskTableName(custom_report_name) {
		const is_bera = this.#isBera(custom_report_name);
		const is_sera = this.#isSera(custom_report_name);

		if (!is_bera && !is_sera) {
			return null;
		}

		return is_bera ? 'bera_reports' : 'sera_reports';
	}

	getWorstScoreField({ task_table_name, custom_report_name }) {
		let score_field = 'MAX(custom_report_results.result) AS score,';

		if (task_table_name) {
			score_field = this.getReportTaskExistField({ task_table_name, custom_report_name });
		}

		return score_field;
	}

	getReportTaskExistField({ task_table_name, custom_report_name }) {
		const has_consolidated_result = this.#isBera(custom_report_name) ? 'AND bera_reports.consolidated = 1' : '';
		const field = `
			MAX(CASE
				WHEN EXISTS(SELECT 1 FROM ${task_table_name} WHERE ${task_table_name}.task_id = tasks.id ${has_consolidated_result})
				THEN 0
				ELSE null
			END) as score,
		`;

		return field;
	}

	#getWorstScoreEntity(params) {
		const { filters, task_table_name } = params;
		const { workstation_id, line_id, sector_id, company_id } = filters;

		if (workstation_id && task_table_name) {
			return 'task';
		}

		if (workstation_id) {
			return 'file';
		}

		if (line_id) {
			return 'workstation';
		}

		if (sector_id) {
			return 'line';
		}

		if (company_id) {
			return 'sector';
		}

		return 'company';
	}

	#isD86(name) {
		const is_d86 = name === CUSTOM_REPORT_NAMES.JDS_D86;
		return is_d86;
	}

	#isSera(name) {
		const is_sera = name === CUSTOM_REPORT_NAMES.SERA;
		return is_sera;
	}

	#isBera(name) {
		const is_bera = name === CUSTOM_REPORT_NAMES.BERA;
		return is_bera;
	}

	async findAllTypes(params) {
		logger.info('[CustomReport] repository - findAllTypes init', { params });
		try {
			const promises = [];
			const mappers = [GetAllCustomReportTypesSQL, GetAllBeraReportCountSQL, GetAllSeraReportCountSQL];

			mappers.forEach((mapper) => {
				const mapper_instance = new mapper(params);
				const { query, replacements } = mapper_instance.getQuery();
				promises.push(
					this.db.sequelize.query(query, { replacements, type: this.db.sequelize.QueryTypes.SELECT })
				);
			});

			const [data, [bera_reports_count], [sera_reports_count]] = await Promise.all(promises);

			data.forEach((report) => {
				if (report.name === CUSTOM_REPORT_NAMES.SERA) {
					report.total_reports = sera_reports_count?.total || 0;
				} else if (report.name === CUSTOM_REPORT_NAMES.BERA) {
					report.total_reports = bera_reports_count?.total || 0;
				}
			});

			logger.info('[CustomReport] repository - findAllTypes finish');
			return [data, null];
		} catch (error) {
			logger.error('[CustomReport] repository - findAllTypes error', { error });
			return [null, error];
		} finally {
			logger.info('[CustomReport] repository - findAllTypes finish');
		}
	}
}
