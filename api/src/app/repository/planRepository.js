import { logger } from '../helpers/logger.js';
import { BaseRepository } from './userRepository.js';

export default class PlanRepository extends BaseRepository {
	constructor(database) {
		super(database);
	}

	async getPlan(parameters) {
		return await this.db.Plan.findOne(parameters);
	}

	async videosProcessed(customer_id) {
		const query = `
			SELECT
				CAST(COUNT(*) AS UNSIGNED) as processed_videos,
				CAST(COALESCE(SUM(file.duration), 0) AS UNSIGNED) as minutes_processed
			FROM
				files AS file
					INNER JOIN users AS user
						ON file.user_id = user.id
					INNER JOIN customer_informations AS customer
						ON user.customer_information_id = customer.id
			WHERE(
				file.status = 'PROCESSED'
					&&
				customer.id = :customer_id
					&&
				file.created_at <= customer.expiration_plan
					&&
				file.created_at >= DATE_SUB(customer.expiration_plan, INTERVAL 30 DAY)
			)`;

		const config = {
			nest: true,
			replacements: {
				customer_id: customer_id
			}
		};

		const [result] = await this.db.sequelize.query(query, config);
		return result;
	}

	// Deprecado
	async getCustomerByPlan(userId) {
		const query = `
			SELECT
				customer.*
			FROM users AS user
				INNER JOIN customer_informations AS customer ON
					user.customer_information_id = customer.id
						WHERE (
							user.id = :user_id
								&&
							user.is_active = 1
								&&
							customer.is_active = 1
						)
		`;
		const config = {
			nest: true,
			replacements: {
				user_id: userId
			}
		};
		const [result] = await this.db.sequelize.query(query, config);
		return result;
	}

	async getUserPlanExists(id) {
		logger.info('[PlanRepository] - getUserPlanExists - Init');
		logger.info(`[PlanRepository] - getUserPlanExists - Parameters: ${JSON.stringify(id)}`);

		const query = `
			SELECT
				customer.id AS customer_id,
				customer.token_transaction
			FROM users AS user
				INNER JOIN customer_informations AS customer ON
					user.customer_information_id = customer.id
			WHERE (
				user.id = :user_id
					&&
				user.is_active = 1
					&&
				customer.is_active = 1
			)
		`;

		const config = {
			nest: true,
			replacements: {
				user_id: id
			}
		};

		logger.info('[PlanRepository] - Querying Database');
		const [result] = await this.db.sequelize.query(query, config);

		logger.info('[PlanRepository] - getUserPlanExists - Finish');
		return result;
	}

	async getPlanByCustomer(id) {
		const query = `
			SELECT
				customer_info.*,
				plan.plan_external_id,
				plan.name AS plan_name,
				plan.amount AS plan_amount
			FROM
				customer_informations as customer_info
					INNER JOIN customer_plans as customer_plan ON
						customer_info.id = customer_plan.customer_id
							INNER JOIN plans as plan ON
								customer_plan.plan_id = plan.id
			WHERE(
				customer_info.id = :customer_info_id
					&&
				customer_info.is_active = 1
					&&
				plan.is_active = 1
			)
		`;

		const config = {
			nest: true,
			replacements: {
				customer_info_id: id
			}
		};

		// logger.info('[PlanRepository] - Querying Database');
		const [result] = await this.db.sequelize.query(query, config);

		// logger.info('[PlanRepository] - getUserPlanExists - Finish');
		return result;
	}

	async currentPlanCustomer(customerId) {
		const query = `
			SELECT
				plan.*
			FROM
				customer_informations AS cs_info
					INNER JOIN
				customer_plans AS cs_plan ON cs_info.id = cs_plan.customer_id
					INNER JOIN
				plans AS plan ON cs_plan.plan_id = plan.id
			WHERE
				(cs_info.id = :customer_info_id);
		`;

		const config = {
			nest: true,
			replacements: {
				customer_info_id: customerId
			}
		};

		const [result] = await this.db.sequelize.query(query, config);

		return result;
	}
}
