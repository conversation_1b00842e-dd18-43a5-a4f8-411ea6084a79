import { BaseRepository } from './userRepository.js';

export default class OrganizationRepository extends BaseRepository {
	constructor(database) {
		super(database);
	}

	async userListOrganization({ id }) {
		const query = `
      SELECT
        DISTINCT org.id,
        org.name,
        org.url_logo
      FROM
        users AS u
        INNER JOIN user_accesses AS acl
          ON u.id = acl.user_id
        INNER JOIN organizations AS org
          ON acl.organization_id = org.id
      WHERE (
        u.id =:id
          AND
        u.is_active = 1
          AND
        acl.is_active = 1
          AND
        org.is_active = 1
      )
    `;

		const config = {
			nest: true,
			replacements: { id }
		};

		const result = await this.db.sequelize.query(query, config);

		return result;
	}

	async checkPermissionByOrganization({ user_id, organization_id }) {
		const query = `
      SELECT
        acl.*
      FROM
        user_accesses AS acl
          INNER JOIN users AS usr
            ON usr.id = acl.user_id
              INNER JOIN organizations AS org
                ON acl.organization_id = org.id
      WHERE (
	      org.id =:organization_id
          AND
        acl.user_id =:user_id
          AND
        org.is_active = 1
          AND
        acl.is_active = 1
      );
    `;

		const config = {
			nest: true,
			replacements: { user_id, organization_id }
		};

		const [result] = await this.db.sequelize.query(query, config);

		return result;
	}
}
