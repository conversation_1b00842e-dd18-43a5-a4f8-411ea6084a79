import { BaseRepository } from './v2/base-repository.js';
import { logger } from '../helpers/logger.js';

export class SeraReportRepository extends BaseRepository {
	constructor(database) {
		super(database);
		this.sera_report = this.db.SeraReport;
	}

	async create(params, transaction) {
		logger.info('[SeraReport] repository - create init');
		const sera_report = await this.sera_report.create(params, transaction);
		logger.info('[SeraReport] repository - create finish');
		return sera_report;
	}

	async bulkCreate(params, options) {
		logger.info('[SeraReport] repository - bulkCreate init');
		const sera_reports = await this.sera_report.bulkCreate(params, options);
		logger.info('[SeraReport] repository - bulkCreate finish');
		return sera_reports;
	}

	async update(params, options) {
		logger.info('[SeraReport] repository - update init');
		const sera_report = await this.sera_report.update(params, options);
		logger.info('[SeraReport] repository - update finish');
		return sera_report;
	}

	async delete(params, transaction) {
		logger.info('[SeraReport] repository - delete init');
		const deleted_sera_report = await this.sera_report.destroy(params, transaction);
		logger.info('[SeraReport] repository - delete finish');
		return deleted_sera_report;
	}

	async findByPk(id, params) {
		logger.info('[SeraReport] repository - findByPk init');
		const sera_report = await this.sera_report.findByPk(id, params);
		logger.info('[SeraReport] repository - findByPk finish');
		return sera_report;
	}

	async findAllByForeignKey(params) {
		logger.info('[SeraReport] repository - findAllByForeignKey init');
		const sera_reports = await this.sera_report.findAll({
			...params
		});
		logger.info('[SeraReport] repository - findAllByForeignKey finish');
		return sera_reports;
	}

	async findAll(params) {
		logger.info('[SeraReport] repository - findAll init');
		const sera_reports = await this.sera_report.findAll({
			...params
		});
		logger.info('[SeraReport] repository - findAll finish');
		return sera_reports;
	}

	async sumRPN(params) {
		logger.info('[SeraReport] repository - sumRPN init', { params });
		const {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_ids,
			start_date,
			end_date,
			user_id
		} = params;

		try {
			const last_reviews_query = this.getQueryLastReviews({
				organization_id,
				company_id,
				sector_id,
				line_id,
				workstation_id,
				companies_ids,
				start_date,
				end_date,
				user_id
			});

			const query = `
				SELECT
					COALESCE(SUM(sera.sum_rpn), 0) as sum_rpn
				FROM (${last_reviews_query}) AS max_review
					JOIN sera_summary_reviews sera ON sera.sera_summary_id = max_review.sera_summary_id AND sera.review = max_review.last_review;
			`;

			const data = await this.db.sequelize.query(query, {
				plain: true,
				replacements: {
					organization_id,
					company_id,
					sector_id,
					line_id,
					workstation_id,
					companies_ids,
					start_date,
					end_date,
					user_id
				},
				type: this.db.sequelize.QueryTypes.SELECT
			});

			logger.info('[SeraReport] repository - sumRPN success');
			return [data, null];
		} catch (error) {
			logger.error('[SeraReport] repository - sumRPN error');
			return [null, error];
		} finally {
			logger.info('[SeraReport] repository - sumRPN finish');
		}
	}

	async hierarchySumRPN(params) {
		logger.info('[SeraReport] repository - hierarchySumRPN init', { params });
		const {
			limit,
			offset,
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_ids,
			start_date,
			end_date,
			user_id
		} = params;
		try {
			const { fields, join } = this.getHierarchyFields({
				company_id,
				sector_id,
				line_id,
				workstation_id
			});
			const last_reviews_query = this.getQueryLastReviews({
				organization_id,
				company_id,
				sector_id,
				line_id,
				workstation_id,
				companies_ids,
				start_date,
				end_date,
				full_hierarchy: true,
				user_id
			});

			const query = `
				SELECT
					${fields},
					COALESCE(SUM(sera.sum_rpn), 0) as total_rpn
				FROM (${last_reviews_query}) AS max_review
					JOIN sera_summary_reviews sera ON sera.sera_summary_id = max_review.sera_summary_id AND sera.review = max_review.last_review
					${join}
				GROUP BY ${fields}
				LIMIT :limit
				OFFSET :offset
			`;

			const data = await this.db.sequelize.query(query, {
				replacements: {
					organization_id,
					company_id,
					sector_id,
					line_id,
					workstation_id,
					companies_ids,
					start_date,
					end_date,
					limit,
					offset: (offset - 1) * limit,
					user_id
				},
				type: this.db.sequelize.QueryTypes.SELECT
			});

			logger.info('[SeraReport] repository - hierarchySumRPN success');
			return [data, null];
		} catch (error) {
			logger.error('[SeraReport] repository - hierarchySumRPN error');
			return [null, error];
		} finally {
			logger.info('[SeraReport] repository - hierarchySumRPN finish');
		}
	}

	async incidenceCategories(params) {
		logger.info('[SeraReport] repository - incidenceCategories init', { params });
		const {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_ids,
			start_date,
			end_date,
			user_id
		} = params;
		try {
			const last_reviews_query = this.getQueryLastReviews({
				organization_id,
				company_id,
				sector_id,
				line_id,
				workstation_id,
				companies_ids,
				start_date,
				end_date,
				user_id
			});

			const query = `
				SELECT
					risk.description,
					sera_report.risk_category_id,
					COUNT(sera_report.risk_category_id) as count
				FROM (${last_reviews_query}) AS max_review
					JOIN sera_summary_reviews sera ON sera.sera_summary_id = max_review.sera_summary_id AND sera.review = max_review.last_review
					JOIN sera_reports sera_report on sera_report.sera_summary_review_id = sera.id
					JOIN risk_categories risk on risk.id = sera_report.risk_category_id
				GROUP BY sera_report.risk_category_id, risk.description
			`;

			const data = await this.db.sequelize.query(query, {
				replacements: {
					organization_id,
					company_id,
					sector_id,
					line_id,
					workstation_id,
					companies_ids,
					start_date,
					end_date,
					user_id
				},
				type: this.db.sequelize.QueryTypes.SELECT
			});

			logger.info('[SeraReport] repository - incidenceCategories success');
			return [data, null];
		} catch (error) {
			logger.error('[SeraReport] repository - incidenceCategories error');
			return [null, error];
		} finally {
			logger.info('[SeraReport] repository - incidenceCategories finish');
		}
	}

	getHierarchyFields(params) {
		const { workstation_id, line_id, sector_id, company_id } = params;

		let fields = 'companies.name, companies.id';
		let join = 'JOIN companies ON companies.id = max_review.company_id';

		if (company_id) {
			fields = 'sectors.name, sectors.id';
			join = 'JOIN sectors ON sectors.id = max_review.sector_id';
		}

		if (sector_id) {
			fields = 'lines.name, lines.id';
			join = 'JOIN lines ON lines.id = max_review.line_id';
		}

		if (line_id) {
			fields = 'workstations.name, workstations.id';
			join = 'JOIN workstations ON workstations.id = max_review.workstation_id';
		}

		if (workstation_id) {
			fields = 'tasks.name, tasks.id';
			join = 'JOIN tasks ON tasks.id = max_review.task_id';
		}

		return { fields, join };
	}

	getQueryLastReviews(params) {
		const {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_ids,
			full_hierarchy,
			start_date,
			end_date,
			user_id
		} = params;

		const last_reviews_query = `
			SELECT
				sera_summary_id, MAX(review) as last_review
				${full_hierarchy ? ', t.id as task_id, f.organization_id, c.id as company_id, f.sector_id, w.line_id, f.workstation_id' : ''}
    		FROM
				sera_summary_reviews
				INNER JOIN sera_summaries summary ON summary.id = sera_summary_reviews.sera_summary_id and summary.deleted_at IS NULL
  				INNER JOIN sera_review_tasks_results tr ON tr.sera_summary_review_id = sera_summary_reviews.id
  				INNER JOIN tasks t ON t.id = tr.task_id
  				INNER JOIN tasks_files tf ON tf.task_id = t.id
  				INNER JOIN files f ON tf.file_id = f.id AND f.is_active = 1
				INNER JOIN workstations w ON w.id = f.workstation_id  AND w.deleted_at IS NULL
				INNER JOIN lines AS l ON l.id = w.line_id AND l.deleted_at IS NULL
				INNER JOIN sectors s ON s.id = l.sector_id AND s.is_active = 1
				INNER JOIN companies c ON c.id = s.company_id AND c.is_active = 1
			WHERE
				sera_summary_reviews.deleted_at IS NULL
				${
					start_date && end_date
						? 'AND DATE(sera_summary_reviews.created_at) BETWEEN DATE(:start_date) AND DATE(:end_date)'
						: ''
				}
				${company_id ? 'AND t.company_id = :company_id' : ''}
				${!company_id && companies_ids ? 'AND t.company_id IN(:companies_ids)' : ''}
				${organization_id ? 'AND f.organization_id = :organization_id' : ''}
				${company_id ? 'AND c.id = :company_id' : ''}
				${!company_id && companies_ids ? 'AND c.id IN(:companies_ids)' : ''}
				${line_id ? 'AND w.line_id = :line_id' : ''}
				${sector_id ? 'AND s.id = :sector_id' : ''}
				${workstation_id ? 'AND f.workstation_id = :workstation_id' : ''}
				${user_id ? 'AND sera_summary_reviews.user_id = :user_id' : ''}
			GROUP BY sera_summary_id
		`;

		return last_reviews_query;
	}
}
