import Sequelize from 'sequelize';
import _ from 'lodash';

import { BaseRepository } from './v2/base-repository.js';
import { logger } from '../helpers/logger.js';

const { Op } = Sequelize;

export class RiskCategoryRepository extends BaseRepository {
	constructor(database) {
		super(database);
		this.risk_category = this.db.RiskCategory;
	}

	async findByPk(id, params) {
		logger.info('[RiskCategory] repository - findByPk init');
		const risk_category = await this.risk_category.findByPk(id, params);
		logger.info('[RiskCategory] repository - findByPk finish');
		return risk_category;
	}

	async create(params, transaction) {
		logger.info('[RiskCategory] repository - create init');
		const risk_category = await this.risk_category.create(params, transaction);
		logger.info('[RiskCategory] repository - create finish');
		return risk_category;
	}

	async update(params, options) {
		logger.info('[RiskCategory] repository - update init');
		const risk_category = await this.risk_category.update(params, options);
		logger.info('[RiskCategory] repository - update finish');
		return risk_category;
	}

	async delete(params, transaction) {
		logger.info('[RiskCategory] repository - delete init');
		const risk_category = await this.risk_category.destroy(params, transaction);
		logger.info('[RiskCategory] repository - delete finish');
		return risk_category;
	}

	async index(params) {
		logger.info('[RiskCategory] repository - index init');
		const risk_categories = await this.risk_category.findAll({
			...params
		});
		logger.info('[RiskCategory] repository - index finish');
		return risk_categories;
	}

	async findAllByForeignKey(params) {
		logger.info('[RiskCategory] repository - findAllByForeignKey init');
		const risk_categories = await this.risk_category.findAll({
			...params,
			logging: (sql) => {
				logger.info('[RiskCategory] repository - findAllByForeignKey sql', { sql });
			}
		});
		logger.info('[RiskCategory] repository - findAllByForeignKey finish');
		return risk_categories;
	}

	async findAndCountAll(params) {
		logger.info('[RiskCategory] repository - findAndCountAll init');
		const { custom_report_step_key_id, limit, offset, name, created_start_date, created_end_date } = params;
		const find_all_options = {
			where: {
				custom_report_step_key_id
			},
			include: [
				{
					association: 'risk_description',
					required: false,
					where: {
						custom_report_step_key_id
					},
					attributes: ['id', 'description', 'risk_category_id', 'created_at', 'updated_at']
				}
			],
			attributes: ['id', 'description', 'created_at', 'updated_at'],
			limit,
			distinct: true,
			offset: offset * limit
		};

		if (name) {
			_.set(find_all_options, 'where', {
				...find_all_options.where,
				description: {
					[Op.like]: `%${name}%`
				}
			});
		}

		if (created_start_date && created_end_date) {
			_.set(find_all_options, 'where', {
				...find_all_options.where,
				created_at: {
					[Op.between]: [created_start_date, created_end_date]
				}
			});
		}

		const risk_categories = await this.risk_category.findAndCountAll({
			...find_all_options
		});
		logger.info('[RiskCategory] repository - findAndCountAll finish');
		return risk_categories;
	}
}
