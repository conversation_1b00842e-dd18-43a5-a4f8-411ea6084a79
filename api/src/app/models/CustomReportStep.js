import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomReportStep = sequelize.define(
		'CustomReportStep',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			sequence: {
				type: DataTypes.INTEGER,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'custom_report_steps',
			modelName: 'custom_report_steps',
			paranoid: true
		}
	);

	CustomReportStep.associate = (models) => {
		CustomReportStep.belongsTo(models.CustomReport, {
			foreignKey: 'custom_report_id',
			as: 'custom_report'
		});

		CustomReportStep.hasMany(models.CustomReportStepKey, {
			foreignKey: 'custom_report_step_id',
			as: 'step_key'
		});
	};

	return CustomReportStep;
};
