import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const PEAToSuperPEA = sequelize.define(
		'PEAToSuperPEA',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			pea_id: {
				type: DataTypes.UUID,
				allowNull: false
			},
			super_pea_id: {
				type: DataTypes.UUID,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'pea_to_super_pea',
			modelName: 'pea_to_super_pea'
		}
	);

	PEAToSuperPEA.associate = (models) => {
		PEAToSuperPEA.belongsTo(models.SuperPeaReport, {
			foreignKey: 'super_pea_id',
			as: 'super_pea'
		});
	};

	return PEAToSuperPEA;
};
