import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const ActionPlanOrigin = sequelize.define(
		'ActionPlanOrigin',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV4,
				primaryKey: true
			},
			origin_name: {
				type: DataTypes.STRING,
				allowNull: true
			},
			table_name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			column_id: {
				type: DataTypes.UUID,
				allowNull: false
			},
			created_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			updated_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			deleted_at: {
				type: DataTypes.DATE,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'action_plan_origins',
			modelName: 'action_plan_origins',
			paranoid: true
		}
	);

	ActionPlanOrigin.associate = (models) => {
		ActionPlanOrigin.hasMany(models.ActionPlanV2, {
			foreignKey: 'action_plan_origin_id',
			as: 'action_plans'
		});
	};

	return ActionPlanOrigin;
};
