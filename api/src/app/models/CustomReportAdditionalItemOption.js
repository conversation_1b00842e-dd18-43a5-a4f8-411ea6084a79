import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomReportAdditionalItemOption = sequelize.define(
		'CustomReportAdditionalItemOption',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.STRING,
				allowNull: false
			},
			score: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			sequence: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			type: {
				type: DataTypes.ENUM,
				values: ['radio', 'select', 'sub_select', 'slider'],
				defaultValue: null,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'custom_report_additional_item_options',
			modelName: 'custom_report_additional_item_options',
			paranoid: true
		}
	);

	CustomReportAdditionalItemOption.associate = (models) => {
		CustomReportAdditionalItemOption.belongsTo(models.CustomReportAdditionalItem, {
			foreignKey: 'custom_report_additional_item_id',
			as: 'additional_item'
		});

		CustomReportAdditionalItemOption.hasMany(models.CustomReportStepKeyAdditionalItemResult, {
			foreignKey: 'additional_item_option_id_1',
			as: 'additional_item_results_1'
		});
		CustomReportAdditionalItemOption.hasMany(models.CustomReportStepKeyAdditionalItemResult, {
			foreignKey: 'additional_item_option_id_2',
			as: 'additional_item_results_2'
		});
	};

	return CustomReportAdditionalItemOption;
};
