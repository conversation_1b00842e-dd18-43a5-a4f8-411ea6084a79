import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomReportStepKeysErgonomicTools = sequelize.define(
		'CustomReportStepKeysErgonomicTools',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'custom_report_step_keys_ergonomic_tools',
			modelName: 'custom_report_step_keys_ergonomic_tools',
			paranoid: true
		}
	);

	return CustomReportStepKeysErgonomicTools;
};
