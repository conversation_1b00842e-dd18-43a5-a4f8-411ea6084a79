import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const SystemOfUnits = sequelize.define(
		'SystemOfUnits',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.STRING,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'systems_of_units',
			modelName: 'systems_of_units',
			paranoid: true
		}
	);

	SystemOfUnits.associate = (models) => {
		SystemOfUnits.hasMany(models.LibertyMutualReport, {
			foreignKey: 'system_of_units_id',
			as: 'liberty_mutual_report'
		});
	};

	return SystemOfUnits;
};
