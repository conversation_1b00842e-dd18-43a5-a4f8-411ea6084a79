import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomReportResult = sequelize.define(
		'CustomReportResult',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: true
			},
			interviewee_name: {
				type: DataTypes.STRING,
				allowNull: true
			},
			interviewer_name: {
				type: DataTypes.STRING,
				allowNull: true
			},
			role_name: {
				type: DataTypes.STRING,
				allowNull: true
			},
			worst_score: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			sum_score: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			average_score: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			result: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			comment: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			location: {
				type: DataTypes.STRING,
				allowNull: true
			},
			consolidated: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: false
			},
			collection_date: {
				type: DataTypes.DATE,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'custom_report_results',
			modelName: 'custom_report_results',
			paranoid: true,
			hooks: {
				afterCreate: async (instance) => {
					const { file_id, id, custom_report_id } = instance;
					try {
						const file_to_custom_report = await sequelize.models.FileToCustomReport.findOne({
							where: {
								file_id,
								report_id: id,
								custom_report_id
							}
						});

						if (file_to_custom_report) {
							return;
						}

						await sequelize.models.FileToCustomReport.create({
							file_id,
							custom_report_id,
							report_id: id
						});
					} catch (error) {
						throw error;
					}
				},
				afterDestroy: async (instance) => {
					const { id } = instance;
					try {
						await sequelize.models.FileToCustomReport.destroy({
							where: {
								report_id: id
							}
						});
					} catch (error) {
						throw error;
					}
				},
				afterBulkDestroy: async (options) => {
					const { where } = options;
					if (!where) {
						return;
					}

					try {
						const deleted_reports = await sequelize.models.CustomReportResult.findAll({
							where,
							attributes: ['id'],
							paranoid: false
						});

						for (const report of deleted_reports) {
							const { id } = report;
							await sequelize.models.FileToCustomReport.destroy({
								where: {
									report_id: id
								}
							});
						}
					} catch (error) {
						throw error;
					}
				}
			}
		}
	);

	CustomReportResult.associate = (models) => {
		CustomReportResult.belongsTo(models.CustomReport, {
			foreignKey: 'custom_report_id',
			as: 'custom_report'
		});

		CustomReportResult.belongsTo(models.Evaluator, {
			foreignKey: 'evaluator_id',
			as: 'evaluator'
		});

		CustomReportResult.belongsTo(models.File, {
			foreignKey: 'file_id',
			as: 'file'
		});

		CustomReportResult.belongsTo(models.User, {
			foreignKey: 'created_by_user_id',
			as: 'created_by_user'
		});
		CustomReportResult.belongsTo(models.Activity, {
			foreignKey: 'activity_id',
			as: 'activity'
		});

		CustomReportResult.belongsTo(models.CustomReportStep, {
			foreignKey: 'current_step_id',
			as: 'current_step'
		});

		CustomReportResult.hasMany(models.CustomReportStepKeyAdditionalItemResult, {
			foreignKey: 'custom_report_result_id',
			as: 'additional_items_result'
		});

		CustomReportResult.hasMany(models.CustomReportStepKeyResult, {
			foreignKey: 'custom_report_result_id',
			as: 'step_key_results'
		});

		CustomReportResult.hasMany(models.CustomReportSubStepKeyResult, {
			foreignKey: 'custom_report_result_id',
			as: 'sub_step_key_results'
		});

		CustomReportResult.hasMany(models.CustomReportResultActionLog, {
			foreignKey: 'custom_report_result_id',
			as: 'actions_logs'
		});

		CustomReportResult.hasOne(models.CustomReportReview, {
			foreignKey: 'custom_report_result_id',
			as: 'custom_report_review'
		});

		CustomReportResult.hasMany(models.CustomReportReview, {
			foreignKey: 'original_custom_report_result_id',
			as: 'custom_report_reviews'
		});
	};

	return CustomReportResult;
};
