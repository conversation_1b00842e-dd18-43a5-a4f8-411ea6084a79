import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const ErgonomicTool = sequelize.define(
		'ErgonomicTool',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.STRING,
				allowNull: false
			},
			title: {
				type: DataTypes.STRING,
				allowNull: false
			},
			subtitle: {
				type: DataTypes.STRING,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'ergonomic_tools',
			modelName: 'ergonomic_tools',
			paranoid: true
		}
	);

	ErgonomicTool.associate = (models) => {
		ErgonomicTool.belongsToMany(models.CustomReportStepKey, {
			through: 'CustomReportStepKeysErgonomicTools',
			as: 'step_key',
			foreignKey: 'ergonomic_tool_id',
			otherKey: 'custom_report_step_key_id'
		});
	};

	return ErgonomicTool;
};
