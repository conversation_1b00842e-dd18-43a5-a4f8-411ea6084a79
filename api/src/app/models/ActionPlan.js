import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const ActionPlan = sequelize.define(
		'ActionPlan',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			title: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			deadline: {
				type: DataTypes.DATE,
				allowNull: true
			},
			board: {
				type: DataTypes.ENUM('TO DO', 'DOING', 'DONE'),
				allowNull: false,
				defaultValue: 'TO DO'
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			completed_at: {
				type: DataTypes.DATE,
				field: 'completed_at',
				allowNull: true,
				defaultValue: null
			},
			created_at: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updated_at: {
				type: DataTypes.DATE,
				field: 'updated_at'
			},
			lexo_rank: {
				type: DataTypes.STRING(1000),
				allowNull: true,
				defaultValue: null
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'action_plans',
			modelName: 'action_plans',
			timestamps: true
		}
	);

	ActionPlan.associate = (models) => {
		ActionPlan.belongsTo(models.User, {
			foreignKey: 'user_id',
			as: 'user'
		});
		ActionPlan.belongsTo(models.User, {
			foreignKey: 'responsible_user_id',
			as: 'responsible_user'
		});
		ActionPlan.belongsTo(models.File, {
			foreignKey: 'file_id',
			as: 'file'
		});
		ActionPlan.belongsTo(models.PreliminaryAnalysisStep, {
			foreignKey: 'step_id',
			as: 'pea_step'
		});
		ActionPlan.belongsTo(models.SeraSummaryReview, {
			foreignKey: 'sera_summary_review_id',
			as: 'sera_review'
		});
		ActionPlan.belongsTo(models.CustomReportStepKey, {
			foreignKey: 'custom_report_step_key_id',
			as: 'custom_report_step_key'
		});
		ActionPlan.belongsTo(models.CustomReportSubStepKey, {
			foreignKey: 'custom_report_sub_step_key_id',
			as: 'custom_report_sub_step_key'
		});

		ActionPlan.hasMany(models.ActionPlansTask, {
			foreignKey: 'action_plan_id',
			as: 'action_plan_task'
		});
	};

	return ActionPlan;
};
