import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const TasksFiles = sequelize.define(
		'TasksFiles',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			task_id: {
				type: DataTypes.UUID,
				allowNull: false
			},
			file_id: {
				type: DataTypes.UUID,
				allowNull: false
			},
			created_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			updated_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			deleted_at: {
				type: DataTypes.DATE,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'tasks_files',
			modelName: 'tasks_files',
			paranoid: true
		}
	);

	TasksFiles.associate = (models) => {
		TasksFiles.belongsTo(models.Task, {
			foreignKey: 'task_id',
			as: 'task'
		});
		TasksFiles.belongsTo(models.File, {
			foreignKey: 'file_id',
			as: 'file'
		});
	};

	return TasksFiles;
};
