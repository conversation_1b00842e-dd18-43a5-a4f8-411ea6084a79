import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const LibertyMutualTask = sequelize.define(
		'LibertyMutualTask',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.STRING,
				allowNull: false
			},
			plural_description: {
				type: DataTypes.STRING,
				allowNull: false
			},
			sequence: {
				type: DataTypes.INTEGER,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'liberty_mutual_tasks',
			modelName: 'liberty_mutual_tasks',
			paranoid: true
		}
	);

	LibertyMutualTask.associate = (models) => {
		LibertyMutualTask.hasMany(models.LibertyMutualReportInput, {
			foreignKey: 'task_id',
			as: 'liberty_mutual_report_input'
		});
	};

	return LibertyMutualTask;
};
