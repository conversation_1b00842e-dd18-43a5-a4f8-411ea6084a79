import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const Vulnerability = sequelize.define(
		'Vulnerability',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.STRING,
				allowNull: false
			},
			score: {
				type: DataTypes.FLOAT,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'vulnerabilities',
			modelName: 'vulnerabilities',
			paranoid: true
		}
	);

	Vulnerability.associate = (models) => {
		Vulnerability.belongsTo(models.CustomReportStepKey, {
			foreignKey: 'custom_report_step_key_id',
			as: 'step_key'
		});

		Vulnerability.hasMany(models.SeraReport, {
			foreignKey: 'vulnerability_id',
			as: 'sera_report'
		});
		Vulnerability.hasMany(models.CustomReportStepKeyResult, {
			foreignKey: 'vulnerability_id',
			as: 'step_key_result'
		});
		Vulnerability.hasMany(models.CustomReportSubStepKeyResult, {
			foreignKey: 'vulnerability_id',
			as: 'sub_step_key_result'
		});
	};

	return Vulnerability;
};
