import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const ActionPlanV2 = sequelize.define(
		'ActionPlanV2',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV4,
				primaryKey: true
			},
			title: {
				type: DataTypes.STRING,
				allowNull: false
			},
			status: {
				type: DataTypes.ENUM('TO DO', 'DOING', 'DONE'),
				defaultValue: 'TO DO',
				allowNull: false
			},
			score: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			priority: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			investment_range: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			investment_value: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			description: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			due_date: {
				type: DataTypes.DATE,
				allowNull: false
			},
			lexo_rank: {
				type: DataTypes.STRING,
				allowNull: false,
				defaultValue: 'z'
			},
			completed_at: {
				type: DataTypes.DATE,
				allowNull: true
			},
			created_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			updated_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			deleted_at: {
				type: DataTypes.DATE,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'action_plans_v2',
			modelName: 'action_plans_v2',
			paranoid: true
		}
	);

	ActionPlanV2.associate = (models) => {
		ActionPlanV2.belongsTo(models.File, {
			foreignKey: 'file_id',
			as: 'file'
		});
		ActionPlanV2.belongsTo(models.Activity, {
			foreignKey: 'activity_id',
			as: 'activity'
		});
		ActionPlanV2.belongsTo(models.Workstation, {
			foreignKey: 'workstation_id',
			as: 'workstation'
		});
		ActionPlanV2.belongsTo(models.User, {
			foreignKey: 'user_id',
			as: 'user'
		});
		ActionPlanV2.belongsTo(models.User, {
			foreignKey: 'responsible_user_id',
			as: 'responsible_user'
		});
		ActionPlanV2.belongsTo(models.ActionPlanOrigin, {
			foreignKey: 'action_plan_origin_id',
			as: 'action_plan_origin'
		});
		ActionPlanV2.hasMany(models.ActionPlanRelatedReport, {
			foreignKey: 'action_plan_id',
			as: 'action_plan_related_reports'
		});

		ActionPlanV2.hasMany(models.ActionPlanTask, {
			foreignKey: 'action_plan_id',
			as: 'action_plan_tasks'
		});
	};

	return ActionPlanV2;
};
