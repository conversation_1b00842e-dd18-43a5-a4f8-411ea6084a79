import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomReportReview = sequelize.define(
		'CustomReportReview',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			version: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			comment: {
				type: DataTypes.TEXT,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'custom_report_reviews',
			modelName: 'custom_report_reviews',
			paranoid: true
		}
	);

	CustomReportReview.associate = (models) => {
		CustomReportReview.belongsTo(models.CustomReportResult, {
			foreignKey: 'custom_report_result_id',
			as: 'custom_report_result'
		});
		CustomReportReview.belongsTo(models.CustomReportResult, {
			foreignKey: 'original_custom_report_result_id',
			as: 'first_report_result'
		});
	};

	return CustomReportReview;
};
