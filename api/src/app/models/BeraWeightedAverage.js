import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const BeraWeightedAverage = sequelize.define(
		'BeraWeightedAverage',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			average_type: {
				type: DataTypes.ENUM({
					values: ['RSI', 'RPN']
				}),
				allowNull: false
			},
			average: {
				type: DataTypes.FLOAT,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'bera_weighted_averages',
			modelName: 'bera_weighted_averages',
			paranoid: true
		}
	);

	BeraWeightedAverage.associate = (models) => {
		BeraWeightedAverage.belongsTo(models.BeraJobSummary, {
			foreignKey: 'bera_job_summary_id',
			as: 'bera_job_summary'
		});
		BeraWeightedAverage.belongsTo(models.CustomReportStepKey, {
			foreignKey: 'custom_report_step_key_id',
			as: 'custom_report_step_key'
		});
	};

	return BeraWeightedAverage;
};
