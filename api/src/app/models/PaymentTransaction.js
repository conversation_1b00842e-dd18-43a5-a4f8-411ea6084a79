import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const PaymentTransaction = sequelize.define(
		'PaymentTransaction',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			hash: {
				type: DataTypes.STRING,
				allowNull: true
			},
			amount: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			type: {
				type: DataTypes.ENUM,
				values: ['renew', 'new', 'cancel', 'refund'],
				allowNull: false
			},
			token_transaction: {
				type: DataTypes.STRING,
				allowNull: true
			},
			charge_id: {
				type: DataTypes.STRING,
				allowNull: true
			},
			recurrence: {
				type: DataTypes.STRING,
				allowNull: true
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'payment_transactions',
			modelName: 'payment_transactions'
		}
	);

	PaymentTransaction.associate = (models) => {
		PaymentTransaction.belongsTo(models.CustomerInformation, {
			foreignKey: 'customer_id',
			as: 'customer_information'
		});
	};

	return PaymentTransaction;
};
