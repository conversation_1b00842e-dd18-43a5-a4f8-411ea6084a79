import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const UserAccess = sequelize.define(
		'UserAccess',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			isActive: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			},
			OrganizationId: {
				type: DataTypes.UUID,
				allowNull: false,
				field: 'organization_id'
			},
			CompanyId: {
				type: DataTypes.UUID,
				allowNull: false,
				field: 'company_id'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'user_accesses',
			modelName: 'user_accesses'
		}
	);

	UserAccess.associate = function (models) {
		UserAccess.belongsTo(models.User, {
			foreignKey: 'user_id',
			as: 'user'
		});
		UserAccess.belongsTo(models.Company, {
			foreignKey: 'company_id',
			as: 'company'
		});
		UserAccess.belongsTo(models.Organization, {
			foreignKey: 'organization_id',
			as: 'organization'
		});
	};

	return UserAccess;
};
