import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const File = sequelize.define(
		'File',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			original_name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			generated_name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			size: {
				type: DataTypes.BIGINT,
				allowNull: false
			},
			url: {
				type: DataTypes.STRING,
				allowNull: false
			},
			status: {
				type: DataTypes.ENUM({
					values: ['CORRUPTED_FILE', 'EXTRACTED_DATA', 'NOT_PROCESSED', 'PROCESSING', 'PROCESSED', 'IN_QUEUE']
				}),
				allowNull: true,
				defaultValue: 'NOT_PROCESSED'
			},
			file_processed: {
				type: DataTypes.STRING,
				allowNull: true,
				defaultValue: false
			},
			workstation: {
				type: DataTypes.STRING,
				allowNull: true,
				defaultValue: null
			},
			duration: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: 0
			},
			rulaScore: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: 0,
				field: 'rula_score'
			},
			tool: {
				type: DataTypes.STRING,
				allowNull: false,
				defaultValue: 'RULA'
			},
			blurFace: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'blur_face'
			},
			isActive: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'files',
			modelName: 'files'
		},
		{
			hooks: {
				beforeCreate: async (file, options) => {
					file.tool = file.tool.toUpperCase();
				}
			}
		}
	);

	File.associate = (models) => {
		File.belongsToMany(models.CustomReport, {
			through: 'CustomReportsFiles',
			as: 'custom_report',
			foreignKey: 'file_id',
			otherKey: 'custom_report_id'
		});
		File.belongsToMany(models.Task, {
			through: 'TasksFiles',
			as: 'task',
			foreignKey: 'file_id',
			otherKey: 'task_id'
		});
		File.belongsTo(models.User, {
			foreignKey: 'user_id',
			as: 'User'
		});
		File.belongsTo(models.User, {
			foreignKey: 'user_id',
			as: 'user'
		});
		File.belongsTo(models.Workstation, {
			foreignKey: 'workstation_id',
			as: 'workstations'
		});

		File.hasOne(models.AngleTimeReport, {
			foreignKey: 'file_id',
			as: 'AngleTimeReport'
		});
		File.hasOne(models.AngleTimeReport, {
			foreignKey: 'file_id',
			as: 'angle_time'
		});
		File.hasMany(models.BeraReport, {
			foreignKey: 'file_id',
			as: 'bera_report'
		});

		// Deprecated
		File.hasMany(models.RebaReport, { as: 'RebaReport' });

		File.belongsTo(models.Organization, { foreignKey: 'organization_id', as: 'organization' });
		File.belongsTo(models.Company, { foreignKey: 'company_id', as: 'company' });
		File.belongsTo(models.Sector, { foreignKey: 'sector_id', as: 'sector' });

		File.hasOne(models.RebaReport, { foreignKey: 'file_id', as: 'reba' });
		File.hasOne(models.NioshReport, { foreignKey: 'file_id', as: 'niosh' });
		File.hasOne(models.StrainIndexReport, { foreignKey: 'file_id', as: 'strain_index' });
		File.hasOne(models.KimManualHandlingReport, { foreignKey: 'file_id', as: 'kim_mho' });
		File.hasOne(models.KimPushPullReport, { foreignKey: 'file_id', as: 'kim_push_pull' });
		File.hasOne(models.ActionPlanV2, { foreignKey: 'file_id', as: 'action_plan' });
		File.hasOne(models.PreliminaryAnalysis, { foreignKey: 'file_id', as: 'preliminary_analysis' });
		File.hasOne(models.LibertyMutualReport, {
			foreignKey: 'file_id',
			as: 'liberty_mutual'
		});
		File.hasOne(models.BackCompressiveForceEstimationReport, {
			foreignKey: 'file_id',
			as: 'back_compressive_force_estimation'
		});

		File.hasMany(models.BeraJobSummaryFiles, {
			foreignKey: 'file_id',
			as: 'bera_job_summary_files'
		});

		// Do not use !
		File.belongsTo(models.Organization, {
			foreignKey: 'organization_id',
			as: 'Organization'
		});
		File.hasMany(models.KimPushPullReport, {
			as: 'kim_push_pull_report',
			foreignKey: 'file_id'
		});
		File.belongsTo(models.Company, { foreignKey: 'company_id', as: 'Company' });
		File.belongsTo(models.Sector, { foreignKey: 'sector_id', as: 'Sector' });
		File.hasMany(models.SeraSummaryFiles, {
			foreignKey: 'file_id',
			as: 'sera_summary_files'
		});
	};

	return File;
};
