import { optionsDefaultTable } from './commons/default-options-table.js';
import { CUSTOM_REPORT_NAMES } from '../util/constants-custom-report.js';

export default (sequelize, DataTypes) => {
	const SeraReport = sequelize.define(
		'SeraReport',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			specifications: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			existing_prevention_measures: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			rpn: {
				type: DataTypes.INTEGER,
				allowNull: false,
				defaultValue: 0
			}
		},
		{
			...optionsDefaultTable,
			paranoid: true,
			tableName: 'sera_reports',
			modelName: 'sera_reports',
			hooks: {
				afterCreate: async (instance) => {
					const { id } = instance;
					try {
						const sera_report_files = await sequelize.models.SeraReport.findAll({
							attributes: ['id', 'task_id'],
							where: {
								id
							},
							include: [
								{
									model: sequelize.models.Task,
									as: 'task',
									where: {
										deleted_at: null
									},
									required: true,
									include: [
										{
											model: sequelize.models.File,
											as: 'file',
											through: {
												attributes: ['file_id', 'task_id']
											},
											attributes: ['id'],
											where: {
												is_active: true
											},
											required: true
										}
									]
								}
							],
							paranoid: false
						});

						const task_files_ids = sera_report_files
							.map(({ task }) => {
								return task?.file?.map(({ id }) => {
									return id;
								});
							})
							.flat();

						const custom_report = await sequelize.models.CustomReport.findOne({
							where: {
								name: CUSTOM_REPORT_NAMES.SERA
							},
							attributes: ['id'],
							plain: true,
							raw: true
						});

						const custom_report_id = custom_report?.id;

						const promises = task_files_ids.map((file_id) => {
							return sequelize.models.FileToCustomReport.create({
								file_id,
								custom_report_id,
								report_id: id
							});
						});

						await Promise.all(promises);
					} catch (error) {
						throw error;
					}
				},
				afterDestroy: async (instance) => {
					const { id } = instance;
					try {
						await sequelize.models.FileToCustomReport.destroy({
							where: {
								report_id: id
							}
						});
					} catch (error) {
						throw error;
					}
				},
				afterBulkDestroy: async (instance) => {
					const { where } = instance;
					try {
						const deleted_reports = await sequelize.models.SeraReport.findAll({
							where,
							attributes: ['id'],
							paranoid: false
						});

						for (const report of deleted_reports) {
							await sequelize.models.FileToCustomReport.destroy({
								where: {
									report_id: report.id
								}
							});
						}
					} catch (error) {
						throw error;
					}
				}
			}
		}
	);

	SeraReport.associate = (models) => {
		SeraReport.belongsTo(models.Evaluator, {
			foreignKey: 'evaluator_id',
			as: 'reviewer'
		});
		SeraReport.belongsTo(models.Task, {
			foreignKey: 'task_id',
			as: 'task'
		});
		SeraReport.belongsTo(models.SeraSummaryReview, {
			foreignKey: 'sera_summary_review_id',
			as: 'review'
		});
		SeraReport.belongsTo(models.RiskCategory, {
			foreignKey: 'risk_category_id',
			as: 'risk_category'
		});
		SeraReport.belongsTo(models.RiskDescription, {
			foreignKey: 'risk_description_id',
			as: 'risk_description'
		});
		SeraReport.belongsTo(models.RiskDamage, {
			foreignKey: 'risk_damage_id',
			as: 'risk_damage'
		});
		SeraReport.belongsTo(models.Severity, {
			foreignKey: 'severity_id',
			as: 'severity'
		});
		SeraReport.belongsTo(models.Exposure, {
			foreignKey: 'exposure_id',
			as: 'exposure'
		});
		SeraReport.belongsTo(models.Vulnerability, {
			foreignKey: 'vulnerability_id',
			as: 'vulnerability'
		});

		SeraReport.hasMany(models.SeraReportUpdated, {
			foreignKey: 'sera_report_id',
			as: 'sera_report_updated'
		});
		SeraReport.hasMany(models.SeraReportUpdated, {
			foreignKey: 'sera_report_updated',
			as: 'sera_report_updated_by'
		});
	};

	return SeraReport;
};
