import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomReportsOrganizations = sequelize.define(
		'CustomReportsOrganizations',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'custom_reports_organizations',
			modelName: 'custom_reports_organizations',
			paranoid: true
		}
	);

	return CustomReportsOrganizations;
};
