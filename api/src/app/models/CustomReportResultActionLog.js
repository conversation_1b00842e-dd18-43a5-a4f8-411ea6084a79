import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomReportResultActionLog = sequelize.define(
		'CustomReportResultActionLog',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			action: {
				type: DataTypes.ENUM(['consolidate', 'deconsolidate', 'download_pdf', 'review']),
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'custom_report_result_actions_logs',
			modelName: 'custom_report_result_actions_logs',
			paranoid: true
		}
	);

	CustomReportResultActionLog.associate = (models) => {
		CustomReportResultActionLog.belongsTo(models.User, {
			foreignKey: 'user_id',
			as: 'user'
		});
		CustomReportResultActionLog.belongsTo(models.CustomReportResult, {
			foreignKey: 'custom_report_result_id',
			as: 'report_result'
		});
	};

	return CustomReportResultActionLog;
};
