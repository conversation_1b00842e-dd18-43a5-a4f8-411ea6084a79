import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomReportStepKeyAdditionalItemResult = sequelize.define(
		'CustomReportStepKeyAdditionalItemResult',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			result: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			value_1: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			value_2: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			value_3: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			value_4: {
				type: DataTypes.FLOAT,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'custom_report_step_key_additional_item_results',
			modelName: 'custom_report_step_key_additional_item_results',
			paranoid: true
		}
	);

	CustomReportStepKeyAdditionalItemResult.associate = (models) => {
		CustomReportStepKeyAdditionalItemResult.belongsTo(models.CustomReportAdditionalItemOption, {
			foreignKey: 'additional_item_option_id_1',
			as: 'option_1'
		});
		CustomReportStepKeyAdditionalItemResult.belongsTo(models.CustomReportAdditionalItemOption, {
			foreignKey: 'additional_item_option_id_2',
			as: 'option_2'
		});
		CustomReportStepKeyAdditionalItemResult.belongsTo(models.CustomReportStepKeysAdditionalItem, {
			foreignKey: 'custom_report_step_key_additional_item_id',
			as: 'step_key_additional_item'
		});
		CustomReportStepKeyAdditionalItemResult.belongsTo(models.CustomReportResult, {
			foreignKey: 'custom_report_result_id',
			as: 'custom_report_result'
		});
	};

	return CustomReportStepKeyAdditionalItemResult;
};
