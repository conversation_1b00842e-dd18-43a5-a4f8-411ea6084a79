import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const SeraSummaryReview = sequelize.define(
		'SeraSummaryReview',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			review: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			analyzed_reports: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			reviewed_reports: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			highest_rpn: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			sum_rpn: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			average_rpn: {
				type: DataTypes.FLOAT,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'sera_summary_reviews',
			modelName: 'sera_summary_reviews',
			paranoid: true
		}
	);

	SeraSummaryReview.associate = (models) => {
		SeraSummaryReview.belongsTo(models.SeraSummary, {
			foreignKey: 'sera_summary_id',
			as: 'sera_summary'
		});
		SeraSummaryReview.belongsTo(models.User, {
			foreignKey: 'user_id',
			as: 'review_responsible'
		});

		SeraSummaryReview.hasOne(models.SeraReviewSelector, {
			foreignKey: 'sera_summary_review_id',
			as: 'sera_review_selector'
		});
		SeraSummaryReview.hasMany(models.SeraReport, {
			foreignKey: 'sera_summary_review_id',
			as: 'sera_report'
		});
		SeraSummaryReview.hasMany(models.SeraReviewTasksResult, {
			foreignKey: 'sera_summary_review_id',
			as: 'sera_review_task_result'
		});
		SeraSummaryReview.hasMany(models.ActionPlan, {
			foreignKey: 'sera_summary_review_id',
			as: 'action_plan'
		});
	};

	return SeraSummaryReview;
};
