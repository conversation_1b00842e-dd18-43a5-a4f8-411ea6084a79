import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const RangeRisk = sequelize.define(
		'RangeRisk',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.TEXT,
				allowNull: true,
				length: 100
			},
			standard: {
				type: DataTypes.BOOLEAN,
				allowNull: true,
				defaultValue: false
			},
			generated_name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			location: {
				type: DataTypes.STRING,
				allowNull: true
			},
			customer_information_id: {
				type: DataTypes.UUID,
				allowNull: true,
				defaultValue: null,
				references: {
					key: 'id',
					model: 'customer_informations'
				},
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE'
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'range_risks',
			modelName: 'range_risks'
		}
	);

	RangeRisk.associate = (models) => {
		RangeRisk.belongsTo(models.CustomerInformation, {
			foreignKey: 'customer_information_id',
			as: 'customer_information'
		});
	};

	return RangeRisk;
};
