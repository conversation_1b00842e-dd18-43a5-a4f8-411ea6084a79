import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const RiskCategory = sequelize.define(
		'RiskCategory',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.STRING,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'risk_categories',
			modelName: 'risk_categories',
			paranoid: true
		}
	);

	RiskCategory.associate = (models) => {
		RiskCategory.belongsTo(models.CustomReportStepKey, {
			foreignKey: 'custom_report_step_key_id',
			as: 'step_key'
		});

		RiskCategory.hasMany(models.RiskDescription, {
			foreignKey: 'risk_category_id',
			as: 'risk_description'
		});
		RiskCategory.hasMany(models.SeraReport, {
			foreignKey: 'risk_category_id',
			as: 'sera_report'
		});
		RiskCategory.hasMany(models.CustomReportStepKeyResult, {
			foreignKey: 'risk_category_id',
			as: 'step_key_result'
		});
		RiskCategory.hasMany(models.CustomReportSubStepKeyResult, {
			foreignKey: 'risk_category_id',
			as: 'sub_step_key_result'
		});
		RiskCategory.hasMany(models.CustomReportStepKeysDefaultRisk, {
			foreignKey: 'risk_category_id',
			as: 'step_key_default_risk'
		});
		RiskCategory.hasMany(models.CustomReportSubStepKeysDefaultRisk, {
			foreignKey: 'risk_category_id',
			as: 'sub_step_key_default_risk'
		});
	};

	return RiskCategory;
};
