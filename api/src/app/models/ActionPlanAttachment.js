import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const ActionPlanAttachment = sequelize.define(
		'ActionPlanAttachment',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV4,
				primaryKey: true
			},
			description: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			location: {
				type: DataTypes.STRING,
				allowNull: false
			},
			file_name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			size: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			user_id: {
				type: DataTypes.UUID,
				allowNull: false
			},
			action_plan_id: {
				type: DataTypes.UUID,
				allowNull: true
			},
			created_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			updated_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			deleted_at: {
				type: DataTypes.DATE,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'action_plan_attachments',
			modelName: 'action_plan_attachments',
			paranoid: true
		}
	);

	ActionPlanAttachment.associate = (models) => {
		ActionPlanAttachment.belongsTo(models.ActionPlanV2, {
			foreignKey: 'action_plan_id',
			as: 'action_plan'
		});
		ActionPlanAttachment.belongsTo(models.User, {
			foreignKey: 'user_id',
			as: 'user'
		});
	};

	return ActionPlanAttachment;
};
