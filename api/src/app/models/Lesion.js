import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const Lesion = sequelize.define(
		'Lesion',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.STRING,
				allowNull: false
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'injuries',
			modelName: 'injuries'
		}
	);

	Lesion.associate = (models) => {
		Lesion.belongsToMany(models.PreliminaryAnalysisStep, {
			as: 'steps',
			foreignKey: 'lesion_id',
			otherKey: 'pea_step_id',
			through: 'PreliminaryAnalysisStepLesion'
		});
	};

	return Lesion;
};
