import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const ActionPlanHistory = sequelize.define(
		'action_plan_history',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			event: {
				type: DataTypes.ENUM(
					'Task created',
					'Task deleted',
					'Task renamed',
					'Task checked',
					'Task unchecked',
					'Action plan created',
					'Action plan renamed',
					'Task type changed',
					'Task and type changed',
					'Status changed',
					'Deadline changed',
					'Responsible user changed'
				),
				allowNull: false
			},
			event_item_id: {
				type: DataTypes.UUID,
				allowNull: true
			},
			event_value: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			event_date: {
				type: DataTypes.DATE,
				allowNull: false
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'action_plan_history',
			modelName: 'action_plan_history',
			freezeTableName: true
		}
	);

	ActionPlanHistory.associate = function (models) {
		ActionPlanHistory.belongsTo(models.ActionPlan, {
			foreignKey: 'action_plan_id',
			as: 'action_plan'
		});

		ActionPlanHistory.belongsTo(models.User, {
			foreignKey: 'user_id',
			as: 'user'
		});

		ActionPlanHistory.belongsTo(models.User, {
			foreignKey: 'responsible_user_id',
			as: 'responsible_user'
		});
	};
	return ActionPlanHistory;
};
