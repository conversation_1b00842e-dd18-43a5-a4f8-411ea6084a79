import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const Anthropometry = sequelize.define(
		'Anthropometry',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			location: {
				type: DataTypes.STRING,
				allowNull: false
			},
			found: {
				type: DataTypes.FLOAT,
				allowNull: false
			},
			foreseen: {
				type: DataTypes.FLOAT,
				allowNull: false
			},
			condition: {
				type: DataTypes.ENUM,
				values: ['ok', 'not_ok'],
				allowNull: false
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'anthropometries',
			modelName: 'anthropometries'
		}
	);

	return Anthropometry;
};
