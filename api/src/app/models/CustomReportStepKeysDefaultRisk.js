import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomReportStepKeysDefaultRisk = sequelize.define(
		'CustomReportStepKeysDefaultRisk',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'custom_report_step_keys_default_risks',
			modelName: 'custom_report_step_keys_default_risks',
			paranoid: true
		}
	);

	CustomReportStepKeysDefaultRisk.associate = (models) => {
		CustomReportStepKeysDefaultRisk.belongsTo(models.CustomReportStepKey, {
			foreignKey: 'custom_report_step_key_id',
			as: 'step_key'
		});
		CustomReportStepKeysDefaultRisk.belongsTo(models.RiskDamage, {
			foreignKey: 'risk_damage_id',
			as: 'risk_damage'
		});
		CustomReportStepKeysDefaultRisk.belongsTo(models.RiskDescription, {
			foreignKey: 'risk_description_id',
			as: 'risk_description'
		});
		CustomReportStepKeysDefaultRisk.belongsTo(models.RiskCategory, {
			foreignKey: 'risk_category_id',
			as: 'risk_category'
		});
	};

	return CustomReportStepKeysDefaultRisk;
};
