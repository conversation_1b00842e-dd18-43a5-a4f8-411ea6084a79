import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const PreliminaryAnalysisStep = sequelize.define(
		'PreliminaryAnalysisStep',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			result: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			score: {
				type: DataTypes.INTEGER,
				allowNull: false,
				default: 0
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'preliminary_analysis_steps',
			modelName: 'preliminary_analysis_steps'
		}
	);

	PreliminaryAnalysisStep.associate = (models) => {
		PreliminaryAnalysisStep.belongsTo(models.PreliminaryAnalysis, {
			foreignKey: 'preliminary_analysis_id',
			as: 'preliminary_analysis'
		});
		PreliminaryAnalysisStep.belongsTo(models.PreliminaryAnalysisStepKey, {
			foreignKey: 'preliminary_analysis_step_key_id',
			as: 'step_key'
		});
		PreliminaryAnalysisStep.belongsTo(models.Consequence, {
			foreignKey: 'consequence_id',
			as: 'consequence'
		});
		PreliminaryAnalysisStep.belongsTo(models.Probability, {
			foreignKey: 'probability_id',
			as: 'probability'
		});
		PreliminaryAnalysisStep.belongsTo(models.Exhibition, {
			foreignKey: 'exhibition_id',
			as: 'exhibition'
		});
		PreliminaryAnalysisStep.hasMany(models.Anthropometry, {
			foreignKey: 'pea_step_id',
			as: { singular: 'anthropometry', plural: 'anthropometries' }
		});

		PreliminaryAnalysisStep.hasOne(models.ActionPlan, {
			foreignKey: 'step_id',
			as: 'action_plan'
		});

		PreliminaryAnalysisStep.belongsToMany(models.Lesion, {
			as: { singular: 'lesion', plural: 'injuries' },
			foreignKey: 'pea_step_id',
			otherKey: 'lesion_id',
			through: 'PreliminaryAnalysisStepLesion'
		});
	};

	return PreliminaryAnalysisStep;
};
