import { optionsDefaultTable } from './commons/default-options-table.js';
import { ERGONOMIC_TOOL_NAMES } from '../util/constants-ergonomic-tools.js';
import { FileToErgonomicToolHookManager } from '../helpers/file-to-ergonomic-tool-hook-manager.js';

export default (sequelize, DataTypes) => {
	const KimManualHandlingReport = sequelize.define(
		'kim_mho_reports',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			duration: {
				type: DataTypes.INTEGER,
				allowNull: false,
				validate: {
					min: 1,
					max: 10
				}
			},
			left_force_type: {
				type: DataTypes.ENUM(['HOLDING', 'MOVING']),
				allowNull: false
			},
			left_force_frequency: {
				type: DataTypes.INTEGER,
				allowNull: false,
				validate: {
					min: 1,
					max: 90
				}
			},
			left_force_intensity: {
				type: DataTypes.ENUM(['VERY_LOW', 'MODERATE', 'HIGH', 'VERY_HIGH', 'PEAK', 'POWERFUL_HITTING']),
				allowNull: false
			},
			right_force_type: {
				type: DataTypes.ENUM(['HOLDING', 'MOVING']),
				allowNull: false
			},
			right_force_frequency: {
				type: DataTypes.INTEGER,
				allowNull: false,
				validate: {
					min: 1,
					max: 90
				}
			},
			right_force_intensity: {
				type: DataTypes.ENUM(['VERY_LOW', 'MODERATE', 'HIGH', 'VERY_HIGH', 'PEAK', 'POWERFUL_HITTING']),
				allowNull: false
			},
			force_transfer: {
				type: DataTypes.ENUM(['OPTIMUM', 'RESTRICTED', 'HINDERED']),
				allowNull: false
			},
			arm_posture: {
				type: DataTypes.ENUM(['GOOD', 'RESTRICTED', 'UNFAVOURABLE', 'POOR']),
				allowNull: false
			},
			work_conditions: {
				type: DataTypes.ENUM(['GOOD', 'RESTRICTED', 'UNFAVOURABLE']),
				allowNull: false
			},
			temporal_distribution: {
				type: DataTypes.ENUM(['GOOD', 'RESTRICTED', 'UNFAVOURABLE']),
				allowNull: false
			},
			body_posture: {
				type: DataTypes.ENUM([
					'ALTERNATED_SITTING_STANDING',
					'OCCASIONAL_WALKING',
					'NO_WALKING',
					'SEVERELY_INCLINED'
				]),
				allowNull: false
			},
			risk_score: {
				type: DataTypes.INTEGER,
				allowNull: false,
				validate: {
					min: 0,
					max: 1200
				}
			},
			risk_load: {
				type: DataTypes.ENUM(['LOW', 'SLIGHTLY_INCREASED', 'SUBSTANTIALLY_INCREASED', 'HIGH']),
				allowNull: false
			},
			collection_date: {
				type: DataTypes.DATE,
				allowNull: false
			},
			comment: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			isActive: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'kim_mho_reports',
			modelName: 'kim_mho_reports',
			hooks: {
				afterCreate: async (instance) => {
					await FileToErgonomicToolHookManager.afterCreate({
						instance,
						tool_name: ERGONOMIC_TOOL_NAMES.KIM_MHO,
						sequelize
					});
				},
				afterDestroy: async (instance) => {
					await FileToErgonomicToolHookManager.afterDestroy({
						instance,
						sequelize
					});
				},
				afterBulkDestroy: async (options) => {
					await FileToErgonomicToolHookManager.afterBulkDestroy({
						options,
						sequelize
					});
				}
			}
		}
	);

	KimManualHandlingReport.associate = function (models) {
		KimManualHandlingReport.belongsTo(models.File, {
			foreignKey: 'file_id',
			as: 'file'
		});
		KimManualHandlingReport.belongsTo(models.User, {
			foreignKey: 'report_user_id',
			as: 'report_user'
		});
	};

	return KimManualHandlingReport;
};
