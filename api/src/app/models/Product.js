import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const Product = sequelize.define(
		'Product',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.STRING,
				allowNull: true
			},
			wc_product_id: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			type: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'products',
			modelName: 'products'
		}
	);

	Product.associate = (models) => {
		Product.hasOne(models.Plan, {
			foreignKey: 'product_id',
			as: 'plan'
		});
	};

	return Product;
};
