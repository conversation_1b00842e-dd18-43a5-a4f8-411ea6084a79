import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const SeraReviewSelector = sequelize.define(
		'SeraReviewSelector',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.STRING,
				allowNull: false
			},
			sequence: {
				type: DataTypes.INTEGER,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'sera_reviews_selector',
			modelName: 'sera_reviews_selector',
			paranoid: true
		}
	);

	SeraReviewSelector.associate = (models) => {
		SeraReviewSelector.belongsTo(models.SeraSummary, {
			foreignKey: 'sera_summary_id',
			as: 'sera_summary'
		});
		SeraReviewSelector.belongsTo(models.SeraSummaryReview, {
			foreignKey: 'sera_summary_review_id',
			as: 'sera_summary_review'
		});
	};

	return SeraReviewSelector;
};
