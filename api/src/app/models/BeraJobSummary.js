import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const BeraJobSummary = sequelize.define(
		'BeraJobSummary',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			report_name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			total_time: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			overall_score: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			weighted_rsi: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			comment: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			gns: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'bera_job_summaries',
			modelName: 'bera_job_summaries',
			paranoid: true
		}
	);

	BeraJobSummary.associate = (models) => {
		BeraJobSummary.belongsTo(models.Cycle, {
			foreignKey: 'cycle_id',
			as: 'cycle'
		});
		BeraJobSummary.belongsTo(models.User, {
			foreignKey: 'user_id',
			as: 'user'
		});

		BeraJobSummary.hasMany(models.BeraReport, {
			foreignKey: 'bera_job_summary_id',
			as: 'bera_report'
		});
		BeraJobSummary.hasMany(models.BeraWeightedAverage, {
			foreignKey: 'bera_job_summary_id',
			as: 'bera_weighted_average'
		});
		BeraJobSummary.hasMany(models.BeraJobSummaryFiles, {
			foreignKey: 'bera_job_summary_id',
			as: 'bera_job_summary_files'
		});
	};

	return BeraJobSummary;
};
