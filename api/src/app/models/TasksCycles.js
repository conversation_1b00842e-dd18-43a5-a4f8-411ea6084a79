import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const TasksCycles = sequelize.define(
		'TasksCycles',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'tasks_cycles',
			modelName: 'tasks_cycles',
			paranoid: true
		}
	);

	return TasksCycles;
};
