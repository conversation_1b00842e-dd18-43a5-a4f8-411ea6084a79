import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomReportStepKey = sequelize.define(
		'CustomReportStepKey',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			sequence: {
				type: DataTypes.INTEGER,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'custom_report_step_keys',
			modelName: 'custom_report_step_keys',
			paranoid: true
		}
	);

	CustomReportStepKey.associate = (models) => {
		CustomReportStepKey.belongsToMany(models.ErgonomicTool, {
			through: 'CustomReportStepKeysErgonomicTools',
			as: 'ergonomic_tool',
			foreignKey: 'custom_report_step_key_id',
			otherKey: 'ergonomic_tool_id'
		});
		CustomReportStepKey.belongsToMany(models.CustomReportAdditionalItem, {
			through: 'CustomReportStepKeysAdditionalItem',
			as: 'additional_items',
			foreignKey: 'custom_report_step_key_id',
			otherKey: 'additional_item_id'
		});
		CustomReportStepKey.belongsTo(models.CustomReportStep, {
			foreignKey: 'custom_report_step_id',
			as: 'step'
		});

		CustomReportStepKey.hasMany(models.CustomReportSubStepKey, {
			foreignKey: 'custom_report_step_key_id',
			as: 'sub_step_keys'
		});
		CustomReportStepKey.hasMany(models.StressLevel, {
			foreignKey: 'custom_report_step_key_id',
			as: 'stress_level'
		});
		CustomReportStepKey.hasMany(models.Frequency, {
			foreignKey: 'custom_report_step_key_id',
			as: 'frequency'
		});
		CustomReportStepKey.hasMany(models.TotalTaskDuration, {
			foreignKey: 'custom_report_step_key_id',
			as: 'total_task_duration'
		});
		CustomReportStepKey.hasMany(models.BeraStepKeyResult, {
			foreignKey: 'custom_report_step_key_id',
			as: 'bera_step_key_result'
		});
		CustomReportStepKey.hasMany(models.BeraWeightedAverage, {
			foreignKey: 'custom_report_step_key_id',
			as: 'bera_weighted_average'
		});
		CustomReportStepKey.hasMany(models.RiskDamage, {
			foreignKey: 'custom_report_step_key_id',
			as: 'risk_damage'
		});
		CustomReportStepKey.hasMany(models.RiskCategory, {
			foreignKey: 'custom_report_step_key_id',
			as: 'risk_category'
		});
		CustomReportStepKey.hasMany(models.RiskDescription, {
			foreignKey: 'custom_report_step_key_id',
			as: 'risk_description'
		});
		CustomReportStepKey.hasMany(models.Severity, {
			foreignKey: 'custom_report_step_key_id',
			as: 'severity'
		});
		CustomReportStepKey.hasMany(models.Exposure, {
			foreignKey: 'custom_report_step_key_id',
			as: 'exposure'
		});
		CustomReportStepKey.hasMany(models.Vulnerability, {
			foreignKey: 'custom_report_step_key_id',
			as: 'vulnerability'
		});
		CustomReportStepKey.hasMany(models.ActionPlan, {
			foreignKey: 'custom_report_step_key_id',
			as: 'action_plans'
		});
		CustomReportStepKey.hasOne(models.CustomReportStepKeysDefaultRisk, {
			foreignKey: 'custom_report_step_key_id',
			as: 'step_key_default_risk'
		});
		CustomReportStepKey.hasOne(models.CustomReportStepKeyResult, {
			foreignKey: 'custom_report_step_key_id',
			as: 'custom_report_step_key_result'
		});
	};

	return CustomReportStepKey;
};
