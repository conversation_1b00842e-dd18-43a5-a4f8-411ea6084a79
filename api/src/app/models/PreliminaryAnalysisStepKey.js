import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const PreliminaryAnalysisStepKey = sequelize.define(
		'PreliminaryAnalysisStepKey',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.TEXT,
				allowNull: true,
				type: DataTypes.TEXT,
				get: function () {
					return JSON.parse(this.getDataValue('name'));
				},
				set: function (value) {
					return this.setDataValue('name', JSON.stringify(value));
				}
			},
			description: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			sequence: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'preliminary_analysis_step_keys',
			modelName: 'preliminary_analysis_step_keys'
		}
	);

	return PreliminaryAnalysisStepKey;
};
