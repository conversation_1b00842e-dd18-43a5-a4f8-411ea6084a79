import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const Task = sequelize.define(
		'Task',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.STRING,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'tasks',
			modelName: 'tasks',
			paranoid: true
		}
	);

	Task.associate = (models) => {
		Task.belongsTo(models.Company, {
			foreignKey: 'company_id',
			as: 'company'
		});
		Task.hasMany(models.SeraReport, {
			foreignKey: 'task_id',
			as: 'sera_report'
		});
		Task.hasMany(models.SeraReviewTasksResult, {
			foreignKey: 'task_id',
			as: 'sera_review_task_result'
		});
		Task.belongsToMany(models.Cycle, {
			through: 'TasksCycles',
			as: 'cycle',
			foreignKey: 'task_id',
			otherKey: 'cycle_id'
		});
		Task.belongsToMany(models.File, {
			through: 'TasksFiles',
			as: 'file',
			foreignKey: 'task_id',
			otherKey: 'file_id'
		});
	};

	return Task;
};
