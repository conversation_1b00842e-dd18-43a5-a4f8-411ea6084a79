import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const Activity = sequelize.define(
		'Activity',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			created_at: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updated_at: {
				type: DataTypes.DATE,
				field: 'updated_at'
			},
			deleted_at: {
				type: DataTypes.DATE,
				allowNull: true,
				defaultValue: null
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'activities',
			modelName: 'activities',
			paranoid: true
		}
	);

	Activity.associate = (models) => {
		Activity.belongsTo(models.Workstation, {
			foreignKey: 'workstation_id',
			as: 'workstation'
		});
		Activity.hasMany(models.CustomReportResult, {
			foreignKey: 'activity_id',
			as: 'custom_report_result'
		});
	};

	return Activity;
};
