import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const BusinessInformation = sequelize.define(
		'BusinessInformation',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			company_name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			fantasy_name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			cnae: {
				type: DataTypes.STRING,
				allowNull: false
			},
			cnpj: {
				type: DataTypes.STRING(19),
				allowNull: false
			},
			address: {
				type: DataTypes.STRING,
				allowNull: false
			},
			city: {
				type: DataTypes.STRING,
				allowNull: false
			},
			state: {
				type: DataTypes.STRING,
				allowNull: false
			},
			zipcode: {
				type: DataTypes.STRING,
				allowNull: false
			},
			district: {
				type: DataTypes.STRING,
				allowNull: false
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'business_informations',
			modelName: 'business_informations'
		}
	);

	BusinessInformation.associate = (models) => {
		BusinessInformation.belongsTo(models.Organization, {
			foreignKey: 'organization_id',
			as: 'organization'
		});
		BusinessInformation.belongsTo(models.Company, {
			foreignKey: 'company_id',
			as: 'company'
		});
	};

	return BusinessInformation;
};
