import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const Workstation = sequelize.define(
		'Workstation',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'workstations',
			modelName: 'workstations',
			paranoid: true
		}
	);

	Workstation.associate = (models) => {
		Workstation.hasMany(models.File, {
			foreignKey: 'workstation_id',
			as: 'file'
		});
		Workstation.belongsTo(models.Line, {
			as: 'line',
			foreignKey: 'line_id'
		});
	};

	return Workstation;
};
