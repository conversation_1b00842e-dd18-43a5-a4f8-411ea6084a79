import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomReportAdditionalItem = sequelize.define(
		'CustomReportAdditionalItem',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			title_1: {
				type: DataTypes.STRING,
				allowNull: false
			},
			unit_1: {
				type: DataTypes.STRING,
				allowNull: true
			},
			title_2: {
				type: DataTypes.STRING,
				allowNull: true
			},
			unit_2: {
				type: DataTypes.STRING,
				allowNull: true
			},
			title_3: {
				type: DataTypes.STRING,
				allowNull: true
			},
			unit_3: {
				type: DataTypes.STRING,
				allowNull: true
			},
			title_4: {
				type: DataTypes.STRING,
				allowNull: true
			},
			unit_4: {
				type: DataTypes.STRING,
				allowNull: true
			},
			title_5: {
				type: DataTypes.STRING,
				allowNull: true
			},
			unit_5: {
				type: DataTypes.STRING,
				allowNull: true
			},
			title_6: {
				type: DataTypes.STRING,
				allowNull: true
			},
			unit_6: {
				type: DataTypes.STRING,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'custom_report_additional_items',
			modelName: 'custom_report_additional_items',
			paranoid: true
		}
	);

	CustomReportAdditionalItem.associate = (models) => {
		CustomReportAdditionalItem.belongsToMany(models.CustomReportStepKey, {
			through: 'CustomReportStepKeysAdditionalItem',
			as: 'step_keys',
			foreignKey: 'additional_item_id',
			otherKey: 'custom_report_step_key_id'
		});
		CustomReportAdditionalItem.belongsTo(models.CustomReportAdditionalItemType, {
			foreignKey: 'type_id',
			as: 'additional_item_type'
		});

		CustomReportAdditionalItem.hasMany(models.CustomReportAdditionalItemOption, {
			foreignKey: 'custom_report_additional_item_id',
			as: 'options'
		});
	};

	return CustomReportAdditionalItem;
};
