import { optionsDefaultTable } from './commons/default-options-table.js';
import { FileToErgonomicToolHookManager } from '../helpers/file-to-ergonomic-tool-hook-manager.js';
import { ERGONOMIC_TOOL_NAMES } from '../util/constants-ergonomic-tools.js';

export default (sequelize, DataTypes) => {
	const RebaReport = sequelize.define(
		'RebaReport',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			comment: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			repetition: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			coupling: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			force: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			collection_date: {
				type: DataTypes.DATE,
				allowNull: true
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			},
			trunk: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: null
			},
			neck: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: null
			},
			left_lower_arm: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: null
			},
			right_lower_arm: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: null
			},
			left_upper_arm: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: null
			},
			right_upper_arm: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: null
			},
			left_knee: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: null
			},
			right_knee: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: null
			},
			left_ankle: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: null
			},
			right_ankle: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: null
			},
			hip: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: null
			},
			score_seconds: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: null
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'reba_reports',
			modelName: 'reba_reports',
			hooks: {
				afterCreate: async (instance) => {
					await FileToErgonomicToolHookManager.afterCreate({
						instance,
						tool_name: ERGONOMIC_TOOL_NAMES.REBA,
						sequelize
					});
				},
				afterDestroy: async (instance) => {
					await FileToErgonomicToolHookManager.afterDestroy({
						instance,
						sequelize
					});
				},
				afterBulkDestroy: async (options) => {
					await FileToErgonomicToolHookManager.afterBulkDestroy({
						options,
						sequelize
					});
				}
			}
		}
	);
	RebaReport.associate = (models) => {
		RebaReport.belongsTo(models.File, {
			foreignKey: 'file_id',
			as: 'file'
		});
		RebaReport.belongsTo(models.User, {
			foreignKey: 'report_user_id',
			as: 'report_user'
		});
	};
	return RebaReport;
};
