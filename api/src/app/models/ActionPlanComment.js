import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const ActionPlanComment = sequelize.define(
		'ActionPlanComment',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV4,
				primaryKey: true
			},
			description: {
				type: DataTypes.TEXT,
				allowNull: false
			},
			user_id: {
				type: DataTypes.UUID,
				allowNull: false
			},
			action_plan_id: {
				type: DataTypes.UUID,
				allowNull: false
			},
			created_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			updated_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			deleted_at: {
				type: DataTypes.DATE,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'action_plan_comments',
			modelName: 'action_plan_comments',
			paranoid: true
		}
	);

	ActionPlanComment.associate = (models) => {
		ActionPlanComment.belongsTo(models.ActionPlanV2, {
			foreignKey: 'action_plan_id',
			as: 'action_plan'
		});
		ActionPlanComment.belongsTo(models.User, {
			foreignKey: 'user_id',
			as: 'user'
		});
	};

	return ActionPlanComment;
};
