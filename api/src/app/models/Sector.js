import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const Sector = sequelize.define(
		'Sector',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'sectors',
			modelName: 'sectors'
		}
	);

	Sector.associate = (models) => {
		Sector.hasMany(models.RecoveryReport, {
			as: 'recovery_report',
			foreignKey: 'sector_id'
		});
		Sector.hasMany(models.Line, {
			foreignKey: 'sector_id',
			as: 'line'
		});
		Sector.belongsTo(models.Company, {
			as: 'Company',
			foreignKey: 'company_id'
		});

		// Deprecado
		Sector.belongsTo(models.Company, {
			as: 'company',
			foreignKey: 'company_id'
		});
	};

	return Sector;
};
