import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const Company = sequelize.define(
		'Company',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			url_logo: {
				type: DataTypes.STRING,
				allowNull: true
			},
			isActive: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'companies',
			modelName: 'companies'
		}
	);

	Company.associate = (models) => {
		Company.belongsTo(models.Organization, {
			foreignKey: 'organization_id',
			as: 'Organization'
		});
		Company.belongsTo(models.Organization, {
			foreignKey: 'organization_id',
			as: 'organization'
		});

		Company.hasOne(models.File, {
			foreignKey: 'company_id',
			as: 'file'
		});
		Company.hasMany(models.Sector, {
			foreignKey: 'company_id',
			as: 'sector'
		});
		Company.hasMany(models.Cycle, {
			foreignKey: 'company_id',
			as: 'cycle'
		});
		Company.hasMany(models.Evaluator, {
			foreignKey: 'company_id',
			as: 'evaluator'
		});
		Company.hasMany(models.UserAccess, {
			as: 'UserAccess'
		});
		Company.hasOne(models.BusinessInformation, {
			foreignKey: 'company_id',
			as: 'business_information'
		});
	};

	return Company;
};
