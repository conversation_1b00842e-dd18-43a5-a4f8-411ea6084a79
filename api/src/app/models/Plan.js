import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const Plan = sequelize.define(
		'Plan',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			amount_yearly: {
				type: DataTypes.FLOAT,
				allowNull: false,
				defaultValue: 0
			},
			max_minutes: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			max_users: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			amount: {
				type: DataTypes.FLOAT,
				allowNull: false
			},
			plan_external_id: {
				type: DataTypes.STRING,
				allowNull: true,
				unique: true
			},
			max_upload: {
				type: DataTypes.INTEGER,
				allowNull: false,
				defaultValue: 4
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'plans',
			modelName: 'plans'
		}
	);

	Plan.associate = (models) => {
		Plan.belongsTo(models.Product, {
			foreignKey: 'product_id',
			as: 'product'
		});
		Plan.belongsTo(models.Voucher, {
			foreignKey: 'voucher_id',
			as: 'voucher'
		});
	};

	return Plan;
};
