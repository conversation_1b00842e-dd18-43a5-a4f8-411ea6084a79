import { optionsDefaultTable } from './commons/default-options-table.js';
import { CUSTOM_REPORT_NAMES } from '../util/constants-custom-report.js';

export default (sequelize, DataTypes) => {
	const BeraReport = sequelize.define(
		'BeraReport',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			operator_evaluated: {
				type: DataTypes.STRING,
				allowNull: true
			},
			work_center: {
				type: DataTypes.STRING,
				allowNull: true
			},
			task_time: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			task_time_format: {
				defaultValue: 'seconds',
				type: DataTypes.ENUM({
					values: ['minutes', 'seconds']
				}),
				allowNull: false
			},
			cycle_time: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			has_known_injury: {
				type: DataTypes.BOOLEAN,
				allowNull: true
			},
			collection_date: {
				type: DataTypes.DATE,
				allowNull: true
			},
			consolidated: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: false
			},
			comment: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			severity: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			exposure: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			vulnerability: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			rpn: {
				type: DataTypes.INTEGER,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'bera_reports',
			modelName: 'bera_reports',
			paranoid: true,
			hooks: {
				afterCreate: async (instance) => {
					const { id, file_id } = instance;
					try {
						const custom_report = await sequelize.models.CustomReport.findOne({
							where: {
								name: CUSTOM_REPORT_NAMES.BERA
							},
							attributes: ['id'],
							plain: true,
							raw: true
						});

						const custom_report_id = custom_report?.id;

						await sequelize.models.FileToCustomReport.create({
							file_id,
							report_id: id,
							custom_report_id
						});
					} catch (error) {
						throw error;
					}
				},
				afterDestroy: async (instance) => {
					const { id } = instance;
					try {
						await sequelize.models.FileToCustomReport.destroy({
							where: {
								report_id: id
							}
						});
					} catch (error) {
						throw error;
					}
				},
				afterBulkDestroy: async (instance) => {
					const { where } = instance;
					try {
						const deleted_reports = await sequelize.models.BeraReport.findAll({
							where,
							attributes: ['id'],
							paranoid: false
						});

						for (const report of deleted_reports) {
							await sequelize.models.FileToCustomReport.destroy({
								where: {
									report_id: report.id
								}
							});
						}
					} catch (error) {
						throw error;
					}
				}
			}
		}
	);

	BeraReport.associate = (models) => {
		BeraReport.belongsTo(models.Evaluator, {
			foreignKey: 'evaluator_id',
			as: 'evaluator'
		});
		BeraReport.belongsTo(models.Task, {
			foreignKey: 'task_id',
			as: 'task'
		});
		BeraReport.belongsTo(models.File, {
			foreignKey: 'file_id',
			as: 'file'
		});
		BeraReport.belongsTo(models.BeraJobSummary, {
			foreignKey: 'bera_job_summary_id',
			as: 'bera_job_summary'
		});
		BeraReport.hasMany(models.BeraStepKeyResult, {
			foreignKey: 'bera_report_id',
			as: 'bera_step_key_result'
		});
	};

	return BeraReport;
};
