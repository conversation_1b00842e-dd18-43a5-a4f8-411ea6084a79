import config from 'config';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { optionsDefaultTable } from './commons/default-options-table.js';

const SECRET = config.get('App.jwt.secret');
const REFRESH_SECRET = config.get('App.jwt.refresh-secret');

export default (sequelize, DataTypes) => {
	const User = sequelize.define(
		'User',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			email: {
				type: DataTypes.STRING,
				allowNull: false,
				unique: true,
				validate: {
					isEmail: { msg: 'Email address must be valid' },
					isLowercase: true
				}
			},
			password: {
				type: DataTypes.STRING,
				allowNull: false
			},
			passwordResetToken: {
				type: DataTypes.STRING,
				allowNull: true,
				field: 'password_reset_token'
			},
			passwordResetExpire: {
				type: DataTypes.DATE,
				allowNull: true,
				field: 'password_reset_expire'
			},
			expirationPlan: {
				type: DataTypes.DATE,
				allowNull: true,
				field: 'expiration_plan'
			},
			role: {
				type: DataTypes.ENUM({
					values: ['USER', 'SUPERVISOR', 'MASTER', 'ADMIN']
				}),
				allowNull: false,
				defaultValue: 'USER'
			},
			isActive: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			created_at: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'users',
			modelName: 'users',
			hooks: {
				beforeCreate: async (user) => {
					if (user.password) {
						user.password = await bcrypt.hash(user.password, 8);
					}
				},
				beforeValidate: (user) => {
					if (user.email) {
						user.email = user.email.toLowerCase();
					}
				},
				afterCreate: (record) => {
					['createdAt', 'updatedAt', 'isActive', 'password'].forEach(
						(prop) => delete record.dataValues[prop]
					);
				}
			},
			scopes: {
				withoutResetPassword: {
					attributes: {
						exclude: ['passwordResetToken', 'passwordResetExpire', 'createdAt', 'updatedAt']
					}
				}
			}
		}
	);

	User.prototype.hashPassword = async function (password) {
		return await bcrypt.hash(password, 8);
	};

	User.prototype.checkPassword = function (password) {
		return bcrypt.compare(password, this.password);
	};

	User.prototype.generateToken = function () {
		return jwt.sign({ id: this.id, role: this.role }, SECRET, {
			expiresIn: '3h'
		});
	};
	User.prototype.generateRefreshToken = function () {
		return jwt.sign({ id: this.id, isRefresh: true }, REFRESH_SECRET, {
			expiresIn: '14d'
		});
	};

	User.associate = (models) => {
		User.hasMany(models.UserAccess, {
			foreignKey: 'user_id',
			as: 'user_accesses'
		});
		User.hasMany(models.RecoveryReport, {
			foreignKey: 'user_id',
			as: 'recovery_report'
		});
		User.belongsTo(models.UserPlan, {
			// foreignKey: 'user_plan_id', as: 'user_plans'
			foreignKey: 'user_plan_id',
			as: 'userPlan'
		});
		User.belongsTo(models.CustomerInformation, {
			// foreignKey: 'user_plan_id', as: 'user_plans'
			foreignKey: 'customer_information_id',
			as: 'customer_info'
		});
		User.hasMany(models.File, {
			foreignKey: 'user_id',
			as: 'File'
		});

		User.hasMany(models.File, {
			foreignKey: 'user_id',
			as: 'file'
		});
		User.belongsTo(models.CustomerInformation, {
			// foreignKey: 'user_plan_id', as: 'user_plans'
			foreignKey: 'customer_information_id',
			as: 'customer'
		});
		User.hasMany(models.KimPushPullReport, {
			foreignKey: 'report_user_id',
			as: 'kim_push_pull_report'
		});
		User.hasMany(models.LibertyMutualReport, {
			foreignKey: 'report_user_id',
			as: 'liberty_mutual_report'
		});
		User.hasMany(models.LibertyMutualReportInput, {
			foreignKey: 'report_user_id',
			as: 'liberty_mutual_report_input'
		});

		User.hasOne(models.SuperPeaReport, {
			foreignKey: 'user_id',
			as: 'super_pea_report'
		});

		User.hasOne(models.TwoFactorAuthentication, {
			foreignKey: 'user_id',
			as: '2fa'
		});

		User.hasMany(models.TwoFactorRecoveryTokens, {
			foreignKey: 'user_id',
			as: '2fa_tokens'
		});
		User.hasMany(models.BeraJobSummary, {
			foreignKey: 'user_id',
			as: 'bera_job_summary'
		});
		User.hasMany(models.SeraSummary, {
			foreignKey: 'user_id',
			as: 'sera_summary'
		});
		User.hasMany(models.SeraSummaryReview, {
			foreignKey: 'user_id',
			as: 'sera_summary_review'
		});
	};

	return User;
};
