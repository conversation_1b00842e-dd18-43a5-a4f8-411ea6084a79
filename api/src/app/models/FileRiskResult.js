import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const FileRiskResult = sequelize.define(
		'FileRiskResult',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			risk: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			percentage: {
				type: DataTypes.FLOAT(2),
				allowNull: false,
				defaultValue: 0
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'file_risk_results',
			modelName: 'file_risk_results',
			paranoid: true
		}
	);

	FileRiskResult.associate = (models) => {
		FileRiskResult.belongsTo(models.File, {
			foreignKey: 'file_id',
			as: 'file'
		});
		FileRiskResult.belongsTo(models.BodyPart, {
			foreignKey: 'body_part_id',
			as: 'body_part'
		});
		FileRiskResult.belongsTo(models.RangeRisk, {
			foreignKey: 'range_risk_id',
			as: 'range_risk'
		});
	};

	return FileRiskResult;
};
