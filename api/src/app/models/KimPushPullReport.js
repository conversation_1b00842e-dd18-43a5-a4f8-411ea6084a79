import { optionsDefaultTable } from './commons/default-options-table.js';
import { ERGONOMIC_TOOL_NAMES } from '../util/constants-ergonomic-tools.js';
import { FileToErgonomicToolHookManager } from '../helpers/file-to-ergonomic-tool-hook-manager.js';

export default (sequelize, DataTypes) => {
	const KimPushPullReport = sequelize.define(
		'KimPushPullReport',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			workstation: {
				type: DataTypes.STRING,
				allowNull: false
			},
			gender: {
				type: DataTypes.TINYINT,
				allowNull: false
			},
			pair: {
				type: DataTypes.TINYINT,
				allowNull: false
			},
			distance_or_duration: {
				type: DataTypes.TINYINT,
				allowNull: false
			},
			vehicle: {
				type: DataTypes.TINYINT,
				allowNull: false
			},
			mass: {
				type: DataTypes.TINYINT,
				allowNull: false
			},
			driveway_conditions: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: null
			},
			inclination_or_stairs: {
				type: DataTypes.TINYINT,
				allowNull: false
			},
			working_conditions: {
				type: DataTypes.JSON,
				allowNull: false
			},
			properties: {
				type: DataTypes.JSON,
				allowNull: false
			},
			posture: {
				type: DataTypes.TINYINT,
				allowNull: false
			},
			temporal_distribution: {
				type: DataTypes.TINYINT,
				allowNull: false
			},
			collection_date: {
				type: DataTypes.DATE,
				field: 'collection_date'
			},
			score: {
				type: DataTypes.FLOAT,
				allowNull: false
			},
			comment: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			isActive: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'kim_push_pull_reports',
			modelName: 'kim_push_pull_reports',
			hooks: {
				afterCreate: async (instance) => {
					await FileToErgonomicToolHookManager.afterCreate({
						instance,
						tool_name: ERGONOMIC_TOOL_NAMES.KIM_PP,
						sequelize
					});
				},
				afterDestroy: async (instance) => {
					await FileToErgonomicToolHookManager.afterDestroy({
						instance,
						sequelize
					});
				},
				afterBulkDestroy: async (options) => {
					await FileToErgonomicToolHookManager.afterBulkDestroy({
						options,
						sequelize
					});
				}
			}
		}
	);

	KimPushPullReport.associate = function (models) {
		KimPushPullReport.belongsTo(models.File, {
			foreignKey: 'file_id',
			as: 'file'
		});
		KimPushPullReport.belongsTo(models.User, {
			foreignKey: 'report_user_id',
			as: 'report_user'
		});
		KimPushPullReport.belongsTo(models.Sector, {
			foreignKey: 'sector_id',
			as: 'sector'
		});
	};

	return KimPushPullReport;
};
