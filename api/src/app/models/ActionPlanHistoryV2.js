import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const ActionPlanHistoryV2 = sequelize.define(
		'ActionPlanHistoryV2',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV4,
				primaryKey: true
			},
			user_id: {
				type: DataTypes.UUID,
				allowNull: false
			},
			description: {
				type: DataTypes.STRING,
				allowNull: true
			},
			action_plan_id: {
				type: DataTypes.UUID,
				allowNull: false
			},
			action_plan_history_type_id: {
				type: DataTypes.UUID,
				allowNull: false
			},
			created_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			updated_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			deleted_at: {
				type: DataTypes.DATE,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'action_plan_histories',
			modelName: 'action_plan_histories',
			paranoid: true
		}
	);

	ActionPlanHistoryV2.associate = (models) => {
		ActionPlanHistoryV2.belongsTo(models.User, {
			foreignKey: 'user_id',
			as: 'user'
		});
		ActionPlanHistoryV2.belongsTo(models.ActionPlanV2, {
			foreignKey: 'action_plan_id',
			as: 'action_plan'
		});
		ActionPlanHistoryV2.belongsTo(models.ActionPlanHistoryType, {
			foreignKey: 'action_plan_history_type_id',
			as: 'history_type'
		});
	};

	return ActionPlanHistoryV2;
};
