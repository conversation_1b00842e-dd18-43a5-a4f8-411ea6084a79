import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const WorkConditionResult = sequelize.define(
		'WorkConditionResult',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			work_schedule: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			place_description: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			expected_task_description: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			performed_task_description: {
				type: DataTypes.TEXT,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'work_condition_results',
			modelName: 'work_condition_results',
			paranoid: true
		}
	);

	WorkConditionResult.associate = (models) => {
		WorkConditionResult.belongsTo(models.Workstation, {
			foreignKey: 'workstation_id',
			as: 'workstation'
		});
	};

	return WorkConditionResult;
};
