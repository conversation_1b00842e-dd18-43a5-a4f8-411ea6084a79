import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const SeraSummaryFiles = sequelize.define(
		'SeraSummaryFiles',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			sera_summary_id: {
				type: DataTypes.STRING,
				allowNull: false
			},
			file_id: {
				type: DataTypes.STRING,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'sera_summary_files',
			modelName: 'sera_summary_files',
			paranoid: true
		}
	);

	SeraSummaryFiles.associate = (models) => {
		SeraSummaryFiles.belongsTo(models.SeraSummary, {
			foreignKey: 'sera_summary_id',
			as: 'sera_summary'
		});

		SeraSummaryFiles.belongsTo(models.File, {
			foreignKey: 'file_id',
			as: 'file'
		});
	};

	return SeraSummaryFiles;
};
