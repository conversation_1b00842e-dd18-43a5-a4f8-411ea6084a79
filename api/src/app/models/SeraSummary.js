import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const SeraSummary = sequelize.define(
		'SeraSummary',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			report_name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			status: {
				type: DataTypes.ENUM({
					values: ['COMPLETED', 'UNCOMPLETED', 'REVIEWED']
				}),
				allowNull: false,
				defaultValue: 'UNCOMPLETED'
			},
			comment: {
				type: DataTypes.TEXT,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'sera_summaries',
			modelName: 'sera_summaries',
			paranoid: true
		}
	);

	SeraSummary.associate = (models) => {
		SeraSummary.belongsTo(models.Evaluator, {
			foreignKey: 'evaluator_id',
			as: 'evaluator'
		});
		SeraSummary.belongsTo(models.Cycle, {
			foreignKey: 'cycle_id',
			as: 'cycle'
		});
		SeraSummary.belongsTo(models.User, {
			foreignKey: 'user_id',
			as: 'sera_responsible'
		});
		SeraSummary.hasMany(models.SeraSummaryReview, {
			foreignKey: 'sera_summary_id',
			as: 'review'
		});
		SeraSummary.hasMany(models.SeraReviewSelector, {
			foreignKey: 'sera_summary_id',
			as: 'sera_review_selector'
		});
		SeraSummary.hasMany(models.SeraSummaryFiles, {
			foreignKey: 'sera_summary_id',
			as: 'sera_summary_files'
		});
	};

	return SeraSummary;
};
