import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const Voucher = sequelize.define(
		'Voucher',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false,
				validate: {
					max: 6,
					min: 20
				}
			},
			percentage: {
				type: DataTypes.INTEGER,
				allowNull: false,
				validate: {
					max: 99,
					min: 1
				}
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'vouchers',
			modelName: 'vouchers',
			hooks: {
				beforeCreate: async (voucher) => {
					voucher.name = voucher.name.toUpperCase();
				}
			}
		}
	);

	Voucher.associate = (models) => {
		Voucher.hasMany(models.Plan, {
			foreignKey: 'voucher_id',
			as: 'plans'
		});
	};

	return Voucher;
};
