import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const LibertyMutualReportInput = sequelize.define(
		'LibertyMutualReportInput',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			report_name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			frequency_time_format: {
				type: DataTypes.ENUM({
					values: ['HOUR', 'MINUTE']
				}),
				allowNull: false,
				defaultValue: 'MINUTE'
			},
			frequency: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			hand_coupling: {
				type: DataTypes.ENUM({
					values: ['GOOD', 'FAIR', 'POOR']
				}),
				allowNull: true
			},
			object_weight: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			start_hand_height: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			end_hand_height: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			start_hand_distance: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			end_hand_distance: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			initial_force: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			sustained_force: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			horizontal_distance: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			vertical_hand_height: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			percentile_man: {
				type: DataTypes.STRING,
				allowNull: true
			},
			percentile_woman: {
				type: DataTypes.STRING,
				allowNull: true
			},
			percentile_man_initial: {
				type: DataTypes.STRING,
				allowNull: true
			},
			percentile_woman_initial: {
				type: DataTypes.STRING,
				allowNull: true
			},
			percentile_man_sustain: {
				type: DataTypes.STRING,
				allowNull: true
			},
			percentile_woman_sustain: {
				type: DataTypes.STRING,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'liberty_mutual_report_inputs',
			modelName: 'liberty_mutual_report_inputs',
			paranoid: true
		}
	);

	LibertyMutualReportInput.associate = (models) => {
		LibertyMutualReportInput.belongsTo(models.User, {
			foreignKey: 'report_user_id',
			as: 'user'
		});
		LibertyMutualReportInput.belongsTo(models.LibertyMutualTask, {
			foreignKey: 'task_id',
			as: 'task'
		});
		LibertyMutualReportInput.belongsTo(models.LibertyMutualReport, {
			foreignKey: 'liberty_mutual_report_id',
			as: 'report'
		});
	};

	return LibertyMutualReportInput;
};
