import { optionsDefaultTable } from './commons/default-options-table.js';
import { ERGONOMIC_TOOL_NAMES } from '../util/constants-ergonomic-tools.js';
import { FileToErgonomicToolHookManager } from '../helpers/file-to-ergonomic-tool-hook-manager.js';

export default (sequelize, DataTypes) => {
	const AngleTimeReport = sequelize.define(
		'AngleTimeReport',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			comment: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			collection_date: {
				type: DataTypes.DATE,
				allowNull: true
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			body_parts: {
				type: DataTypes.JSON,
				allowNull: true
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'angle_time_reports',
			modelName: 'angle_time_reports',
			hooks: {
				afterCreate: async (instance) => {
					await FileToErgonomicToolHookManager.afterCreate({
						instance,
						tool_name: ERGONOMIC_TOOL_NAMES.ANGLE_TIME,
						sequelize
					});
				},
				afterDestroy: async (instance) => {
					await FileToErgonomicToolHookManager.afterDestroy({
						instance,
						sequelize
					});
				},
				afterBulkDestroy: async (options) => {
					await FileToErgonomicToolHookManager.afterBulkDestroy({
						options,
						sequelize
					});
				}
			}
		}
	);

	AngleTimeReport.associate = (models) => {
		AngleTimeReport.belongsTo(models.File, {
			foreignKey: 'file_id',
			as: 'file'
		});
		AngleTimeReport.belongsTo(models.User, {
			foreignKey: 'report_user_id',
			as: 'report_user'
		});
		AngleTimeReport.belongsTo(models.RangeRisk, {
			foreignKey: 'range_risk_id',
			as: 'RangeRisk'
		});
	};

	return AngleTimeReport;
};
