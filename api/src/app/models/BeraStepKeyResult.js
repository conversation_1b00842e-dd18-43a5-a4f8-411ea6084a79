import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const BeraStepKeyResult = sequelize.define(
		'BeraStepKeyResult',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			task_rpn: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			pre_populate: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: false
			},
			job_element: {
				type: DataTypes.TEXT,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'bera_step_key_results',
			modelName: 'bera_step_key_results',
			paranoid: true
		}
	);

	BeraStepKeyResult.associate = (models) => {
		BeraStepKeyResult.belongsTo(models.BeraReport, {
			foreignKey: 'bera_report_id',
			as: 'bera_report'
		});
		BeraStepKeyResult.belongsTo(models.StressLevel, {
			foreignKey: 'video_analysis_stress_level_id',
			as: 'video_stress_level'
		});
		BeraStepKeyResult.belongsTo(models.StressLevel, {
			foreignKey: 'stress_level_id',
			as: 'stress_level'
		});
		BeraStepKeyResult.belongsTo(models.Frequency, {
			foreignKey: 'video_analysis_frequency_id',
			as: 'video_frequency'
		});
		BeraStepKeyResult.belongsTo(models.Frequency, {
			foreignKey: 'frequency_id',
			as: 'frequency'
		});
		BeraStepKeyResult.belongsTo(models.TotalTaskDuration, {
			foreignKey: 'video_analysis_total_task_duration_id',
			as: 'video_total_task_duration'
		});
		BeraStepKeyResult.belongsTo(models.TotalTaskDuration, {
			foreignKey: 'total_task_duration_id',
			as: 'total_task_duration'
		});
		BeraStepKeyResult.belongsTo(models.CustomReportStepKey, {
			foreignKey: 'custom_report_step_key_id',
			as: 'custom_report_step_key'
		});
	};

	return BeraStepKeyResult;
};
