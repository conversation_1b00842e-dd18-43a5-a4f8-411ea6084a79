import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const RiskDescription = sequelize.define(
		'RiskDescription',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.TEXT,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'risk_descriptions',
			modelName: 'risk_descriptions',
			paranoid: true
		}
	);

	RiskDescription.associate = (models) => {
		RiskDescription.belongsTo(models.RiskCategory, {
			foreignKey: 'risk_category_id',
			as: 'risk_category'
		});
		RiskDescription.hasMany(models.SeraReport, {
			foreignKey: 'risk_description_id',
			as: 'sera_report'
		});
		RiskDescription.hasOne(models.RiskDamage, {
			foreignKey: 'risk_description_id',
			as: 'risk_damage'
		});
		RiskDescription.hasMany(models.CustomReportStepKeyResult, {
			foreignKey: 'risk_description_id',
			as: 'step_key_result'
		});
		RiskDescription.hasMany(models.CustomReportSubStepKeyResult, {
			foreignKey: 'risk_description_id',
			as: 'sub_step_key_result'
		});
		RiskDescription.hasMany(models.CustomReportStepKeysDefaultRisk, {
			foreignKey: 'risk_description_id',
			as: 'step_key_default_risk'
		});
		RiskDescription.hasMany(models.CustomReportSubStepKeysDefaultRisk, {
			foreignKey: 'risk_description_id',
			as: 'sub_step_key_default_risk'
		});
	};

	return RiskDescription;
};
