import { optionsDefaultTable } from './commons/default-options-table.js';
import { ERGONOMIC_TOOL_NAMES } from '../util/constants-ergonomic-tools.js';
import { FileToErgonomicToolHookManager } from '../helpers/file-to-ergonomic-tool-hook-manager.js';

export default (sequelize, DataTypes) => {
	const BackCompressiveForceEstimationReport = sequelize.define(
		'BackCompressiveForceEstimationReport',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			person_weight: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			person_height: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			trunk_angle_to_horizontal: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			load_in_hands: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			horizontal_distance_from_hands_to_low_back: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			total_compressive_force_result: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			comment: {
				type: DataTypes.TEXT,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			paranoid: true,
			tableName: 'back_compressive_force_estimation_reports',
			modelName: 'back_compressive_force_estimation_reports',
			hooks: {
				afterCreate: async (instance) => {
					await FileToErgonomicToolHookManager.afterCreate({
						instance,
						tool_name: ERGONOMIC_TOOL_NAMES.BACK_COMPRESSIVE_FORCE_ESTIMATION,
						sequelize
					});
				},
				afterDestroy: async (instance) => {
					await FileToErgonomicToolHookManager.afterDestroy({
						instance,
						sequelize
					});
				},
				afterBulkDestroy: async (options) => {
					await FileToErgonomicToolHookManager.afterBulkDestroy({
						options,
						sequelize
					});
				}
			}
		}
	);

	BackCompressiveForceEstimationReport.associate = (models) => {
		BackCompressiveForceEstimationReport.belongsTo(models.User, {
			foreignKey: 'report_user_id',
			as: 'user'
		});
		BackCompressiveForceEstimationReport.belongsTo(models.File, {
			foreignKey: 'file_id',
			as: 'file'
		});
		BackCompressiveForceEstimationReport.belongsTo(models.SystemOfUnits, {
			foreignKey: 'system_of_units_id',
			as: 'system_of_units'
		});
	};

	return BackCompressiveForceEstimationReport;
};
