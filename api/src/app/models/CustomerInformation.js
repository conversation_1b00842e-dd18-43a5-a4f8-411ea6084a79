import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomerInformation = sequelize.define(
		'CustomerInformation',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			email: {
				type: DataTypes.STRING,
				allowNull: false,
				unique: true,
				validate: {
					isEmail: true,
					isLowercase: true
				}
			},
			zipcode: {
				type: DataTypes.STRING,
				allowNull: true
			},
			address: {
				type: DataTypes.STRING,
				allowNull: true
			},
			document: {
				type: DataTypes.STRING,
				allowNull: true
			},
			phone_number: {
				type: DataTypes.STRING,
				allowNull: true
			},
			token_transaction: {
				type: DataTypes.STRING,
				allowNull: true
			},
			street_number: {
				type: DataTypes.STRING,
				allowNull: true
			},
			country: {
				type: DataTypes.STRING,
				allowNull: false
			},
			state: {
				type: DataTypes.STRING,
				allowNull: false
			},
			city: {
				type: DataTypes.STRING,
				allowNull: false
			},
			expiration_plan: {
				type: DataTypes.DATE,
				allowNull: true
			},
			canceled_on: {
				type: DataTypes.BOOLEAN,
				allowNull: true
			},
			logo: {
				type: DataTypes.STRING,
				allowNull: true,
				defaultValue: 'https://kinebot-statics.s3.amazonaws.com/kinebot-report.png'
			},
			force_two_fa: {
				type: DataTypes.BOOLEAN,
				allowNull: true
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'customer_informations',
			modelName: 'customer_informations'
		}
	);

	CustomerInformation.addHook('beforeValidate', (customer, options) => {
		customer.document = customer.document?.replace(/[^\d]+/g, '');
	});

	CustomerInformation.associate = (models) => {
		CustomerInformation.hasOne(models.CustomerCredits, {
			foreignKey: 'customer_id',
			as: 'customer_credits'
		});
		CustomerInformation.hasOne(models.CustomerPlan, {
			foreignKey: 'customer_id',
			as: 'customer_plan'
		});
		CustomerInformation.hasMany(models.RangeRisk, {
			foreignKey: 'customer_information_id',
			as: 'range_risk'
		});
		CustomerInformation.hasMany(models.User, {
			foreignKey: 'customer_information_id',
			as: 'user'
		});
	};

	return CustomerInformation;
};
