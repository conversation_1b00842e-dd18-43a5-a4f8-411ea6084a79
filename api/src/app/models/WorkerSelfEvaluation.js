import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const WorkerSelfEvaluation = sequelize.define(
		'WorkerSelfEvaluation',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.STRING,
				allowNull: false
			},
			score: {
				type: DataTypes.INTEGER,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'worker_self_evaluations',
			modelName: 'worker_self_evaluations',
			paranoid: true
		}
	);

	WorkerSelfEvaluation.associate = (models) => {
		WorkerSelfEvaluation.hasMany(models.CharacteristicResult, {
			foreignKey: 'worker_self_evaluation_id',
			as: 'characteristics'
		});
	};

	return WorkerSelfEvaluation;
};
