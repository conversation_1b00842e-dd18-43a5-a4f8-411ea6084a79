import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomReportSubStepKeysDefaultRisk = sequelize.define(
		'CustomReportSubStepKeysDefaultRisk',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'custom_report_sub_step_keys_default_risks',
			modelName: 'custom_report_sub_step_keys_default_risks',
			paranoid: true
		}
	);

	CustomReportSubStepKeysDefaultRisk.associate = (models) => {
		CustomReportSubStepKeysDefaultRisk.belongsTo(models.CustomReportSubStepKey, {
			foreignKey: 'custom_report_sub_step_key_id',
			as: 'step_key'
		});
		CustomReportSubStepKeysDefaultRisk.belongsTo(models.RiskDamage, {
			foreignKey: 'risk_damage_id',
			as: 'risk_damage'
		});
		CustomReportSubStepKeysDefaultRisk.belongsTo(models.RiskDescription, {
			foreignKey: 'risk_description_id',
			as: 'risk_description'
		});
		CustomReportSubStepKeysDefaultRisk.belongsTo(models.RiskCategory, {
			foreignKey: 'risk_category_id',
			as: 'risk_category'
		});
	};

	return CustomReportSubStepKeysDefaultRisk;
};
