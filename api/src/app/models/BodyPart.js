import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const BodyPart = sequelize.define(
		'BodyPart',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.ENUM(
					'neck',
					'trunk',
					'right_upper_arm',
					'left_upper_arm',
					'hip',
					'left_hand',
					'right_hand',
					'left_knee',
					'left_lower_arm',
					'right_lower_arm',
					'right_knee',
					'left_ankle',
					'right_ankle'
				),
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'body_parts',
			modelName: 'body_parts',
			paranoid: true
		}
	);

	return BodyPart;
};
