import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomReportStepKeysAdditionalItem = sequelize.define(
		'CustomReportStepKeysAdditionalItem',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'custom_report_step_keys_additional_items',
			modelName: 'custom_report_step_keys_additional_items',
			paranoid: true
		}
	);

	CustomReportStepKeysAdditionalItem.associate = (models) => {
		CustomReportStepKeysAdditionalItem.belongsTo(models.CustomReportStepKey, {
			foreignKey: 'custom_report_step_key_id',
			as: 'step_key'
		});
		CustomReportStepKeysAdditionalItem.belongsTo(models.CustomReportAdditionalItem, {
			foreignKey: 'additional_item_id',
			as: 'additional_item'
		});
		CustomReportStepKeysAdditionalItem.hasMany(models.CustomReportStepKeyAdditionalItemResult, {
			foreignKey: 'custom_report_step_key_additional_item_id',
			as: 'additional_item_results'
		});
	};

	return CustomReportStepKeysAdditionalItem;
};
