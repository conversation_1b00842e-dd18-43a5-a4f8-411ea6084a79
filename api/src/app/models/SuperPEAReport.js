import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const SuperPeaReport = sequelize.define(
		'SuperPeaReport',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			conclusion: {
				type: DataTypes.STRING,
				allowNull: true
			},
			pea_ids: {
				type: DataTypes.JSON,
				allowNull: false
			},
			collection_date: {
				type: DataTypes.DATE,
				field: 'collection_date'
			},
			reference_date: {
				type: DataTypes.DATE,
				field: 'reference_date'
			},
			location: {
				type: DataTypes.STRING,
				allowNull: true
			},
			isActive: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'super_pea_reports',
			modelName: 'super_pea_reports'
		}
	);

	SuperPeaReport.associate = function (models) {
		SuperPeaReport.belongsTo(models.User, {
			foreignKey: 'user_id',
			as: 'user'
		});

		SuperPeaReport.belongsToMany(models.PreliminaryAnalysis, {
			through: 'PEAToSuperPEA',
			as: 'preliminary_analysis',
			foreignKey: 'super_pea_id',
			otherKey: 'pea_id'
		});
	};

	return SuperPeaReport;
};
