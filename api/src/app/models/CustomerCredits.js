import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomerCredits = sequelize.define(
		'CustomerCredits',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			minutes: {
				type: DataTypes.INTEGER,
				allowNull: false,
				defaultValue: 0
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'customer_credits',
			modelName: 'customer_credits'
		}
	);

	CustomerCredits.associate = (models) => {
		CustomerCredits.belongsTo(models.CustomerInformation, {
			foreignKey: 'customer_id',
			as: 'customer'
		});
		CustomerCredits.hasMany(models.CustomerTransactions, {
			foreignKey: 'customer_credits_id',
			as: 'customer_transactions'
		});
	};

	return CustomerCredits;
};
