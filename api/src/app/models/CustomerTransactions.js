import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomerTransactions = sequelize.define(
		'CustomerTransactions',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			minutes: {
				type: DataTypes.INTEGER,
				allowNull: false,
				defaultValue: 0
			},
			order_number_id: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			purchase_date: {
				type: DataTypes.DATE,
				allowNull: false
			},
			status: {
				type: DataTypes.STRING,
				allowNull: true
			},
			wc_customer_id: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			product_id: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			payment_transaction_id: {
				type: DataTypes.STRING,
				allowNull: true
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'customer_transactions',
			modelName: 'customer_transactions'
		}
	);

	CustomerTransactions.associate = (models) => {
		CustomerTransactions.belongsTo(models.CustomerCredits, {
			foreignKey: 'customer_credits_id',
			as: 'customer_credits'
		});
		// CustomerTransactions.belongsTo(models.CustomerCredits, {
		// 	foreignKey: 'product_id',
		// 	as: 'product',
		// });

		CustomerTransactions.belongsTo(models.Product, {
			foreignKey: 'product_id',
			as: 'product'
		});
	};

	return CustomerTransactions;
};
