import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const UserPlan = sequelize.define(
		'UserPlan',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			type: {
				type: DataTypes.ENUM({
					values: ['TEST', 'INDIVIDUAL', 'TEAM', 'BUSINESS']
				}),
				allowNull: true,
				defaultValue: 'TEST'
			},
			maxUsers: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: 0,
				field: 'max_users'
			},
			maxMinutes: {
				type: DataTypes.INTEGER,
				allowNull: true,
				defaultValue: 900,
				field: 'max_minutes'
			},
			isActive: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'user_plans',
			modelName: 'user_plans',
			hooks: {
				afterCreate: (record) => {
					['createdAt', 'updatedAt', 'isActive'].forEach((prop) => delete record.dataValues[prop]);
				}
			}
		}
	);

	return UserPlan;
};
