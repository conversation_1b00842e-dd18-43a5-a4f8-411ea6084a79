import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const ActionPlanTask = sequelize.define(
		'ActionPlanTask',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV4,
				primaryKey: true
			},
			title: {
				type: DataTypes.STRING,
				allowNull: true
			},
			due_date: {
				type: DataTypes.DATE,
				allowNull: true
			},
			is_completed: {
				type: DataTypes.BOOLEAN,
				allowNull: true
			},
			user_id: {
				type: DataTypes.UUID,
				allowNull: false
			},
			responsible_user_id: {
				type: DataTypes.UUID,
				allowNull: true
			},
			action_plan_id: {
				type: DataTypes.UUID,
				allowNull: false
			},
			created_at: {
				type: DataTypes.DATE,
				allowNull: true
			},
			updated_at: {
				type: DataTypes.DATE,
				allowNull: true
			},
			deleted_at: {
				type: DataTypes.DATE,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'action_plan_tasks',
			modelName: 'action_plan_tasks',
			paranoid: true
		}
	);

	ActionPlanTask.associate = (models) => {
		ActionPlanTask.belongsTo(models.ActionPlanV2, {
			foreignKey: 'action_plan_id',
			as: 'action_plan'
		});
		ActionPlanTask.belongsTo(models.User, {
			foreignKey: 'user_id',
			as: 'user'
		});
		ActionPlanTask.belongsTo(models.User, {
			foreignKey: 'responsible_user_id',
			as: 'responsible_user'
		});
	};

	return ActionPlanTask;
};
