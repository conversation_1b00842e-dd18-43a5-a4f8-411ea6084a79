import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const Cycle = sequelize.define(
		'Cycle',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'cycles',
			modelName: 'cycles',
			paranoid: true
		}
	);

	Cycle.associate = (models) => {
		Cycle.belongsToMany(models.Task, {
			through: 'TasksCycles',
			as: 'task',
			foreignKey: 'cycle_id',
			otherKey: 'task_id'
		});
		Cycle.belongsTo(models.Company, {
			as: 'company',
			foreignKey: 'company_id'
		});

		Cycle.hasMany(models.BeraJobSummary, {
			foreignKey: 'cycle_id',
			as: 'bera_job_summary'
		});
	};

	return Cycle;
};
