import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CharacteristicResult = sequelize.define(
		'CharacteristicResult',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			working_population_men: {
				type: DataTypes.INTEGER,
				defaultValue: 0,
				allowNull: true
			},
			working_population_women: {
				type: DataTypes.INTEGER,
				defaultValue: 0,
				allowNull: true
			},
			working_population_others: {
				type: DataTypes.INTEGER,
				defaultValue: 0,
				allowNull: true
			},
			total_working_population: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			particularities_description: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			worker_verbalization_description: {
				type: DataTypes.TEXT,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'characteristic_results',
			modelName: 'characteristic_results',
			paranoid: true
		}
	);

	CharacteristicResult.associate = (models) => {
		CharacteristicResult.belongsTo(models.Workstation, {
			foreignKey: 'workstation_id',
			as: 'workstation'
		});
		CharacteristicResult.belongsTo(models.WorkerSelfEvaluation, {
			foreignKey: 'worker_self_evaluation_id',
			as: 'worker_self_evaluation'
		});
	};

	return CharacteristicResult;
};
