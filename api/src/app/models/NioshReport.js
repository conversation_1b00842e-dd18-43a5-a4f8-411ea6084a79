import { optionsDefaultTable } from './commons/default-options-table.js';
import { ERGONOMIC_TOOL_NAMES } from '../util/constants-ergonomic-tools.js';
import { FileToErgonomicToolHookManager } from '../helpers/file-to-ergonomic-tool-hook-manager.js';

export default (sequelize, DataTypes) => {
	const NioshReport = sequelize.define(
		'NioshReport',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			report_type: {
				type: DataTypes.ENUM(['ISO_11228', 'NIOSH']),
				allowNull: false
			},
			gender: {
				type: DataTypes.ENUM(['MALE', 'FEMALE']),
				allowNull: true
			},
			age: {
				type: DataTypes.ENUM(['LESS_THAN_20', 'BETWEEN_20_AND_45', 'MORE_THAN_45']),
				allowNull: true
			},
			workers: {
				type: DataTypes.INTEGER,
				allowNull: false,
				validate: {
					isIn: [[1, 2, 3]]
				}
			},
			hands: {
				type: DataTypes.INTEGER,
				allowNull: false,
				validate: {
					isIn: [[1, 2]]
				}
			},
			coupling: {
				type: DataTypes.ENUM(['POOR', 'FAIR', 'GOOD']),
				allowNull: false
			},
			frequency: {
				type: DataTypes.DECIMAL(3, 1),
				allowNull: false,
				validate: {
					isIn: [[0.2, 0.5, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]]
				}
			},
			duration: {
				type: DataTypes.ENUM(['LESS_THAN_1H', 'BETWEEN_1_AND_2H', 'BETWEEN_2_AND_8H']),
				allowNull: false
			},
			mass_m: {
				type: DataTypes.DECIMAL(3, 1),
				allowNull: false
			},
			distance_dc: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			dc_factor: {
				type: DataTypes.DECIMAL(3, 2),
				allowNull: false
			},
			distance_vc: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			vc_factor: {
				type: DataTypes.DECIMAL(3, 2),
				allowNull: false
			},
			distance_h: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			h_factor: {
				type: DataTypes.DECIMAL(3, 2),
				allowNull: false
			},
			angle_a: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			a_factor: {
				type: DataTypes.DECIMAL(3, 2),
				allowNull: false
			},
			coupling_factor: {
				type: DataTypes.DECIMAL(3, 2),
				allowNull: false
			},
			frequency_factor: {
				type: DataTypes.DECIMAL(3, 2),
				allowNull: false
			},
			one_handed_factor: {
				type: DataTypes.DECIMAL(3, 2),
				allowNull: false
			},
			multiple_workers_factor: {
				type: DataTypes.DECIMAL(3, 2),
				allowNull: false
			},
			reference_weight: {
				type: DataTypes.INTEGER,
				allowNull: false,
				validate: {
					isIn: [[15, 20, 23, 25]]
				}
			},
			recommended_weight_limit: {
				type: DataTypes.DECIMAL(3, 1),
				allowNull: false
			},
			lifting_index: {
				type: DataTypes.DECIMAL(4, 2),
				allowNull: false
			},
			risk: {
				type: DataTypes.ENUM(['VERY_LOW', 'LOW', 'MODERATE', 'HIGH', 'VERY_HIGH']),
				allowNull: false
			},
			collection_date: {
				type: DataTypes.DATE,
				allowNull: false
			},
			comment: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			isActive: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'niosh_reports',
			modelName: 'niosh_reports',
			hooks: {
				afterCreate: async (instance) => {
					await FileToErgonomicToolHookManager.afterCreate({
						instance,
						tool_name: ERGONOMIC_TOOL_NAMES.NIOSH,
						sequelize
					});
				},
				afterDestroy: async (instance) => {
					await FileToErgonomicToolHookManager.afterDestroy({
						instance,
						sequelize
					});
				},
				afterBulkDestroy: async (options) => {
					await FileToErgonomicToolHookManager.afterBulkDestroy({
						options,
						sequelize
					});
				}
			}
		}
	);

	NioshReport.associate = (models) => {
		NioshReport.belongsTo(models.File, {
			foreignKey: 'file_id',
			as: 'file'
		});
		NioshReport.belongsTo(models.User, {
			foreignKey: 'report_user_id',
			as: 'report_user'
		});
	};

	return NioshReport;
};
