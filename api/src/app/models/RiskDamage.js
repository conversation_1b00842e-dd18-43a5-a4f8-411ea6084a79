import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const RiskDamage = sequelize.define(
		'RiskDamage',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.STRING,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'risk_damages',
			modelName: 'risk_damages',
			paranoid: true
		}
	);

	RiskDamage.associate = (models) => {
		RiskDamage.belongsTo(models.Severity, {
			foreignKey: 'severity_id',
			as: 'severity'
		});
		RiskDamage.belongsTo(models.CustomReportStepKey, {
			foreignKey: 'custom_report_step_key_id',
			as: 'step_key'
		});
		RiskDamage.belongsTo(models.RiskDescription, {
			foreignKey: 'risk_description_id',
			as: 'risk_description'
		});

		RiskDamage.hasMany(models.SeraReport, {
			foreignKey: 'risk_damage_id',
			as: 'sera_report'
		});
		RiskDamage.hasMany(models.CustomReportStepKeyResult, {
			foreignKey: 'risk_damage_id',
			as: 'step_key_result'
		});
		RiskDamage.hasMany(models.CustomReportSubStepKeyResult, {
			foreignKey: 'risk_damage_id',
			as: 'sub_step_key_result'
		});
		RiskDamage.hasMany(models.CustomReportStepKeysDefaultRisk, {
			foreignKey: 'risk_damage_id',
			as: 'step_key_default_risk'
		});
		RiskDamage.hasMany(models.CustomReportSubStepKeysDefaultRisk, {
			foreignKey: 'risk_damage_id',
			as: 'sub_step_key_default_risk'
		});
	};

	return RiskDamage;
};
