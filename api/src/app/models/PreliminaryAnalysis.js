import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const PreliminaryAnalysis = sequelize.define(
		'PreliminaryAnalysis',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},

			// // Informations
			analyst_name: {
				type: DataTypes.STRING,
				allowNull: true
			},
			role_name: {
				type: DataTypes.STRING,
				allowNull: true
			},
			activity_name: {
				type: DataTypes.STRING,
				allowNull: true
			},

			// // WorkConditions
			work_schedule: {
				type: DataTypes.INTEGER,
				allowNull: true
			},
			place_description: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			expected_task_description: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			performed_task_description: {
				type: DataTypes.TEXT,
				allowNull: true
			},

			// //Characters
			working_population_men: {
				type: DataTypes.INTEGER,
				defaultValue: 0,
				allowNull: true
			},
			working_population_women: {
				type: DataTypes.INTEGER,
				defaultValue: 0,
				allowNull: true
			},
			working_population_others: {
				type: DataTypes.INTEGER,
				defaultValue: 0,
				allowNull: true
			},
			total_working_population: {
				type: DataTypes.INTEGER,
				defaultValue: 0,
				allowNull: true
			},
			particularities_description: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			worker_verbalization_description: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			self_evaluation: {
				type: DataTypes.ENUM([
					'nothing_stressful',
					'little_stressful',
					'stressful',
					'very_stressful',
					'extremely_stressful'
				]),
				defaultValue: 'nothing_stressful',
				allowNull: false
			},
			collection_date: {
				type: DataTypes.DATE,
				allowNull: true
			},
			location: {
				type: DataTypes.STRING,
				allowNull: true
			},
			score_sum: {
				type: DataTypes.INTEGER,
				defaultValue: 0,
				allowNull: true
			},
			worst_score: {
				type: DataTypes.INTEGER,
				defaultValue: null,
				allowNull: true
			},
			consolidated: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: false
			},
			comments: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'preliminary_analyzes',
			modelName: 'preliminary_analyzes'
		}
	);

	PreliminaryAnalysis.associate = (models) => {
		PreliminaryAnalysis.belongsTo(models.File, {
			foreignKey: 'file_id',
			as: 'file'
		});
		PreliminaryAnalysis.belongsToMany(models.SuperPeaReport, {
			through: 'PEAToSuperPEA',
			as: 'super_pea',
			foreignKey: 'pea_id',
			otherKey: 'super_pea_id'
		});
		PreliminaryAnalysis.hasMany(models.PreliminaryAnalysisStep, {
			foreignKey: 'preliminary_analysis_id',
			as: 'steps'
		});
	};

	return PreliminaryAnalysis;
};
