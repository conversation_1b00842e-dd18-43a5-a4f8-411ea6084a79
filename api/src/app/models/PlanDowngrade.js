import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const PlanDowngrade = sequelize.define(
		'PlanDowngrade',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			token_transaction: {
				type: DataTypes.STRING,
				allowNull: true
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'plan_downgrades',
			modelName: 'plan_downgrades'
		}
	);

	PlanDowngrade.associate = (models) => {
		PlanDowngrade.belongsTo(models.CustomerInformation, {
			foreignKey: 'customer_id',
			as: 'customer_information'
		});
		PlanDowngrade.belongsTo(models.Plan, {
			foreignKey: 'plan_id',
			as: 'plan'
		});
	};

	return PlanDowngrade;
};
