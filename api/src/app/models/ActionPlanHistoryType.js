import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const ActionPlanHistoryType = sequelize.define(
		'ActionPlanHistoryType',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV4,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.STRING,
				allowNull: false
			},
			created_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			updated_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			deleted_at: {
				type: DataTypes.DATE,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'action_plan_history_types',
			modelName: 'action_plan_history_types',
			paranoid: true
		}
	);

	ActionPlanHistoryType.associate = (models) => {
		ActionPlanHistoryType.hasMany(models.ActionPlanHistoryV2, {
			foreignKey: 'action_plan_history_type_id',
			as: 'action_plan_histories'
		});
	};

	return ActionPlanHistoryType;
};
