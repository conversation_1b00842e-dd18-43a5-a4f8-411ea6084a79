import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const Organization = sequelize.define(
		'Organization',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			url_logo: {
				type: DataTypes.STRING,
				allowNull: true
			},
			isActive: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'organizations',
			modelName: 'organizations'
		}
	);

	Organization.associate = (models) => {
		Organization.belongsToMany(models.CustomReport, {
			through: 'CustomReportsOrganizations',
			as: 'custom_report',
			foreignKey: 'organization_id',
			otherKey: 'custom_report_id'
		});
		Organization.hasMany(models.Company, {
			foreignKey: 'organization_id',
			as: 'company'
		});
		Organization.hasOne(models.File, {
			foreignKey: 'organization_id',
			as: 'file'
		});
		Organization.hasMany(models.UserAccess, { as: 'UserAccess' });

		Organization.hasOne(models.BusinessInformation, {
			foreignKey: 'organization_id',
			as: 'business_information'
		});
	};

	return Organization;
};
