import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomReportAdditionalItemType = sequelize.define(
		'CustomReportAdditionalItemType',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			type: {
				type: DataTypes.STRING,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'custom_report_additional_item_types',
			modelName: 'custom_report_additional_item_types',
			paranoid: true
		}
	);

	CustomReportAdditionalItemType.associate = (models) => {
		CustomReportAdditionalItemType.hasMany(models.CustomReportAdditionalItem, {
			foreignKey: 'type_id',
			as: 'additional_items'
		});
	};

	return CustomReportAdditionalItemType;
};
