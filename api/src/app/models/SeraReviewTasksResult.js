import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const SeraReviewTasksResult = sequelize.define(
		'SeraReviewTasksResult',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			highest_rpn: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			sum_rpn: {
				type: DataTypes.FLOAT,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'sera_review_tasks_results',
			modelName: 'sera_review_tasks_results',
			paranoid: true
		}
	);

	SeraReviewTasksResult.associate = (models) => {
		SeraReviewTasksResult.belongsTo(models.SeraSummaryReview, {
			foreignKey: 'sera_summary_review_id',
			as: 'sera_summary_review'
		});
		SeraReviewTasksResult.belongsTo(models.Task, {
			foreignKey: 'task_id',
			as: 'task_statistics'
		});
	};

	return SeraReviewTasksResult;
};
