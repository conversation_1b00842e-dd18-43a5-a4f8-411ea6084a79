import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const TwoFactorRecoveryTokens = sequelize.define(
		'TwoFactorRecoveryTokens',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			token_hash: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			user_id: {
				type: DataTypes.UUID,
				allowNull: false,
				references: {
					key: 'id',
					model: 'users'
				},
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE'
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true
			},
			created_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			updated_at: {
				type: DataTypes.DATE,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'two_fa_recovery_tokens',
			modelName: 'two_fa_recovery_tokens'
		}
	);

	TwoFactorRecoveryTokens.associate = function (models) {
		TwoFactorRecoveryTokens.belongsTo(models.User, {
			foreignKey: 'user_id',
			as: 'user'
		});
	};

	return TwoFactorRecoveryTokens;
};
