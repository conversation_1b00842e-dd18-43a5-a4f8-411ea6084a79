import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomReportsFiles = sequelize.define(
		'CustomReportsFiles',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'custom_reports_files',
			modelName: 'custom_reports_files',
			paranoid: true
		}
	);

	return CustomReportsFiles;
};
