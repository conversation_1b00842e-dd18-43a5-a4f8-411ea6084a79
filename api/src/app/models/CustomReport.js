import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomReport = sequelize.define(
		'CustomReport',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.STRING,
				allowNull: false
			},
			acronym: {
				type: DataTypes.STRING,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'custom_reports',
			modelName: 'custom_reports',
			paranoid: true
		}
	);

	CustomReport.associate = (models) => {
		CustomReport.belongsToMany(models.Organization, {
			through: 'CustomReportsOrganizations',
			as: 'organization',
			foreignKey: 'custom_report_id',
			otherKey: 'organization_id'
		});
		CustomReport.belongsToMany(models.File, {
			through: 'CustomReportsFiles',
			as: 'file',
			foreignKey: 'custom_report_id',
			otherKey: 'file_id'
		});

		CustomReport.hasMany(models.CustomReportStep, {
			foreignKey: 'custom_report_id',
			as: 'step'
		});
	};

	return CustomReport;
};
