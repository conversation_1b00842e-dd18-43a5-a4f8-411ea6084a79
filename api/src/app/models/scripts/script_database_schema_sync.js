import config from '../../../config/database.cjs';
import sandbox_config from '../../../config/sandbox.cjs';
import { logger } from '../../helpers/index.js';
import { DatabaseSyncSchemaSQL } from '../../mappers/DatabaseSyncSchemaSQL.js';
import db from '../index.js';
import dbSandbox from '../sandbox.js';

export class ScriptDatabaseSchemaSync {
	static source_db = db;
	static target_db = dbSandbox;
	static mapper = new DatabaseSyncSchemaSQL();

	static async syncSandbox() {
		await dbSandbox.sequelize.sync();
	}

	static async getTableInfoCollumns(table_name, database, db = this.source_db) {
		const { query, replacements } = this.mapper.getTableCollumns(table_name, database);
		return await db.sequelize.query(query, { nest: true, replacements });
	}

	static async getTableInfoForeignKeys(table_name, database, db = this.source_db) {
		const { query, replacements } = this.mapper.getTableForeignKeys(table_name, database);
		return await db.sequelize.query(query, { nest: true, replacements });
	}

	static async getTableInfoIndexes(table_name, db = this.source_db) {
		const { query, replacements } = this.mapper.getTableIndexes(table_name);
		return await db.sequelize.query(query, { nest: true, replacements });
	}

	static async getTableInfo(table_name, database) {
		const columns = await this.getTableInfoCollumns(table_name, database);
		const foreign_keys = await this.getTableInfoForeignKeys(table_name, database);
		const indexes = await this.getTableInfoIndexes(table_name);

		return { columns, foreign_keys, indexes };
	}

	static async tableExists(database, table_name, db = this.target_db) {
		const { query, replacements } = this.mapper.getAllTables(database, { table_name });
		const tables = await db.sequelize.query(query, { nest: true, replacements });
		return tables.length > 0;
	}

	static validateTableStructureColumns(source_columns, target_columns) {
		for (let i = 0; i < source_columns.length; i++) {
			const source_col = source_columns[i];
			const target_col = target_columns[i];

			if (
				source_col.COLUMN_NAME !== target_col.COLUMN_NAME ||
				source_col.COLUMN_TYPE !== target_col.COLUMN_TYPE ||
				source_col.IS_NULLABLE !== target_col.IS_NULLABLE ||
				source_col.COLUMN_DEFAULT !== target_col.COLUMN_DEFAULT ||
				source_col.EXTRA !== target_col.EXTRA ||
				source_col.CHARACTER_SET_NAME !== target_col.CHARACTER_SET_NAME ||
				source_col.COLLATION_NAME !== target_col.COLLATION_NAME
			) {
				return false;
			}
		}
		return true;
	}

	static async compareTableStructure(source_info, target_info) {
		const source_columns = source_info.columns;
		const target_columns = target_info.columns;

		if (source_columns.length !== target_columns.length) {
			return false;
		}

		return this.validateTableStructureColumns(source_columns, target_columns);
	}

	static async getColumnInfo(table_name, column_name, database, db = this.source_db) {
		const { query, replacements } = this.mapper.getColumnInfo(table_name, column_name, database);
		const columns = await db.sequelize.query(query, { nest: true, replacements });
		logger.info('Column info results:', JSON.stringify(columns, null, 2));
		return columns[0];
	}

	static async areColumnsCompatible(source_column, target_column) {
		if (source_column.COLUMN_TYPE !== target_column.COLUMN_TYPE) {
			return false;
		}

		if (
			source_column.CHARACTER_SET_NAME !== target_column.CHARACTER_SET_NAME ||
			source_column.COLLATION_NAME !== target_column.COLLATION_NAME
		) {
			return false;
		}

		if (target_column.IS_NULLABLE === 'NO' && source_column.IS_NULLABLE === 'YES') {
			return false;
		}

		return true;
	}

	static async alterTableStatement(table_name, column_name, target_column, db = this.target_db) {
		const { query, replacements } = this.mapper.alterTableStatement(table_name, column_name, target_column);
		await db.sequelize.query(query, { nest: true, type: 'ALTER', replacements });
	}

	static async makeColumnsCompatible(table_name, column_name, referenced_table, referenced_column) {
		try {
			const target_column = await this.getColumnInfo(referenced_table, referenced_column, config.database);

			if (!target_column) {
				logger.warn(`Could not find referenced column ${referenced_column} in table ${referenced_table}`);
				return false;
			}

			await this.alterTableStatement(table_name, column_name, target_column);

			const modified_column = await this.getColumnInfo(
				table_name,
				column_name,
				sandbox_config.database,
				this.target_db
			);

			if (!modified_column) {
				logger.warn(`Could not verify column modification`);
				return false;
			}

			const is_compatible = await this.areColumnsCompatible(modified_column, target_column);
			if (!is_compatible) {
				logger.warn(`Column modification did not result in compatible columns`);
				return false;
			}

			return true;
		} catch (error) {
			logger.warn(`Failed to make columns compatible: ${error.message}`);
			return false;
		}
	}

	static async createColumnDefinitions(source_info) {
		return source_info.columns.map((col) => {
			let def = `\`${col.COLUMN_NAME}\` ${col.COLUMN_TYPE}`;

			if (col.DATA_TYPE.includes('char') || col.DATA_TYPE.includes('text')) {
				def += ` CHARACTER SET ${col.CHARACTER_SET_NAME} COLLATE ${col.COLLATION_NAME}`;
			}

			if (col.IS_NULLABLE === 'NO') def += ' NOT NULL';
			if (col.COLUMN_DEFAULT !== null) {
				if (col.COLUMN_DEFAULT === 'CURRENT_TIMESTAMP') {
					def += ` DEFAULT ${col.COLUMN_DEFAULT}`;
				} else {
					def += ` DEFAULT '${col.COLUMN_DEFAULT}'`;
				}
			} else if (col.IS_NULLABLE === 'YES') {
				def += ' DEFAULT NULL';
			}
			if (col.EXTRA.includes('auto_increment')) def += ' AUTO_INCREMENT';
			return def;
		});
	}

	static async addPrimaryKeyAndUniqueIndexes(source_info, column_defs) {
		const primary_key = source_info.columns.find((col) => col.COLUMN_KEY === 'PRI');
		if (primary_key) {
			column_defs.push(`PRIMARY KEY (\`${primary_key.COLUMN_NAME}\`)`);
		}

		const unique_indexes = source_info.indexes.filter((idx) => idx.Non_unique === 0 && idx.Key_name !== 'PRIMARY');
		unique_indexes.forEach((idx) => {
			column_defs.push(`UNIQUE KEY \`${idx.Key_name}\` (\`${idx.Column_name}\`)`);
		});
	}

	static async createTableWithRetry(target_table, create_table_sql, max_retries) {
		let retry_count = 0;
		let success = false;

		while (retry_count < max_retries && !success) {
			try {
				logger.info(`Creating table ${target_table} (attempt ${retry_count + 1}/${max_retries})`);
				await this.target_db.sequelize.query(create_table_sql);
				success = true;
				logger.info(`Successfully created table ${target_table}`);
			} catch (error) {
				retry_count++;
				logger.error(`Failed to create table ${target_table} (attempt ${retry_count}/${max_retries}):`, error);

				if (retry_count === max_retries) {
					throw error;
				}

				await new Promise((resolve) => setTimeout(resolve, 1000 * retry_count));
			}
		}
	}

	static async createNonUniqueIndexes(target_table, source_info) {
		const non_unique_indexes = source_info.indexes.filter((idx) => idx.Non_unique === 1);
		for (const index of non_unique_indexes) {
			try {
				const { query } = this.mapper.createIndexStatement(target_table, index.Key_name, index.Column_name);
				await this.target_db.sequelize.query(query);
				logger.info(`Created index ${index.Key_name} on table ${target_table}`);
			} catch (error) {
				logger.warn(`Failed to create index ${index.Key_name} on table ${target_table}:`, error);
			}
		}
	}

	static async ensureColumnsCompatibility(source_table, target_table, source_info, target_info) {
		for (let i = 0; i < source_info.columns.length; i++) {
			const source_col = source_info.columns[i];
			const target_col = target_info.columns[i];

			if (
				source_col.CHARACTER_SET_NAME !== target_col.CHARACTER_SET_NAME ||
				source_col.COLLATION_NAME !== target_col.COLLATION_NAME
			) {
				logger.info(`Making columns compatible for ${source_col.COLUMN_NAME} ${target_col.COLUMN_NAME}`);
				logger.info(`Collation: ${source_col.COLLATION_NAME} ${target_col.COLLATION_NAME}`);
				logger.info(`Character set: ${source_col.CHARACTER_SET_NAME} ${target_col.CHARACTER_SET_NAME}`);
				const success = await this.makeColumnsCompatible(
					target_table,
					target_col.COLUMN_NAME,
					source_table,
					source_col.COLUMN_NAME
				);

				if (!success) {
					throw new Error(`Failed to make columns compatible for ${target_col.COLUMN_NAME}`);
				}
			}
		}
	}

	static async copyTableSchema(source_table, target_table, max_retries = 3) {
		try {
			logger.info(`Starting schema copy from ${source_table} to ${target_table}`);

			const source_info = await this.getTableInfo(source_table, config.database);
			if (!source_info || !source_info.columns || source_info.columns.length === 0) {
				throw new Error(`No column information found for source table ${source_table}`);
			}

			const charset = 'latin1';
			const collation = 'latin1_swedish_ci';
			logger.info(`Using charset ${charset} and collation ${collation} for table ${target_table}`);

			const target_exists = await this.tableExists(sandbox_config.database, target_table);
			if (target_exists) {
				logger.info(`Target table ${target_table} already exists, dropping it first`);
				const { query } = this.mapper.dropTableStatement(target_table);
				await this.target_db.sequelize.query(query);
			}

			const column_defs = await this.createColumnDefinitions(source_info);
			await this.addPrimaryKeyAndUniqueIndexes(source_info, column_defs);

			const { query: create_table_sql } = this.mapper.createTableStatement(
				target_table,
				column_defs,
				charset,
				collation
			);
			await this.createTableWithRetry(target_table, create_table_sql, max_retries);

			await this.createNonUniqueIndexes(target_table, source_info);

			const target_info = await this.getTableInfo(target_table, sandbox_config.database);
			const is_compatible = await this.compareTableStructure(source_info, target_info);

			if (!is_compatible) {
				await this.ensureColumnsCompatibility(source_table, target_table, source_info, target_info);
			}

			logger.info(`Successfully copied schema from ${source_table} to ${target_table}`);
			return true;
		} catch (error) {
			logger.error(`Failed to copy schema from ${source_table} to ${target_table}:`, error);
			throw error;
		}
	}

	static async getAllTables(database, db = this.target_db) {
		const { query, replacements } = this.mapper.getAllTables(database);
		return await db.sequelize.query(query, { nest: true, replacements });
	}

	static async addForeignKeys(table_name, source_info, database) {
		try {
			if (!source_info.foreign_keys || source_info.foreign_keys.length === 0) {
				return;
			}

			for (const fk of source_info.foreign_keys) {
				try {
					const referenced_table_exists = await this.tableExists(database, fk.REFERENCED_TABLE_NAME);

					if (!referenced_table_exists) {
						logger.warn(
							`Referenced table ${fk.REFERENCED_TABLE_NAME} does not exist yet, skipping FK creation`
						);
						continue;
					}

					const { query: check_query, replacements: check_replacements } = this.mapper.checkForeignKeyExists(
						table_name,
						fk.CONSTRAINT_NAME,
						database
					);
					const result = await this.target_db.sequelize.query(check_query, {
						nest: true,
						replacements: check_replacements
					});

					if (result[0].count > 0) {
						logger.info(
							`Foreign key ${fk.CONSTRAINT_NAME} already exists in table ${table_name}, skipping creation`
						);
						continue;
					}

					const { query, replacements } = this.mapper.addForeignKey(table_name, fk);
					await this.target_db.sequelize.query(query, { nest: true, type: 'ALTER', replacements });
					logger.info(`Added foreign key ${fk.CONSTRAINT_NAME} to table ${table_name}`);
				} catch (error) {
					logger.warn(`Failed to add foreign key ${fk.CONSTRAINT_NAME} to table ${table_name}:`, error);
				}
			}
		} catch (error) {
			logger.error(`Failed to add foreign keys to table ${table_name}:`, error);
		}
	}

	static async checkAndAddMissingColumns(table_name, source_info, database) {
		try {
			const target_info = await this.getTableInfo(table_name, database);
			const source_columns = source_info.columns;
			const target_columns = target_info.columns;
			const source_foreign_keys = source_info.foreign_keys || [];

			for (const source_col of source_columns) {
				const target_col = target_columns.find((col) => col.COLUMN_NAME === source_col.COLUMN_NAME);

				if (!target_col) {
					const fk_info = source_foreign_keys.find((fk) => fk.COLUMN_NAME === source_col.COLUMN_NAME);

					if (fk_info) {
						const referenced_table_exists = await this.tableExists(
							sandbox_config.database,
							fk_info.REFERENCED_TABLE_NAME
						);

						if (!referenced_table_exists) {
							logger.warn(
								`Cannot add column ${source_col.COLUMN_NAME} as foreign key because referenced table ${fk_info.REFERENCED_TABLE_NAME} does not exist in sandbox database`
							);
							continue;
						}

						const referenced_column_info = await this.getColumnInfo(
							fk_info.REFERENCED_TABLE_NAME,
							fk_info.REFERENCED_COLUMN_NAME,
							sandbox_config.database
						);

						if (!referenced_column_info) {
							logger.warn(
								`Cannot add column ${source_col.COLUMN_NAME} as foreign key because referenced column ${fk_info.REFERENCED_COLUMN_NAME} does not exist in table ${fk_info.REFERENCED_TABLE_NAME}`
							);
							continue;
						}
					}

					logger.info(`Column ${source_col.COLUMN_NAME} does not exist in table ${table_name}, adding it...`);

					const { query, replacements } = this.mapper.addColumnStatement(table_name, source_col);
					await this.target_db.sequelize.query(query, { type: 'ALTER', replacements });
					logger.info(`Successfully added column ${source_col.COLUMN_NAME} to table ${table_name}`);

					if (fk_info) {
						try {
							const { query, replacements } = this.mapper.addForeignKey(table_name, fk_info);
							await this.target_db.sequelize.query(query, { type: 'ALTER', replacements });
							logger.info(
								`Successfully added foreign key constraint for column ${source_col.COLUMN_NAME}`
							);
						} catch (error) {
							logger.warn(
								`Failed to add foreign key constraint for column ${source_col.COLUMN_NAME}:`,
								error
							);
						}
					}
				}
			}
		} catch (error) {
			logger.error(`Failed to check/add missing columns for table ${table_name}:`, error);
			throw error;
		}
	}

	static async copyAllSandboxTableSchemas() {
		try {
			logger.info('Starting to copy all table schemas from kinebot database');

			let table_infos = new Map();

			const tables = await this.getAllTables(config.database, this.source_db);

			if (!tables || tables.length === 0) {
				throw new Error('No tables found in source database');
			}

			logger.info(`Found ${tables.length} tables to copy`);

			const results = {
				total: tables.length,
				successful: 0,
				failed: [],
				skipped: []
			};

			for (const table of tables) {
				const table_name = table.TABLE_NAME || table.table_name;
				try {
					logger.info(`\nProcessing table: ${table_name}`);

					const source_info = await this.getTableInfo(table_name, config.database);
					table_infos.set(table_name, source_info);

					const exists = await this.tableExists(sandbox_config.database, table_name);
					if (exists) {
						logger.info(
							`Table ${table_name} already exists in target database, checking for missing columns...`
						);
						await this.checkAndAddMissingColumns(table_name, source_info, sandbox_config.database);
						results.skipped.push(table_name);
						continue;
					}

					await this.copyTableSchema(table_name, table_name);
					results.successful++;
					logger.info(`Successfully copied schema for table ${table_name}`);
				} catch (error) {
					logger.error(`Failed to copy schema for table ${table_name}:`, error);
					results.failed.push({
						table: table_name,
						error: error.message
					});
				}
			}

			for (const [table_name, source_info] of table_infos) {
				try {
					await this.addForeignKeys(table_name, source_info, sandbox_config.database);
				} catch (error) {
					logger.error(`Failed to add foreign keys to table ${table_name}:`, error);
				}
			}

			logger.info('\nSchema copy completed with the following results:');
			logger.info(`Total tables: ${results.total}`);
			logger.info(`Successfully copied: ${results.successful}`);
			logger.info(`Skipped (already exist): ${results.skipped.length}`);
			logger.info(`Failed: ${results.failed.length}`);

			if (results.failed.length > 0) {
				logger.warn('\nFailed tables:');
				results.failed.forEach((failure) => {
					logger.warn(`- ${failure.table}: ${failure.error}`);
				});
			}
		} catch (error) {
			logger.error('Failed to copy all table schemas:', error);
			throw error;
		}
	}
}
