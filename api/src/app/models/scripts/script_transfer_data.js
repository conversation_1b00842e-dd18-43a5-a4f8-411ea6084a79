import { logger } from '../../helpers/index.js';
import db from '../index.js';
import dbSandbox from '../sandbox.js';
import { TRACKED_TABLES } from '../../helpers/constants.js';

export class ScriptTransferData {
	static source_db = db;
	static target_db = dbSandbox;

	static sanitizeDateValue(value) {
		if (!value) return null;
		if (typeof value === 'string' && value.includes('T')) {
			const date = new Date(value);
			if (isNaN(date.getTime())) return null;
			return date.toISOString().slice(0, 19).replace('T', ' ');
		}
		return value;
	}

	static sanitizeRow(row) {
		const sanitized = {};
		for (const [key, value] of Object.entries(row)) {
			if (key.includes('_at') || key.includes('_date')) {
				sanitized[key] = this.sanitizeDateValue(value);
				continue;
			}

			sanitized[key] = value;
		}
		return sanitized;
	}

	static async getTableDependencies(tableName) {
		const [foreignKeys] = await this.source_db.sequelize.query(
			`
            SELECT 
                REFERENCED_TABLE_NAME
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
            WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = ?
            AND REFERENCED_TABLE_NAME IS NOT NULL
        `,
			{
				replacements: [tableName]
			}
		);

		return foreignKeys.map((fk) => fk.REFERENCED_TABLE_NAME);
	}

	static async getTablesInOrder() {
		const [tables] = await this.source_db.sequelize.query(`
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = DATABASE()
        `);

		const tableNames = tables.map((t) => t.TABLE_NAME);
		const dependencies = {};
		const processed = new Set();
		const order = [];

		for (const tableName of tableNames) {
			dependencies[tableName] = await this.getTableDependencies(tableName);
		}

		while (processed.size < tableNames.length) {
			let tablesProcessed = false;

			for (const tableName of tableNames) {
				if (processed.has(tableName)) continue;

				const tableDeps = dependencies[tableName];
				const canProcess = tableDeps.every((dep) => processed.has(dep));

				if (canProcess) {
					order.push(tableName);
					processed.add(tableName);
					tablesProcessed = true;
				}
			}

			if (!tablesProcessed) {
				throw new Error('Circular dependency detected in foreign keys');
			}
		}

		return order;
	}

	static async deleteData(tables) {
		try {
			logger.info('Starting data deletion');

			for (const table of tables) {
				logger.info(`Deleting data from table: ${table}`);
				await this.target_db.sequelize.query(`DELETE FROM \`${table}\``);
			}

			logger.info('Data deleted successfully');
		} catch (error) {
			logger.error('Error deleting data:', error);
			throw error;
		}
	}

	static async insertRow(tableName, row) {
		try {
			const sanitizedRow = this.sanitizeRow(row);

			const hasInvalidDate = Object.entries(sanitizedRow).some(([key, value]) => {
				if (key.includes('_at') || key.includes('_date')) {
					return value === null && row[key] !== null;
				}
				return false;
			});

			if (hasInvalidDate) {
				logger.warn(`Skipping row in ${tableName} due to invalid date values:`, row);
				return false;
			}

			const insertQuery = `
                INSERT INTO \`${tableName}\` 
                (${Object.keys(sanitizedRow)
					.map((key) => `\`${key}\``)
					.join(', ')})
                VALUES (${Object.values(sanitizedRow)
					.map(() => '?')
					.join(', ')})
            `;

			await this.target_db.sequelize.query(insertQuery, {
				replacements: Object.values(sanitizedRow)
			});

			return true;
		} catch (error) {
			if (error.name === 'SequelizeForeignKeyConstraintError') {
				const match = error.message.match(/REFERENCES `([^`]+)`/);
				if (match) {
					const referencedTable = match[1].split('_ibfk')[0];
					logger.info(`Foreign key error for ${tableName}, checking ${referencedTable}`);

					const [foreignKeys] = await this.source_db.sequelize.query(
						`
                        SELECT COLUMN_NAME, REFERENCED_COLUMN_NAME
                        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                        WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
                        AND TABLE_NAME = ?
                        AND REFERENCED_TABLE_NAME = ?
                    `,
						{
							replacements: [tableName, referencedTable]
						}
					);

					for (const fk of foreignKeys) {
						const fkValue = row[fk.COLUMN_NAME];
						if (!fkValue) continue;

						const [existingRow] = await this.target_db.sequelize.query(
							`
                            SELECT * FROM \`${referencedTable}\`
                            WHERE \`${fk.REFERENCED_COLUMN_NAME}\` = ?
                        `,
							{
								replacements: [fkValue]
							}
						);

						if (!existingRow || existingRow.length === 0) {
							const [sourceRow] = await this.source_db.sequelize.query(
								`
                                SELECT * FROM \`${referencedTable}\`
                                WHERE \`${fk.REFERENCED_COLUMN_NAME}\` = ?
                            `,
								{
									replacements: [fkValue]
								}
							);

							if (sourceRow && sourceRow.length > 0) {
								await this.insertRow(referencedTable, sourceRow[0]);
							}
						}
					}

					return this.insertRow(tableName, row);
				}
			}
			logger.warn(`Skipping row in ${tableName} due to error:`, error.message);
			return false;
		}
	}

	static async buildDependencyGraph() {
		const [tables] = await this.source_db.sequelize.query(`
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = DATABASE()
        `);

		const graph = {};
		const tableNames = tables.map((t) => t.TABLE_NAME);

		for (const tableName of tableNames) {
			graph[tableName] = {
				name: tableName,
				children: [],
				parents: [],
				data: []
			};
		}

		for (const tableName of tableNames) {
			const [foreignKeys] = await this.source_db.sequelize.query(
				`
                SELECT 
                    TABLE_NAME,
                    COLUMN_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = ?
                AND REFERENCED_TABLE_NAME IS NOT NULL
            `,
				{
					replacements: [tableName]
				}
			);

			for (const fk of foreignKeys) {
				const parentTable = fk.REFERENCED_TABLE_NAME;
				const childTable = fk.TABLE_NAME;

				if (graph[parentTable] && graph[childTable]) {
					graph[parentTable].children.push(graph[childTable]);
					graph[childTable].parents.push(graph[parentTable]);
				}
			}
		}

		return graph;
	}

	static async loadTableData(graph) {
		for (const tableName in graph) {
			if (!TRACKED_TABLES.includes(tableName)) continue;

			const [rows] = await this.source_db.sequelize.query(`
                SELECT * FROM \`${tableName}\`
            `);

			graph[tableName].data = rows;
			logger.info(`Loaded ${rows.length} rows from ${tableName}`);
		}
	}

	static getOrderedTables(graph) {
		const visited = new Set();
		const order = [];

		function visit(node) {
			if (visited.has(node.name)) return;
			visited.add(node.name);

			for (const parent of node.parents) {
				visit(parent);
			}

			order.push(node);
		}

		for (const tableName in graph) {
			visit(graph[tableName]);
		}

		return order;
	}

	static async transferData() {
		try {
			logger.info('Starting data transfer');

			const graph = await this.buildDependencyGraph();
			logger.info('Dependency graph built');

			await this.loadTableData(graph);
			logger.info('All table data loaded');

			const orderedTables = this.getOrderedTables(graph);
			logger.info('Tables ordered by dependencies');

			await this.deleteData(orderedTables.map((t) => t.name).reverse());
			logger.info('Data deleted in reverse order');

			for (const table of orderedTables) {
				if (!TRACKED_TABLES.includes(table.name)) continue;

				logger.info(`Processing table: ${table.name}`);

				if (!table.data || table.data.length === 0) {
					logger.info(`No data found for table: ${table.name}`);
					continue;
				}

				let successCount = 0;
				let skipCount = 0;

				for (const row of table.data) {
					try {
						const success = await this.insertRow(table.name, row);
						if (success) {
							successCount++;
							continue;
						}
						skipCount++;
					} catch (error) {
						skipCount++;
					}
				}

				logger.info(`Transferred ${successCount} rows for table: ${table.name} (skipped ${skipCount} rows)`);
			}

			logger.info('Data transfer completed successfully');
			return true;
		} catch (error) {
			logger.error('Error transferring data:', error);
			throw error;
		}
	}
}
