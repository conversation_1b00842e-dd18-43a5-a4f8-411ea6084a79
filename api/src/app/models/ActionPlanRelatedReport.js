import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const ActionPlanRelatedReport = sequelize.define(
		'ActionPlanRelatedReport',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV4,
				primaryKey: true
			},
			action_plan_id: {
				type: DataTypes.UUID,
				allowNull: false
			},
			report_id: {
				type: DataTypes.UUID,
				allowNull: false
			},
			ergonomic_tool_id: {
				type: DataTypes.UUID,
				allowNull: false
			},
			created_at: {
				type: DataTypes.DATE,
				allowNull: true
			},
			updated_at: {
				type: DataTypes.DATE,
				allowNull: true
			},
			deleted_at: {
				type: DataTypes.DATE,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'action_plan_related_reports',
			modelName: 'action_plan_related_reports',
			paranoid: true
		}
	);

	ActionPlanRelatedReport.associate = (models) => {
		ActionPlanRelatedReport.belongsTo(models.ActionPlanV2, {
			foreignKey: 'action_plan_id',
			as: 'action_plan'
		});
		ActionPlanRelatedReport.belongsTo(models.ErgonomicTool, {
			foreignKey: 'ergonomic_tool_id',
			as: 'ergonomic_tool'
		});
	};

	return ActionPlanRelatedReport;
};
