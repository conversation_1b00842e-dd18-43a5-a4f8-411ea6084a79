import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const PotentialCustomer = sequelize.define(
		'PotentialCustomer',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			email: {
				type: DataTypes.STRING,
				allowNull: false,
				unique: true
			},
			teamSize: {
				type: DataTypes.INTEGER,
				allowNull: false,
				field: 'team_size',
				defaultValue: 0
			},
			phoneNumber: {
				type: DataTypes.STRING,
				allowNull: false,
				field: 'phone_number'
			},
			organization: {
				type: DataTypes.STRING,
				allowNull: true
			},
			responsibility: {
				type: DataTypes.STRING,
				allowNull: false
			},
			occupationArea: {
				type: DataTypes.STRING,
				allowNull: false,
				field: 'occupation_area'
			},
			isActive: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: false,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				allowNull: false,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				allowNull: false,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'potential_customers',
			modelName: 'potential_customers'
		}
	);

	return PotentialCustomer;
};
