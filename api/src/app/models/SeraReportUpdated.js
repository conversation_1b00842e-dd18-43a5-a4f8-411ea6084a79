import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const SeraReportUpdated = sequelize.define(
		'SeraReportUpdated',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'sera_reports_updated',
			modelName: 'sera_reports_updated',
			paranoid: true
		}
	);

	SeraReportUpdated.associate = (models) => {
		SeraReportUpdated.belongsTo(models.SeraReport, {
			foreignKey: 'sera_report_id',
			as: 'current_sera_report'
		});
		SeraReportUpdated.belongsTo(models.SeraReport, {
			foreignKey: 'sera_report_updated',
			as: 'updated_sera_report'
		});
	};

	return SeraReportUpdated;
};
