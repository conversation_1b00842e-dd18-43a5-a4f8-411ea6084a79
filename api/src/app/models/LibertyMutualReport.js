import { optionsDefaultTable } from './commons/default-options-table.js';
import { ERGONOMIC_TOOL_NAMES } from '../util/constants-ergonomic-tools.js';
import { FileToErgonomicToolHookManager } from '../helpers/file-to-ergonomic-tool-hook-manager.js';

export default (sequelize, DataTypes) => {
	const LibertyMutualReport = sequelize.define(
		'LibertyMutualReport',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			comment: {
				type: DataTypes.TEXT,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			paranoid: true,
			tableName: 'liberty_mutual_reports',
			modelName: 'liberty_mutual_reports',
			hooks: {
				afterCreate: async (instance) => {
					await FileToErgonomicToolHookManager.afterCreate({
						instance,
						tool_name: ERGONOMIC_TOOL_NAMES.LIBERTY_MUTUAL,
						sequelize
					});
				},
				afterDestroy: async (instance) => {
					await FileToErgonomicToolHookManager.afterDestroy({
						instance,
						sequelize
					});
				},
				afterBulkDestroy: async (options) => {
					await FileToErgonomicToolHookManager.afterBulkDestroy({
						options,
						sequelize
					});
				}
			}
		}
	);

	LibertyMutualReport.associate = (models) => {
		LibertyMutualReport.belongsTo(models.User, {
			foreignKey: 'report_user_id',
			as: 'user'
		});
		LibertyMutualReport.belongsTo(models.File, {
			foreignKey: 'file_id',
			as: 'file'
		});
		LibertyMutualReport.belongsTo(models.SystemOfUnits, {
			foreignKey: 'system_of_units_id',
			as: 'system_of_units'
		});

		LibertyMutualReport.hasMany(models.LibertyMutualReportInput, {
			foreignKey: 'liberty_mutual_report_id',
			as: 'report_inputs'
		});
	};

	return LibertyMutualReport;
};
