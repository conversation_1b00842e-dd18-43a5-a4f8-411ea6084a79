import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const ActionPlanUserPreference = sequelize.define(
		'ActionPlanUserPreference',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV4,
				primaryKey: true
			},
			user_id: {
				type: DataTypes.UUID,
				allowNull: false
			},
			action_plan_column_field_id: {
				type: DataTypes.UUID,
				allowNull: false
			},
			created_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			updated_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			deleted_at: {
				type: DataTypes.DATE,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'action_plan_user_preferences',
			modelName: 'action_plan_user_preferences',
			paranoid: true
		}
	);

	ActionPlanUserPreference.associate = (models) => {
		ActionPlanUserPreference.belongsTo(models.User, {
			foreignKey: 'user_id',
			as: 'user'
		});
		ActionPlanUserPreference.belongsTo(models.ActionPlanColumnField, {
			foreignKey: 'action_plan_column_field_id',
			as: 'column_field'
		});
	};

	return ActionPlanUserPreference;
};
