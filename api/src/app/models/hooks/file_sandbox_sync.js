import { logger } from '../../helpers/index.js';
import db from '../index.js';
import { FileSandboxSyncSQL } from '../../mappers/FileSandboxSyncSQL.js';

export class FileSandboxSyncHook {
	static mapper = new FileSandboxSyncSQL();

	static mapAttributesToColumns(instance, data) {
		const model = instance.constructor;
		const mapped_data = {};
		const mapped_columns = [];
		const mapped_values = [];

		Object.entries(data).forEach(([attributeName, value]) => {
			const attribute = model.rawAttributes[attributeName];
			const column_name = attribute?.field || attributeName;

			mapped_data[column_name] = value;
			mapped_columns.push(column_name);
			mapped_values.push(value);
		});

		return { mapped_data, mapped_columns, mapped_values };
	}

	static async beforeCreate(instance, options) {
		try {
			const model_name = instance.constructor.name;
			const table_name = instance.constructor.tableName;

			if (table_name !== 'files') return;

			logger.info(`[FileSandboxSync] Capturing CREATE operation for model ${model_name}`);

			const raw_data = instance.toJSON();
			const { mapped_data, mapped_columns } = FileSandboxSyncHook.mapAttributesToColumns(instance, raw_data);

			options.file_sandbox_sync = {
				operation: 'CREATE',
				table_name: table_name,
				data: mapped_data,
				columns: mapped_columns
			};
		} catch (error) {
			logger.error('[FileSandboxSync] Error in beforeCreate:', error);
		}
	}

	static async afterCreate(instance, options) {
		try {
			const sync_info = options.file_sandbox_sync;
			if (!sync_info) return;

			logger.info(
				`[FileSandboxSync] Syncing CREATE operation from sandbox to production for table ${sync_info.table_name}`
			);

			const columns = sync_info.columns;
			const values = Object.values(sync_info.data);

			const { query } = FileSandboxSyncHook.mapper.createInsertStatement(sync_info.table_name, columns);
			logger.info(`[FileSandboxSync] Query: ${query}`);

			await db.sequelize.query(query, {
				replacements: values,
				type: 'INSERT'
			});

			logger.info(`[FileSandboxSync] Successfully synced CREATE operation from sandbox to production`);
		} catch (error) {
			logger.error('[FileSandboxSync] Error in afterCreate:', error);
		}
	}

	static async beforeUpdate(instance, options) {
		try {
			const model_name = instance.constructor.name;
			const table_name = instance.constructor.tableName;

			if (table_name !== 'files') return;

			logger.info(`[FileSandboxSync] Capturing UPDATE operation for model ${model_name}`);

			const raw_data = instance.toJSON();
			const { mapped_data, mapped_columns } = FileSandboxSyncHook.mapAttributesToColumns(instance, raw_data);

			options.file_sandbox_sync = {
				operation: 'UPDATE',
				table_name: table_name,
				data: mapped_data,
				columns: mapped_columns,
				previousData: instance._previousDataValues
			};
		} catch (error) {
			logger.error('[FileSandboxSync] Error in beforeUpdate:', error);
		}
	}

	static async afterUpdate(instance, options) {
		try {
			const sync_info = options.file_sandbox_sync;
			if (!sync_info) return;

			logger.info(
				`[FileSandboxSync] Syncing UPDATE operation from sandbox to production for table ${sync_info.table_name}`
			);

			const columns = sync_info.columns;
			const values = Object.entries(sync_info.data)
				.filter(([key]) => key !== 'id')
				.map(([_, value]) => value);

			const { query } = FileSandboxSyncHook.mapper.createUpdateStatement(sync_info.table_name, columns);

			await db.sequelize.query(query, {
				replacements: [...values, sync_info.data.id],
				type: 'UPDATE'
			});

			logger.info(`[FileSandboxSync] Successfully synced UPDATE operation from sandbox to production`);
		} catch (error) {
			logger.error('[FileSandboxSync] Error in afterUpdate:', error);
		}
	}
}
