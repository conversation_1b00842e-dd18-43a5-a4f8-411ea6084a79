import { logger } from '../../helpers/index.js';
import dbSandbox from '../sandbox.js';
import { TRACKED_TABLES } from '../../helpers/index.js';
import { SandboxSyncSQL } from '../../mappers/SandboxSyncSQL.js';

export class SandboxSyncHook {
	static mapper = new SandboxSyncSQL();

	static mapAttributesToColumns(instance, data) {
		const model = instance.constructor;
		const mapped_data = {};
		const mapped_columns = [];

		Object.entries(data).forEach(([attributeName, value]) => {
			const attribute = model.rawAttributes[attributeName];
			const column_name = attribute?.field || attributeName;

			mapped_data[column_name] = value;
			mapped_columns.push(column_name);
		});

		return { mapped_data, mapped_columns };
	}

	static async beforeCreate(instance, options) {
		try {
			const model_name = instance.constructor.name;
			const table_name = instance.constructor.tableName;
			if (!TRACKED_TABLES.includes(table_name)) return;

			logger.info(`[SandboxSync] Capturing CREATE operation for model ${model_name}`);

			const rawData = instance.toJSON();
			const { mapped_data, mapped_columns } = SandboxSyncHook.mapAttributesToColumns(instance, rawData);

			options.sandbox_sync = {
				operation: 'CREATE',
				table_name: table_name,
				data: mapped_data,
				columns: mapped_columns
			};
		} catch (error) {
			logger.error('[SandboxSync] Error in beforeCreate:', error);
		}
	}

	static async afterCreate(instance, options) {
		try {
			const sync_info = options.sandbox_sync;
			if (!sync_info) return;

			logger.info(`[SandboxSync] Syncing CREATE operation to sandbox for table ${sync_info.table_name}`);

			const columns = sync_info.columns;
			const values = Object.values(sync_info.data);

			const { query } = SandboxSyncHook.mapper.createInsertStatement(sync_info.table_name, columns);
			logger.info(`[SandboxSync] Query: ${query}`);

			await dbSandbox.sequelize.query(query, {
				replacements: values,
				type: 'INSERT'
			});

			logger.info(`[SandboxSync] Successfully synced CREATE operation to sandbox`);
		} catch (error) {
			logger.error('[SandboxSync] Error in afterCreate:', error);
		}
	}

	static async beforeBulkCreate(instances, options) {
		try {
			if (!instances || !instances.length) return;

			const model = instances[0].constructor;
			const table_name = model.tableName;

			if (!TRACKED_TABLES.includes(table_name)) return;

			// Mapear todas as instâncias para usar nomes de colunas corretos
			const mapped_data = instances.map((instance) => {
				const raw_data = instance.toJSON();
				const { mapped_data } = SandboxSyncHook.mapAttributesToColumns(instance, raw_data);
				return mapped_data;
			});

			// Usar as colunas da primeira instância (todas devem ter as mesmas colunas)
			const { mapped_columns } = SandboxSyncHook.mapAttributesToColumns(instances[0], instances[0].toJSON());

			options.sandbox_sync = {
				operation: 'CREATE',
				table_name: table_name,
				data: mapped_data,
				columns: mapped_columns
			};

			logger.info(
				`[SandboxSync] Capturing BULK CREATE operation for table ${table_name} with ${instances.length} records`
			);
		} catch (error) {
			logger.error('[SandboxSync] Error in beforeBulkCreate:', error);
		}
	}

	static async afterBulkCreate(instances, options) {
		try {
			const sync_info = options.sandbox_sync;
			if (!sync_info || !sync_info.data?.length) return;

			logger.info(`[SandboxSync] Syncing BULK CREATE operation to sandbox for table ${sync_info.table_name}`);

			const columns = sync_info.columns;
			const { query } = SandboxSyncHook.mapper.createInsertStatement(sync_info.table_name, columns);
			logger.info(`[SandboxSync] Query: ${query}`);

			for (const record of sync_info.data) {
				const values = Object.values(record);
				await dbSandbox.sequelize.query(query, {
					replacements: values,
					type: 'INSERT'
				});
			}

			logger.info(`[SandboxSync] Successfully synced BULK CREATE operation to sandbox`);
		} catch (error) {
			logger.error('[SandboxSync] Error in afterBulkCreate:', error);
		}
	}

	static async beforeUpdate(instance, options) {
		try {
			const model_name = instance.constructor.name;
			const table_name = instance.constructor.tableName;
			if (!TRACKED_TABLES.includes(table_name)) return;

			logger.info(`[SandboxSync] Capturing UPDATE operation for model ${model_name}`);

			const raw_data = instance.toJSON();
			const { mapped_data, mapped_columns } = SandboxSyncHook.mapAttributesToColumns(instance, raw_data);

			options.sandbox_sync = {
				operation: 'UPDATE',
				table_name: table_name,
				data: mapped_data,
				columns: mapped_columns,
				previousData: instance._previousDataValues
			};
		} catch (error) {
			logger.error('[SandboxSync] Error in beforeUpdate:', error);
		}
	}

	static async afterUpdate(instance, options) {
		try {
			const sync_info = options.sandbox_sync;
			if (!sync_info) return;

			logger.info(`[SandboxSync] Syncing UPDATE operation to sandbox for table ${sync_info.table_name}`);

			const columns = sync_info.columns;
			const values = Object.entries(sync_info.data)
				.filter(([key]) => key !== 'id')
				.map(([_, value]) => value);

			const { query } = SandboxSyncHook.mapper.createUpdateStatement(sync_info.table_name, columns);

			await dbSandbox.sequelize.query(query, {
				replacements: [...values, sync_info.data.id],
				type: 'UPDATE'
			});

			logger.info(`[SandboxSync] Successfully synced UPDATE operation to sandbox`);
		} catch (error) {
			logger.error('[SandboxSync] Error in afterUpdate:', error);
		}
	}

	static async beforeBulkUpdate(options) {
		try {
			const model_name = options.model?.name;
			const table_name = options.model?.tableName;

			if (!table_name || !TRACKED_TABLES.includes(table_name)) return;

			logger.info(`[SandboxSync] Capturing BULK UPDATE operation for model ${model_name}`);

			const instances = await options.model.findAll({ where: options.where });

			const sync_info_list = instances.map((instance) => {
				const raw_data = instance.toJSON();
				const { mapped_data, mapped_columns } = SandboxSyncHook.mapAttributesToColumns(instance, raw_data);

				return {
					operation: 'UPDATE',
					table_name: table_name,
					data: mapped_data,
					columns: mapped_columns,
					previousData: instance._previousDataValues
				};
			});

			options.sandbox_sync = sync_info_list;
		} catch (error) {
			logger.error('[SandboxSync] Error in beforeBulkUpdate:', error);
		}
	}

	static async afterBulkUpdate(options) {
		try {
			const sync_info_list = options.sandbox_sync;
			if (!sync_info_list || !Array.isArray(sync_info_list) || sync_info_list.length === 0) return;

			logger.info(`[SandboxSync] Syncing BULK UPDATE operations to sandbox`);

			for (const sync_info of sync_info_list) {
				const { table_name, data, columns } = sync_info;

				logger.info(`[SandboxSync] Syncing UPDATE operation to sandbox for table ${table_name}`);

				const values = Object.entries(data)
					.filter(([key]) => key !== 'id')
					.map(([_, value]) => value);

				const { query } = SandboxSyncHook.mapper.createUpdateStatement(table_name, columns);

				await dbSandbox.sequelize.query(query, {
					replacements: [...values, data.id],
					type: 'UPDATE'
				});
			}

			logger.info(`[SandboxSync] Successfully synced BULK UPDATE operations to sandbox`);
		} catch (error) {
			logger.error('[SandboxSync] Error in afterBulkUpdate:', error);
		}
	}

	static async beforeDestroy(instance, options) {
		try {
			const model_name = instance.constructor.name;
			const table_name = instance.constructor.tableName;
			if (!TRACKED_TABLES.includes(table_name)) return;

			logger.info(`[SandboxSync] Capturing DELETE operation for model ${model_name}`);

			const raw_data = instance.toJSON();
			const { mapped_data, mapped_columns } = SandboxSyncHook.mapAttributesToColumns(instance, raw_data);

			options.sandbox_sync = {
				operation: 'DELETE',
				table_name: table_name,
				data: mapped_data,
				columns: mapped_columns
			};
		} catch (error) {
			logger.error('[SandboxSync] Error in beforeDestroy:', error);
		}
	}

	static async afterDestroy(instance, options) {
		try {
			const sync_info = options.sandbox_sync;
			if (!sync_info) return;

			logger.info(`[SandboxSync] Syncing DELETE operation to sandbox for table ${sync_info.table_name}`);

			const { query } = SandboxSyncHook.mapper.createDeleteStatement(sync_info.table_name);

			await dbSandbox.sequelize.query(query, {
				replacements: [sync_info.data.id],
				type: 'DELETE'
			});

			logger.info(`[SandboxSync] Successfully synced DELETE operation to sandbox`);
		} catch (error) {
			logger.error('[SandboxSync] Error in afterDestroy:', error);
		}
	}

	static async beforeBulkDestroy(options) {
		try {
			const model = options.model;
			const table_name = model.tableName;

			if (!TRACKED_TABLES.includes(table_name)) return;

			options.sandbox_sync = {
				operation: 'DELETE',
				table_name: table_name,
				where: options.where
			};

			logger.info(`[SandboxSync] Capturing BULK DELETE operation for table ${table_name}`, {
				where: options.where
			});
		} catch (error) {
			logger.error('[SandboxSync] Error in beforeBulkDestroy:', error);
		}
	}

	static async afterBulkDestroy(options) {
		try {
			const sync_info = options.sandbox_sync;
			if (!sync_info) return;

			const model = options.model;

			logger.info(`[SandboxSync] Syncing BULK DELETE operation to sandbox for table ${sync_info.table_name}`);

			const instances = await model.findAll({ where: sync_info.where, paranoid: false });
			if (!instances.length) return;

			const { query } = SandboxSyncHook.mapper.createDeleteStatement(sync_info.table_name);

			for (const instance of instances) {
				await dbSandbox.sequelize.query(query, {
					replacements: [instance.id],
					type: 'DELETE'
				});
			}

			logger.info(`[SandboxSync] Successfully synced BULK DELETE operation to sandbox`);
		} catch (error) {
			logger.error('[SandboxSync] Error in afterBulkDestroy:', error);
		}
	}
}
