import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const Severity = sequelize.define(
		'Severity',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.STRING,
				allowNull: false
			},
			score: {
				type: DataTypes.FLOAT,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'severities',
			modelName: 'severities',
			paranoid: true
		}
	);

	Severity.associate = (models) => {
		Severity.belongsTo(models.CustomReportStepKey, {
			foreignKey: 'custom_report_step_key_id',
			as: 'step_key'
		});

		Severity.hasMany(models.RiskDamage, {
			foreignKey: 'severity_id',
			as: 'risk_damage'
		});
		Severity.hasMany(models.SeraReport, {
			foreignKey: 'severity_id',
			as: 'sera_report'
		});
		Severity.hasMany(models.CustomReportStepKeyResult, {
			foreignKey: 'severity_id',
			as: 'step_key_result'
		});
		Severity.hasMany(models.CustomReportSubStepKeyResult, {
			foreignKey: 'severity_id',
			as: 'sub_step_key_result'
		});
	};

	return Severity;
};
