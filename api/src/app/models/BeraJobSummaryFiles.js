import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const BeraJobSummaryFiles = sequelize.define(
		'BeraJobSummaryFiles',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			bera_job_summary_id: {
				type: DataTypes.UUID,
				allowNull: false,
				references: {
					model: 'bera_job_summaries',
					key: 'id'
				},
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE'
			},
			file_id: {
				type: DataTypes.UUID,
				allowNull: false,
				references: {
					model: 'files',
					key: 'id'
				},
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE'
			}
		},
		{
			paranoid: true,
			...optionsDefaultTable,
			tableName: 'bera_job_summary_files',
			modelName: 'bera_job_summary_files'
		}
	);

	BeraJobSummaryFiles.associate = (models) => {
		BeraJobSummaryFiles.belongsTo(models.BeraJobSummary, {
			foreignKey: 'bera_job_summary_id',
			as: 'bera_job_summary'
		});
		BeraJobSummaryFiles.belongsTo(models.File, {
			foreignKey: 'file_id',
			as: 'file'
		});
	};

	return BeraJobSummaryFiles;
};
