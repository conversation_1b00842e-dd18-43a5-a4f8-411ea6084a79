import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const ActionPlanTaskAttachment = sequelize.define(
		'ActionPlanTaskAttachment',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV4,
				primaryKey: true
			},
			description: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			file_name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			location: {
				type: DataTypes.STRING,
				allowNull: false
			},
			size: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			user_id: {
				type: DataTypes.UUID,
				allowNull: false
			},
			action_plan_task_id: {
				type: DataTypes.UUID,
				allowNull: true
			},
			created_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			updated_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			deleted_at: {
				type: DataTypes.DATE,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'action_plan_task_attachments',
			modelName: 'action_plan_task_attachments',
			paranoid: true
		}
	);

	ActionPlanTaskAttachment.associate = (models) => {
		ActionPlanTaskAttachment.belongsTo(models.ActionPlanTask, {
			foreignKey: 'action_plan_task_id',
			as: 'action_plan_task'
		});

		ActionPlanTaskAttachment.belongsTo(models.User, {
			foreignKey: 'user_id',
			as: 'user'
		});
	};

	return ActionPlanTaskAttachment;
};
