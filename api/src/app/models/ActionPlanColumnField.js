import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const ActionPlanColumnField = sequelize.define(
		'ActionPlanColumnField',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV4,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			created_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			updated_at: {
				type: DataTypes.DATE,
				allowNull: false
			},
			deleted_at: {
				type: DataTypes.DATE,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'action_plan_column_fields',
			modelName: 'action_plan_column_fields',
			paranoid: true
		}
	);

	ActionPlanColumnField.associate = (models) => {
		ActionPlanColumnField.hasMany(models.ActionPlanUserPreference, {
			foreignKey: 'action_plan_column_field_id',
			as: 'user_preferences'
		});
	};

	return ActionPlanColumnField;
};
