import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const ActionPlansTask = sequelize.define(
		'ActionPlansTask',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			description: {
				type: DataTypes.TEXT,
				allowNull: false
			},
			type: {
				type: DataTypes.ENUM({
					values: ['TASK', 'SUB_TASK']
				}),
				allowNull: false,
				defaultValue: 'TASK'
			},
			is_completed: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: false
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			created_at: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updated_at: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'action_plans_tasks',
			modelName: 'action_plans_tasks'
		}
	);

	ActionPlansTask.associate = (models) => {
		ActionPlansTask.belongsTo(models.ActionPlan, {
			foreignKey: 'action_plan_id',
			as: 'action_plan'
		});
	};

	return ActionPlansTask;
};
