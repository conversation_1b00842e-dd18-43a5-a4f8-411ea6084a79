import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const RecoveryReport = sequelize.define(
		'RecoveryReport',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			type: {
				type: DataTypes.STRING
			},
			workstation: {
				type: DataTypes.STRING,
				allowNull: true
			},
			collection_date: {
				type: DataTypes.DATE,
				allowNull: true
			},
			conclusion: {
				type: DataTypes.STRING
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			defaultScope: {
				attributes: { exclude: ['updatedAt', 'createdAt', 'isActive'] }
			}
		},
		{
			defaultScope: {
				attributes: { exclude: ['updatedAt', 'createdAt', 'isActive'] }
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'recovery_reports',
			modelName: 'recovery_reports'
		}
	);

	RecoveryReport.associate = function (models) {
		RecoveryReport.belongsTo(models.User, {
			foreignKey: 'user_id',
			as: 'user'
		});
		RecoveryReport.belongsTo(models.File, {
			foreignKey: 'file_id',
			as: 'file'
		});
		RecoveryReport.belongsTo(models.Sector, {
			foreignKey: 'sector_id',
			as: 'sector'
		});
	};

	return RecoveryReport;
};
