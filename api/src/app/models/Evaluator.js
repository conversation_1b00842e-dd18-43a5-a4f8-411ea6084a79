import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const Evaluator = sequelize.define(
		'Evaluator',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'evaluators',
			modelName: 'evaluators',
			paranoid: true
		}
	);

	Evaluator.associate = (models) => {
		Evaluator.belongsTo(models.Company, {
			foreignKey: 'company_id',
			as: 'company'
		});

		Evaluator.hasMany(models.SeraReport, {
			foreignKey: 'evaluator_id',
			as: 'sera_report'
		});
		Evaluator.hasMany(models.BeraReport, {
			foreignKey: 'evaluator_id',
			as: 'bera_report'
		});
		Evaluator.hasMany(models.CustomReportResult, {
			foreignKey: 'evaluator_id',
			as: 'custom_report_result'
		});
	};

	return Evaluator;
};
