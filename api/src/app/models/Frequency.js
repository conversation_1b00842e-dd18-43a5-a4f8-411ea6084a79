import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const Frequency = sequelize.define(
		'Frequency',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.STRING,
				allowNull: false
			},
			score: {
				type: DataTypes.FLOAT,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'frequencies',
			modelName: 'frequencies',
			paranoid: true
		}
	);

	Frequency.associate = (models) => {
		Frequency.belongsTo(models.CustomReportStepKey, {
			foreignKey: 'custom_report_step_key_id',
			as: 'step_key'
		});
	};

	return Frequency;
};
