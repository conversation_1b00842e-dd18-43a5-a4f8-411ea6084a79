import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const TwoFactorAuthentication = sequelize.define(
		'TwoFactorAuthentication',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			type: {
				type: DataTypes.ENUM({
					values: ['EMAIL', 'AUTHENTICATOR']
				}),
				allowNull: false,
				defaultValue: 'EMAIL'
			},
			email_token_hash: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			email_token_expiration_date: {
				type: DataTypes.DATE,
				allowNull: true
			},
			last_email_sent: {
				type: DataTypes.DATE,
				allowNull: true
			},
			secret_hash: {
				type: DataTypes.STRING,
				allowNull: true
			},
			qr_code_url_hash: {
				type: DataTypes.STRING,
				allowNull: true
			},
			mac_address_hash: {
				type: DataTypes.STRING,
				allowNull: true
			},
			device_hash: {
				type: DataTypes.STRING,
				allowNull: true
			},
			validated: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: false
			},
			is_2fa_login_enabled: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: false
			},
			qr_code_auth_enabled: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: false
			},
			last_login: {
				type: DataTypes.DATE,
				allowNull: true
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true
			},
			created_at: {
				type: DataTypes.DATE
			},
			updated_at: {
				type: DataTypes.DATE
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'two_fa_users',
			modelName: 'two_fa_users'
		}
	);
	TwoFactorAuthentication.tableName = 'two_fa_users';

	TwoFactorAuthentication.associate = function (models) {
		TwoFactorAuthentication.belongsTo(models.User, {
			foreignKey: 'user_id',
			as: 'user'
		});
	};

	return TwoFactorAuthentication;
};
