import { optionsDefaultTable } from './commons/default-options-table.js';
import { ERGONOMIC_TOOL_NAMES } from '../util/constants-ergonomic-tools.js';
import { FileToErgonomicToolHookManager } from '../helpers/file-to-ergonomic-tool-hook-manager.js';

export default (sequelize, DataTypes) => {
	const StrainIndexReport = sequelize.define(
		'StrainIndexReport',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			input_left_borg_scale: {
				type: DataTypes.INTEGER,
				allowNull: false,
				validate: {
					min: 0,
					max: 10
				}
			},
			input_left_exertions: {
				type: DataTypes.INTEGER,
				allowNull: false,
				validate: {
					min: 0
				}
			},
			input_left_observation_time: {
				type: DataTypes.FLOAT(10, 2),
				allowNull: false,
				validate: {
					min: 0
				}
			},
			input_left_exertion_duration: {
				type: DataTypes.FLOAT(10, 2),
				allowNull: false,
				validate: {
					min: 0
				}
			},
			input_left_wrist_posture: {
				type: DataTypes.ENUM(['FLEXION', 'EXTENSION']),
				allowNull: false
			},
			input_left_wrist_angle: {
				type: DataTypes.FLOAT(10, 2),
				allowNull: false,
				validate: {
					min: 0,
					max: 90
				}
			},
			input_left_daily_duration: {
				type: DataTypes.FLOAT(10, 2),
				allowNull: false,
				validate: {
					min: 0,
					max: 12
				}
			},
			score_left_borg_scale: {
				type: DataTypes.FLOAT(10, 2),
				allowNull: false
			},
			score_left_efforts_per_minute: {
				type: DataTypes.FLOAT(10, 2),
				allowNull: false
			},
			score_left_exertion_duration: {
				type: DataTypes.FLOAT(10, 2),
				allowNull: false
			},
			score_left_wrist_posture: {
				type: DataTypes.FLOAT(10, 2),
				allowNull: false
			},
			score_left_daily_duration: {
				type: DataTypes.FLOAT(10, 2),
				allowNull: false
			},
			score_left_rsi: {
				type: DataTypes.FLOAT(10, 2),
				allowNull: false
			},
			left_risk: {
				type: DataTypes.ENUM(['SAFE', 'HAZARDOUS']),
				allowNull: false
			},
			input_right_borg_scale: {
				type: DataTypes.INTEGER,
				allowNull: false,
				validate: {
					min: 0,
					max: 10
				}
			},
			input_right_exertions: {
				type: DataTypes.INTEGER,
				allowNull: false,
				validate: {
					min: 0
				}
			},
			input_right_observation_time: {
				type: DataTypes.FLOAT(10, 2),
				allowNull: false,
				validate: {
					min: 0
				}
			},
			input_right_exertion_duration: {
				type: DataTypes.FLOAT(10, 2),
				allowNull: false,
				validate: {
					min: 0
				}
			},
			input_right_wrist_posture: {
				type: DataTypes.ENUM(['FLEXION', 'EXTENSION']),
				allowNull: false
			},
			input_right_wrist_angle: {
				type: DataTypes.FLOAT(10, 2),
				allowNull: false,
				validate: {
					min: 0,
					max: 90
				}
			},
			input_right_daily_duration: {
				type: DataTypes.FLOAT(10, 2),
				allowNull: false,
				validate: {
					min: 0,
					max: 12
				}
			},
			score_right_borg_scale: {
				type: DataTypes.FLOAT(10, 2),
				allowNull: false
			},
			score_right_efforts_per_minute: {
				type: DataTypes.FLOAT(10, 2),
				allowNull: false
			},
			score_right_exertion_duration: {
				type: DataTypes.FLOAT(10, 2),
				allowNull: false
			},
			score_right_wrist_posture: {
				type: DataTypes.FLOAT(10, 2),
				allowNull: false
			},
			score_right_daily_duration: {
				type: DataTypes.FLOAT(10, 2),
				allowNull: false
			},
			score_right_rsi: {
				type: DataTypes.FLOAT(10, 2),
				allowNull: false
			},
			right_risk: {
				type: DataTypes.ENUM(['SAFE', 'HAZARDOUS']),
				allowNull: false
			},
			collection_date: {
				type: DataTypes.DATE,
				allowNull: false
			},
			comment: {
				type: DataTypes.TEXT,
				allowNull: true
			},
			isActive: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true,
				field: 'is_active'
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'strain_index_reports',
			modelName: 'strain_index_reports',
			hooks: {
				afterCreate: async (instance) => {
					await FileToErgonomicToolHookManager.afterCreate({
						instance,
						tool_name: ERGONOMIC_TOOL_NAMES.STRAIN_INDEX,
						sequelize
					});
				},
				afterDestroy: async (instance) => {
					await FileToErgonomicToolHookManager.afterDestroy({
						instance,
						sequelize
					});
				},
				afterBulkDestroy: async (options) => {
					await FileToErgonomicToolHookManager.afterBulkDestroy({
						options,
						sequelize
					});
				}
			}
		}
	);

	StrainIndexReport.associate = (models) => {
		StrainIndexReport.belongsTo(models.File, {
			foreignKey: 'file_id',
			as: 'file'
		});
		StrainIndexReport.belongsTo(models.User, {
			foreignKey: 'report_user_id',
			as: 'report_user'
		});
	};

	return StrainIndexReport;
};
