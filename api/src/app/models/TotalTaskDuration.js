import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const TotalTaskDuration = sequelize.define(
		'TotalTaskDuration',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.STRING,
				allowNull: false
			},
			score: {
				type: DataTypes.FLOAT,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'total_task_durations',
			modelName: 'total_task_durations',
			paranoid: true
		}
	);

	TotalTaskDuration.associate = (models) => {
		TotalTaskDuration.belongsTo(models.CustomReportStepKey, {
			foreignKey: 'custom_report_step_key_id',
			as: 'step_key'
		});
	};

	return TotalTaskDuration;
};
