import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomReportStepKeyResult = sequelize.define(
		'CustomReportStepKeyResult',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			score: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			result: {
				type: DataTypes.FLOAT,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'custom_report_step_key_results',
			modelName: 'custom_report_step_key_results',
			paranoid: true
		}
	);

	CustomReportStepKeyResult.associate = (models) => {
		CustomReportStepKeyResult.belongsTo(models.CustomReportResult, {
			foreignKey: 'custom_report_result_id',
			as: 'custom_report_result'
		});
		CustomReportStepKeyResult.belongsTo(models.CustomReportStepKey, {
			foreignKey: 'custom_report_step_key_id',
			as: 'step_key'
		});
		CustomReportStepKeyResult.belongsTo(models.RiskCategory, {
			foreignKey: 'risk_category_id',
			as: 'risk_category'
		});
		CustomReportStepKeyResult.belongsTo(models.RiskDescription, {
			foreignKey: 'risk_description_id',
			as: 'risk_description'
		});
		CustomReportStepKeyResult.belongsTo(models.RiskDamage, {
			foreignKey: 'risk_damage_id',
			as: 'risk_damage'
		});
		CustomReportStepKeyResult.belongsTo(models.Severity, {
			foreignKey: 'severity_id',
			as: 'severity'
		});
		CustomReportStepKeyResult.belongsTo(models.Exposure, {
			foreignKey: 'exposure_id',
			as: 'exposure'
		});
		CustomReportStepKeyResult.belongsTo(models.Vulnerability, {
			foreignKey: 'vulnerability_id',
			as: 'vulnerability'
		});

		CustomReportStepKeyResult.hasMany(models.CustomReportStepKeyResult, {
			foreignKey: 'custom_report_result_id',
			as: 'step_key_results'
		});
	};

	return CustomReportStepKeyResult;
};
