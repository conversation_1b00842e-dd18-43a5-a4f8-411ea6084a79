'use strict';

import { Sequelize } from 'sequelize';
import config from '../../config/sandbox.cjs';
import { FileSandboxSyncHook } from './hooks/file_sandbox_sync.js';

import File from './File.js';
import Task from './Task.js';
import Cycle from './Cycle.js';
import Activity from './Activity.js';
import Severity from './Severity.js';
import BodyPart from './BodyPart.js';
import Line from '../models/Line.js';
import Exposure from './Exposure.js';
import Frequency from './Frequency.js';
import Evaluator from './Evaluator.js';
import RiskDamage from './RiskDamage.js';
import SeraReport from './SeraReport.js';
import Lesion from '../models/Lesion.js';
import BeraReport from './BeraReport.js';
import TasksFiles from './TasksFiles.js';
import SeraSummary from './SeraSummary.js';
import TasksCycles from './TasksCycles.js';
import StressLevel from './StressLevel.js';
import Product from '../models/Product.js';
import PlanFactory from '../models/Plan.js';
import UserFactory from '../models/User.js';
import RiskCategory from './RiskCategory.js';
import CustomReport from './CustomReport.js';
import SystemOfUnits from './SystemOfUnits.js';
import ErgonomicTool from './ErgonomicTool.js';
import Vulnerability from './Vulnerability.js';
import SectorFactory from '../models/Sector.js';
import BeraJobSummary from './BeraJobSummary.js';
import RebaReport from '../models/RebaReport.js';
import ActionPlan from '../models/ActionPlan.js';
import Exhibition from '../models/Exhibition.js';
import FileRiskResult from './FileRiskResult.js';
import CompanyFactory from '../models/Company.js';
import VoucherFactory from '../models/Voucher.js';
import RiskDescription from './RiskDescription.js';
import Workstation from '../models/Workstation.js';
import NioshReport from '../models/NioshReport.js';
import Probability from '../models/Probability.js';
import Consequence from '../models/Consequence.js';
import UserPlanFactory from '../models/UserPlan.js';
import CustomReportStep from './CustomReportStep.js';
import RangeRiskFactory from '../models/RangeRisk.js';
import SeraSummaryReview from './SeraSummaryReview.js';
import TotalTaskDuration from './TotalTaskDuration.js';
import SeraReportUpdated from './SeraReportUpdated.js';
import PEAToSuperPEA from '../models/PEAToSuperPEA.js';
import Anthropometry from '../models/Anthropometry.js';
import LibertyMutualTask from './LibertyMutualTask.js';
import BeraStepKeyResult from './BeraStepKeyResult.js';
import UserAccessFactory from '../models/UserAccess.js';
import CustomReportReview from './CustomReportReview.js';
import CustomReportsFiles from './CustomReportsFiles.js';
import SeraReviewSelector from './SeraReviewSelector.js';
import CustomReportResult from './CustomReportResult.js';
import SuperPeaReport from '../models/SuperPEAReport.js';
import BeraWeightedAverage from './BeraWeightedAverage.js';
import ActionPlansTask from '../models/ActionPlansTask.js';
import WorkConditionResult from './WorkConditionResult.js';
import AngleTimeReport from '../models/AngleTimeReport.js';
import LibertyMutualReport from './LibertyMutualReport.js';
import CustomReportStepKey from './CustomReportStepKey.js';
import CustomerCredits from '../models/CustomerCredits.js';
import OrganizationFactory from '../models/Organization.js';
import CustomerPlanFactory from '../models/CustomerPlan.js';
import CharacteristicResult from './CharacteristicResult.js';
import WorkerSelfEvaluation from './WorkerSelfEvaluation.js';
import PlanDowngradeFactory from '../models/PlanDowngrade.js';
import ActionPlanHistory from '../models/ActionPlanHistory.js';
import StrainIndexReport from '../models/StrainIndexReport.js';
import SeraReviewTasksResult from './SeraReviewTasksResult.js';
import RecoveryReportFactory from '../models/RecoveryReport.js';
import CustomReportSubStepKey from './CustomReportSubStepKey.js';
import TwoFactorAuthentication from './TwoFactorAuthentication.js';
import TwoFactorRecoveryTokens from './TwoFactorRecoveryTokens.js';
import PreliminaryAnalysis from '../models/PreliminaryAnalysis.js';
import BusinessInformation from '../models/BusinessInformation.js';
import CustomerTransactions from '../models/CustomerTransactions.js';
import LibertyMutualReportInput from './LibertyMutualReportInput.js';
import KimPushPullReportFactory from '../models/KimPushPullReport.js';
import PotentialCustomerFactory from '../models/PotentialCustomer.js';
import CustomReportStepKeyResult from './CustomReportStepKeyResult.js';
import PaymentTransactionFactory from '../models/PaymentTransaction.js';
import CustomReportAdditionalItem from './CustomReportAdditionalItem.js';
import CustomReportsOrganizations from './CustomReportsOrganizations.js';
import CustomerInformationFactory from '../models/CustomerInformation.js';
import KimManualHandlingReport from '../models/KimManualHandlingReport.js';
import PreliminaryAnalysisStep from '../models/PreliminaryAnalysisStep.js';
import CustomReportResultActionLog from './CustomReportResultActionLog.js';
import CustomReportSubStepKeyResult from './CustomReportSubStepKeyResult.js';
import CustomReportAdditionalItemType from './CustomReportAdditionalItemType.js';
import PreliminaryAnalysisStepKey from '../models/PreliminaryAnalysisStepKey.js';
import CustomReportStepKeysDefaultRisk from './CustomReportStepKeysDefaultRisk.js';
import CustomReportAdditionalItemOption from './CustomReportAdditionalItemOption.js';
import PreliminaryAnalysisStepLesion from '../models/PreliminaryAnalysisStepLesion.js';
import CustomReportSubStepKeysDefaultRisk from './CustomReportSubStepKeysDefaultRisk.js';
import CustomReportStepKeysAdditionalItem from './CustomReportStepKeysAdditionalItem.js';
import CustomReportStepKeysErgonomicTools from './CustomReportStepKeysErgonomicTools.js';
import BackCompressiveForceEstimationReport from './BackCompressiveForceEstimationReport.js';
import CustomReportStepKeyAdditionalItemResult from './CustomReportStepKeyAdditionalItemResult.js';
import ActionPlanV2 from './ActionPlanV2.js';
import ActionPlanHistoryV2 from './ActionPlanHistoryV2.js';
import ActionPlanHistoryType from './ActionPlanHistoryType.js';
import ActionPlanColumnField from './ActionPlanColumnField.js';
import ActionPlanUserPreference from './ActionPlanUserPreference.js';
import ActionPlanAttachment from './ActionPlanAttachment.js';
import ActionPlanComment from './ActionPlanComment.js';
import ActionPlanOrigin from './ActionPlanOrigin.js';
import ActionPlanRelatedReport from './ActionPlanRelatedReport.js';
import ActionPlanTask from './ActionPlanTask.js';
import ActionPlanTaskAttachment from './ActionPlanTaskAttachment.js';
import Notification from './Notification.js';
import NotificationType from './NotificationType.js';
import NotificationMethod from './NotificationMethod.js';
import UserNotificationPreference from './UserNotificationPreference.js';
import BeraJobSummaryFiles from './BeraJobSummaryFiles.js';
import SeraSummaryFiles from './SeraSummaryFiles.js';

const sequelize = new Sequelize(config.database, config.username, config.password, config);

const addHooksToModel = (model) => {
	model.addHook('beforeCreate', FileSandboxSyncHook.beforeCreate);
	model.addHook('afterCreate', FileSandboxSyncHook.afterCreate);
	model.addHook('beforeUpdate', FileSandboxSyncHook.beforeUpdate);
	model.addHook('afterUpdate', FileSandboxSyncHook.afterUpdate);
};

const dbSandbox = {
	Product: Product(sequelize, Sequelize),
	RebaReport: RebaReport(sequelize, Sequelize),
	NioshReport: NioshReport(sequelize, Sequelize),
	SystemOfUnits: SystemOfUnits(sequelize, Sequelize),
	AngleTimeReport: AngleTimeReport(sequelize, Sequelize),
	CustomerCredits: CustomerCredits(sequelize, Sequelize),
	LibertyMutualTask: LibertyMutualTask(sequelize, Sequelize),
	StrainIndexReport: StrainIndexReport(sequelize, Sequelize),
	LibertyMutualReport: LibertyMutualReport(sequelize, Sequelize),
	BusinessInformation: BusinessInformation(sequelize, Sequelize),
	CustomerTransactions: CustomerTransactions(sequelize, Sequelize),
	KimPushPullReport: KimPushPullReportFactory(sequelize, Sequelize),
	KimManualHandlingReport: KimManualHandlingReport(sequelize, Sequelize),
	LibertyMutualReportInput: LibertyMutualReportInput(sequelize, Sequelize),

	Lesion: Lesion(sequelize, Sequelize),
	Exhibition: Exhibition(sequelize, Sequelize),
	Probability: Probability(sequelize, Sequelize),
	Consequence: Consequence(sequelize, Sequelize),
	Anthropometry: Anthropometry(sequelize, Sequelize),
	PEAToSuperPEA: PEAToSuperPEA(sequelize, Sequelize),
	SuperPeaReport: SuperPeaReport(sequelize, Sequelize),
	PreliminaryAnalysis: PreliminaryAnalysis(sequelize, Sequelize),
	PreliminaryAnalysisStep: PreliminaryAnalysisStep(sequelize, Sequelize),
	PreliminaryAnalysisStepKey: PreliminaryAnalysisStepKey(sequelize, Sequelize),
	PreliminaryAnalysisStepLesion: PreliminaryAnalysisStepLesion(sequelize, Sequelize),

	ActionPlan: ActionPlan(sequelize, Sequelize),
	ActionPlansTask: ActionPlansTask(sequelize, Sequelize),
	ActionPlanHistory: ActionPlanHistory(sequelize, Sequelize),

	ActionPlanV2: ActionPlanV2(sequelize, Sequelize),
	ActionPlanHistoryV2: ActionPlanHistoryV2(sequelize, Sequelize),
	ActionPlanHistoryType: ActionPlanHistoryType(sequelize, Sequelize),
	ActionPlanColumnField: ActionPlanColumnField(sequelize, Sequelize),
	ActionPlanUserPreference: ActionPlanUserPreference(sequelize, Sequelize),
	ActionPlanAttachment: ActionPlanAttachment(sequelize, Sequelize),
	ActionPlanComment: ActionPlanComment(sequelize, Sequelize),
	ActionPlanOrigin: ActionPlanOrigin(sequelize, Sequelize),
	ActionPlanRelatedReport: ActionPlanRelatedReport(sequelize, Sequelize),
	ActionPlanTask: ActionPlanTask(sequelize, Sequelize),
	ActionPlanTaskAttachment: ActionPlanTaskAttachment(sequelize, Sequelize),

	Notification: Notification(sequelize, Sequelize),
	NotificationType: NotificationType(sequelize, Sequelize),
	NotificationMethod: NotificationMethod(sequelize, Sequelize),
	UserNotificationPreference: UserNotificationPreference(sequelize, Sequelize),

	Task: Task(sequelize, Sequelize),
	Cycle: Cycle(sequelize, Sequelize),
	Activity: Activity(sequelize, Sequelize),
	Exposure: Exposure(sequelize, Sequelize),
	Severity: Severity(sequelize, Sequelize),
	Evaluator: Evaluator(sequelize, Sequelize),
	Frequency: Frequency(sequelize, Sequelize),
	RiskDamage: RiskDamage(sequelize, Sequelize),
	SeraReport: SeraReport(sequelize, Sequelize),
	TasksFiles: TasksFiles(sequelize, Sequelize),
	BeraReport: BeraReport(sequelize, Sequelize),
	TasksCycles: TasksCycles(sequelize, Sequelize),
	StressLevel: StressLevel(sequelize, Sequelize),
	SeraSummary: SeraSummary(sequelize, Sequelize),
	RiskCategory: RiskCategory(sequelize, Sequelize),
	CustomReport: CustomReport(sequelize, Sequelize),
	Vulnerability: Vulnerability(sequelize, Sequelize),
	BeraJobSummary: BeraJobSummary(sequelize, Sequelize),
	RiskDescription: RiskDescription(sequelize, Sequelize),
	CustomReportStep: CustomReportStep(sequelize, Sequelize),
	SeraReportUpdated: SeraReportUpdated(sequelize, Sequelize),
	SeraSummaryReview: SeraSummaryReview(sequelize, Sequelize),
	BeraStepKeyResult: BeraStepKeyResult(sequelize, Sequelize),
	TotalTaskDuration: TotalTaskDuration(sequelize, Sequelize),
	SeraReviewSelector: SeraReviewSelector(sequelize, Sequelize),
	CustomReportsFiles: CustomReportsFiles(sequelize, Sequelize),
	CustomReportResult: CustomReportResult(sequelize, Sequelize),
	CustomReportReview: CustomReportReview(sequelize, Sequelize),
	WorkConditionResult: WorkConditionResult(sequelize, Sequelize),
	BeraWeightedAverage: BeraWeightedAverage(sequelize, Sequelize),
	CustomReportStepKey: CustomReportStepKey(sequelize, Sequelize),
	CharacteristicResult: CharacteristicResult(sequelize, Sequelize),
	WorkerSelfEvaluation: WorkerSelfEvaluation(sequelize, Sequelize),
	SeraReviewTasksResult: SeraReviewTasksResult(sequelize, Sequelize),
	CustomReportSubStepKey: CustomReportSubStepKey(sequelize, Sequelize),
	CustomReportStepKeyResult: CustomReportStepKeyResult(sequelize, Sequelize),
	CustomReportAdditionalItem: CustomReportAdditionalItem(sequelize, Sequelize),
	CustomReportsOrganizations: CustomReportsOrganizations(sequelize, Sequelize),
	CustomReportResultActionLog: CustomReportResultActionLog(sequelize, Sequelize),
	CustomReportSubStepKeyResult: CustomReportSubStepKeyResult(sequelize, Sequelize),
	CustomReportStepKeysDefaultRisk: CustomReportStepKeysDefaultRisk(sequelize, Sequelize),
	CustomReportAdditionalItemType: CustomReportAdditionalItemType(sequelize, Sequelize),
	CustomReportAdditionalItemOption: CustomReportAdditionalItemOption(sequelize, Sequelize),
	CustomReportSubStepKeysDefaultRisk: CustomReportSubStepKeysDefaultRisk(sequelize, Sequelize),
	CustomReportStepKeysAdditionalItem: CustomReportStepKeysAdditionalItem(sequelize, Sequelize),
	CustomReportStepKeysErgonomicTools: CustomReportStepKeysErgonomicTools(sequelize, Sequelize),
	CustomReportStepKeyAdditionalItemResult: CustomReportStepKeyAdditionalItemResult(sequelize, Sequelize),

	Line: Line(sequelize, Sequelize),
	File: File(sequelize, Sequelize),
	Plan: PlanFactory(sequelize, Sequelize),
	User: UserFactory(sequelize, Sequelize),
	BodyPart: BodyPart(sequelize, Sequelize),
	Sector: SectorFactory(sequelize, Sequelize),
	Company: CompanyFactory(sequelize, Sequelize),
	Voucher: VoucherFactory(sequelize, Sequelize),
	Workstation: Workstation(sequelize, Sequelize),
	UserPlan: UserPlanFactory(sequelize, Sequelize),
	RangeRisk: RangeRiskFactory(sequelize, Sequelize),
	ErgonomicTool: ErgonomicTool(sequelize, Sequelize),
	UserAccess: UserAccessFactory(sequelize, Sequelize),
	FileRiskResult: FileRiskResult(sequelize, Sequelize),
	Organization: OrganizationFactory(sequelize, Sequelize),
	CustomerPlan: CustomerPlanFactory(sequelize, Sequelize),
	PlanDowngrade: PlanDowngradeFactory(sequelize, Sequelize),
	RecoveryReport: RecoveryReportFactory(sequelize, Sequelize),
	PotentialCustomer: PotentialCustomerFactory(sequelize, Sequelize),
	PaymentTransaction: PaymentTransactionFactory(sequelize, Sequelize),
	CustomerInformation: CustomerInformationFactory(sequelize, Sequelize),
	TwoFactorAuthentication: TwoFactorAuthentication(sequelize, Sequelize),
	TwoFactorRecoveryTokens: TwoFactorRecoveryTokens(sequelize, Sequelize),
	BackCompressiveForceEstimationReport: BackCompressiveForceEstimationReport(sequelize, Sequelize),
	BeraJobSummaryFiles: BeraJobSummaryFiles(sequelize, Sequelize),
	SeraSummaryFiles: SeraSummaryFiles(sequelize, Sequelize)
};

Object.keys(dbSandbox).forEach((modelName) => {
	if (dbSandbox[modelName].associate) {
		dbSandbox[modelName].associate(dbSandbox);
	}
});

Object.entries(dbSandbox).forEach(([modelName, model]) => {
	if (model.tableName) {
		addHooksToModel(model, model.tableName);
	}
});

dbSandbox.sequelize = sequelize;
dbSandbox.Sequelize = Sequelize;

// await dbSandbox.sequelize.sync();

export default dbSandbox;
