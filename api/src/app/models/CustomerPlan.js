import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomerPlan = sequelize.define(
		'CustomerPlan',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: true
			},
			createdAt: {
				type: DataTypes.DATE,
				field: 'created_at'
			},
			updatedAt: {
				type: DataTypes.DATE,
				field: 'updated_at'
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'customer_plans',
			modelName: 'customer_plans'
		}
	);

	CustomerPlan.associate = (models) => {
		CustomerPlan.belongsTo(models.CustomerInformation, {
			foreignKey: 'customer_id',
			as: 'customer_information'
		});
		CustomerPlan.belongsTo(models.Plan, {
			foreignKey: 'plan_id',
			as: 'plan'
		});
	};

	return CustomerPlan;
};
