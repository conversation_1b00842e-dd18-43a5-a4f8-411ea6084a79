import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomReportSubStepKeyResult = sequelize.define(
		'CustomReportSubStepKeyResult',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			score: {
				type: DataTypes.FLOAT,
				allowNull: true
			},
			result: {
				type: DataTypes.FLOAT,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'custom_report_sub_step_key_results',
			modelName: 'custom_report_sub_step_key_results',
			paranoid: true
		}
	);

	CustomReportSubStepKeyResult.associate = (models) => {
		CustomReportSubStepKeyResult.belongsTo(models.CustomReportResult, {
			foreignKey: 'custom_report_result_id',
			as: 'custom_report_result'
		});
		CustomReportSubStepKeyResult.belongsTo(models.CustomReportSubStepKey, {
			foreignKey: 'custom_report_sub_step_key_id',
			as: 'sub_step_key'
		});
		CustomReportSubStepKeyResult.belongsTo(models.RiskCategory, {
			foreignKey: 'risk_category_id',
			as: 'risk_category'
		});
		CustomReportSubStepKeyResult.belongsTo(models.RiskDescription, {
			foreignKey: 'risk_description_id',
			as: 'risk_description'
		});
		CustomReportSubStepKeyResult.belongsTo(models.RiskDamage, {
			foreignKey: 'risk_damage_id',
			as: 'risk_damage'
		});
		CustomReportSubStepKeyResult.belongsTo(models.Severity, {
			foreignKey: 'severity_id',
			as: 'severity'
		});
		CustomReportSubStepKeyResult.belongsTo(models.Exposure, {
			foreignKey: 'exposure_id',
			as: 'exposure'
		});
		CustomReportSubStepKeyResult.belongsTo(models.Vulnerability, {
			foreignKey: 'vulnerability_id',
			as: 'vulnerability'
		});
	};

	return CustomReportSubStepKeyResult;
};
