import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const CustomReportSubStepKey = sequelize.define(
		'CustomReportSubStepKey',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			title: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.STRING,
				allowNull: false
			},
			sequence: {
				type: DataTypes.INTEGER,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'custom_report_sub_step_keys',
			modelName: 'custom_report_sub_step_keys',
			paranoid: true
		}
	);

	CustomReportSubStepKey.associate = (models) => {
		CustomReportSubStepKey.belongsTo(models.CustomReportStepKey, {
			foreignKey: 'custom_report_step_key_id',
			as: 'step_key'
		});

		CustomReportSubStepKey.hasMany(models.ActionPlan, {
			foreignKey: 'custom_report_sub_step_key_id',
			as: 'action_plan'
		});
		CustomReportSubStepKey.hasOne(models.CustomReportSubStepKeyResult, {
			foreignKey: 'custom_report_sub_step_key_id',
			as: 'custom_report_sub_step_key_result'
		});
		CustomReportSubStepKey.hasOne(models.CustomReportSubStepKeysDefaultRisk, {
			foreignKey: 'custom_report_sub_step_key_id',
			as: 'sub_step_key_default_risk'
		});
	};

	return CustomReportSubStepKey;
};
