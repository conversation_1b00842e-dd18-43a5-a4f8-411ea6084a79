import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const Exposure = sequelize.define(
		'Exposure',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			},
			description: {
				type: DataTypes.STRING,
				allowNull: false
			},
			score: {
				type: DataTypes.FLOAT,
				allowNull: true
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'exposures',
			modelName: 'exposures',
			paranoid: true
		}
	);

	Exposure.associate = (models) => {
		Exposure.belongsTo(models.CustomReportStepKey, {
			foreignKey: 'custom_report_step_key_id',
			as: 'step_key'
		});

		Exposure.hasMany(models.SeraReport, {
			foreignKey: 'exposure_id',
			as: 'sera_report'
		});
		Exposure.hasMany(models.CustomReportStepKeyResult, {
			foreignKey: 'exposure_id',
			as: 'step_key_result'
		});
		Exposure.hasMany(models.CustomReportSubStepKeyResult, {
			foreignKey: 'exposure_id',
			as: 'sub_step_key_result'
		});
	};

	return Exposure;
};
