import { optionsDefaultTable } from './commons/default-options-table.js';

export default (sequelize, DataTypes) => {
	const Line = sequelize.define(
		'Line',
		{
			id: {
				type: DataTypes.UUID,
				defaultValue: DataTypes.UUIDV1,
				primaryKey: true
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false
			}
		},
		{
			...optionsDefaultTable,
			tableName: 'lines',
			modelName: 'lines',
			paranoid: true,
			hooks: {
				beforeBulkDestroy: function (options) {
					options.individualHooks = true;
					return options;
				},
				afterDestroy: async function (instance, options) {
					const workstations = await sequelize.models.Workstation.findAll({
						where: {
							line_id: instance.id
						}
					});

					const workstations_ids = workstations
						.map((workstation) => workstation?.id)
						.filter((workstation) => workstation);

					await sequelize.models.Workstation.destroy({
						where: {
							id: workstations_ids
						}
					});
				}
			}
		}
	);

	Line.associate = (models) => {
		Line.belongsTo(models.Sector, {
			as: 'sector',
			foreignKey: 'sector_id'
		});
		Line.hasMany(models.Workstation, {
			foreignKey: 'line_id',
			as: 'workstation'
		});
	};

	return Line;
};
