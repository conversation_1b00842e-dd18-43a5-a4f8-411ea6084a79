export const BODY_PARTS_ENUM = {
	TRUNK: 'trunk',
	NECK: 'neck',
	LEFT_LOWER_ARM: 'left_lower_arm',
	RIGHT_LOWER_ARM: 'right_lower_arm',
	LEFT_UPPER_ARM: 'left_upper_arm',
	RIGHT_UPPER_ARM: 'right_upper_arm',
	LEFT_KNEE: 'left_knee',
	RIGHT_KNEE: 'right_knee',
	LEFT_ANKLE: 'left_ankle',
	RIGHT_ANK<PERSON>: 'right_ankle',
	HIP: 'hip',
	LEFT_HAND: 'left_hand',
	RIGHT_HAND: 'right_hand',
	LEFT_SQUAT: 'left_squat',
	RIGHT_SQUAT: 'right_squat'
};

export const KINEBOT_STEP_KEY_EXPOSURE_DESCRIPTION_MAPPER = {
	remotely: 'Remotely (>90% green)',
	rarely: 'Rarely (80 to 89% green)',
	frequent: 'Frequent (10 to 29% green)',
	irregular: 'Irregular (50 to 79% green)',
	to_be_continued: 'Continues (<9% green)',
	occasionally: 'Occasionally (30 to 49% green)'
};

export const WORKER_SELF_EVALUATION_ENUM = {
	STRESSFUL: 'stressful',
	VERY_STRESSFUL: 'very_stressful',
	LITTLE_STRESSFUL: 'little_stressful',
	NOTHING_STRESSFUL: 'nothing_stressful',
	EXTREMELY_STRESSFUL: 'extremely_stressful'
};

export const PDF_SECTIONS_SELECTION_ENUM = {
	REBA: 'reba',
	NIOSH: 'niosh',
	KIM_PP: 'kim_pp',
	KIM_MHO: 'kim_mho',
	ACTION_PLANS: 'action_plans',
	STRAIN_INDEX: 'strain_index',
	NOT_EVALUATED: 'not_evaluated',
	LIBERTY_MUTUAL: 'liberty_mutual',
	CHARACTERISTICS: 'characteristics',
	WORK_CONDITIONS: 'work_conditions'
};

export const CUSTOM_REPORTS_NAMES_ENUM = {
	bera: 'bera',
	sera: 'sera',
	ewa_d86: 'ewa_d86'
};

export const CUSTOM_REPORT_DEFAULT_STEPS_ENUM = {
	RESULT: 'results',
	INFORMATION: 'informations',
	VIDEO_SELECTION: 'video_selection',
	WORK_CONDITIONS: 'work_conditions',
	CHARACTERISTICS: 'characteristics'
};

export const supportMimeTypes = [
	'video/mp4',
	'video/avi',
	'video/MP2T',
	'video/mpeg',
	'video/3gpp',
	'video/x-flv',
	'video/x-ms-wmv',
	'video/quicktime',
	'video/x-msvideo',
	'video/x-matroska',
	'application/x-mpegURL'
];

export const ENUM_STATUS_FILE = {
	CORRUPTED_FILE: 'CORRUPTED_FILE',
	EXTRACTED_DATA: 'EXTRACTED_DATA',
	NOT_PROCESSED: 'NOT_PROCESSED',
	PROCESSING: 'PROCESSING',
	PROCESSED: 'PROCESSED',
	IN_QUEUE: 'IN_QUEUE'
};

export const TEST_PERIOD_DAYS = 10;

export const PRODUCT_ENUM = {
	TEST: 'test'
};

export const ROLE_USER_ENUM = {
	ADMIN: 'ADMIN',
	MASTER: 'MASTER',
	SUPERVISOR: 'SUPERVISOR',
	USER: 'USER'
};

export const WC_TOPIC_EVENT_ENUM = {
	NEW_USER: 'customer.created',
	RETRIEVE_ORDER: 'order.restored',
	UPDATED_ORDER: 'order.updated',
	CREATED_ORDER: 'order.created'
};

export const STRIPE_TOPIC_EVENT_ENUM = {
	PAYMENT_INTENT_CREATE: 'payment_intent.created',
	PAYMENT_INTENT_SUCCESS: 'payment_intent.succeeded',
	CHARGE_SUCCESS: 'charge.succeeded'
};

export const CUSTOMER_TRANSACTIONS_STATUS_ENUM = {
	CREATED: 'created',
	PROCESSING: 'processing',
	FINISHED: 'finished',
	FAILURE: 'failure'
};

export const RESPONSE_ERROR_STATUS = {
	AUTH_NOT_AUTHORIZED: {
		code: 403,
		message: 'Unauthorized requisition'
	},
	AUTH_TOKEN_NOT_INFORMED: {
		code: 400,
		message: 'Authorization header is missing'
	},
	INVALID_PARAMETERS: {
		code: 400,
		message: 'Check the parameters entered'
	},
	DATABASE_FAILED_PERFORM_QUERY: {
		code: 500,
		message: 'Failed to query the database'
	},
	DATABASE_FAILED_SAVE_DATA: {
		code: 500,
		message: 'Failed to save data'
	}
};

export const RESPONSE_ERROR_REPORTS = {
	REBA: {
		CALCULATION_SECOND_NOT_FOUND: {
			code: 500,
			message: 'Calculation per second not found'
		}
	},
	LIBERTY_MUTUAL: {
		INVALID_LIFT_HAND_HEIGHT: {
			code: 500,
			message: 'End hand height lower than start hand height. Should it be Lower task?'
		},
		INVALID_LOWER_HAND_HEIGHT: {
			code: 500,
			message: 'End hand height higher than start hand height. Should it be Lift task?'
		}
	}
};

export const RESPONSE_ERROR_ENTITIES = {
	ERGONOMIC_TOOL: {
		NOT_FOUND: {
			code: 404,
			message: 'Ergonomic tool not found'
		}
	},
	BUSINESS_INFORMATION: {
		NOT_FOUND: {
			code: 404,
			message: 'Business information not found'
		}
	},
	WORKER_SELF_EVALUATION: {
		NOT_FOUND: {
			code: 404,
			message: 'Worker self evaluation not found'
		}
	},
	ACTION_PLAN: {
		NOT_FOUND: {
			code: 404,
			message: 'Action plan not found'
		},
		HISTORY_FAIL_CREATE: {
			code: 500,
			message: 'Failed to create an history'
		},
		HISTORY_TYPE_NOT_FOUND: {
			code: 404,
			message: 'Action plan history type not found'
		},
		ALREADY_CREATED: {
			code: 400,
			message: 'Already created action plan'
		},
		FAIL_DELETE: {
			code: 500,
			message: 'Failed to deleted selected action plans'
		},
		FAIL_UPDATE: {
			code: 500,
			message: 'Failed to update selected action plans'
		},
		TASK_NOT_FOUND: {
			code: 404,
			message: 'Action plan task not found'
		},
		ORIGIN_REPORT_NOT_FOUND: {
			code: 404,
			message: 'Action plan origin report not found'
		},
		ORIGIN_TABLE_NOT_FOUND: {
			code: 404,
			message: 'Action plan origin table not found'
		},
		HISTORY_NOT_FOUND: {
			code: 404,
			message: 'Action plan history not found'
		},
		ATTACHMENT_NOT_FOUND: {
			code: 404,
			message: 'Action plan attachment not found'
		},
		EVIDENCE_NOT_FOUND: {
			code: 404,
			message: 'Action plan evidence not found'
		},
		MAX_ATTACHMENTS_REACHED: {
			code: 400,
			message: 'Maximum attachments reached'
		}
	},
	NOTIFICATION: {
		NOT_FOUND: {
			code: 404,
			message: 'Notification not found'
		},
		FAIL_CREATE: {
			code: 500,
			message: 'Failed to create notification'
		},
		TYPE_NOT_FOUND: {
			code: 404,
			message: 'Notification type not found'
		}
	},
	ACTION_PLAN_TASK: {
		NOT_FOUND: {
			code: 404,
			message: 'Action plan task not found'
		}
	},
	ACTION_PLAN_COMMENT: {
		NOT_FOUND: {
			code: 404,
			message: 'Action plan comment not found'
		},
		FAIL_CREATE: {
			code: 500,
			message: 'Failed to create action plan comment'
		},
		NOT_AUTHORIZED: {
			code: 403,
			message: 'Action plan comment not authorized'
		},
		FAIL_UPDATE: {
			code: 500,
			message: 'Failed to update action plan comment'
		}
	},
	TWO_FA: {
		NOT_FOUND: {
			code: 404,
			message: 'Two-factor authentication not found'
		},
		ALREADY_CREATED: {
			code: 400,
			message: 'Already created two-factor authentication'
		},
		INVALID_TOKEN: {
			code: 400,
			message: 'Invalid token'
		},
		ALREADY_VALIDATED: {
			code: 400,
			message: 'Token already validated'
		},
		NOT_YET_VALIDATED: {
			code: 400,
			message: 'Token not yet validated'
		},
		EXPIRED: {
			code: 400,
			message: 'Token expired'
		},
		TOO_EARLY: {
			code: 400,
			message: 'Too early to resend email'
		},
		FAILED_TO_CHECK_TOKEN: {
			code: 500,
			message: 'Something went wrong checking token'
		},
		SAME_TYPE: {
			code: 400,
			message: 'Authentication type same as default'
		},
		AUTH_TYPE_ENABLED: {
			code: 400,
			message: 'Authentication type through app already enabled'
		},
		NOT_ENABLED: {
			code: 400,
			message: 'Two-factor authentication not enabled'
		}
	},
	PRELIMINARY_ANALYSIS: {
		NOT_FOUND: {
			code: 404,
			message: 'Preliminary analysis not found'
		},
		STEP_DEFAULT_NOT_FOUND: {
			code: 404,
			message: 'Default step not found'
		}
	},
	PRELIMINARY_ANALYSIS_STEP: {
		NOT_FOUND: {
			code: 404,
			message: 'Analysis step not found'
		}
	},
	PROBABILITY: {
		NOT_FOUND: {
			code: 404,
			message: 'Probabilty analysis not found'
		}
	},
	CONSEQUENCE: {
		NOT_FOUND: {
			code: 404,
			message: 'Consequence analysis not found'
		}
	},
	EXHIBITION: {
		NOT_FOUND: {
			code: 404,
			message: 'Exhibition analysis not found'
		}
	},
	RANGE_RISK: {
		NOT_FOUND: {
			code: 404,
			message: 'Range risk not found'
		},
		FAIL_DOWNLOAD_DATA: {
			code: 500,
			message: 'Failed to fetch risk tracks'
		},
		CREATION_LIMIT: {
			code: 400,
			message: 'Limit of bands reached'
		}
	},
	ORGANIZATION: {
		NOT_FOUND: {
			message: 'Organization not found',
			code: 404
		},
		EXISTS_COMPANIES_REGISTERED: {
			code: 400,
			message: 'Organization still has registered companies'
		},
		FAIL_CREATE: {
			code: 500,
			message: 'Failed to create an organization'
		}
	},
	COMPANY: {
		NOT_FOUND: {
			code: 404,
			message: 'Company not found'
		},
		FAIL_CREATE: {
			code: 500,
			message: 'Failed to create company'
		},
		NOT_AUTHORIZED: {
			code: 403,
			message: 'Not authorized to access company'
		}
	},
	SECTOR: {
		NOT_FOUND: {
			code: 404,
			message: 'Sector not found'
		}
	},
	WORKSTATION: {
		NOT_FOUND: {
			code: 404,
			message: 'Workstation not found'
		}
	},
	SYSTEM_OF_UNITS: {
		NOT_FOUND: {
			code: 404,
			message: 'System of units not found'
		}
	},
	LIBERTY_MUTUAL_TASK: {
		NOT_FOUND: {
			code: 404,
			message: 'Liberty Mutual task not found'
		}
	},
	LIBERTY_MUTUAL_REPORT: {
		NOT_FOUND: {
			code: 404,
			message: 'Liberty Mutual report not found'
		}
	},
	CUSTOM_REPORT_RESULT: {
		NOT_FOUND: {
			code: 404,
			message: 'Custom report result not found'
		},
		ALREADY_CREATED: {
			code: 404,
			message: 'Already created custom report result'
		}
	},
	CUSTOM_REPORT_STEP_KEY_RESULT: {
		NOT_FOUND: {
			code: 404,
			message: 'Custom report step key result not found'
		},
		ALREADY_CREATED: {
			code: 404,
			message: 'Already created custom report step key result'
		},
		FAIL_CREATE: {
			code: 500,
			message: 'Failed to create custom report step key result'
		},
		FAIL_UPDATE: {
			code: 500,
			message: 'Failed to update custom report step key result'
		}
	},
	CUSTOM_REPORT_STEP_KEY_ADDITIONAL_ITEM_RESULT: {
		NOT_FOUND: {
			code: 404,
			message: 'Custom report step key additional item result not found'
		},
		FAIL_CREATE: {
			code: 500,
			message: 'Failed to create custom report step key additional item result'
		},
		FAIL_UPDATE: {
			code: 500,
			message: 'Failed to update custom report step key additional item result'
		}
	},
	WORK_CONDITION_RESULT: {
		NOT_FOUND: {
			code: 404,
			message: 'Work condition result not found'
		},
		ALREADY_CREATED: {
			code: 400,
			message: 'Already created work condition result'
		}
	},
	CHARACTERISTIC_RESULT: {
		NOT_FOUND: {
			code: 404,
			message: 'Characteristic result not found'
		},
		ALREADY_CREATED: {
			code: 400,
			message: 'Already created characteristic result'
		}
	},
	CUSTOM_REPORT_STEP: {
		NOT_FOUND: {
			code: 404,
			message: 'Custom report step not found'
		}
	},
	CUSTOM_REPORT_STEP_KEY: {
		NOT_FOUND: {
			code: 404,
			message: 'Custom report step key not found'
		}
	},
	CUSTOM_REPORT_SUB_STEP_KEY: {
		NOT_FOUND: {
			code: 404,
			message: 'Custom report sub step key not found'
		}
	},
	FILE: {
		UNSUPPORTED: {
			code: 400,
			message: 'Unsupported file'
		},
		CORRUPTED_VIDEO: {
			code: 400,
			message: 'Corrupted video'
		},
		NOT_FOUND: {
			code: 404,
			message: 'File not found'
		},
		DATA_NOT_FOUND: {
			code: 404,
			message: 'Data not found'
		},
		FAIL_READ_SENT_FILE: {
			code: 500,
			message: 'Failed to read sent file'
		},
		FAILED_SAVE_FILE: {
			code: 500,
			message: 'Failed to save file'
		},
		FAILED_CREATE_DOWNLOAD_URL: {
			code: 500,
			message: 'Failed to create download url'
		},
		FILE_ALREADY_QUEUED: {
			code: 400,
			message: 'File already queued'
		},
		FILE_ALREADY_PROCESSED: {
			code: 400,
			message: 'File already processed'
		},
		READ_FAILURE: {
			code: 500,
			message: 'Failed to read file'
		}
	},
	USER: {
		ALREADY_REGISTERED: {
			code: 400,
			message: 'Already registered user'
		},
		NOT_FOUND: {
			code: 404,
			message: 'User not found'
		},
		INVALID_CURRENT_PASSWORD: {
			code: 400,
			message: 'Invalid current password'
		},
		FAILED_CREATE: {
			code: 500,
			message: 'Failed to create user'
		},
		INVALID_EMAIL_OR_PASSWORD: {
			code: 400,
			message: 'Invalid email or password'
		},
		USER_ALREADY_REGISTERED: {
			code: 400,
			message: 'User already registered'
		}
	},
	CUSTOMER_PLAN: {
		FAIL_CREATE: {
			code: 500,
			status: 'fail',
			message: 'Failed to create customer plan'
		},
		FAILED_CREATE: {
			code: 500,
			message: 'Failed to create user plan'
		},
		NOT_FOUND: {
			code: 404,
			message: 'Customer plan not found'
		},
		MAX_REGISTERED_USERS: {
			code: 400,
			message: 'Maximum registered users'
		}
	},
	CUSTOMER_TRANSACTIONS: {
		FAIL_CREATE_TRANSACTION: {
			code: 500,
			status: 'fail',
			message: 'Failed to create customer transaction'
		},
		NOT_FOUND: {
			code: 404,
			message: 'Customer transaction not found'
		},
		TRANSACTION_ALREADY_COMPLETED: {
			code: 400,
			message: 'Customer transaction already completed'
		}
	},
	PRODUCT: {
		NOT_FOUND: {
			code: 404,
			message: 'Product not found'
		}
	},
	CUSTOMER_CREDITS: {
		FAIL_CREATE_CUSTOMER_CREDITS: {
			code: 500,
			status: 'fail',
			message: 'Failed to create customer credits'
		}
	},
	PLAN: {
		NOT_FOUND: {
			code: 404,
			message: 'Plan not found'
		},
		MINUTES_LIMITE_REACHED: {
			code: 400,
			message: 'Processed minutes limit reached'
		},
		INVALID_RECURRENCE: {
			code: 400,
			message: 'Invalid recurrence'
		},
		ALREADY_CANCELED: {
			code: 400,
			message: 'Plan already canceled'
		},
		INVALID_SUBSCRIPTION_TYPE: {
			code: 500,
			message: 'Invalid subscription type'
		},
		RECENTLY_HIRED_PLAN: {
			code: 400,
			message: 'It is necessary to spend a day after contracting the service to change the plan'
		},
		SELECTED_PLAN_ALREADY_IN_USE: {
			code: 400,
			message: 'Choose a plan different from your current plan'
		},
		DOWNGRADE_YEARLY_PLAN: {
			code: 400,
			message: 'It is not possible to downgrade the annual plan'
		}
	},
	WEB_HOOKS: {
		INVALID_EVENT: {
			code: 400,
			message: 'Invalid event'
		}
	},
	USER_ACCESS: {
		NOT_AUTHORIZED: {
			code: 403,
			message: 'Not authorized'
		},
		NOT_FOUND: {
			message: 'User access not found',
			code: 404
		},
		FAIL_CREATE: {
			code: 500,
			message: 'Failed to create a user permission'
		}
	},
	CUSTOMER_INFORMATION: {
		NOT_FOUND: {
			code: 404,
			message: 'Customer not found'
		},
		UNREGISTERED_CUSTOMER: {
			code: 400,
			message: 'Unregistered customer'
		},
		ALREADY_REGISTERED: {
			code: 400,
			message: 'Already registered customer'
		},
		FAIL_CREATE_CUSTOMER: {
			code: 500,
			status: 'fail',
			message: 'Failed to create customer'
		},
		FAIL_UPDATE_CUSTOMER: {
			code: 500,
			status: 'fail',
			message: 'Failed to update customer'
		},
		EXPIRATION_PLAN: {
			code: 400,
			message: 'Account subscription expired, renew to continue.'
		},
		EXPIRATION_PLAN_NOT_FOUND: {
			code: 400,
			message: 'Plan expiration date not found'
		},
		DOCUMENT_ALREADY_REGISTERED: {
			code: 400,
			message: 'Document already registered'
		},
		CANCELED_PLAN: {
			code: 400,
			message: 'Account subscription cancelled, renew to continue.'
		}
	},
	REPORT: {
		NOT_FOUND: {
			code: 404,
			message: 'Report not found'
		},
		ALREADY_CREATED: {
			code: 400,
			message: 'Already created report'
		},
		FAIL_CREATE: {
			code: 500,
			status: 'fail',
			message: 'Failed to create report'
		},
		FAIL_CREATE_PDF: {
			code: 500,
			status: 'fail',
			message: 'Failed to create report pdf'
		},
		FAIL_UPDATE: {
			code: 500,
			status: 'fail',
			message: 'Failed to update report'
		},
		DEPRECATED_DATA_FILE: {
			code: 400,
			status: 'error',
			message: 'Requested video data is old, please upload to continue'
		}
	},
	TASK: {
		NOT_FOUND: {
			code: 404,
			message: 'Task not found'
		},
		ALREADY_EXISTS: {
			code: 409,
			message: 'Task already exists'
		}
	},
	CYCLE: {
		NOT_FOUND: {
			code: 404,
			message: 'Cycle not found'
		},
		ALREADY_EXISTS: {
			code: 409,
			message: 'Cycle already exists'
		}
	},
	STRESS_LEVEL: {
		NOT_FOUND: {
			code: 404,
			message: 'Stress level not found'
		}
	},
	FREQUENCY: {
		NOT_FOUND: {
			code: 404,
			message: 'Frequency not found'
		}
	},
	TOTAL_TASK_DURATION: {
		NOT_FOUND: {
			code: 404,
			message: 'Total task duration not found'
		}
	},
	EVALUATOR: {
		NOT_FOUND: {
			code: 404,
			message: 'Evaluator not found'
		}
	},
	EXPOSURE: {
		NOT_FOUND: {
			code: 404,
			message: 'Exposure not found'
		}
	},
	VULNERABILITY: {
		NOT_FOUND: {
			code: 404,
			message: 'Vulnerability not found'
		}
	},
	SEVERITY: {
		NOT_FOUND: {
			code: 404,
			message: 'Severity not found'
		}
	},
	RISK_DESCRIPTION: {
		NOT_FOUND: {
			code: 404,
			message: 'Risk description not found'
		}
	},
	RISK_DAMAGE: {
		NOT_FOUND: {
			code: 404,
			message: 'Risk damage not found'
		},
		SEVERITY_ALREADY_SET: {
			code: 409,
			message: 'Severity already set to this risk damage'
		}
	},
	RISK_CATEGORY: {
		NOT_FOUND: {
			code: 404,
			message: 'Risk category not found'
		}
	},
	CUSTOM_REPORT: {
		NOT_FOUND: {
			code: 404,
			message: 'Custom report not found'
		},
		FAILED_FIND_RESULTS_BY_REPORT_TYPE: {
			code: 500,
			message: 'Failed to find results by report type'
		}
	},
	CUSTOM_REPORT_ADDITIONAL_ITEM_OPTION: {
		NOT_FOUND: {
			code: 404,
			message: 'Custom report additional item option not found'
		}
	},
	CUSTOM_REPORT_ADDITIONAL_ITEM: {
		CALCULATE_ERROR: {
			code: 400,
			message: 'Error calculating custom report additional item result'
		}
	},
	CUSTOM_REPORT_REVIEW: {
		NOT_FOUND: {
			code: 404,
			message: 'Custom report review not found'
		},
		HISTORY_NOT_FOUND: {
			code: 404,
			message: 'Custom report review history not found'
		}
	},
	BERA_STEP_KEY_RESULT: {
		NOT_FOUND: {
			code: 404,
			message: 'Bera step key result not found'
		},
		ALREADY_EXISTS: {
			code: 409,
			message: 'Bera step key result already exists'
		}
	},
	BERA_WEIGHTED_AVERAGE: {
		NOT_FOUND: {
			code: 404,
			message: 'Bera weighted average not found'
		},
		ALREADY_EXISTS: {
			code: 409,
			message: 'Bera weighted average already exists'
		}
	},
	BERA_REPORT: {
		NOT_FOUND: {
			code: 404,
			message: 'Bera report not found'
		},
		ALREADY_EXISTS: {
			code: 409,
			message: 'Bera report already exists'
		}
	},
	BERA_JOB_SUMMARY: {
		NOT_FOUND: {
			code: 404,
			message: 'Bera job summary not found'
		},
		RESULT_NOT_FOUND: {
			code: 404,
			message: 'Bera job summary result not found'
		},
		ALREADY_EXISTS: {
			code: 409,
			message: 'Bera job summary already exists'
		}
	},
	SERA_REPORT: {
		NOT_FOUND: {
			code: 404,
			message: 'Sera report not found'
		},
		ALREADY_EXISTS: {
			code: 409,
			message: 'Sera report already exists'
		}
	},
	SERA_SUMMARY: {
		NOT_FOUND: {
			code: 404,
			message: 'Sera summary not found'
		},
		ALREADY_EXISTS: {
			code: 409,
			message: 'Sera summary already exists'
		}
	},
	SERA_SUMMARY_REVIEW: {
		NOT_FOUND: {
			code: 404,
			message: 'Sera summary review not found'
		},
		ALREADY_EXISTS: {
			code: 409,
			message: 'Sera summary review already exists'
		}
	},
	STRAIN_INDEX: {
		NOT_FOUND: {
			code: 404,
			message: 'Report data not found'
		}
	},
	RECOVERY_REPORT: {
		NOT_FOUND: {
			code: 404,
			message: 'Report data not found'
		}
	},
	MASTER: {
		NOT_FOUND: {
			code: 404,
			message: 'Master user not found'
		}
	},
	POTENTIAL_CUSTOMER: {
		NOT_FOUND: {
			code: 404,
			message: 'Potential customer not found'
		},
		CUSTOMER_REGISTERED: {
			code: 400,
			message: 'Customer already registered'
		}
	},
	VOUCHER: {
		NOT_FOUND: {
			code: 404,
			message: 'Voucher not found'
		},
		INVALID: {
			code: 400,
			message: 'Invalid voucher'
		}
	},
	HOOKS: {
		BAD_REQUEST: {
			code: 400,
			message: 'Failed to decode payload'
		}
	},
	LINE: {
		NOT_FOUND: {
			code: 404,
			message: 'Line not found'
		}
	}
};

export const ERROR_RESPONSE_EXTERNAL_SERVICE = {
	STORAGE: {
		FAIL_UPLOAD_FILE: {
			code: 500,
			message: 'Failed to upload file to storage'
		},
		FAIL_CREATE_SIGNATURE: {
			code: 500,
			message: 'Failed to create file signature'
		},
		FAIL_DOWNLOAD_DATA: {
			code: 500,
			message: 'Failed to download data'
		}
	},
	QUEUE: {
		FAIL_COMMUNICATION: {
			code: 500,
			message: 'Reply queue communication failure'
		}
	},
	SNS: {
		FAIL_SEND_MESSAGE: {
			code: 500,
			message: 'Failed to send message to sns'
		}
	}
};

export const RESPONSE_ENTITIES = {
	PRELIMINARY_ANALYSIS: {
		CANNOT_CHANGE_CONSOLIDATED: 'Cannot change a consolidated analysis',
		COMMENT_SUCCESSFULLY_SAVED: 'Comments successfully saved'
	},
	ACTION_PLAN: {
		CREATED: 'Action plan created',
		UPDATED: 'Action plan updated',
		DELETED: 'Selected action plans deleted successfully',
		EVIDENCE_REMOVED: 'Evidence removed',
		ATTACHMENT_REMOVED: 'Attachment removed',
		TASK_UPDATED: 'Task updated successfully',
		TASK_CREATED: 'Task created successfully',
		TASK_REMOVED: 'Task removed successfully',
		ATTACHMENT_UPDATED: 'Attachment updated successfully',
		EVIDENCE_UPDATED: 'Evidence updated successfully',
		PREFERENCES_UPDATED: 'Action plan preferences updated',
		COMMENT_UPDATED: 'Action plan comment updated successfully',
		COMMENT_DELETED: 'Action plan comment deleted successfully'
	},
	CRON_JOB: {
		STARTED_SUCCESSFULLY: 'Cron job started successfully'
	}
};

export const RESPONSE_STATUS = {
	SUCCESS: 'success',
	WARNING: 'warning',
	ERROR: 'error'
};

export const SOCKET_EVENTS = {
	BACK_COMPRESSIVE_FORCE_ESTIMATION_REPORT: 'BACK_COMPRESSIVE_FORCE_ESTIMATION_REPORT',
	LIBERTY_MUTUAL_REPORT: 'LIBERTY_MUTUAL_REPORT',
	STRAIN_INDEX_REPORT: 'STRAIN_INDEX_REPORT',
	UPDATE_FILE_STATUS: 'UPDATE_FILE_STATUS',
	KIM_MHO_REPORT: 'KIM_MHO_REPORT',
	KIM_PP_REPORT: 'KIM_PP_REPORT',
	NIOSH_REPORT: 'NIOSH_REPORT',
	DATA_EXTRACTION_COMPLETED: 'DATA_EXTRACTION_COMPLETED'
};

export const FILE_STATUS = {
	EXTRACTED_DATA: 'EXTRACTED_DATA',
	NOT_PROCESSED: 'NOT_PROCESSED',
	PROCESSING: 'PROCESSING',
	PROCESSED: 'PROCESSED'
};

export const DEFAULT_BUSINESS_INFORMATION = {
	company_name: 'Kinebot',
	fantasy_name: 'Kinebot - Tecnologia da Informacao LTDA',
	cnpj: '41.350.359/0001-76',
	address: 'Marginal Comendador Franco, 1341',
	city: 'Curitiba',
	state: 'Paraná',
	zipcode: '80215-090',
	district: 'Jardim Botânico'
};

export const TRACKED_TABLES = [
	'sectors',
	'lines',
	'workstations',
	'activities',
	'cycles',
	'evaluators',
	'risk_categories',
	'risk_descriptions',
	'risk_damages',
	'tasks',
	'tasks_cycles',
	'work_condition_results',
	'companies',
	'business_informations',
	'organizations',
	'consequences',
	'custom_report_additional_item_options',
	'custom_report_additional_item_types',
	'custom_report_additional_items',
	'body_parts',
	'custom_report_step_keys',
	'custom_report_sub_step_keys',
	'custom_report_step_keys_default_risks',
	'custom_report_step_keys_ergonomic_tools',
	'custom_report_steps',
	'custom_reports',
	'customer_credits',
	'customer_informations',
	'customer_plans',
	'ergonomic_tools',
	'exhibitions',
	'exposures',
	'frequencies',
	'injuries',
	'liberty_mutual_tasks',
	'plans',
	'probabilities',
	'products',
	'range_risks',
	'severities',
	'stress_levels',
	'systems_of_units',
	'total_task_durations',
	'two_fa_recovery_tokens',
	'two_fa_users',
	'users',
	'user_plans',
	'user_accesses',
	'vulnerabilities',
	'worker_self_evaluations',
	'action_plan_history_types'
];
