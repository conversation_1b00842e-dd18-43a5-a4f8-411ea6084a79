import AWS from 'aws-sdk';
import config from 'config';
import winston from 'winston';
import WinstonCloudWatch from 'winston-cloudwatch';
import { credentials } from './credentials/aws.js';
import { StorageContext } from '../utils/storage_context.js';

const PRODUCTION = 'production';

const levels = {
	fatal: 0,
	error: 1,
	warn: 2,
	info: 3,
	debug: 4,
	trace: 5
};

const winston_config = {
	levels: levels,
	format: winston.format.json(),
	transports: [
		new winston.transports.Console({
			timestamp: true,
			colorize: true
		})
	]
};

const logger = new winston.createLogger(winston_config);

if (process.env.NODE_ENV === PRODUCTION) {
	const cloudwatch_config = {
		cloudWatchLogs: new AWS.CloudWatchLogs(credentials),
		logGroupName: config.get('App.logger.cloudwatch.group_name'),
		logStreamName: config.get('App.logger.cloudwatch.stream_name'),
		messageFormatter: ({ level, message, tags, meta }) => {
			let message_format = `[${level}] : ${message} - meta: ${JSON.stringify({ tags, ...meta })}`;
			return message_format;
		}
	};
	logger.add(new WinstonCloudWatch(cloudwatch_config));
}

const loggerRequest = (req, _, next) => {
	const log_request_payload = {
		tags: 'http',
		meta: {
			body: req.body,
			params: req.params,
			query: req.query,
			headers: req.headers
		}
	};

	logger.info(`Requesting ${req.method} ${req.originalUrl}`, log_request_payload);

	next();
};

const loggerProxy = new Proxy(logger, {
	get(target, prop) {
		if (typeof target[prop] === 'function') {
			return function (message, meta = {}) {
				const context = StorageContext.getStore();
				const environment = context?.environment;

				const prefix =
					environment && environment !== 'kinebot'
						? `[${environment[0].toUpperCase() + environment.substring(1)}]`
						: '';

				return target[prop](`${prefix}${message}`, { ...meta });
			};
		}
		return target[prop];
	}
});

export { loggerProxy as logger, loggerRequest };
