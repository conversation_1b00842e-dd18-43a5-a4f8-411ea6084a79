export class PeaReport {
	custom_report_result;
	custom_report_step_key_results;
	custom_report_result_actions_logs;
	custom_report_step_key_additional_item_results;
	file_id;
	processed_file_id;
	hierarchy = [
		'niosh',
		'kim_mho',
		'reba',
		'kim_push_pull',
		'strain_index',
		'liberty_mutual',
		'back_compressive_force_estimation',
		'action_plan'
	];

	setCustomReportResult(custom_report_result) {
		this.custom_report_result = custom_report_result;
	}

	setCustomReportStepKeyResults(custom_report_step_key_results) {
		this.custom_report_step_key_results = custom_report_step_key_results;
	}

	setCustomReportResultActionsLogs(custom_report_result_actions_logs) {
		this.custom_report_result_actions_logs = custom_report_result_actions_logs;
	}

	setCustomReportStepKeyAdditionalItemResults(custom_report_step_key_additional_item_results) {
		this.custom_report_step_key_additional_item_results = custom_report_step_key_additional_item_results;
	}

	getCustomReportResult() {
		return this.custom_report_result;
	}

	getCustomReportStepKeyResults() {
		return this.custom_report_step_key_results;
	}

	getCustomReportResultActionsLogs() {
		return this.custom_report_result_actions_logs;
	}

	getCustomReportStepKeyAdditionalItemResults() {
		return this.custom_report_step_key_additional_item_results;
	}

	getFileId() {
		return this.file_id;
	}

	getProcessedFileId() {
		return this.processed_file_id;
	}

	getFileAETHierarchy() {
		return this.hierarchy;
	}
}
