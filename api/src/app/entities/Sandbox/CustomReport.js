export class CustomReport {
	custom_report_reviews;
	custom_report_results;
	custom_report_step_key_results;
	custom_report_sub_step_key_results;
	custom_report_result_actions_logs;
	custom_report_step_key_additional_item_results;
	hierarchy = [
		'niosh',
		'kim_mho',
		'reba',
		'kim_push_pull',
		'strain_index',
		'liberty_mutual',
		'back_compressive_force_estimation',
		'action_plan'
	];
	processed_file_ids = [];
	file_ids = [];

	setCustomReportReviews(custom_report_reviews) {
		this.custom_report_reviews = custom_report_reviews;
	}

	setCustomReportResults(custom_report_results) {
		this.custom_report_results = custom_report_results;
	}

	setCustomReportStepKeyResults(custom_report_step_key_results) {
		this.custom_report_step_key_results = custom_report_step_key_results;
	}

	setCustomReportResultActionsLogs(custom_report_result_actions_logs) {
		this.custom_report_result_actions_logs = custom_report_result_actions_logs;
	}

	setCustomReportStepKeyAdditionalItemResults(custom_report_step_key_additional_item_results) {
		this.custom_report_step_key_additional_item_results = custom_report_step_key_additional_item_results;
	}

	setCustomReportSubStepKeyResults(custom_report_sub_step_key_results) {
		this.custom_report_sub_step_key_results = custom_report_sub_step_key_results;
	}

	getCustomReportReviews() {
		return this.custom_report_reviews;
	}

	getCustomReportResults() {
		return this.custom_report_results;
	}

	getCustomReportStepKeyResults() {
		return this.custom_report_step_key_results;
	}

	getCustomReportResultActionsLogs() {
		return this.custom_report_result_actions_logs;
	}

	getCustomReportStepKeyAdditionalItemResults() {
		return this.custom_report_step_key_additional_item_results;
	}

	getCustomReportSubStepKeyResults() {
		return this.custom_report_sub_step_key_results;
	}

	getFileAETHierarchy() {
		return this.hierarchy;
	}
}
