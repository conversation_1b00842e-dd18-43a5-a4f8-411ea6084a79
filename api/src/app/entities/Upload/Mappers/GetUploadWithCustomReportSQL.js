export class GetUploadWithCustomReportSQL {
	constructor({ limit, offset, file_ids, user_id }) {
		this.limit = limit;
		this.offset = offset;
		this.file_ids = file_ids;
		this.user_id = user_id;
	}

	#getCommonQuery() {
		return `
			FROM files f
			LEFT OUTER JOIN custom_reports_files crf ON crf.file_id = f.id
			LEFT OUTER JOIN sectors s ON f.sector_id = s.id
			LEFT OUTER JOIN workstations w ON f.workstation_id = w.id
			LEFT OUTER JOIN \`lines\` AS l ON w.line_id = l.id
			LEFT OUTER JOIN (
				SELECT pa1.*
				FROM custom_report_results pa1
				INNER JOIN (
					SELECT file_id, MAX(consolidated) AS max_consolidated
					FROM custom_report_results
					WHERE deleted_at IS NULL
					GROUP BY file_id
				) pa2 ON pa1.file_id = pa2.file_id AND pa1.consolidated = pa2.max_consolidated
				WHERE pa1.deleted_at IS NULL
			) AS pa ON f.id = pa.file_id
			LEFT OUTER JOIN custom_reports cr_name ON pa.custom_report_id = cr_name.id AND cr_name.name = 'ewa'
			WHERE (
				f.id IN ${this.file_ids}
				AND f.is_active = true
				AND crf.id IS NULL
				${this.user_id ? `AND f.user_id = '${this.user_id}'` : ''}
			)
		`;
	}

	getCount() {
		const joins = this.#getCommonQuery();
		return `
			SELECT
				COUNT(distinct f.id) as count
			${joins}
		`;
	}

	getRows() {
		const joins = this.#getCommonQuery();
		return `
			SELECT
				f.*,
				s.id AS sector_id,
				s.name AS sector_name,
				w.id AS workstations_id,
				w.name AS workstations_name,
				l.id AS line_id,
				l.name AS line_name,
				pa.id AS preliminary_analysis_id,
				pa.worst_score AS preliminary_analysis_worst_score,
				pa.consolidated AS preliminary_analysis_consolidated
			${joins}
			GROUP BY f.id
			ORDER BY f.created_at DESC
			LIMIT ${this.limit}
			OFFSET ${this.offset}
		`;
	}
}
