import { CUSTOM_REPORT_DEFAULT_STEPS_ENUM, FormatTime, HelpersUtil } from '../../../../../helpers/index.js';
import { EWAD86DataToPDFMapper, WorkingPopulationConverter } from '../../../../index.js';
import { CUSTOM_REPORT_NAMES } from '../../../../../util/constants-custom-report.js';

const initialStep = {
	[CUSTOM_REPORT_NAMES.EWA_D86]: CUSTOM_REPORT_DEFAULT_STEPS_ENUM.INFORMATION,
	[CUSTOM_REPORT_NAMES.JDS_D86]: CUSTOM_REPORT_DEFAULT_STEPS_ENUM.VIDEO_SELECTION
};

export class EwaD86Mapper {
	#format_time;
	#helpers_util;
	#working_population_converter;

	constructor() {
		this.#format_time = new FormatTime();
		this.#helpers_util = new HelpersUtil();
		this.#working_population_converter = new WorkingPopulationConverter();
	}

	async setResponseInitialValue({
		file,
		result,
		file_id,
		custom_report,
		nothing_stressful,
		previous_step_key_result_scores
	}) {
		const mapped_custom_report_steps = custom_report.step.map(({ id, name, description, sequence, step_key }) => {
			const step_keys = step_key?.map((step_key) => {
				const step_key_result = step_key?.custom_report_step_key_result;
				const has_sub_step_key_result = step_key?.sub_step_keys?.some(
					({ custom_report_sub_step_key_result }) => !!custom_report_sub_step_key_result?.result
				);
				const has_step_key_result = !!step_key_result?.result;
				const checked = has_step_key_result || has_sub_step_key_result;

				let previous_result = {};
				if (previous_step_key_result_scores) {
					previous_result = previous_step_key_result_scores[step_key.id];
				}

				return {
					...step_key,
					checked,
					previous_result
				};
			});

			return {
				id,
				name,
				sequence,
				description,
				step_keys: step_keys ?? []
			};
		});
		return {
			file_id: file?.id ?? file_id,
			current_step: 0,
			comment: undefined,
			is_completed: false,
			id: custom_report?.id,
			result_id: result?.id,
			steps: mapped_custom_report_steps,
			total_steps: custom_report.step.length,
			informations: this.mapInformationsResult({ file, result }),
			current_step_name: initialStep[custom_report.name],
			work_conditions: {
				work_hours: 8,
				work_minutes: 0
			},
			tools: {
				reba: (await this.#setRebaData(file)) ?? null,
				niosh: file?.niosh ?? null,
				kim_mho: file?.kim_mho ?? null,
				kim_pp: file?.kim_push_pull ?? null,
				strain_index: file?.strain_index ?? null,
				liberty_mutual: file?.liberty_mutual ?? null
			},
			characteristics: {
				working_population_men: 0,
				working_population_women: 0,
				working_population_others: 0,
				worker_self_evaluation_id: nothing_stressful.id
			},
			tools_to_show: this.mapToolsToShow(file)
		};
	}

	mapToolsToShow(file) {
		const tools_to_show = [];
		if (file?.reba) {
			tools_to_show.push('reba');
		}
		if (file?.niosh) {
			tools_to_show.push('niosh');
		}
		if (file?.kim_mho) {
			tools_to_show.push('kim_mho');
		}
		if (file?.kim_push_pull) {
			tools_to_show.push('kim_pp');
		}
		if (file?.strain_index) {
			tools_to_show.push('strain_index');
		}
		if (file?.liberty_mutual) {
			tools_to_show.push('liberty_mutual');
		}
		return tools_to_show;
	}

	setStepsInitialValues({ step_keys, sub_step_keys, steps }) {
		const sub_step_keys_initial_values = sub_step_keys.reduce((initial_values, current_sub_step_key) => {
			const { id, custom_report_step_key_id } = current_sub_step_key;
			return {
				...initial_values,
				[custom_report_step_key_id]: {
					...initial_values[custom_report_step_key_id],
					[id]: false
				}
			};
		}, {});

		const step_keys_intial_values = step_keys.reduce((initial_values, current_step_key) => {
			const { id, custom_report_step_id } = current_step_key;
			return {
				...initial_values,
				[custom_report_step_id]: {
					...initial_values[custom_report_step_id],
					[id]: sub_step_keys_initial_values[id] ?? false
				}
			};
		}, {});

		let steps_initial_values = steps.reduce((initial_values, current_step) => {
			const { id } = current_step;
			return {
				...initial_values,
				[id]: {
					...step_keys_intial_values[id]
				}
			};
		}, {});
		return steps_initial_values;
	}

	mapInformationsResult({ file, result }) {
		let file_informations = {};
		let result_informations = {};

		if (file) {
			const { workstations } = file;
			const line = workstations?.line;
			const sector = line?.sector;
			const company = sector?.company;
			const organization = company?.organization;

			file_informations = {
				line_id: line?.id,
				sector_id: sector?.id,
				uploaded_date: file.createdAt,
				workstation_id: workstations?.id,
				workstation_name: workstations?.name,
				original_name: file.original_name,
				company_id: company?.id ?? file?.company_id,
				organization_id: organization?.id ?? file?.organization_id,
				duration: this.#format_time.formatSecondsToUnabbreviatedTime(file.duration)
			};
		}

		if (result) {
			result_informations = {
				name: result.name ?? result?.previous_custom_report_result?.name,
				role_name: result.role_name,
				activity_id: result.activity_id,
				created_date: result.created_at,
				evaluator_id: result.evaluator_id,
				collection_date: result.collection_date,
				interviewee_name: result.interviewee_name,
				interviewer_name: result.interviewer_name
			};
		}

		return {
			...file_informations,
			...result_informations
		};
	}

	mapCharacteristicsResult(characteristic, nothing_stressful) {
		const [working_population_men, working_population_women, working_population_others] =
			this.#working_population_converter.setWorkingPopulationAbsoluteValues(characteristic);
		return {
			id: characteristic?.id,
			working_population_men,
			working_population_women,
			working_population_others,
			total_working_population: characteristic?.total_working_population,
			particularities_description: characteristic?.particularities_description,
			worker_verbalization_description: characteristic?.worker_verbalization_description,
			worker_self_evaluation_id: characteristic?.worker_self_evaluation?.id ?? nothing_stressful.id
		};
	}

	mapWorkConditionsResult(work_conditions) {
		const { hours, minutes } = this.#format_time.convertSecondsToHoursAndMinutes(work_conditions?.work_schedule);
		const mapped_work_conditions = {
			work_hours: hours,
			work_minutes: minutes,
			id: work_conditions?.id,
			place_description: work_conditions?.place_description,
			expected_task_description: work_conditions?.expected_task_description,
			performed_task_description: work_conditions?.performed_task_description
		};
		return mapped_work_conditions;
	}

	mapStepKeysResultsByStep({
		custom_report_step_keys_results,
		custom_report_sub_step_keys_results,
		additional_item_results_for_step_key_and_additional_item_result_id
	}) {
		const sub_step_keys_results_by_step_key = this.#setSubStepKeysResultsByStepKey(
			custom_report_sub_step_keys_results
		);
		const step_keys_results = custom_report_step_keys_results?.map((result) => result?.get({ plain: true }));
		const step_keys_results_by_step = this.#setStepKeysResultsByStep({
			step_keys_results,
			sub_step_keys_results_by_step_key,
			additional_item_results_for_step_key_and_additional_item_result_id
		});
		return step_keys_results_by_step;
	}

	#setSubStepKeysResultsByStepKey(custom_report_sub_step_keys_results) {
		const sub_step_keys_results = custom_report_sub_step_keys_results?.map((result) =>
			result?.get({ plain: true })
		);
		const sub_step_keys_results_by_step_key = sub_step_keys_results?.reduce(
			(results_by_step_key, current_result) => {
				const { custom_report_sub_step_key_id, sub_step_key } = current_result;
				const { step_key } = sub_step_key;

				let result = false;

				if (this.#hasCurrentResult(current_result)) {
					result = current_result;
				}

				return {
					...results_by_step_key,
					[step_key.id]: {
						...results_by_step_key[step_key.id],
						[custom_report_sub_step_key_id]: result
					}
				};
			},
			{}
		);
		return sub_step_keys_results_by_step_key;
	}

	#setStepKeysResultsByStep({
		step_keys_results,
		sub_step_keys_results_by_step_key,
		additional_item_results_for_step_key_and_additional_item_result_id
	}) {
		return step_keys_results?.reduce((results_by_step, current_result) => {
			const { custom_report_step_key_id, step_key } = current_result;
			const { step } = step_key;
			const additional_item = additional_item_results_for_step_key_and_additional_item_result_id[step_key.id];

			let result = false;

			if (this.#hasCurrentResult(current_result)) {
				result = current_result;

				if (this.#hasAdditionalItemForCurrentStepKey(additional_item)) {
					result.additional_items = additional_item;
				}
			}

			if (this.#hasCurrentStepKeySubStepKeyResult(sub_step_keys_results_by_step_key, step_key)) {
				result = sub_step_keys_results_by_step_key[step_key.id];
			}

			return {
				...results_by_step,
				[step.id]: {
					...results_by_step[step.id],
					[custom_report_step_key_id]: result
				}
			};
		}, {});
	}

	#hasCurrentStepKeySubStepKeyResult(sub_step_keys_results_by_step_key, step_key) {
		return !!sub_step_keys_results_by_step_key[step_key.id];
	}

	#hasCurrentResult(current_result) {
		return !!current_result?.result;
	}

	#hasAdditionalItemForCurrentStepKey(additional_item) {
		return !!additional_item;
	}

	async #setRebaData(file) {
		if (file?.reba) {
			const reba_data_mapper = new EWAD86DataToPDFMapper();
			const prefix = this.#helpers_util.getPrefix(file.organization_id, file.company_id);
			const file_name = this.#helpers_util.removeExtension(file.generated_name);
			return {
				...file.reba,
				data: await reba_data_mapper.normalizeInputs({
					prefix,
					file_name
				})
			};
		}
		return null;
	}
}
