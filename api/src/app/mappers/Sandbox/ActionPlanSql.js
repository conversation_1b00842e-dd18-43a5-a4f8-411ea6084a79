export class ActionPlanSql {
	getCustomReportWithReviewId() {
		const query = `
            SELECT
                crr2.original_custom_report_result_id 
            FROM 
                action_plans_v2 apv INNER JOIN 
                action_plan_origins apo ON apv.action_plan_origin_id = apo.id INNER JOIN 
                custom_report_step_keys crsk ON apo.column_id = crsk.id INNER JOIN 
                custom_report_steps crs ON crsk.custom_report_step_id = crs.id INNER JOIN
                custom_report_results crr ON crs.custom_report_id = crr.custom_report_id AND apv.file_id = crr.file_id INNER JOIN
                custom_report_reviews crr2 ON crr2.custom_report_result_id = crr.id
            WHERE apv.id = :id
        `;

		return query;
	}

	getDirectCustomReportWithReviewId() {
		const query = `
            SELECT
                crr2.original_custom_report_result_id 
            FROM
                custom_report_reviews crr2
            WHERE
                crr2.custom_report_result_id = :id
        `;

		return query;
	}

	getCustomReportId() {
		const query = `
            SELECT
                crr.id
            FROM 
                action_plans_v2 apv JOIN 
                action_plan_origins apo ON apv.action_plan_origin_id = apo.id INNER JOIN 
                custom_report_step_keys crsk ON apo.column_id = crsk.id INNER JOIN 
                custom_report_steps crs ON crsk.custom_report_step_id = crs.id INNER JOIN
                custom_report_results crr ON crs.custom_report_id = crr.custom_report_id AND apv.file_id = crr.file_id
            WHERE apv.id = :id
        `;

		return query;
	}

	getDirectCustomReportId() {
		const query = `
            SELECT
                crr.id
            FROM
                custom_report_results crr
            WHERE
                crr.id = :id
        `;

		return query;
	}

	getSeraSummaryId() {
		const query = `
            SELECT 
                ssr.id
            FROM 
                action_plans_v2 apv INNER JOIN
                action_plan_origins apo ON apo.id = apv.action_plan_origin_id INNER JOIN
                sera_summary_reviews ssr ON ssr.id = apo.column_id
            WHERE apv.id = :id
        `;

		return query;
	}

	getEwaReportId(table_name) {
		const query = `
            SELECT
                id
            FROM
                ${table_name}
            WHERE
                id = :id
        `;

		return query;
	}
}
