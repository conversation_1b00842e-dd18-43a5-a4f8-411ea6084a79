export class DatabaseSyncSchemaSQL {
	getTableCollumns(table_name, database) {
		const query = `
           SELECT
               COLUMN_NAME,
               COLUMN_TYPE,
               IS_NULLABLE,
               COLUMN_KEY,
               COLUMN_DEFAULT,
               EXTRA,
               DATA_TYPE,
               CHARACTER_MAXIMUM_LENGTH,
               NUMERIC_PRECISION,
               NUMERIC_SCALE,
               CHARACTER_SET_NAME,
               COLLATION_NAME
           FROM INFORMATION_SCHEMA.COLUMNS
           WHERE TABLE_SCHEMA = '${database}'
           AND TABLE_NAME = '${table_name}'
           ORDER BY ORDINAL_POSITION
       `;

		const replacements = {
			table_name,
			database
		};

		return { query, replacements };
	}

	getTableForeignKeys(table_name, database) {
		const query = `
           SELECT
               COLUMN_NAME,
               REFERENCED_TABLE_NAME,
               REFERENCED_COLUMN_NAME,
               CONSTRAINT_NAME
           FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
           WHERE TABLE_SCHEMA = '${database}'
           AND TABLE_NAME = '${table_name}'
           AND REFERENCED_TABLE_NAME IS NOT NULL
       `;

		const replacements = {
			table_name,
			database
		};

		return { query, replacements };
	}

	getTableIndexes(table_name) {
		const query = `SHOW INDEX FROM \`${table_name}\``;
		return { query, replacements: { table_name } };
	}

	getColumnInfo(table_name, column_name, database) {
		const query = `
           SELECT
               COLUMN_TYPE,
               CHARACTER_SET_NAME,
               COLLATION_NAME,
               IS_NULLABLE,
               COLUMN_DEFAULT,
               EXTRA,
               DATA_TYPE
           FROM INFORMATION_SCHEMA.COLUMNS
           WHERE TABLE_SCHEMA = '${database}'
           AND TABLE_NAME = '${table_name}'
           AND COLUMN_NAME = '${column_name}'
       `;

		return { query, replacements: { table_name, column_name, database } };
	}

	alterTableStatement(table_name, column_name, target_column) {
		const { COLUMN_TYPE: collumn_type } = target_column;
		const { filters, replacements } = this.#alterTableStatementFilters(target_column);

		const query = `
			ALTER TABLE \`${table_name}\`
			MODIFY \`${column_name}\` ${collumn_type}
			${filters}
        `;

		return { query, replacements };
	}

	addColumnStatement(table_name, column_info) {
		const { COLUMN_TYPE: collumn_type } = column_info;
		const { filters, replacements } = this.#addColumnStatementFilters(column_info);

		const query = `
            ALTER TABLE \`${table_name}\`
            ADD COLUMN \`${column_info.COLUMN_NAME}\` ${collumn_type}
            ${filters}
        `;

		return { query, replacements };
	}

	#addColumnStatementFilters(column_info) {
		let filters = ``;

		let replacements = {
			collumn_type: column_info.COLUMN_TYPE
		};

		if (column_info.DATA_TYPE.includes('char') || column_info.DATA_TYPE.includes('text')) {
			filters += ` CHARACTER SET ${column_info.CHARACTER_SET_NAME} COLLATE ${column_info.COLLATION_NAME} \n`;
			replacements.character_set_name = column_info.CHARACTER_SET_NAME;
			replacements.collation_name = column_info.COLLATION_NAME;
		}

		if (column_info.IS_NULLABLE === 'NO') {
			filters += ' NOT NULL \n';
			replacements.is_nullable = 'NO';
		}

		if (column_info.COLUMN_DEFAULT !== null) {
			if (column_info.COLUMN_DEFAULT === 'CURRENT_TIMESTAMP') {
				filters += ` DEFAULT ${column_info.COLUMN_DEFAULT} \n`;
			} else {
				filters += ` DEFAULT '${column_info.COLUMN_DEFAULT}' \n`;
			}
			replacements.column_default = column_info.COLUMN_DEFAULT;
		} else if (column_info.IS_NULLABLE === 'YES') {
			filters += ' DEFAULT NULL \n';
		}

		if (column_info.EXTRA.includes('auto_increment')) {
			filters += ' AUTO_INCREMENT \n';
			replacements.auto_increment = 'AUTO_INCREMENT';
		}

		return { filters, replacements };
	}

	#alterTableStatementFilters(target_column) {
		let filters = ``;

		let replacements = {
			collumn_type: target_column.COLUMN_TYPE
		};

		if (target_column.CHARACTER_SET_NAME) {
			filters += ` CHARACTER SET ${target_column.CHARACTER_SET_NAME} \n`;
			replacements.character_set_name = target_column.CHARACTER_SET_NAME;
		}

		if (target_column.COLLATION_NAME) {
			filters += ` COLLATE ${target_column.COLLATION_NAME} \n`;
			replacements.collation_name = target_column.COLLATION_NAME;
		}

		if (target_column.IS_NULLABLE === 'NO') {
			filters += ' NOT NULL \n';
			replacements.is_nullable = 'NO';
		}

		if (target_column.COLUMN_DEFAULT) {
			filters += ` DEFAULT ${target_column.COLUMN_DEFAULT} \n`;
			replacements.column_default = target_column.COLUMN_DEFAULT;
		}

		if (target_column.EXTRA.includes('auto_increment')) {
			filters += ' AUTO_INCREMENT \n';
			replacements.auto_increment = 'AUTO_INCREMENT';
		}

		return { filters, replacements };
	}

	getAllTables(database, db_filters = {}) {
		const { filters, replacements } = this.#getAllTablesFilters(database, db_filters);

		const query = `
               SELECT table_name
               FROM information_schema.tables
               WHERE table_schema = '${database}'
               AND table_type = 'BASE TABLE'
               ${filters}
               ORDER BY table_name
           `;

		return { query, replacements };
	}

	#getAllTablesFilters(database, db_filters) {
		let filters = ``;

		let replacements = {
			database
		};

		if (db_filters.table_name) {
			filters += `${filters} AND table_name = '${db_filters.table_name}'`;
			replacements.table_name = db_filters.table_name;
		}

		return { filters, replacements };
	}

	addForeignKey(table_name, foreign_key_info) {
		const {
			CONSTRAINT_NAME: constraint_name,
			COLUMN_NAME: column_name,
			REFERENCED_TABLE_NAME: referenced_table,
			REFERENCED_COLUMN_NAME: referenced_column
		} = foreign_key_info;

		const query = `
            ALTER TABLE \`${table_name}\`
            ADD CONSTRAINT \`${constraint_name}\`
            FOREIGN KEY (\`${column_name}\`)
            REFERENCES \`${referenced_table}\` (\`${referenced_column}\`)
        `;

		const replacements = {
			table_name,
			column_name,
			constraint_name,
			referenced_table,
			referenced_column
		};

		return { query, replacements };
	}

	createTableStatement(table_name, column_defs, charset, collation) {
		const query = `
            CREATE TABLE \`${table_name}\` (
                ${column_defs.join(',\n                ')}
            ) ENGINE=InnoDB DEFAULT CHARSET=${charset} COLLATE=${collation}
        `;

		return { query, replacements: {} };
	}

	createIndexStatement(table_name, index_name, column_name) {
		const query = `CREATE INDEX \`${index_name}\` ON \`${table_name}\` (\`${column_name}\`)`;
		return { query, replacements: {} };
	}

	dropTableStatement(table_name) {
		const query = `DROP TABLE \`${table_name}\``;
		return { query, replacements: {} };
	}

	checkForeignKeyExists(table_name, constraint_name, database) {
		const query = `
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
            WHERE TABLE_SCHEMA = '${database}'
            AND TABLE_NAME = '${table_name}'
            AND CONSTRAINT_NAME = '${constraint_name}'
            AND CONSTRAINT_TYPE = 'FOREIGN KEY'
        `;

		const replacements = {
			database,
			table_name,
			constraint_name
		};

		return { query, replacements };
	}
}
