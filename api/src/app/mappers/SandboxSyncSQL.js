export class SandboxSyncSQL {
	createInsertStatement(table_name, columns) {
		const placeholders = columns.map(() => '?').join(', ');
		const query = `
            INSERT INTO \`${table_name}\` 
            (\`${columns.join('`, `')}\`)
            VALUES (${placeholders})
        `;
		return { query, replacements: {} };
	}

	createUpdateStatement(table_name, columns) {
		const set_clause = columns
			.filter((col) => col !== 'id')
			.map((col) => `\`${col}\` = ?`)
			.join(', ');

		const query = `
            UPDATE \`${table_name}\`
            SET ${set_clause}
            WHERE \`id\` = ?
        `;

		return { query, replacements: {} };
	}

	createDeleteStatement(table_name) {
		const query = `
            DELETE FROM \`${table_name}\`
            WHERE \`id\` = ?
        `;

		return { query, replacements: {} };
	}
}
