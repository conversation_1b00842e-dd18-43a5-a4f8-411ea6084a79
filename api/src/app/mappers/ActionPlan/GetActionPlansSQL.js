import { logger } from '../../helpers/logger.js';

export class GetActionPlansSQL {
	constructor(parameters) {
		const {
			company_id,
			sector_id,
			line_id,
			workstation_id,
			activity_id,
			companies_with_user_access,
			title,
			status,
			responsible_id,
			start_date,
			end_date,
			priority,
			investment_range,
			due_date_start,
			due_date_end,
			origin_name,
			sort,
			user_id
		} = parameters;
		this.company_id = company_id;
		this.sector_id = sector_id;
		this.line_id = line_id;
		this.workstation_id = workstation_id;
		this.activity_id = activity_id;
		this.companies_with_user_access = companies_with_user_access;
		this.title = title;
		this.priority = priority;
		this.investment_range = investment_range;
		this.responsible_id = responsible_id;
		this.start_date = start_date;
		this.end_date = end_date;
		this.due_date_start = due_date_start;
		this.due_date_end = due_date_end;
		this.origin_name = origin_name;
		this.sort = sort;
		this.status = status;
		this.user_id = user_id || '';
	}

	getIndexQuery() {
		logger.info('[GetActionPlansSQL] mappers - getIndexQuery init');
		const order_by = this.getSort();
		const hierarcy_filter = this.getHierarchyFilter();
		const action_plan_filter = this.getActionPlanFilter();

		const query = `
			SELECT
				ap.id, ap.title, ap.status, ap.score, ap.priority, ap.investment_range AS investment, companies.id AS company_id,
				COUNT(DISTINCT ap_attachments.id) AS attachments,
				origin.origin_name,
				ap.file_id,
				JSON_OBJECT('table_name', origin.table_name, 'column_id', origin.column_id, 'origin_name', origin.origin_name) AS action_plan_origin,
				JSON_OBJECT('name', author.name, 'url_logo', null) AS author,
				JSON_OBJECT('name', responsible.name, 'url_logo', null) AS responsible,
				COUNT(DISTINCT CASE WHEN ap_tasks.is_completed = TRUE THEN ap_tasks.id END) AS completed_tasks,
				COUNT(DISTINCT ap_task_attachments.id) AS evidences,
				COUNT(DISTINCT ap_tasks.id) AS total_tasks,
				ap.due_date, ap.completed_at, ap.created_at
			FROM action_plans_v2 ap
				LEFT JOIN action_plan_attachments ap_attachments ON ap_attachments.action_plan_id = ap.id AND ap_attachments.deleted_at IS NULL
				LEFT JOIN action_plan_tasks ap_tasks ON ap_tasks.action_plan_id = ap.id AND ap_tasks.deleted_at IS NULL
				LEFT JOIN action_plan_task_attachments ap_task_attachments ON ap_task_attachments.action_plan_task_id = ap_tasks.id AND ap_task_attachments.deleted_at IS NULL
				LEFT JOIN action_plan_origins origin ON origin.id = ap.action_plan_origin_id AND origin.deleted_at IS NULL
				INNER JOIN users AS author ON ap.user_id = author.id
				INNER JOIN users AS responsible ON ap.responsible_user_id = responsible.id
				INNER JOIN workstations ON workstations.id = ap.workstation_id AND workstations.deleted_at IS NULL
				INNER JOIN kinebot.lines AS l ON l.id = workstations.line_id AND l.deleted_at IS NULL
				INNER JOIN sectors ON sectors.id = l.sector_id AND sectors.is_active IS TRUE
				INNER JOIN companies ON companies.id = sectors.company_id AND companies.is_active IS TRUE
				LEFT JOIN activities ON activities.workstation_id = workstations.id AND activities.deleted_at IS NULL
				LEFT JOIN files ON files.id = ap.file_id
			WHERE
				companies.organization_id = :organization_id
				AND ((ap.file_id IS NOT NULL AND files.is_active = 1) OR (ap.file_id is NULL))
				AND ap.deleted_at IS NULL
				${hierarcy_filter}
				${action_plan_filter}
			GROUP BY ap.id
			${order_by}
			LIMIT :limit
			OFFSET :offset
			`;

		logger.info('[GetActionPlansSQL] mappers - getIndexQuery finish');
		return query;
	}

	getCountAllQuery() {
		logger.info('[GetActionPlansSQL] mappers - getCountAllQuery init');
		const hierarcy_filter = this.getHierarchyFilter();
		const action_plan_filter = this.getActionPlanFilter();

		const query = `
			SELECT
				COUNT(DISTINCT ap.id) AS total,
				COUNT(*) as total_rows
			FROM action_plans_v2 ap
				LEFT JOIN action_plan_origins origin ON origin.id = ap.action_plan_origin_id AND origin.deleted_at IS NULL
				INNER JOIN workstations ON workstations.id = ap.workstation_id AND workstations.deleted_at IS NULL
				INNER JOIN kinebot.lines AS l ON l.id = workstations.line_id AND l.deleted_at IS NULL
				INNER JOIN sectors ON sectors.id = l.sector_id AND sectors.is_active IS TRUE
				INNER JOIN companies ON companies.id = sectors.company_id AND companies.is_active IS TRUE
				LEFT JOIN activities ON activities.workstation_id = workstations.id AND activities.deleted_at IS NULL
				LEFT JOIN files ON files.id = ap.file_id
			WHERE
				companies.organization_id = :organization_id
				AND (ap.deleted_at IS NULL)
				AND ((ap.file_id IS NOT NULL AND files.is_active = 1) OR (ap.file_id is NULL))
				${hierarcy_filter}
				${action_plan_filter}
		`;

		return query;
	}

	getHierarchyFilter() {
		let filter = ``;

		if (this.company_id) {
			filter += 'AND companies.id = :company_id\n';
		} else {
			filter += 'AND companies.id IN(:companies_with_user_access)\n';
		}

		if (this.sector_id) {
			filter += `AND sectors.id = :sector_id\n`;
		}

		if (this.line_id) {
			filter += `AND l.id = :line_id\n`;
		}

		if (this.workstation_id) {
			filter += `AND workstations.id = :workstation_id\n`;
		}

		if (this.activity_id) {
			filter += `AND activities.id = :activity_id\n`;
		}

		return filter;
	}

	getActionPlanFilter() {
		let filter = '';

		if (this.investment_range) {
			filter += `AND ap.investment_range = :investment_range\n`;
		}

		if (this.title) {
			filter += `AND lower(ap.title) LIKE CONCAT('%', lower(:title), '%')\n`;
		}

		if (this.priority) {
			filter += `AND ap.priority = :priority\n`;
		}

		if (this.responsible_id) {
			filter += `AND ap.responsible_user_id = :responsible_id\n`;
		}

		if (this.start_date) {
			filter += `AND DATE(ap.created_at) >= DATE(:start_date)\n`;
		}

		if (this.end_date) {
			filter += `AND DATE(ap.created_at) <= DATE(:end_date)\n`;
		}

		if (this.due_date_start) {
			filter += `AND DATE(ap.due_date) >= DATE(:due_date_start)\n`;
		}

		if (this.due_date_end) {
			filter += `AND DATE(ap.due_date) <= DATE(:due_date_end)\n`;
		}

		if (this.status) {
			filter += `AND ap.status = :status\n`;
		}

		if (this.origin_name) {
			filter += `AND origin.origin_name = :origin_name\n`;
		}

		if (this.user_id) {
			filter += `AND ap.responsible_user_id = :user_id\n`;
		}

		return filter;
	}

	getSort() {
		const options = {
			alph_asc: 'LOWER(ap.title)',
			alph_desc: 'LOWER(ap.title) DESC',
			most_recent: 'ap.created_at DESC',
			least_recent: 'ap.created_at',
			nearest_date: 'ap.due_date ASC',
			furthest_date: 'ap.due_date DESC',
			high_priority: 'ap.priority DESC',
			low_priority: '(ap.priority IS NULL), ap.priority',
			high_investment: 'ap.investment_range DESC',
			lower_investment: 'ap.investment_range'
		};

		if (this.sort) {
			return `ORDER BY ${options[this.sort]}`;
		}

		return 'ORDER BY ap.status DESC, ap.score DESC';
	}
}
