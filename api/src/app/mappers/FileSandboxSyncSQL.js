export class FileSandboxSyncSQL {
	createInsertStatement(table_name, columns) {
		const placeholders = columns.map(() => '?').join(', ');

		const query = `
            INSERT INTO \`${table_name}\` 
            (\`${columns.join('`, `')}\`)
            VALUES (${placeholders})
        `;

		return { query, replacements: {} };
	}

	createUpdateStatement(table_name, columns) {
		const setClause = columns
			.filter((col) => col !== 'id' && col !== 'is_active')
			.map((col) => `\`${col}\` = ?`)
			.join(', ');

		const query = `
            UPDATE \`${table_name}\`
            SET ${setClause}
            WHERE \`id\` = ?
        `;

		return { query, replacements: {} };
	}
}
