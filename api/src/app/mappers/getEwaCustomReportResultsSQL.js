import { logger } from '../helpers/logger.js';

export class GetEwaCustomReportResultsSQL {
	constructor({
		organization_id,
		company_id,
		companies_with_user_access,
		sector_id,
		line_id,
		workstation_id,
		evaluator_id,
		collection_date_start,
		collection_date_end,
		created_at_start,
		created_at_end,
		file_name,
		user_id
	}) {
		this.organization_id = organization_id;
		this.company_id = company_id;
		this.companies_with_user_access = companies_with_user_access;
		this.sector_id = sector_id;
		this.line_id = line_id;
		this.workstation_id = workstation_id;
		this.evaluator_id = evaluator_id;
		this.collection_date_start = collection_date_start;
		this.collection_date_end = collection_date_end;
		this.created_at_start = created_at_start;
		this.created_at_end = created_at_end;
		this.file_name = file_name;
		this.user_id = user_id;
	}

	getIndexQuery() {
		logger.info('[GetEwaCustomReportResultsSQL] mappers - getIndexQuery init');
		const hierarcy_filter = this.getHierarchyFilter();
		const custom_report_filter = this.getCustomReportFilters();

		const query = `
			SELECT crr.id, crr.name, crr.result, f.original_name as file_name, e.name as analyst_name, crr.collection_date, crr.created_at, crr.updated_at,
                JSON_OBJECT('id', cr.id, 'name', cr.name, 'description', cr.description, 'acronym', cr.acronym) AS custom_report
            FROM custom_report_results crr
                INNER JOIN custom_reports cr ON cr.id = crr.custom_report_id AND cr.deleted_at IS NULL
                INNER JOIN files f ON crr.file_id = f.id AND f.is_active IS TRUE
                INNER JOIN evaluators e ON e.id = crr.evaluator_id AND e.deleted_at IS NULL
                INNER JOIN workstations w ON w.id = f.workstation_id AND w.deleted_at IS NULL
                INNER JOIN lines l ON l.id = w.line_id AND l.deleted_at IS NULL
                INNER JOIN sectors s ON s.id = l.sector_id AND s.is_active IS TRUE
                INNER JOIN companies c ON c.id = s.company_id AND c.is_active IS TRUE
            WHERE crr.deleted_at IS NULL
                AND (cr.name = 'ewa' OR cr.name = 'ewa_d86')
                AND crr.consolidated IS TRUE
                AND c.organization_id = :organization_id
                ${hierarcy_filter}
                ${custom_report_filter}
			LIMIT :limit
			OFFSET :offset
		`;

		logger.info('[GetEwaCustomReportResultsSQL] mappers - getIndexQuery finish');
		return query;
	}

	getCountAllQuery() {
		logger.info('[GetEwaCustomReportResultsSQL] mappers - getCountAllQuery init');
		const hierarcy_filter = this.getHierarchyFilter();
		const custom_report_filter = this.getCustomReportFilters();

		const query = `
			SELECT COUNT(DISTINCT crr.id) AS total
            FROM custom_report_results crr 
                INNER JOIN custom_reports cr ON cr.id = crr.custom_report_id AND cr.deleted_at IS NULL
                INNER JOIN files f ON crr.file_id = f.id AND f.is_active IS TRUE
                INNER JOIN evaluators e ON e.id = crr.evaluator_id AND e.deleted_at IS NULL
                INNER JOIN workstations w ON w.id = f.workstation_id AND w.deleted_at IS NULL
                INNER JOIN lines l ON l.id = w.line_id AND l.deleted_at IS NULL
                INNER JOIN sectors s ON s.id = l.sector_id AND s.is_active IS TRUE
                INNER JOIN companies c ON c.id = s.company_id AND c.is_active IS TRUE
            WHERE crr.deleted_at IS NULL
                AND (cr.name = 'ewa' OR cr.name = 'ewa_d86')
                AND crr.consolidated IS TRUE
                AND c.organization_id = :organization_id
                ${hierarcy_filter}
                ${custom_report_filter}
		`;

		return query;
	}

	getHierarchyFilter() {
		let filter = ``;

		if (this.company_id) {
			filter += 'AND c.id = :company_id\n';
		} else {
			filter += 'AND c.id IN(:companies_with_user_access)\n';
		}

		if (this.sector_id) {
			filter += `AND s.id = :sector_id\n`;
		}

		if (this.line_id) {
			filter += `AND l.id = :line_id\n`;
		}

		if (this.workstation_id) {
			filter += `AND w.id = :workstation_id\n`;
		}

		return filter;
	}

	getCustomReportFilters() {
		let filter = ``;

		if (this.evaluator_id) {
			filter += 'AND e.id = :evaluator_id\n';
		}

		if (this.collection_date_start) {
			filter += `AND DATE(crr.collection_date) >= DATE(:collection_date_start)\n`;
		}

		if (this.collection_date_end) {
			filter += `AND DATE(crr.collection_date) <= DATE(:collection_date_end)\n`;
		}

		if (this.created_at_start) {
			filter += `AND DATE(crr.created_at) >= DATE(:created_at_start)\n`;
		}

		if (this.created_at_end) {
			filter += `AND DATE(crr.created_at) <= DATE(:created_at_end)\n`;
		}

		if (this.file_name) {
			filter += `AND f.original_name LIKE '%${this.file_name}%'\n`;
		}

		if (this.user_id) {
			filter += `AND crr.created_by_user_id = :user_id\n`;
		}

		return filter;
	}
}
