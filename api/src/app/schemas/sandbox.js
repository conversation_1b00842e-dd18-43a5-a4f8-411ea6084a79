import Joi from 'joi';
import { BaseSchema } from './base-schema.js';
import { logger } from '../helpers/logger.js';
import { SANDBOX_REPORT_TYPE } from '../utils/constants.js';

export class SandboxSchema extends BaseSchema {
	constructor() {
		super();
	}

	sendReport = (req, res, next) => {
		logger.info('[Sandbox] schema - sendReport init');
		const schema = Joi.object({
			organization_id: Joi.string().guid().required(),
			workstation_id: Joi.string().guid().optional(),
			company_id: Joi.string().guid().when('sector_id', {
				is: Joi.exist(),
				then: Joi.required()
			}),
			report_type: Joi.string().valid(...SANDBOX_REPORT_TYPE),
			id: Joi.string().guid()
		});
		this.validateRequest(req, res, next, schema);
		logger.info('[Sandbox] schema - sendReport finish');
	};
}
