import { StorageContext } from '../utils/storage_context.js';
import { ERROR_RESPONSE_ENTITIES_ENUM } from '../util/enum.js';
import { AppError, logger, RESPONSE_ERROR_STATUS } from '../helpers/index.js';
import { SeraSummaryReviewCalculator, SeraReport } from '../entities/index.js';
import { ACTION_PLAN_ORIGIN_TABLE_NAME, SERA_CATEGORIES_ORDER } from '../utils/constants.js';

const { DATABASE_FAILED_PERFORM_QUERY } = RESPONSE_ERROR_STATUS;

export class SeraReportService {
	constructor({
		repository,
		action_plan_repository,
		sera_summary_repository,
		sera_summary_review_repository,
		sera_report_updated_repository,
		sera_review_selector_repository,
		sera_review_tasks_result_repository
	}) {
		this.repository = repository;
		this.action_plan_repository = action_plan_repository;
		this.sera_summary_repository = sera_summary_repository;
		this.sera_summary_review_repository = sera_summary_review_repository;
		this.sera_report_updated_repository = sera_report_updated_repository;
		this.sera_review_selector_repository = sera_review_selector_repository;
		this.sera_review_tasks_result_repository = sera_review_tasks_result_repository;
	}

	async create(payload) {
		logger.info('[SeraReport] service - create init');
		const { sera_report_data, sera_summary_review_id, user_id } = payload;
		let transaction;
		try {
			const [sera_summary_review] = await this.sera_summary_review_repository.findAllByForeignKey({
				where: {
					id: sera_summary_review_id
				},
				order: [['review', 'DESC']]
			});

			const seraReview = new SeraSummaryReviewCalculator(sera_report_data);
			const seraReport = new SeraReport(sera_report_data);
			const { analyzed_reports, average_rpn, highest_rpn, sum_rpn } =
				seraReport.calculateSeraAnalysisStatistics();
			const sera_review_statistics = seraReview.calculateSeraReviewStatistics();

			transaction = await this.repository.db.sequelize.transaction();
			const promises = sera_report_data.map((report) => {
				const mapped_report = {
					rpn: report.rpn,
					sera_summary_review_id,
					task_id: report.task_id,
					severity_id: report.severity_id,
					exposure_id: report.exposure_id,
					evaluator_id: report.evaluator_id,
					specifications: report.specifications,
					risk_damage_id: report.risk_damage_id,
					vulnerability_id: report.vulnerability_id,
					risk_category_id: report.risk_category_id,
					risk_description_id: report.risk_description_id,
					existing_prevention_measures: report.existing_prevention_measures
				};

				return this.repository.create(mapped_report, {
					transaction
				});
			});

			await this.sera_summary_repository.update(
				{
					status: 'COMPLETED'
				},
				{
					where: {
						id: sera_summary_review.sera_summary_id
					},
					transaction
				}
			);

			sera_review_statistics.forEach(async ({ highest_rpn, sum_rpn, task_id }) => {
				await this.sera_review_tasks_result_repository.create(
					{
						task_id,
						sera_summary_review_id: sera_summary_review.id,
						highest_rpn,
						sum_rpn
					},
					{
						transaction
					}
				);
			});

			await this.sera_summary_review_repository.update(
				{
					analyzed_reports,
					average_rpn,
					highest_rpn,
					sum_rpn,
					user_id,
					reviewed_reports: 0
				},
				{
					where: {
						id: sera_summary_review.id
					},
					transaction
				}
			);

			const reviews = [
				{
					name: 'all',
					description: 'General history',
					sequence: 0,
					sera_summary_id: sera_summary_review.sera_summary_id
				},
				{
					name: 'review_0',
					description: 'Original',
					sequence: 1,
					sera_summary_id: sera_summary_review.sera_summary_id,
					sera_summary_review_id: sera_summary_review.id
				}
			];

			await this.sera_review_selector_repository.bulkCreate(reviews, {
				transaction
			});

			const result = await Promise.all(promises);

			await transaction.commit();

			logger.info('[SeraReport] service - create finish');
			return result;
		} catch (error) {
			logger.error('[SeraReport] service - create error', { error });
			await transaction?.rollback();
			throw error;
		}
	}

	async update(payload) {
		logger.info('[SeraReport] service - update init');
		const { sera_report_data, sera_summary_review_id, user_id } = payload;
		let transaction;
		try {
			const [sera_summary_review] = await this.sera_summary_review_repository.findAllByForeignKey({
				where: {
					id: sera_summary_review_id
				},
				order: [['review', 'DESC']]
			});

			const sera_review_calculator = new SeraSummaryReviewCalculator(sera_report_data);
			const sera_report = new SeraReport(sera_report_data);
			const { analyzed_reports, average_rpn, highest_rpn, sum_rpn } =
				sera_report.calculateSeraAnalysisStatistics();
			const sera_review_statistics = sera_review_calculator.calculateSeraReviewStatistics();

			transaction = await this.repository.db.sequelize.transaction();
			const promises = [];
			sera_report_data.forEach((report) => {
				const mapped_report = {
					rpn: report.rpn,
					task_id: report.task_id,
					severity_id: report.severity_id,
					exposure_id: report.exposure_id,
					evaluator_id: report.evaluator_id,
					specifications: report.specifications,
					risk_damage_id: report.risk_damage_id,
					vulnerability_id: report.vulnerability_id,
					risk_category_id: report.risk_category_id,
					risk_description_id: report.risk_description_id,
					sera_summary_review_id: sera_summary_review_id,
					existing_prevention_measures: report.existing_prevention_measures
				};

				const promise = new Promise(async (resolve, reject) => {
					try {
						if (report?.id) {
							await this.repository.update(mapped_report, {
								where: {
									id: report.id
								},
								transaction
							});
							const updated = await this.repository.findByPk(report.id);

							const [, updated_score_error] = await this.action_plan_repository.updateReportScoreOrigin(
								{
									table_name: ACTION_PLAN_ORIGIN_TABLE_NAME.SERA_SUMMARY_REVIEW,
									task_id: report.task_id,
									sera_summary_review_id
								},
								{ transaction }
							);

							if (updated_score_error) {
								logger.error(
									`${updated_score_error.message}, stack trace - ${updated_score_error.stack}`
								);
								throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
							}

							resolve(updated);
						} else {
							const created_report = await this.repository.create(mapped_report, {
								transaction
							});
							resolve(created_report);
						}
					} catch (error) {
						reject(error);
					}
				});

				promises.push(promise);
			});

			sera_review_statistics.forEach(async ({ highest_rpn, sum_rpn, task_id }) => {
				await this.sera_review_tasks_result_repository.update(
					{
						task_id,
						sera_summary_review_id: sera_summary_review.id,
						highest_rpn,
						sum_rpn
					},
					{
						where: {
							task_id,
							sera_summary_review_id: sera_summary_review.id
						},
						transaction
					}
				);
			});

			await this.sera_summary_review_repository.update(
				{
					analyzed_reports,
					average_rpn,
					highest_rpn,
					sum_rpn,
					user_id,
					reviewed_reports: 0
				},
				{
					where: {
						id: sera_summary_review.id
					},
					transaction
				}
			);

			const data = await Promise.allSettled(promises);

			data.forEach((elem) => {
				if (elem.status === 'rejected') {
					throw elem.reason;
				}
			});

			const [, updated_score_error] = await this.action_plan_repository.updateReportScoreOrigin(
				{
					table_name: ACTION_PLAN_ORIGIN_TABLE_NAME.SERA_SUMMARY,
					sera_summary_review_id
				},
				{ transaction }
			);

			if (updated_score_error) {
				logger.error(`${updated_score_error.message}, stack trace - ${updated_score_error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			const result = data.map((data) => data.value);

			await transaction.commit();

			logger.info('[SeraReport] service - update finish');
			return result;
		} catch (error) {
			logger.error('[SeraReport] service - update error', { error });
			await transaction?.rollback();
			throw error;
		}
	}

	async createReview(payload) {
		logger.info('[SeraReport] service - createReview init');
		const { sera_report_data, sera_summary_id, reviewed_reports, user_id } = payload;
		let transaction;
		try {
			const [existing_review] = await this.sera_summary_review_repository.findAllByForeignKey({
				where: {
					sera_summary_id
				},
				include: [
					{
						association: 'sera_summary',
						attributes: ['evaluator_id']
					}
				],
				order: [['review', 'DESC']]
			});

			const seraReview = new SeraSummaryReviewCalculator(sera_report_data);
			const seraReport = new SeraReport(sera_report_data);
			const { analyzed_reports, average_rpn, highest_rpn, sum_rpn } =
				seraReport.calculateSeraAnalysisStatistics();
			const sera_review_statistics = seraReview.calculateSeraReviewStatistics();

			transaction = await this.repository.db.sequelize.transaction();

			const review_number = existing_review.review + 1;
			const created_review = await this.sera_summary_review_repository.create(
				{
					review: review_number,
					sera_summary_id,
					user_id
				},
				{
					transaction
				}
			);

			const promises = [];
			sera_report_data.forEach((report) => {
				const mapped_report = {
					rpn: report.rpn,
					task_id: report.task_id,
					severity_id: report.severity_id,
					exposure_id: report.exposure_id,
					specifications: report.specifications,
					risk_damage_id: report.risk_damage_id,
					vulnerability_id: report.vulnerability_id,
					risk_category_id: report.risk_category_id,
					risk_description_id: report.risk_description_id,
					existing_prevention_measures: report.existing_prevention_measures,
					evaluator_id: report.evaluator_id ?? existing_review?.sera_summary?.evaluator_id
				};

				const promise = new Promise(async (resolve, reject) => {
					try {
						const created_report = await this.repository.create(
							{
								...mapped_report,
								sera_summary_review_id: created_review.id
							},
							{
								transaction
							}
						);

						if (report.previous_sera_report_id) {
							await this.sera_report_updated_repository.create(
								{
									sera_report_id: created_report.id,
									sera_report_updated: report.previous_sera_report_id
								},
								{
									transaction
								}
							);
						}

						resolve(created_report);
					} catch (error) {
						reject(error);
					}
				});

				promises.push(promise);
			});

			const result = await Promise.allSettled(promises);

			result.forEach((elem) => {
				if (elem.status === 'rejected') {
					throw elem.reason;
				}
			});

			const review = {
				name: `review_${review_number}`,
				description: review_number,
				sequence: review_number + 1,
				sera_summary_id,
				sera_summary_review_id: created_review.id
			};

			sera_review_statistics.forEach(async ({ highest_rpn, sum_rpn, task_id }) => {
				await this.sera_review_tasks_result_repository.create(
					{
						task_id,
						sera_summary_review_id: created_review.id,
						highest_rpn,
						sum_rpn
					},
					{
						transaction
					}
				);
			});

			await this.sera_review_selector_repository.create(review, {
				transaction
			});

			await this.sera_summary_repository.update(
				{
					status: 'REVIEWED'
				},
				{
					where: {
						id: sera_summary_id
					},
					transaction
				}
			);

			await this.sera_summary_review_repository.update(
				{
					analyzed_reports,
					average_rpn,
					highest_rpn,
					sum_rpn,
					user_id,
					reviewed_reports
				},
				{
					where: {
						id: created_review.id
					},
					transaction
				}
			);

			await transaction.commit();

			const mapped_result = result.map((data) => data.value);
			logger.info('[SeraReport] service - createReview finish');
			return {
				sera_summary_id,
				mapped_result
			};
		} catch (error) {
			logger.error('[SeraReport] service - createReview error', { error });
			await transaction?.rollback();
			throw error;
		}
	}

	async show() {
		logger.info('[SeraReport] service - show init');
		logger.info('[SeraReport] service - show finish');
	}

	async delete(id) {
		logger.info('[SeraReport] service - delete init');
		let transaction;
		try {
			const existing_sera_report = await this.repository.findByPk(id);

			if (!existing_sera_report) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.SERA_REPORT.NOT_FOUND);
			}

			const existing_sera_review = await this.sera_summary_review_repository.findByPk(
				existing_sera_report.sera_summary_review_id,
				{
					include: [
						{
							association: 'sera_report',
							required: true
						}
					]
				}
			);

			if (!existing_sera_review) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.SERA_SUMMARY_REVIEW.NOT_FOUND);
			}

			const existing_sera_summary = await this.sera_summary_repository.findByPk(
				existing_sera_review.sera_summary_id,
				{
					include: [
						{
							association: 'review',
							required: true,
							include: [
								{
									association: 'sera_report',
									required: true,
									include: [
										{
											association: 'sera_report_updated',
											required: true
										}
									]
								}
							]
						}
					]
				}
			);

			const sera_report_json = existing_sera_review.sera_report.map((report) => report?.toJSON());
			const sera_summary_json = existing_sera_summary?.toJSON();
			const seraReport = new SeraReport(sera_report_json, sera_summary_json);
			const statistics = seraReport.calculateSeraAnalysisStatistics();

			transaction = await this.repository.db.sequelize.transaction();
			await this.repository.delete(
				{
					where: {
						id
					}
				},
				{
					transaction
				}
			);

			await this.sera_summary_review_repository.update(
				{
					analyzed_reports: statistics.analyzed_reports ?? 0,
					average_rpn: statistics.average_rpn ?? 0,
					highest_rpn: statistics.highest_rpn ?? 0,
					sum_rpn: statistics.sum_rpn ?? 0,
					reviewed_reports: statistics.reviewed_reports ?? 0
				},
				{
					where: {
						id: existing_sera_review.sera_summary_id
					},
					transaction
				}
			);

			await transaction.commit();
			const deleted_sera_report = await this.repository.findByPk(id, { paranoid: false });
			logger.info('[SeraReport] service - delete finish');
			return deleted_sera_report;
		} catch (error) {
			logger.error('[SeraReport] service - delete error', { error });
			await transaction?.rollback();
			throw error;
		}
	}

	async findByTaskId({ task_id, sera_summary_review_id }) {
		logger.info('[SeraReport] service - findByTaskId init');
		const existing_sera_reports = await this.repository.findAllByForeignKey({
			where: {
				task_id,
				sera_summary_review_id
			}
		});
		logger.info('[SeraReport] service - findByTaskId finish');
		return existing_sera_reports;
	}

	async findAllBySeraSummaryReviewId(sera_summary_review_id) {
		logger.info('[SeraReport] service - findAllBySeraSummaryReviewId init');
		const existing_sera_reports = await this.repository.findAllByForeignKey({
			where: {
				sera_summary_review_id
			},
			include: [
				{
					association: 'risk_damage',
					attributes: ['id']
				},
				{
					association: 'exposure',
					attributes: ['score']
				},
				{
					association: 'severity',
					attributes: ['score']
				},
				{
					association: 'vulnerability',
					attributes: ['score']
				}
			]
		});
		const mapped_reports = existing_sera_reports
			.map((report) => report.toJSON())
			.map((report) => ({
				...report,
				exposure_score: report.exposure?.score,
				severity_score: report.severity?.score,
				vulnerability_score: report.vulnerability?.score
			}));
		logger.info('[SeraReport] service - findAllBySeraSummaryReviewId finish');
		return mapped_reports;
	}

	async index() {
		logger.info('[SeraReport] service - index init');
		const existing_sera_reports = await this.repository.findAll();
		logger.info('[SeraReport] service - index finish');
		return existing_sera_reports;
	}

	async sumRPN(params) {
		logger.info('[SeraReport] service - sumRPN init', { params });
		const { organization_id, company_id, sector_id, line_id, workstation_id, companies_ids, start_date, end_date } =
			params;

		let user_id = '';

		if (StorageContext.getStore()?.environment === 'sandbox') {
			user_id = StorageContext.getStore().user_id;
		}

		const [result, error] = await this.repository.sumRPN({
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_ids,
			start_date,
			end_date,
			user_id
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[SeraReport] service - sumRPN finish');
		return result;
	}

	async hierarchySumRPN(params) {
		logger.info('[SeraReport] service - hierarchySumRPN init', { params });
		const {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_ids,
			start_date,
			end_date,
			offset,
			limit
		} = params;

		let user_id = '';

		if (StorageContext.getStore()?.environment === 'sandbox') {
			user_id = StorageContext.getStore().user_id;
		}

		const [result, error] = await this.repository.hierarchySumRPN({
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_ids,
			start_date,
			end_date,
			offset,
			limit,
			user_id
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[SeraReport] service - hierarchySumRPN finish');
		return result;
	}

	async incidenceCategories(params) {
		logger.info('[SeraReport] service - incidenceCategories init', { params });
		const { organization_id, company_id, sector_id, line_id, workstation_id, companies_ids, start_date, end_date } =
			params;

		let user_id = '';

		if (StorageContext.getStore()?.environment === 'sandbox') {
			user_id = StorageContext.getStore().user_id;
		}

		const [data, error] = await this.repository.incidenceCategories({
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_ids,
			start_date,
			end_date,
			user_id
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		const order_data = data.sort(
			(a, b) => SERA_CATEGORIES_ORDER.indexOf(a.description) - SERA_CATEGORIES_ORDER.indexOf(b.description)
		);

		const total = data.reduce((acc, data) => acc + data.count, 0);

		const result = order_data.map((category) => ({
			...category,
			percentage: this.#calculatePercentage(category.count, total)
		}));

		logger.info('[SeraReport] service - incidenceCategories finish');
		return result;
	}

	#calculatePercentage(count, total) {
		return Number(((count / total) * 100).toFixed(2));
	}
}
