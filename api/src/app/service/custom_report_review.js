import _ from 'lodash';
import config from 'config';
import sequelize from 'sequelize';
import { ACTIONS_ENUM, ReportPDFGenerator, ReviewHistoryD86 } from '../entities/index.js';
import { AppError, HelpersUtil, RESPONSE_ERROR_ENTITIES, logger } from '../helpers/index.js';
import { StorageContext } from '../utils/storage_context.js';

const { CUSTOM_REPORT_REVIEW, CUSTOM_REPORT, CUSTOM_REPORT_RESULT, FILE } = RESPONSE_ERROR_ENTITIES;

const bucket = config.get('App.bucket');
const { Op } = sequelize;

export class CustomReportReviewService {
	#review_history;
	#pdf_generator;
	#helpers_util;

	constructor({
		repository,
		file_repository,
		line_repository,
		upload_repository,
		sector_repository,
		action_plan_repository,
		workstation_repository,
		custom_report_repository,
		action_plan_task_repository,
		custom_report_step_repository,
		custom_report_result_repository,
		business_information_repository,
		custom_report_step_key_result_repository,
		custom_report_result_actions_log_repository,
		user_repository,
		custom_report_sub_step_key_result_repository,
		custom_report_step_key_additional_item_result_repository
	}) {
		this.repository = repository;
		this.file_repository = file_repository;
		this.line_repository = line_repository;
		this.#helpers_util = new HelpersUtil();
		this.sector_repository = sector_repository;
		this.upload_repository = upload_repository;
		this.#review_history = new ReviewHistoryD86();
		this.#pdf_generator = new ReportPDFGenerator();
		this.action_plan_repository = action_plan_repository;
		this.workstation_repository = workstation_repository;
		this.custom_report_repository = custom_report_repository;
		this.action_plan_task_repository = action_plan_task_repository;
		this.custom_report_step_repository = custom_report_step_repository;
		this.business_information_repository = business_information_repository;
		this.custom_report_result_repository = custom_report_result_repository;
		this.custom_report_step_key_result_repository = custom_report_step_key_result_repository;
		this.custom_report_sub_step_key_result_repository = custom_report_sub_step_key_result_repository;
		this.custom_report_result_actions_log_repository = custom_report_result_actions_log_repository;
		this.user_repository = user_repository;
		this.custom_report_step_key_additional_item_result_repository =
			custom_report_step_key_additional_item_result_repository;
	}

	async create(payload) {
		logger.info('[CustomReportReview] service - create init');
		const {
			user_id,
			file_id,
			step_id,
			sector_id,
			workstation_id,
			custom_report_id,
			original_custom_report_result_id
		} = payload;

		let transaction;
		try {
			const last_review = await this.repository.findLastReviewByOriginalResultId(
				original_custom_report_result_id
			);
			const { custom_report_result, file } = await this.#validateShowMethodIds({
				file_id,
				custom_report_result_id: last_review.custom_report_result_id
			});

			file.workstation_id = workstation_id;
			file.sector_id = sector_id;

			const { step_keys_results, sub_step_keys_results } = await this.#getStepKeysAndSubStepKeysResults(
				custom_report_result.id
			);
			const additional_item_results = await this.#getAdditionalItemsResultByResultId(
				last_review.custom_report_result_id
			);
			const action_plans = await this.#getActionPlansByFileId(custom_report_result.file_id);
			const current_step_id = await this.#setCurrentStepId(step_id, custom_report_id);

			const version_name = `Revaluation ${last_review.version}`;
			const version = last_review.version + 1;
			const new_result = {
				...payload,
				current_step_id,
				created_by_user_id: user_id,
				name: custom_report_result.name,
				result: custom_report_result.result,
				comment: custom_report_result.comment,
				sum_score: custom_report_result.sum_score,
				worst_score: custom_report_result.worst_score,
				average_score: custom_report_result.average_score
			};

			transaction = await this.repository.db.sequelize.transaction();

			let created_custom_report_result = await this.#createCustomReportResult(new_result, transaction);
			const created_result_id = created_custom_report_result.id;
			const created_custom_report_review = await this.#createCustomReportReview(
				{
					version,
					user_id,
					version_name,
					original_custom_report_result_id,
					custom_report_result_id: created_result_id
				},
				transaction
			);
			await this.#createStepKeyResults(step_keys_results, created_result_id, transaction);
			await this.#createSubStepKeyResults(sub_step_keys_results, created_result_id, transaction);
			await this.#createAdditionalItemResults(additional_item_results, created_result_id, transaction);
			await this.#createActionPlansAndTasks(action_plans, file_id, transaction);
			await file.save({ transaction });

			if (custom_report_result.consolidated) {
				await this.#deconsolidateReport({
					user_id,
					transaction,
					custom_report_result_id: custom_report_result.id
				});
			}

			await transaction.commit();

			created_custom_report_result.id = created_custom_report_review.original_custom_report_result_id;

			logger.info('[CustomReportReview] service - create finish');
			return created_custom_report_result;
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async listAll(payload) {
		logger.info('[CustomReportReview] service - listAll init');
		const { custom_report_name, user_company_ids, filters } = payload;

		const custom_report = await this.#getCustomReportByName(custom_report_name);

		const { offset, limit, evaluator_id, type_rpn, min_rpn, max_rpn, reviewed_start_date, reviewed_end_date } =
			filters;

		const sector_ids = await this.#getSectorsByCompany(filters, user_company_ids);
		const line_ids = await this.#getLinesBySector(filters, sector_ids);
		const workstation_ids = await this.#getWorkstationsByLine(filters, line_ids);
		const filtered_file_ids = await this.#getFileIdsByWorkstations(filters, workstation_ids);
		const filtered_custom_report_result_ids = await this.#getCustomReportResultIdsByFiles(
			filters,
			filtered_file_ids,
			custom_report.id
		);

		const params = {
			filters: {
				min_rpn,
				max_rpn,
				type_rpn,
				evaluator_id,
				reviewed_end_date,
				reviewed_start_date,
				original_custom_report_result_ids: filtered_custom_report_result_ids
			}
		};

		const count_last_reviews = await this.repository.countCustomReportResultLastReviews(params);
		const result_last_reviews = await this.repository.findCustomReportResultLastReviews(params, offset, limit);

		const count = count_last_reviews.count;
		const rows = result_last_reviews.map(
			({
				id,
				result,
				version,
				file_id,
				created_at,
				result_name,
				custom_report_id,
				workstation_name,
				original_result_id,
				original_created_at
			}) => {
				return {
					version,
					id: id,
					file_id,
					result: result,
					custom_report_id,
					report_name: result_name,
					workstation: workstation_name,
					created_at: original_created_at,
					original_id: original_result_id,
					last_review_date: version > 1 ? created_at : null
				};
			}
		);

		const response = {
			count,
			rows
		};

		logger.info('[CustomReportReview] service - listAll finish');
		return response;
	}

	async listFiles(parameters) {
		logger.info('[CustomReportReview] listFiles - init');
		const file_ids = await this.#getFilesId(parameters.original_custom_report_result_id);
		const paginated_result = await this.upload_repository.findAndCountAllWithHierarchy({
			...parameters,
			exclude_ids: file_ids
		});
		logger.info('[CustomReportReview] listFiles - finish');
		return paginated_result;
	}

	async show({ original_custom_report_result_id }) {
		logger.info('[CustomReportReview] service - show init');

		await this.#validateCustomReportResultById(original_custom_report_result_id);

		const custom_report_reviews = await this.#getAllCustomReportReviews({
			attributes: ['id', 'name', 'version', 'created_at'],
			where: {
				original_custom_report_result_id,
				version: {
					[Op.gte]: 1
				}
			},
			include: [
				{
					association: 'custom_report_result',
					attributes: ['id', 'name', 'result'],
					include: [
						{
							association: 'evaluator',
							attributes: ['name']
						},
						{
							association: 'file',
							attributes: ['id', 'original_name']
						}
					]
				}
			],
			order: [['version', 'ASC']]
		});

		const response = this.#formatCustomReportReviewData(custom_report_reviews);

		logger.info('[CustomReportReview] service - show finish');
		return response;
	}

	async findResultHistory({ review_id }) {
		logger.info('[CustomReportReview] service - findResultHistory init');

		const custom_report_review = await this.#getCustomReportReviewById(review_id);

		if (custom_report_review.custom_report_result_id) {
			throw new AppError(CUSTOM_REPORT_REVIEW.HISTORY_NOT_FOUND);
		}

		const history = await this.#setResultHistory(custom_report_review);

		logger.info('[CustomReportReview] service - findResultHistory finish');
		return history;
	}

	async findAllReviewsForSelector({ original_custom_report_result_id }) {
		logger.info('[CustomReportReview] service - findAllReviewsForSelector init');

		const custom_report_reviews = await this.#getAllCustomReportReviews({
			where: {
				original_custom_report_result_id
			},
			order: [['version', 'ASC']]
		});

		const response = custom_report_reviews.map((review) => {
			return {
				id: review.id,
				name: review.name,
				version: review.version,
				created_at: review.created_at
			};
		});

		logger.info('[CustomReportReview] service - findAllReviewsForSelector finish');
		return response;
	}

	async deleteLastReview({ original_custom_report_result_id }) {
		logger.info('[CustomReportReview] service - deleteLastReview init');
		let transaction;

		try {
			const last_review = await this.repository.findLastReviewByOriginalResultId(
				original_custom_report_result_id
			);

			transaction = await this.repository.db.sequelize.transaction();

			await this.#deleteCustomReportReview(last_review, transaction);
			await this.#deleteCustomReportResult(last_review, transaction);

			await transaction.commit();
			const deleted_review = await this.repository.findByPk(last_review.id, { paranoid: false });
			logger.info('[CustomReportReview] service - deleteLastReview finish');
			return deleted_review;
		} catch (error) {
			logger.error('[CustomReportReview] service - deleteLastReview error', { error });
			await transaction?.rollback();
			throw error;
		}
	}

	async generateGeneralHistoryPDF(params) {
		logger.info('[CustomReportReview] service - generateGeneralHistoryPDF init');
		const { locale, review_id, organization_id, company_id, user_id } = params;

		let transaction;

		try {
			const custom_report_review = await this.#getCustomReportReviewById(review_id);

			if (custom_report_review.custom_report_result_id) {
				throw new AppError(CUSTOM_REPORT_REVIEW.HISTORY_NOT_FOUND);
			}

			const result = await this.#setResultHistoryPdf({ custom_report_review, locale, user_id });
			const report_name = result.informations.report_name;
			const prefix = this.#helpers_util.getPrefix(organization_id, company_id);
			const file_key = this.#pdf_generator.formatFileKey(report_name, prefix);
			const business_info = await this.#getBusinessInformation(company_id, organization_id);

			const config = this.#setPDFConfigData({
				locale,
				prefix,
				result,
				file_key,
				business_info
			});

			const key = await this.#pdf_generator.generatedPDF(config);
			const target_file = `general-history-${report_name}.pdf`;

			const url = await this.#pdf_generator.generateDownloadURL({
				Key: key,
				Bucket: bucket,
				fileName: target_file
			});

			transaction = await this.repository.db.sequelize.transaction();
			await this.custom_report_result_actions_log_repository.create(
				{
					user_id,
					action: ACTIONS_ENUM.DOWNLOAD_PDF,
					custom_report_result_id: custom_report_review.original_custom_report_result_id
				},
				{
					transaction
				}
			);
			await transaction.commit();

			logger.info('[CustomReportReview] service - generateGeneralHistoryPDF finish');
			return { url };
		} catch (error) {
			logger.error('[CustomReportReview] service - generateGeneralHistoryPDF error', { error });
			await transaction?.rollback();
			throw error;
		}
	}

	async updateComment({ comment, custom_report_review_id }) {
		logger.info('[CustomReportReview] service - updateComment init');
		let transaction;

		try {
			await this.#getCustomReportReviewById(custom_report_review_id);

			transaction = await this.repository.db.sequelize.transaction();
			await this.repository.update(
				{
					comment
				},
				{
					where: {
						id: custom_report_review_id
					},
					transaction
				}
			);
			await transaction.commit();

			logger.info('[CustomReportReview] service - updateComment finish');
			return {
				message: 'Comment updated successfully'
			};
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async #setResultHistory(custom_report_review) {
		const informations = await this.#getResultInformations(custom_report_review);
		const custom_report_review_results = await this.#getAllReviewResultsByOriginalResult(custom_report_review);

		const sum_rpns = custom_report_review_results.map((review) => {
			return {
				name: review.name,
				created_at: review.created_at,
				rpn: review.custom_report_result.sum_score
			};
		});

		const highest_rpns = custom_report_review_results.map((review) => {
			return {
				name: review.name,
				created_at: review.created_at,
				result: review.custom_report_result.result,
				worst_score: review.custom_report_result.worst_score
			};
		});

		const review_results = await this.#getReviewsWithResultsInformation(
			custom_report_review.original_custom_report_result_id
		);

		const reviews_history = this.#review_history.setReviewsHistory(review_results);

		return {
			informations,
			sum_rpns,
			highest_rpns,
			reviews_history,
			comment: custom_report_review.comment
		};
	}

	async #setResultHistoryPdf({ custom_report_review, locale, user_id }) {
		const informations = await this.#getResultInformations(custom_report_review);
		const custom_report_review_results = await this.#getAllReviewResultsByOriginalResult(custom_report_review);

		const sum_rpns = custom_report_review_results.map((review) => {
			return {
				name: review.name,
				created_at: review.created_at,
				rpn: review.custom_report_result.sum_score
			};
		});

		const highest_rpns = custom_report_review_results.map((review) => {
			return {
				name: review.name,
				created_at: review.created_at,
				result: review.custom_report_result.result,
				worst_score: review.custom_report_result.worst_score
			};
		});

		const review_results = await this.#getReviewsWithResultsInformation(
			custom_report_review.original_custom_report_result_id
		);

		const reviews_history = this.#review_history.setReviewsHistoryPdf(review_results, locale);

		const technical_manager = this.#getUserName(user_id);

		return {
			informations,
			sum_rpns,
			highest_rpns,
			reviews_history,
			technical_manager,
			comment: custom_report_review.comment
		};
	}

	async #getSectorsByCompany(filters, user_company_ids) {
		const sectors = await this.sector_repository.findAllByForeignKey({
			where: {
				company_id: filters?.company_id ?? user_company_ids,
				is_active: true
			},
			attributes: ['id']
		});

		const sectors_ids = sectors.map((sector) => sector?.id);
		return sectors_ids;
	}

	async #getLinesBySector(filters, sector_ids) {
		const lines = await this.line_repository.findAllByForeignKey({
			where: {
				sector_id: filters?.sector_id ?? sector_ids
			},
			attributes: ['id']
		});

		const line_ids = lines.map((workstation) => workstation?.id);
		return line_ids;
	}

	async #getWorkstationsByLine(filters, line_ids) {
		const workstations = await this.workstation_repository.findAllByForeignKey({
			where: {
				line_id: filters?.line_id ?? line_ids
			},
			attributes: ['id']
		});

		const workstations_ids = workstations.map((workstation) => workstation?.id);
		return workstations_ids;
	}

	async #getFileIdsByWorkstations(filters, workstation_ids) {
		const where_companies = {
			attributes: ['id'],
			where: {
				workstation_id: filters?.workstation_id ?? workstation_ids,
				is_active: true
			}
		};

		if (StorageContext.getStore()?.environment === 'sandbox') {
			_.set(where_companies, 'where', {
				...where_companies.where,
				user_id: StorageContext.getStore().user_id
			});
		}

		logger.info('[CustomReportReview] service - #getFileIdsByWorkstations where_companies', { ...where_companies });

		const files_response = await this.file_repository.findAll(where_companies);

		if (files_response.length === 0) {
			throw new AppError(FILE.NOT_FOUND);
		}

		const user_access_file_ids = files_response?.map((file) => file?.id);

		return user_access_file_ids;
	}

	async #getCustomReportResultIdsByFiles(filters, file_ids, custom_report_id) {
		const { report_name, created_start_date, created_end_date } = filters;

		const find_custom_report_results = {
			attributes: ['id'],
			where: {
				custom_report_id,
				file_id: file_ids
			}
		};

		if (report_name) {
			_.set(find_custom_report_results, 'where', {
				...find_custom_report_results.where,
				name: {
					[Op.like]: `%${report_name}%`
				}
			});
		}

		if (created_start_date && created_end_date) {
			_.set(find_custom_report_results, 'where', {
				...find_custom_report_results.where,
				created_at: {
					[Op.between]: [created_start_date, created_end_date]
				}
			});
		}

		if (StorageContext.getStore()?.environment === 'sandbox') {
			_.set(find_custom_report_results, 'where', {
				...find_custom_report_results.where,
				created_by_user_id: StorageContext.getStore().user_id
			});
		}

		const custom_report_result_response =
			await this.custom_report_result_repository.findAllByForeignKey(find_custom_report_results);

		if (custom_report_result_response.length === 0) {
			throw new AppError(CUSTOM_REPORT_RESULT.NOT_FOUND);
		}

		const custom_report_result_ids = custom_report_result_response?.map((result) => result?.id);

		return custom_report_result_ids;
	}

	async #getResultInformations(custom_report_review) {
		const include_organization = {
			include: [
				{
					association: 'organization',
					attributes: ['name']
				}
			]
		};

		const include_company = {
			include: [
				{
					association: 'company',
					attributes: ['name'],
					...include_organization
				}
			]
		};

		const include_sector = {
			include: [
				{
					association: 'sector',
					attributes: ['name'],
					...include_company
				}
			]
		};

		const include_line = {
			include: [
				{
					association: 'line',
					attributes: ['name'],
					...include_sector
				}
			]
		};

		const include_hierarchy = {
			include: [
				{
					association: 'workstations',
					attributes: ['name'],
					...include_line
				}
			]
		};

		const find_result_information = {
			attributes: ['name'],
			include: [
				{
					association: 'file',
					attributes: ['id'],
					...include_hierarchy
				}
			]
		};

		const custom_report_result = await this.custom_report_result_repository.findByPk(
			custom_report_review.original_custom_report_result_id,
			find_result_information
		);

		const {
			name,
			file: { workstations }
		} = custom_report_result;

		const result_informations = {
			report_name: name,
			organization_name: workstations.line.sector.company.organization.name,
			company_name: workstations.line.sector.company.name,
			sector_name: workstations.line.sector.name,
			line_name: workstations.line.name,
			workstation_name: workstations.name
		};

		return result_informations;
	}

	async #getAllReviewResultsByOriginalResult(custom_report_review) {
		const find_result_review = {
			attributes: ['id', 'name', 'created_at'],
			where: {
				version: {
					[Op.gte]: 1
				},
				original_custom_report_result_id: custom_report_review.original_custom_report_result_id
			},
			include: [
				{
					association: 'custom_report_result',
					attributes: ['id', 'sum_score', 'result', 'worst_score']
				}
			],
			order: [['created_at', 'ASC']]
		};

		const custom_report_review_results = await this.repository.findAll(find_result_review);
		return custom_report_review_results;
	}

	async #getReviewsWithResultsInformation(original_result_id) {
		const include_risk_description = {
			association: 'risk_description',
			attributes: ['id', 'description']
		};

		const include_risk_category = {
			association: 'risk_category',
			attributes: ['id', 'description']
		};

		const include_risk_damage = {
			association: 'risk_damage',
			attributes: ['id', 'description']
		};

		const include_severity = {
			association: 'severity',
			attributes: ['id', 'description']
		};

		const include_exposure = {
			association: 'exposure',
			attributes: ['id', 'description']
		};

		const include_vulnerability = {
			association: 'vulnerability',
			attributes: ['id', 'description']
		};

		const include_step_key = {
			association: 'step_key_results',
			attributes: ['id', 'score', 'result'],
			include: [
				{
					association: 'step_key',
					attributes: ['id', 'name', 'description', 'sequence'],
					order: [['sequence', 'ASC']]
				},
				include_risk_description,
				include_risk_category,
				include_risk_damage,
				include_severity,
				include_exposure,
				include_vulnerability
			]
		};

		const include_sub_step_key = {
			association: 'sub_step_key_results',
			attributes: ['id', 'score', 'result'],
			include: [
				{
					association: 'sub_step_key',
					attributes: ['id', 'name', 'description']
				},
				{ ...include_risk_description, as: 'sub_step_risk_description' },
				{ ...include_risk_category, as: 'sub_step_risk_category' },
				{ ...include_risk_damage, as: 'sub_step_risk_damage' },
				{ ...include_severity, as: 'sub_step_severity' },
				{ ...include_exposure, as: 'sub_step_exposure' },
				{ ...include_vulnerability, as: 'sub_step_vulnerability' }
			]
		};

		const include_evaluator = {
			association: 'evaluator',
			attributes: ['name']
		};

		const find_results = {
			attributes: ['id', 'name', 'version', 'created_at'],
			where: {
				version: {
					[Op.gte]: 1
				},
				original_custom_report_result_id: original_result_id
			},
			include: [
				{
					attributes: ['id', 'worst_score', 'result'],
					association: 'custom_report_result',
					include: [include_step_key, include_sub_step_key, include_evaluator]
				}
			],
			order: [['version', 'ASC']]
		};

		const review_results = await this.repository.findAll(find_results);
		return review_results;
	}

	async #getAllCustomReportReviews(options) {
		const custom_report_review = await this.repository.findAll(options);

		if (!custom_report_review) {
			throw new AppError(CUSTOM_REPORT_REVIEW.NOT_FOUND);
		}

		return custom_report_review;
	}

	async #getCustomReportReviewById(review_id) {
		const custom_report_review = await this.repository.findByPk(review_id);

		if (!custom_report_review) {
			throw new AppError(CUSTOM_REPORT_REVIEW.NOT_FOUND);
		}
		return custom_report_review;
	}

	async #getBusinessInformation(company_id, organization_id) {
		let business_informations = await this.business_information_repository.findOne({
			where: {
				company_id,
				organization_id,
				is_active: true
			},
			include: [
				{
					association: 'organization',
					attributes: ['id', 'name', 'url_logo']
				}
			],
			attributes: ['company_name', 'fantasy_name', 'cnpj', 'address', 'city', 'state', 'zipcode', 'district']
		});

		if (!business_informations) {
			business_informations = {
				organization: {
					name: 'Kinebot',
					url_logo: null
				},
				company_name: 'Kinebot',
				fantasy_name: 'Kinebot - Tecnologia da Informacao LTDA',
				cnpj: '41.350.359/0001-76',
				address: 'Marginal Comendador Franco, 1341',
				city: 'Curitiba',
				state: 'Paraná',
				zipcode: '80215-090',
				district: 'Jardim Botânico'
			};
		}
		return business_informations;
	}

	async #validateCustomReportResultById(original_custom_report_result_id) {
		const custom_report_result = await this.custom_report_result_repository.findByPk(
			original_custom_report_result_id
		);

		if (!custom_report_result) {
			throw new AppError(CUSTOM_REPORT_RESULT.NOT_FOUND);
		}
	}

	async #deleteCustomReportResult(last_review, transaction) {
		await this.custom_report_result_repository.delete(
			{
				where: {
					id: last_review.custom_report_result_id
				}
			},
			transaction
		);
	}

	async #deleteCustomReportReview(last_review, transaction) {
		if (this.#isInitialVersion(last_review)) {
			await this.repository.delete(
				{
					where: {
						original_custom_report_result_id: last_review.original_custom_report_result_id
					}
				},
				transaction
			);
		}

		await this.repository.delete(
			{
				where: {
					id: last_review.id
				}
			},
			transaction
		);
	}

	async #getCustomReportByName(custom_report_name) {
		const custom_report = await this.custom_report_repository.findOne({
			where: {
				name: custom_report_name
			}
		});

		if (!custom_report) {
			throw new AppError(CUSTOM_REPORT.NOT_FOUND);
		}

		return custom_report.get({ plain: true });
	}

	async #getFilesId() {
		const custom_report_reviews = await this.#getAllCustomReportReviews({
			attributes: ['id'],
			include: [
				{
					association: 'custom_report_result',
					attributes: ['id', 'file_id'],
					required: true
				}
			]
		});

		const files_ids = this.#formatFilesIds(custom_report_reviews);
		return files_ids;
	}

	async #getUserName(user_id) {
		const { name } = await this.user_repository.findByPk(user_id, {
			attributes: ['name']
		});

		return name;
	}

	#setPDFConfigData({ locale, prefix, result, file_key, business_info }) {
		const language = locale.replace('-', '_');

		const organization = {
			cnpj: business_info.cnpj,
			city: business_info.city,
			state: business_info.state,
			zipcode: business_info.zipcode,
			address: business_info.address,
			district: business_info.district,
			fantasy_name: business_info.fantasy_name,
			company_name: business_info.company_name,
			organization_name: business_info.organization?.name,
			organization_logo: business_info.organization?.url_logo
		};

		return {
			functionName: 'pdf_container',
			data: {
				...result,
				bucket,
				prefix,
				language,
				file_key,
				organization,
				report_type: 'john-deere',
				type: 'john-deere/jds-d86-history',
				title: 'General History | JDS-D86'
			}
		};
	}

	#isInitialVersion(last_review) {
		return last_review.version === 1;
	}

	#formatCustomReportReviewData(custom_report_reviews) {
		return custom_report_reviews.map((custom_report_review) => {
			const { id, name, version, created_at, custom_report_result } = custom_report_review;

			return {
				id,
				version,
				created_at,
				version_name: name,
				result: custom_report_result.result,
				file_id: custom_report_result.file.id,
				file_name: custom_report_result.file.original_name,
				evaluator_name: custom_report_result.evaluator.name
			};
		});
	}

	#formatFilesIds(custom_report_reviews) {
		return custom_report_reviews.map((custom_report_review) => custom_report_review.custom_report_result.file_id);
	}

	async #getFileById(file_id) {
		const file = await this.file_repository.findByPk(file_id);
		if (!file) {
			throw new AppError(FILE.NOT_FOUND);
		}
		return file;
	}

	async #createCustomReportResult(
		{
			name,
			result,
			comment,
			file_id,
			sum_score,
			worst_score,
			evaluator_id,
			average_score,
			current_step_id,
			collection_date,
			interviewer_name,
			interviewee_name,
			custom_report_id,
			created_by_user_id
		},
		transaction
	) {
		const custom_report_result = await this.custom_report_result_repository.create(
			{
				name,
				result,
				comment,
				file_id,
				sum_score,
				worst_score,
				evaluator_id,
				average_score,
				current_step_id,
				collection_date,
				interviewer_name,
				interviewee_name,
				custom_report_id,
				created_by_user_id
			},
			{ transaction }
		);

		return custom_report_result.get({ plain: true });
	}

	async #createCustomReportReview(
		{ version, version_name, original_custom_report_result_id, custom_report_result_id, user_id },
		transaction
	) {
		const custom_report_review = await this.repository.create(
			{
				version,
				name: version_name,
				custom_report_result_id,
				original_custom_report_result_id
			},
			{ transaction }
		);

		await this.custom_report_result_actions_log_repository.create(
			{
				user_id,
				custom_report_result_id,
				action: ACTIONS_ENUM.REVIEW
			},
			{ transaction }
		);

		return custom_report_review.get({ plain: true });
	}

	async #createAdditionalItemResults(additional_item_results, custom_report_result_id, transaction) {
		const additional_item_results_data = additional_item_results.map((additional_item_result) => {
			return {
				custom_report_result_id,
				result: additional_item_result.result,
				value_1: additional_item_result.value_1,
				value_2: additional_item_result.value_2,
				value_3: additional_item_result.value_3,
				value_4: additional_item_result.value_4,
				additional_item_option_id_1: additional_item_result.option_1?.id ?? null,
				additional_item_option_id_2: additional_item_result.option_2?.id ?? null,
				custom_report_step_key_additional_item_id: additional_item_result.step_key_additional_item.id
			};
		});

		await this.custom_report_step_key_additional_item_result_repository.bulkCreate(additional_item_results_data, {
			transaction
		});
	}

	async #createSubStepKeyResults(sub_step_key_results, custom_report_result_id, transaction) {
		const sub_step_key_results_data = sub_step_key_results.map((sub_step_key_result) => {
			return {
				custom_report_result_id,
				score: sub_step_key_result.score,
				result: sub_step_key_result.result,
				exposure_id: sub_step_key_result.exposure_id,
				severity_id: sub_step_key_result.severity_id,
				risk_damage_id: sub_step_key_result.risk_damage_id,
				risk_category_id: sub_step_key_result.risk_category_id,
				vulnerability_id: sub_step_key_result.vulnerability_id,
				risk_description_id: sub_step_key_result.risk_description_id,
				custom_report_sub_step_key_id: sub_step_key_result.custom_report_sub_step_key_id
			};
		});

		await this.custom_report_sub_step_key_result_repository.bulkCreate(sub_step_key_results_data, {
			transaction
		});
	}

	async #createStepKeyResults(step_key_results, custom_report_result_id, transaction) {
		const step_key_results_data = step_key_results.map((step_key_result) => {
			return {
				custom_report_result_id,
				score: step_key_result.score,
				result: step_key_result.result,
				exposure_id: step_key_result.exposure_id,
				severity_id: step_key_result.severity_id,
				risk_damage_id: step_key_result.risk_damage_id,
				risk_category_id: step_key_result.risk_category_id,
				vulnerability_id: step_key_result.vulnerability_id,
				risk_description_id: step_key_result.risk_description_id,
				custom_report_step_key_id: step_key_result.custom_report_step_key_id
			};
		});

		await this.custom_report_step_key_result_repository.bulkCreate(step_key_results_data, { transaction });
	}

	async #createActionPlansAndTasks(action_plans, file_id, transaction) {
		for (let action_plan of action_plans) {
			const data = {
				file_id,
				title: action_plan.title,
				board: action_plan.board,
				user_id: action_plan.user_id,
				step_id: action_plan.step_id,
				deadline: action_plan.deadline,
				lexo_rank: action_plan.lexo_rank,
				description: action_plan.description,
				completed_at: action_plan.completed_at,
				responsible_user_id: action_plan.responsible_user_id,
				sera_summary_review_id: action_plan.sera_summary_review_id,
				custom_report_step_key_id: action_plan.custom_report_step_key_id,
				custom_report_sub_step_key_id: action_plan.custom_report_sub_step_key_id
			};

			const created_action_plan = await this.action_plan_repository.create(data, {
				transaction
			});

			const action_plan_tasks = action_plan.action_plan_task.map((task) => {
				return {
					type: task.type,
					description: task.description,
					is_completed: task.is_completed,
					action_plan_id: created_action_plan.id
				};
			});

			await this.action_plan_task_repository.bulkCreate(action_plan_tasks, {
				transaction
			});
		}
	}

	async #validateShowMethodIds({ file_id, custom_report_result_id }) {
		const file = await this.#getFileById(file_id);
		const custom_report_result = await this.#getCustomReportResultById(custom_report_result_id, {
			include: [
				{
					association: 'current_step',
					attributes: ['id', 'name', 'description', 'sequence']
				},
				{
					association: 'evaluator',
					attributes: ['id', 'name']
				},
				{
					association: 'created_by_user',
					attributes: ['id', 'name']
				}
			]
		});

		return {
			file,
			custom_report_result
		};
	}

	async #setCurrentStepId(step_id, custom_report_id) {
		const last_step = await this.#getStepById(step_id);
		const next_step = last_step.sequence + 1;
		const current_step_setting = await this.#getCurrentStepBySequenceAndCustomReportId(next_step, custom_report_id);
		return current_step_setting.id;
	}

	async #getStepById(step_id) {
		const step = await this.custom_report_step_repository.findByPk(step_id);
		if (!step) {
			throw new AppError(CUSTOM_REPORT_STEP.NOT_FOUND);
		}
		return step;
	}

	async #getCurrentStepBySequenceAndCustomReportId(sequence, custom_report_id) {
		const step = await this.custom_report_step_repository.findOne({
			where: {
				sequence,
				custom_report_id
			}
		});
		if (!step) {
			throw new AppError(CUSTOM_REPORT_STEP.NOT_FOUND);
		}
		return step;
	}

	async #getStepKeysAndSubStepKeysResults(custom_report_result_id) {
		const where = {
			[Op.or]: [
				{
					score: {
						[Op.eq]: null
					}
				},
				{
					score: {
						[Op.gte]: 0
					}
				}
			],
			custom_report_result_id
		};

		const step_keys_results = await this.custom_report_step_key_result_repository.findAll({
			where
		});
		const sub_step_keys_results = await this.custom_report_sub_step_key_result_repository.findAll({
			where
		});

		return {
			step_keys_results,
			sub_step_keys_results
		};
	}

	async #getActionPlansByFileId(file_id) {
		const action_plans_response = await this.action_plan_repository.findAll({
			where: {
				file_id,
				is_active: true
			},
			include: [
				{
					association: 'action_plan_task'
				}
			]
		});

		const action_plans = action_plans_response.map((action_plan) => action_plan?.get({ plain: true }));

		return action_plans;
	}

	async #getAdditionalItemsResultByResultId(custom_report_result_id) {
		const additional_items_results_response =
			await this.custom_report_step_key_additional_item_result_repository.findAll({
				attributes: ['id', 'result', 'value_1', 'value_2', 'value_3', 'value_4'],
				where: {
					custom_report_result_id
				},
				include: [
					{
						association: 'option_1',
						attributes: ['id', 'description']
					},
					{
						association: 'option_2',
						attributes: ['id', 'description']
					},
					{
						association: 'step_key_additional_item',
						attributes: ['id', 'additional_item_id', 'custom_report_step_key_id'],
						include: [
							{
								association: 'additional_item'
							}
						]
					}
				]
			});

		const additional_items_results = additional_items_results_response?.map((additional_items_result) =>
			additional_items_result.get({ plain: true })
		);
		return additional_items_results;
	}

	async #getCustomReportResultById(custom_report_result_id, options) {
		const custom_report_result = await this.custom_report_result_repository.findByPk(
			custom_report_result_id,
			options
		);
		if (!custom_report_result) {
			throw new AppError(CUSTOM_REPORT_RESULT.NOT_FOUND);
		}
		return custom_report_result;
	}

	async #deconsolidateReport({ transaction, user_id, custom_report_result_id }) {
		await this.custom_report_result_repository.update(
			{
				location: null,
				consolidated: false
			},
			{
				where: {
					id: custom_report_result_id
				},
				transaction
			}
		);
		await this.custom_report_result_actions_log_repository.create(
			{
				user_id,
				custom_report_result_id,
				action: ACTIONS_ENUM.DECONSOLIDATE
			},
			{
				transaction
			}
		);
	}
}
