import moment from 'moment';
import config from 'config';
import {
	Report<PERSON>apper,
	ResultsMapper,
	STEP_NAME_TO_NUMBER_MAPPER,
	LibertyMutualManualMaterialsHandler
} from '../entities/index.js';
import { Util } from '../helpers/util.js';
import { logger } from '../helpers/logger.js';
import { Lambda } from '../helpers/lambda.js';
import { AppError } from '../helpers/errors.js';
import { Storage } from '../helpers/storage.js';
import { FormatTime } from '../helpers/format_time.js';
import { ENUM_STATUS_FILE, RESPONSE_ERROR_ENTITIES, RESPONSE_ERROR_STATUS } from '../helpers/constants.js';
import { StorageContext } from '../utils/storage_context.js';

const bucket = config.get('App.bucket');
const { DATABASE_FAILED_PERFORM_QUERY } = RESPONSE_ERROR_STATUS;
const { REPORT, FILE, WORKSTATION, SYSTEM_OF_UNITS } = RESPONSE_ERROR_ENTITIES;

export class LibertyMutualReportService {
	#INCLUDE_REPORT_INPUTS = {
		association: 'report_inputs',
		include: [
			{
				association: 'task',
				attributes: ['id', 'name', 'description', 'plural_description']
			},
			{
				association: 'report',
				attributes: ['id', 'comment'],
				include: [
					{
						association: 'system_of_units',
						attributes: ['id', 'name', 'description']
					}
				]
			}
		]
	};

	constructor({
		repository,
		file_repository,
		user_repository,
		workstation_repository,
		system_of_units_repository,
		liberty_mutual_report_input_repository
	}) {
		this.lambda = new Lambda();
		this.repository = repository;
		this.storage = new Storage();
		this.formatTime = new FormatTime();
		this.user_repository = user_repository;
		this.file_repository = file_repository;
		this.reportMapper = new ReportMapper();
		this.resultsMapper = new ResultsMapper();
		this.workstation_repository = workstation_repository;
		this.system_of_units_repository = system_of_units_repository;
		this.liberty_mutual_report_input_repository = liberty_mutual_report_input_repository;
	}

	async show(file_id) {
		logger.info('[LibertyMutualReport] service - show init');
		const existing_liberty_mutual = await this.repository.findOne({
			where: {
				file_id
			},
			include: [
				{
					association: 'system_of_units',
					attributes: ['id', 'name', 'description']
				},
				this.#INCLUDE_REPORT_INPUTS
			]
		});

		const file = await this.#getFileInformations(file_id);
		if (!file) {
			throw new AppError(FILE.NOT_FOUND);
		}

		const file_infomations = {
			uploaded_date: file.createdAt,
			original_name: file.original_name,
			line_id: file.workstations?.line.id,
			workstation_id: file.workstations?.id,
			line_name: file.workstations?.line.name,
			workstation_name: file.workstations?.name,
			sector_id: file.workstations?.line.sector.id,
			sector_name: file.workstations?.line.sector.name,
			company_id: file.workstations?.line.sector.company.id,
			company_name: file.workstations?.line.sector.company.name,
			organization_id: file.workstations?.line.sector.company.Organization.id,
			duration: this.formatTime.formatSecondsToUnabbreviatedTime(file.duration),
			organization_name: file.workstations?.line.sector.company.Organization.name
		};

		if (!existing_liberty_mutual) {
			return {
				...file_infomations,
				step: 0
			};
		}

		const liberty_mutual = existing_liberty_mutual?.get({ plain: true });
		const mapped_liberty_mutual_reports = liberty_mutual.report_inputs.map((report) => {
			const { id, report_name } = this.reportMapper.mapReportData(report, file);
			return {
				report: {
					id,
					report_name
				},
				sub_step: this.reportMapper.setFormSubStep(report)
			};
		});
		const step = this.reportMapper.setFormStep(liberty_mutual);
		logger.info('[LibertyMutualReport] service - show finish');
		return {
			...file_infomations,
			step,
			id: existing_liberty_mutual.id,
			reports: mapped_liberty_mutual_reports,
			comment: existing_liberty_mutual.comment,
			system_of_units: liberty_mutual.system_of_units,
			system_of_units_id: liberty_mutual.system_of_units?.id
		};
	}

	async getResult(file_id) {
		logger.info('[LibertyMutualReport] service - getResult init');
		const existing_liberty_mutual = await this.repository.findOne({
			where: {
				file_id
			},
			include: [
				{
					association: 'system_of_units',
					attributes: ['id', 'name', 'description']
				},
				this.#INCLUDE_REPORT_INPUTS
			]
		});
		if (!existing_liberty_mutual) {
			throw new AppError(REPORT.NOT_FOUND);
		}

		const file = await this.#getFileInformations(file_id);
		if (!file) {
			throw new AppError(FILE.NOT_FOUND);
		}

		const liberty_mutual = existing_liberty_mutual?.get({ plain: true });
		const file_infomations = {
			original_name: file.original_name,
			line_name: file.workstations?.line.name,
			workstation_name: file.workstations?.name,
			sector_name: file.workstations?.line.sector.name,
			uploaded_date: file.createdAt,
			company_name: file.workstations?.line.sector.company.name,
			collection_date: liberty_mutual?.created_at,
			assessment_date: liberty_mutual?.updated_at,
			duration: this.formatTime.formatSecondsToUnabbreviatedTime(file.duration),
			organization_name: file.workstations?.line.sector.company.Organization.name
		};
		const mapped_liberty_mutual_reports = liberty_mutual.report_inputs.map((report) => ({
			report: this.reportMapper.mapReportData(report, file),
			sub_step: this.reportMapper.setFormSubStep(report)
		}));
		const mapped_for_result = mapped_liberty_mutual_reports.map((mapped_liberty_mutual) => {
			const { results, summary, informations } = this.resultsMapper.mapReportForResult(
				mapped_liberty_mutual.report
			);
			return {
				summary,
				results,
				informations,
				task_name: informations.task_name,
				id: mapped_liberty_mutual?.report.id,
				report_name: mapped_liberty_mutual.report.report_name
			};
		});
		logger.info('[LibertyMutualReport] service - getResult finish');
		return {
			id: liberty_mutual.id,
			reports: mapped_for_result,
			informations: file_infomations,
			comment: liberty_mutual.comment
		};
	}

	async create({ file_id, workstation_id, sector_id, system_of_units_id, user_id }) {
		logger.info('[LibertyMutualReport] service - create init');
		let transaction;

		try {
			const existing_liberty_mutual = await this.repository.findOne({
				where: {
					file_id
				}
			});

			if (existing_liberty_mutual) {
				throw new AppError(REPORT.ALREADY_CREATED);
			}

			const existing_file = await this.file_repository.findByPk(file_id, {
				where: {
					is_active: true
				}
			});

			if (!existing_file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			const existing_workstation = await this.workstation_repository.findByPk(workstation_id);

			if (!existing_workstation) {
				throw new AppError(WORKSTATION.NOT_FOUND);
			}

			const existing_system_of_units = await this.system_of_units_repository.findByPk(system_of_units_id);

			if (!existing_system_of_units) {
				throw new AppError(SYSTEM_OF_UNITS.NOT_FOUND);
			}

			existing_file.sector_id = sector_id;
			existing_file.workstation_id = workstation_id;

			transaction = await this.repository.db.sequelize.transaction();
			const created_report = await this.repository.create(
				{
					file_id,
					system_of_units_id,
					report_user_id: user_id
				},
				{
					transaction
				}
			);

			if (!created_report) {
				throw new AppError(REPORT.FAIL_CREATE);
			}

			await existing_file.save({ transaction });
			await transaction.commit();

			const mapped_report = created_report.get({ plain: true });
			logger.info('[LibertyMutualReport] service - create finish');
			return {
				...mapped_report,
				step: STEP_NAME_TO_NUMBER_MAPPER.TASK_LIST
			};
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async updateReportBasicInformation({ file_id, report_id, sector_id, workstation_id, system_of_units_id, user_id }) {
		logger.info('[LibertyMutualReport] service - updateReportBasicInformation init');
		let transaction;
		try {
			const existing_liberty_mutual = await this.repository.findByPk(report_id, {
				include: [
					{
						association: 'system_of_units',
						attributes: ['id', 'name', 'description']
					},
					this.#INCLUDE_REPORT_INPUTS
				]
			});

			if (!existing_liberty_mutual) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			const existing_file = await this.#getFileInformations(file_id);

			if (!existing_file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			const existing_workstation = await this.workstation_repository.findByPk(workstation_id);

			if (!existing_workstation) {
				throw new AppError(WORKSTATION.NOT_FOUND);
			}

			const existing_system_of_units = await this.system_of_units_repository.findByPk(system_of_units_id);

			if (!existing_system_of_units) {
				throw new AppError(SYSTEM_OF_UNITS.NOT_FOUND);
			}

			const reports_input_to_update = [];
			if (existing_liberty_mutual.report_inputs.length > 0) {
				existing_liberty_mutual.report_inputs
					.map((report) => report.toJSON())
					.forEach((report) => {
						const {
							percentile_man,
							percentile_woman,
							percentile_man_initial,
							percentile_man_sustain,
							percentile_woman_initial,
							percentile_woman_sustain
						} = new LibertyMutualManualMaterialsHandler({
							task_name: report.task.name,
							form_inputs: {
								...report
							},
							current_unit_system: existing_system_of_units.name,
							desired_unit_system: 'metric'
						}).calculateResult();
						reports_input_to_update.push({
							...report,
							percentile_man,
							percentile_woman,
							percentile_man_initial,
							percentile_man_sustain,
							percentile_woman_initial,
							percentile_woman_sustain
						});
					});
			}

			existing_file.sector_id = sector_id;
			existing_file.workstation_id = workstation_id;
			existing_liberty_mutual.report_user_id = user_id;
			existing_liberty_mutual.system_of_units_id = system_of_units_id;

			transaction = await this.repository.db.sequelize.transaction();
			await this.liberty_mutual_report_input_repository.bulkCreate(reports_input_to_update, {
				transaction,
				updateOnDuplicate: [
					'percentile_man',
					'percentile_woman',
					'percentile_man_initial',
					'percentile_man_sustain',
					'percentile_woman_initial',
					'percentile_woman_sustain'
				]
			});
			await existing_liberty_mutual.save({ transaction });
			await existing_file.save({ transaction });
			await transaction.commit();

			const updated_liberty_mutual = await this.repository.findByPk(report_id, {
				include: [
					{
						association: 'system_of_units',
						attributes: ['id', 'name', 'description']
					},
					this.#INCLUDE_REPORT_INPUTS
				]
			});

			const file_infomations = {
				original_name: existing_file.original_name,
				line_id: existing_file.workstations?.line.id,
				workstation_id: existing_file.workstations?.id,
				line_name: existing_file.workstations?.line.name,
				workstation_name: existing_file.workstations?.name,
				sector_id: existing_file.workstations?.line.sector.id,
				sector_name: existing_file.workstations?.line.sector.name,
				uploaded_date: existing_file.createdAt,
				company_id: existing_file.workstations?.line.sector.company.id,
				company_name: existing_file.workstations?.line.sector.company.name,
				organization_id: existing_file.workstations?.line.sector.company.Organization.id,
				duration: this.formatTime.formatSecondsToUnabbreviatedTime(existing_file.duration),
				organization_name: existing_file.workstations?.line.sector.company.Organization.name
			};
			const liberty_mutual = updated_liberty_mutual?.get({ plain: true });
			const mapped_liberty_mutual_reports = liberty_mutual.report_inputs.map((report) => {
				const { id, report_name } = this.reportMapper.mapReportData(report, existing_file);
				return {
					report: {
						id,
						report_name
					},
					sub_step: this.reportMapper.setFormSubStep(report)
				};
			});
			logger.info('[LibertyMutualReport] service - updateReportBasicInformation finish');
			return {
				...file_infomations,
				id: existing_liberty_mutual.id,
				system_of_units_id: liberty_mutual.system_of_units?.id,
				system_of_units: liberty_mutual.system_of_units,
				reports: mapped_liberty_mutual_reports,
				step: STEP_NAME_TO_NUMBER_MAPPER.TASK_LIST
			};
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async updateComment({ file_id, comment, user_id }) {
		logger.info('[LibertyMutualReport] service - updateComment init');
		let transaction;
		try {
			const existing_liberty_mutual = await this.repository.findOne({
				where: {
					file_id
				}
			});

			if (!existing_liberty_mutual) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			existing_liberty_mutual.comment = comment;
			existing_liberty_mutual.report_user_id = user_id;

			transaction = await this.repository.db.sequelize.transaction();
			await existing_liberty_mutual.save({ transaction });
			await transaction.commit();
			const liberty_mutual = existing_liberty_mutual?.get({ plain: true });
			logger.info('[LibertyMutualReport] service - updateComment finish');
			return {
				...liberty_mutual,
				comment,
				report_user_id: user_id
			};
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async downloadPDF({ organization_id, company_id, file_id, user_id, locale }) {
		logger.info('[LibertyMutualReport] service - downloadPDF init');

		const { name: technical_manager } = await this.user_repository.findByPk(user_id, { attributes: ['name'] });

		const existing_liberty_mutual = await this.repository.findOne({
			where: {
				file_id
			},
			include: [
				{
					association: 'system_of_units',
					attributes: ['id', 'name', 'description']
				},
				this.#INCLUDE_REPORT_INPUTS
			]
		});

		if (!existing_liberty_mutual) {
			throw new AppError(REPORT.NOT_FOUND);
		}

		const existing_file = await this.#getFileInformations(file_id);

		if (!existing_file) {
			throw new AppError(FILE.NOT_FOUND);
		}

		const [locale_language] = locale.split('-');

		const liberty_mutual = existing_liberty_mutual.get({ plain: true });

		const mapped_liberty_mutual_reports = liberty_mutual.report_inputs.map((report) => ({
			report: this.reportMapper.mapReportData(report, existing_file)
		}));

		const file_infomations = {
			original_name: existing_file.original_name,
			line_name: existing_file.workstations?.line.name,
			workstation_name: existing_file.workstations?.name,
			sector_name: existing_file.workstations?.line.sector.name,
			company_name: existing_file.workstations?.line.sector.company.name,
			uploaded_date: moment(existing_file.createdAt).locale(locale_language).format('L'),
			duration: this.formatTime.formatSecondsToUnabbreviatedTime(existing_file.duration),
			organization_name: existing_file.workstations?.line.sector.company.Organization.name,
			collection_date: moment(liberty_mutual?.created_at).locale(locale_language).format('L'),
			assessment_date: moment(liberty_mutual?.updated_at).locale(locale_language).format('L')
		};

		const mapped_for_pdf = mapped_liberty_mutual_reports.map((mapped_liberty_mutual) => {
			const { results, summary } = this.resultsMapper.mapReportForResult(mapped_liberty_mutual.report);
			return { report_name: mapped_liberty_mutual.report.report_name, results, summary };
		});

		const prefix = Util.getPrefix(organization_id, company_id);
		const remove_extension = this.#removeExtension(existing_file.generated_name);
		const file_key = this.#formatFileKey(remove_extension, prefix);

		const language = locale.replace('-', '_');
		const target_file = `liberty-mutual-${remove_extension}.pdf`;

		const config = {
			functionName: 'pdf_container',
			data: {
				bucket,
				prefix,
				file_key,
				language,
				technical_manager,
				reports: mapped_for_pdf,
				report_type: 'john-deere',
				informations: file_infomations,
				id: existing_liberty_mutual.id,
				comment: liberty_mutual.comment,
				title: 'Liberty Mutual',
				type: '/john-deere/liberty-mutual'
			}
		};

		const key = await this.#generatedPDF(config);
		const url = await this.#generateDownloadURL({ fileName: target_file, Bucket: bucket, Key: key });

		logger.info('[LibertyMutualReport] service - downloadPDF finish');
		return url;
	}

	#removeExtension(fileName) {
		return fileName.substring(0, fileName.lastIndexOf('.')) || fileName;
	}

	#formatFileKey(file_name, prefix) {
		const [, file_legacy] = file_name.split('/');

		// for file legacy;
		if (!file_name.includes('/')) {
			return `${prefix}/liberty-mutual/${file_name}.pdf`;
		}

		return `${prefix}/liberty-mutual/${file_legacy}.pdf`;
	}

	async #generatedPDF(data) {
		const { key } = await this.lambda.call(data);

		if (!key) {
			throw new AppError(REPORT.FAIL_CREATE_PDF);
		}

		return key;
	}

	async #generateDownloadURL({ fileName, Bucket, Key }) {
		const url = await this.storage.createSignatureDownload({ fileName, Bucket, Key });

		if (!url) {
			throw new AppError(FILE.FAILED_CREATE_DOWNLOAD_URL);
		}

		return url;
	}

	async #getFileInformations(file_id) {
		const include_workstation = this.#setFileInnerJoin();

		return await this.file_repository.findByPk(file_id, {
			where: {
				is_active: true,
				status: ENUM_STATUS_FILE.PROCESSED
			},
			attributes: ['id', 'original_name', 'generated_name', 'duration', 'createdAt'],
			include: [include_workstation]
		});
	}

	#setFileInnerJoin() {
		const include_organization = {
			association: 'Organization',
			attributes: ['id', 'name']
		};

		const include_company = {
			association: 'company',
			attributes: ['id', 'name'],
			include: [include_organization]
		};

		const include_sector = {
			association: 'sector',
			attributes: ['id', 'name'],
			include: [include_company]
		};

		const include_line = {
			association: 'line',
			attributes: ['id', 'name'],
			include: [include_sector]
		};

		const include_workstation = {
			association: 'workstations',
			attributes: ['id', 'name'],
			include: [include_line]
		};

		return include_workstation;
	}

	async getPercentileByGender(params) {
		logger.info('[LibertyMutualReport] service - getPercentileByGender init', { params });
		const { organization_id, company_id, sector_id, line_id, workstation_id, companies_ids, start_date, end_date } =
			params;

		let user_id = '';

		if (StorageContext.getStore()?.environment === 'sandbox') {
			user_id = StorageContext.getStore().user_id;
		}

		const [result, error] = await this.repository.getPercentileByGender({
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_ids,
			start_date,
			end_date,
			user_id
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[LibertyMutualReport] service - getPercentileByGender finish');
		return result;
	}
}
