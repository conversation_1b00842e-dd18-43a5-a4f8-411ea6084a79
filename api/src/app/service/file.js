import _ from 'lodash';
import moment from 'moment';
import config from 'config';
import { Sequelize } from 'sequelize';
import { AppError } from '../util/errors.js';
import { logger } from '../helpers/logger.js';
import { Storage } from '../modules/storage.js';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../utils/errors.js';
import { Storage as StorageS3 } from '../util/storage.js';
// import FileRepository from '../repository/fileRepository.js';
import { RESPONSE_ERROR_STATUS } from '../helpers/constants.js';
import { ERROR_RESPONSE_ENTITIES_ENUM, ERROR_RESPONSE_EXTERNAL_SERVICE } from '../util/enum.js';

const { Op } = Sequelize;
const { STORAGE } = ERROR_RESPONSE_EXTERNAL_SERVICE;
const { DATABASE_FAILED_PERFORM_QUERY } = RESPONSE_ERROR_STATUS;
const { FILE, USER, CUSTOMER_INFORMATION } = ERROR_RESPONSE_ENTITIES_ENUM;

class FileServiceOld {
	constructor(repository) {
		this.fileRepository = repository;
		this.storage = new Storage();
	}

	async durationVideoTotal(parameters) {
		const { organization_id, company_id } = parameters;

		let response = {};

		const result = await this.fileRepository.durationVideoTotal({
			organization_id,
			company_id
		});

		if (result === undefined) {
			throw new ErrorHandler(500, 'Internal error');
		}

		const { total } = result;

		if (total === null || total === 0) {
			response.total = 0;
			return response;
		}

		response.total = parseInt(total);

		return response;
	}

	async videoCounterTotal(parameters) {
		const { organization_id, company_id } = parameters;

		const result = await this.fileRepository.counterVideoProcessed({
			organization_id,
			company_id
		});

		if (result === (null || undefined)) {
			throw new ErrorHandler(500, 'Internal server error');
		}

		return { total: result };
	}

	async index(params, query) {
		const { organization_id, company_id } = params;

		const { original_name, workstation, start_date, end_date, sector } = query;

		const filter = { organization_id, company_id, isActive: true };

		if (original_name) {
			_.set(filter, 'original_name', original_name);
		}

		if (sector) {
			_.set(filter, 'sector', sector);
		}

		if (workstation) {
			const like = {
				[this.fileRepository.sequelize.Op.like]: `%${workstation}%`
			};
			_.set(filter, 'workstation', like);
		}

		if (start_date && end_date) {
			const createdAt = {
				[this.fileRepository.sequelize.Op.between]: [start_date, end_date]
			};
			_.set(filter, 'createdAt', createdAt);
		}

		const result = await this.fileRepository.findAllFilter(filter);

		return result;
	}

	async percentRisk(parameters) {
		const { organization_id, company_id } = parameters;

		const result = await this.fileRepository.percentRisk({
			organization_id,
			company_id
		});

		if (!result) {
			throw new ErrorHandler(500, 'Internal error');
		}

		if (result.length === 0) {
			return [];
		}

		let safe = 0,
			info = 0,
			warning = 0,
			danger = 0;

		result.map(({ rula_score, sector_id }) => {
			if (rula_score > 0 && sector_id) {
				if (rula_score >= 1 && rula_score <= 2) {
					return safe++;
				}

				if (rula_score > 2 && rula_score <= 4) {
					return info++;
				}

				if (rula_score > 4 && rula_score <= 6) {
					return warning++;
				}

				if (rula_score > 6 && rula_score >= 7) {
					return danger++;
				}
			}
		});

		const response = [
			['Video', 'Risk'],
			['Seguro', safe],
			['Aceitável', info],
			['Alerta', warning],
			['Crítico', danger]
		];

		return response;
	}

	async videoBySector(parameters) {
		const { organization_id, company_id } = parameters;

		const result = await this.fileRepository.videoBySector({
			organization_id,
			company_id
		});

		let response = [['Setores', 'Seguro', 'Aceitável', 'Alerta', 'Crítico']];

		if (result.length === 0) {
			return result;
		}

		// Remove keys duplicated
		const uniqueKey = [...new Map(result.map((item) => [item['name'], item])).keys()];

		let safe = 0,
			info = 0,
			warning = 0,
			danger = 0;

		// Iterator response
		uniqueKey.map((item) => response.push([item, safe, info, warning, danger]));

		result.map(({ rula_score, name }) => {
			response.map((item2) => {
				if (name === item2[0]) {
					if (rula_score >= 1 && rula_score <= 2) {
						return item2[1]++;
					}

					if (rula_score > 2 && rula_score <= 4) {
						return item2[2]++;
					}

					if (rula_score > 4 && rula_score <= 6) {
						return item2[3]++;
					}

					if (rula_score > 6 && rula_score >= 7) {
						return item2[4]++;
					}
				}
			});
		});

		return response;
	}

	async riskByTime(parameters) {
		const { organization_id, company_id } = parameters;

		const result = await this.fileRepository.riskByTime({
			organization_id,
			company_id
		});

		if (result.length === 0) {
			return result;
		}

		function getDaysInMonth(month, year) {
			const date = new Date(year, month, 1);
			const days = [];
			while (date.getMonth() === month) {
				days.push(new Date(date));
				date.setDate(date.getDate() + 1);
			}
			return days;
		}

		const dateNow = new Date();
		const month = dateNow.getUTCMonth();
		const year = dateNow.getUTCFullYear();

		const daysOfMonth = getDaysInMonth(month, year);

		let response = [['Mês', 'Seguro', 'Aceitável', 'Alerta', 'Crítico']];

		let safe = 0,
			info = 0,
			warning = 0,
			danger = 0;

		daysOfMonth.map((item) => {
			const parseData = new Date(item);
			const month = parseData.getUTCMonth() + 1;
			const day = parseData.getUTCDate();
			const date = `${day}/${month}`;
			response.push([date, safe, info, warning, danger]);
		});

		response.map((item) => {
			result.map(({ created_at, rula_score }) => {
				const createdAt = new Date(created_at);
				const day = createdAt.getUTCDate();
				const month = createdAt.getUTCMonth() + 1;

				const dateStr = `${day}/${month}`;

				if (item[0] === dateStr) {
					if (rula_score >= 1 && rula_score <= 2) {
						return item[1]++;
					}

					if (rula_score > 2 && rula_score <= 4) {
						return item[2]++;
					}

					if (rula_score > 4 && rula_score <= 6) {
						return item[3]++;
					}

					if (rula_score > 6 && rula_score >= 7) {
						return item[4]++;
					}
				}
			});
		});

		return response;
	}
}

export default FileServiceOld;

export class FileService {
	constructor({ fileRepository, userRepository }) {
		this.repository = fileRepository;
		this.userRepository = userRepository;
	}

	removeExtension(filename) {
		return filename.substring(0, filename.lastIndexOf('.')) || filename;
	}

	async deleteFile(parameters) {
		logger.info('[FileService] service - deleteFile init');
		const { file_id, organization_id, company_id } = parameters;
		let transaction;
		try {
			let query = {
				where: {
					id: file_id,
					organization_id: organization_id,
					company_id: company_id,
					isActive: true
				}
			};

			const file = await this.repository.db.File.findOne(query);

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			file.isActive = false;
			transaction = await this.repository.db.sequelize.transaction();
			await file.save({ transaction });
			await transaction.commit();

			logger.info('[FileService] service - deleteFile finish');

			return { file_id: file.id };
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async downloadAngles(params) {
		logger.info('[FileService] downloadAngles - init', {
			tags: 'file',
			additionalInfo: { parameters: params }
		});
		const { file_id, organization_id, company_id, parts } = params;

		try {
			const file = await this.repository.db.File.findOne({
				where: {
					id: file_id,
					organization_id: organization_id,
					company_id: company_id,
					isActive: true
				}
			});

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			const storage = new StorageS3();

			let keyWithoutExtension = this.removeExtension(file.generated_name);

			if (!keyWithoutExtension.includes('/')) {
				let first = organization_id.split('-', 5)[0];
				let second = company_id.split('-', 5)[0];
				keyWithoutExtension = `${first}-${second}/${keyWithoutExtension}`;
			}

			let bucket = config.get('App.bucket');

			const paramsStorage = {
				Bucket: bucket,
				Key: `${keyWithoutExtension}.json`
			};

			const [stream, err] = await storage.getObject(paramsStorage);

			if (err) {
				logger.error('[FileService] downloadAngles - storage.getObject', {
					tags: 'file',
					error: err
				});
				throw new AppError(STORAGE.FAIL_DOWNLOAD_DATA);
			}

			const data = JSON.parse(stream.Body.toString('utf-8'));

			const selectedParts = {};

			for (const part in data) {
				if (parts.includes(part)) {
					_.set(selectedParts, part, data[part]);
				}
			}

			logger.info('[FileService] downloadAngles - finish', {
				tags: 'file'
			});
			return selectedParts;
		} catch (error) {
			throw error;
		}
	}

	async countOfMinutesProcessed(parameters) {
		const { organization_id, company_id, user_id } = parameters;

		try {
			const user = await this.repository.db.User.findOne({
				where: {
					id: user_id,
					isActive: true
				},
				include: [{ association: 'customer_info' }]
			});

			if (!user) {
				throw new AppError(USER.NOT_FOUND);
			}

			if (!user.customer_info) {
				throw new AppError(CUSTOMER_INFORMATION.NOT_FOUND);
			}

			if (!user.customer_info.expiration_plan) {
				throw new AppError(CUSTOMER_INFORMATION.EXPIRATION_PLAN_NOT_FOUND);
			}

			const users = await this.userRepository.getUsersByCustomerId(user.id);
			const userListId = users?.map((item) => item.id);

			// const expirationDate = moment(user.customer_info.expiration_plan).format('YYYY-MM-DD')
			// const expirationDate = moment(user.customer_info.expiration_plan)
			// const currentDate = moment().format('YYYY-MM-DD')

			const FORMAT_DATE = 'YYYY-MM-DD';

			const startOfMonth = moment().startOf('month').format(FORMAT_DATE);
			const endOfMonth = moment().endOf('month').format(FORMAT_DATE);

			const parameters = {
				organization_id: organization_id,
				company_id: company_id,
				users: userListId,
				start: startOfMonth,
				end: endOfMonth
			};

			const result = await this.repository.minutesProcessed(parameters);

			const seconds = result.total ?? 0;
			const minutes = (seconds / 60) | 0;

			return { total: minutes };
		} catch (error) {
			throw error;
		}
	}

	async countFile(parameters) {
		const { organization_id, company_id } = parameters;
		const count = await this.repository.db.File.count({
			where: {
				organization_id: organization_id,
				company_id: company_id,
				status: 'PROCESSED',
				isActive: true
			}
		});
		return { total: count };
	}

	async index(parameters) {
		const {
			user: { role, id },
			params,
			query
		} = parameters;

		const { original_name, workstation_id, start_date, end_date, sector } = query;

		try {
			const options = {
				where: { ...params, isActive: true },
				include: [
					{ association: 'sector', attributes: ['name'] },
					{
						association: 'custom_report',
						required: false
					},
					{
						association: 'workstations',
						attributes: ['name'],
						include: [
							{
								association: 'line',
								attributes: ['name']
							}
						]
					},
					{
						association: 'preliminary_analysis',
						attributes: ['worst_score', 'consolidated'],
						required: false,
						where: {
							is_active: true
						}
					}
				],
				order: [
					['createdAt', 'DESC'],
					['original_name', 'ASC']
				]
			};

			if (!['ADMIN', 'MASTER', 'SUPERVISOR'].includes(role)) {
				_.set(options, 'where.user_id', id);
			}

			if (original_name) {
				const name_like = {
					[Op.like]: `%${original_name}%`
				};
				_.set(options, 'where.original_name', name_like);
			}

			if (workstation_id) {
				_.set(options, 'where.workstation_id', workstation_id);
			}

			if (start_date && end_date) {
				const createdAt = {
					[Op.between]: [start_date, end_date]
				};
				_.set(options, 'where.createdAt', createdAt);
			}

			if (sector) {
				_.set(options.include[0], 'where', { id: sector });
			}

			const files = await this.repository.db.File.findAll(options);

			const filtered_files = files.filter((file) => file.custom_report?.length === 0);

			return filtered_files;
		} catch (error) {
			throw error;
		}
	}

	async getCustomReportsFiles(parameters) {
		logger.info('[FileService] getCustomReportsFiles - init');
		const { limit, offset, report_type_id } = parameters;
		const files_with_applied_filter = await this.repository.findAllWithHierarchy(parameters);
		const files_with_applied_filter_ids = files_with_applied_filter.map((file) => file.id);
		if (files_with_applied_filter_ids.length === 0) {
			return {
				count: 0,
				rows: []
			};
		}
		const options = this.#setFilterForFindAndCountAll({
			files_with_applied_filter_ids,
			report_type_id,
			limit,
			offset
		});
		const files = await this.repository.findAndCountAll(options);
		const filtered_files = this.#filterAndMapRows(files);
		logger.info('[FileService] getCustomReportsFiles - finish');
		return {
			count: files.count,
			rows: filtered_files
		};
	}

	#filterAndMapRows(files) {
		return files.rows
			.filter((file) => file.custom_report?.length > 0)
			.map((file) => file.toJSON())
			.map((file) => {
				return {
					id: file.id,
					size: file.size,
					status: file.status,
					createdAt: file.createdAt,
					workstations: file.workstations,
					original_name: file.original_name,
					custom_report_name: file.custom_report[0].acronym
				};
			});
	}

	#setFilterForFindAndCountAll({ files_with_applied_filter_ids, report_type_id, limit, offset }) {
		const options = {
			where: {
				id: [files_with_applied_filter_ids],
				isActive: true
			},
			include: [
				{
					association: 'custom_report',
					required: true
				},
				{
					association: 'workstations',
					attributes: ['id', 'name']
				}
			],
			limit: Number(limit),
			offset: Number(limit) * Number(offset),
			order: [['createdAt', 'DESC']]
		};

		if (report_type_id) {
			_.set(options.include[0], 'where', { id: report_type_id });
		}

		return options;
	}

	async getReportsAndTools(params) {
		logger.info('[File] service getReportsAndTools - init', { params });
		const { file_id, user_id } = params;
		try {
			const [data, error] = await this.repository.getReportsAndTools({ file_id, user_id });

			if (error) {
				logger.error('[File] service getReportsAndTools - error', { error });
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			const custom_report_results = [...new Set(data.custom_report_results.map((item) => item.report_type))];

			const result = {
				...data,
				custom_report_results: custom_report_results
			};

			logger.info('[FileService] getReportsAndTools - success');
			return result;
		} catch (error) {
			logger.error('[FileService] getReportsAndTools - error', { error });
			throw error;
		} finally {
			logger.info('[File] service getReportsAndTools - success');
		}
	}
}
