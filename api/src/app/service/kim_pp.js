import moment from 'moment';
import { getWorkspace } from '../util/index.js';
import { Puppeteer } from '../util/puppeteer/index.js';
import {
	i18n,
	Socket,
	logger,
	AppError,
	SOCKET_EVENTS,
	RESPONSE_ERROR_STATUS,
	RESPONSE_ERROR_ENTITIES
} from '../helpers/index.js';
import { StorageContext } from '../utils/storage_context.js';

const { KIM_PP_REPORT } = SOCKET_EVENTS;
const { REPORT, FILE } = RESPONSE_ERROR_ENTITIES;
const { DATABASE_FAILED_PERFORM_QUERY } = RESPONSE_ERROR_STATUS;

export class KimPushPullReportService {
	constructor(repository) {
		this.repository = repository;
	}

	async getReport({ file_id }) {
		logger.info('[KimPushPullReport] service - getReport init');

		try {
			let kim_pp = await this.repository.db.KimPushPullReport.findOne({
				where: {
					file_id,
					is_active: 1
				}
			});

			if (!kim_pp) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			const report = this.getLabels(kim_pp.dataValues);

			logger.info('[KimPushPullReport] service - getReport finish');
			return report;
		} catch (error) {
			throw error;
		}
	}

	async createReport({ kim_pp_input, user_id }) {
		logger.info('[KimPushPullReport] service - createReport init');
		const { file_id } = kim_pp_input;

		const kim_pp_results = this.calculateResults({ kim_pp_input });

		const kim_pp_payload = {
			...kim_pp_input,
			...kim_pp_results,
			report_user_id: user_id
		};

		let transaction;
		try {
			let file = await this.repository.db.File.findOne({
				where: { id: file_id, is_active: 1 }
			});

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			const workstation = await this.repository.db.Workstation.findOne({
				where: {
					id: kim_pp_input.workstation_id
				},
				attributes: ['name']
			});

			kim_pp_payload.workstation = workstation?.name;

			transaction = await this.repository.db.sequelize.transaction();

			let created_kim_pp = await this.repository.db.KimPushPullReport.create(kim_pp_payload, {
				transaction
			});

			if (!created_kim_pp) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			file.sector_id = kim_pp_input.sector_id;
			file.workstation = workstation?.name;
			file.workstation_id = kim_pp_input.workstation_id;

			const kim_pp_results = this.getLabels(created_kim_pp.dataValues);

			file.save({ transaction });

			const io = Socket.getInstance().getIO();

			const roomId = getWorkspace({
				organization_id: file.organization_id,
				company_id: file.company_id
			});

			io.of('/report-status')
				.to(roomId)
				.emit(KIM_PP_REPORT, {
					status: true,
					data: {
						id: created_kim_pp?.id,
						mass: created_kim_pp?.mass,
						score: created_kim_pp?.score,
						file_id: created_kim_pp?.file_id,
						updated_at: created_kim_pp?.updated_at
					}
				});

			await transaction.commit();
			logger.info('[KimPushPullReport] service - createReport finish');
			return kim_pp_results;
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}

	async updateReport({ kim_pp_input, user_id }) {
		logger.info('[KimPushPullReport] service - updateReport init');
		const { report_id, file_id } = kim_pp_input;

		const kim_pp_results = this.calculateResults({ kim_pp_input });

		const kim_pp_payload = {
			...kim_pp_input,
			...kim_pp_results,
			report_user_id: user_id
		};

		let transaction;
		try {
			let kim_pp = await this.repository.db.KimPushPullReport.findOne({
				where: {
					id: report_id,
					is_active: 1
				}
			});

			if (!kim_pp) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			let file = await this.repository.db.File.findOne({
				where: { id: file_id, is_active: 1 }
			});

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			file.sector_id = kim_pp_input.sector_id;

			const workstation = await this.repository.db.Workstation.findOne({
				where: {
					id: kim_pp_input.workstation_id
				},
				attributes: ['name']
			});

			file.workstation = workstation?.name;
			kim_pp_payload.workstation = workstation?.name;
			file.workstation_id = kim_pp_input.workstation_id;

			console.log(file);

			await file.save({ transaction });
			await kim_pp.update(kim_pp_payload, { transaction });

			await transaction.commit();

			const kim_pp_formatted = this.getLabels(kim_pp.dataValues);

			logger.info('[KimPushPullReport] service - updateReport finish');
			return kim_pp_formatted;
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}

	async updateComment({ kim_pp_input, user_id }) {
		logger.info('[KimPushPullReport] service - updateComment init');
		const { report_id } = kim_pp_input;

		let transaction;
		try {
			let kim_pp = await this.repository.db.KimPushPullReport.findOne({
				where: {
					id: report_id,
					is_active: 1
				}
			});

			if (!kim_pp) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			kim_pp.comment = kim_pp_input.comment;
			kim_pp.report_user_id = user_id;

			await kim_pp.save({ transaction });
			await transaction.commit();

			const kim_pp_results = this.getLabels(kim_pp.dataValues);

			logger.info('[KimPushPullReport] service - updateComment finish');
			return kim_pp_results;
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}

	calculateResults({ kim_pp_input }) {
		// Colinha para score de inputs para depois colocar no Confluence (vou colocar na ordem que aparecem os inputs para o usuário):
		// gender -> se for FEMALE multiplica o resultado final por 1.3
		// pair -> se SIM multiplica o resultado final por 0.7
		// distance_or_duration -> 0 a 12 é DISTANCE e 13 a 25 DURATION:
		// [ 1 , 1.5 , 2 , 2.5 , 3 , 3.5 , 4 , 5 , 6 , 7 , 8 , 9 , 10 , 1 , 1.5 , 2 , 2.5 , 3 , 3.5 , 4 , 5 , 6 , 7 , 8 , 9 , 10 ]
		// vehicle -> de 0 a 9 veículos, caso seja selecionado 0 a 7, conta driveway conditions também, se não, não conta. Pontuação aqui depende tbm da massa.
		// driveway_conditions -> 0 a 4, pontuação depende do veículo: p/ veículo 0 [ 0 , 0 , 0 , 1 , 3 ], p/ veículo 1 e 2 [ 0 , 0 , 1 , 2 , 5 ], p/ veículo 3 a 7 [ 0 , 1 , 2 , 3 , 6 ]
		// inclination_or_stairs -> AINDA ESTOU EM DÚVIDA SE ESSES PONTOS CONTAM PARA TODOS OS VEÍCULOS OU SÓ PRA 0 A 7 TAMBÉM: 3 categorias de inclinação [ 5 , 10 , 25 , 0 ]
		// mass -> 0 a 9, obtemoms a pontuação combinando com o valor de veículo:
		//
		// Array de valores possíveis, primeiro número é o VEÍCULO e segundo número a MASSA:
		//
		// {[0,0],[1,0],[2,0],[3,0],[4,0],[5,0],[6,0],[7,0],[8,0],[9,0],
		// [0,1],[1,1],[2,1],[3,1],[4,1],[5,1],[6,1],[7,1],[8,1],[9,1],
		// [0,2],[1,2],[2,2],[3,2],[4,2],[5,2],[6,2],[7,2],[8,2],[9,2],
		// [0,3],[1,3],[2,3],[3,3],[4,3],[5,3],[6,3],[7,3],[8,3],[9,3],
		// [0,4],[1,4],[2,4],[3,4],[4,4],[5,4],[6,4],[7,4],[8,4],[9,4],
		// [0,5],[1,5],[2,5],[3,5],[4,5],[5,5],[6,5],[7,5],[8,5],[9,5],
		// [0,6],[1,6],[2,6],[3,6],[4,6],[5,6],[6,6],[7,6],[8,6],[9,6],
		// [0,7],[1,7],[2,7],[3,7],[4,7],[5,7],[6,7],[7,7],[8,7],[9,7],
		// [0,8],[1,8],[2,8],[3,8],[4,8],[5,8],[6,8],[7,8],[8,8],[9,8],
		// [0,9],[1,9],[2,9],[3,9],[4,9],[5,9],[6,9],[7,9],[8,9],[9,9]}
		//
		// Array de pontos:
		//
		// {[3],[2],[2.5],[2.5],[3],[1],[1],[1],[1],[2],
		// [5],[3],[4],[3],[4],[1],[1],[1],[1],[2.5],
		// [10],[6],[7],[4],[6],[2],[1.5],[1.5],[1.5],[3.5],
		// [50],[12],[50],[5],[8],[3],[2],[2],[2],[4.5],
		// [100],[50],[100],[7],[12],[4],[3],[2.5],[2.5],[6],
		// [100],[100],[100],[12],[50],[6],[5],[4],[4],[10],
		// [100],[100],[100],[50],[100],[10],[8],[7],[7],[15],
		// [100],[100],[100],[100],[100],[15],[12],[10],[10],[50],
		// [100],[100],[100],[100],[100],[50],[50],[50],[20],[100],
		// [100],[100],[100],[100],[100],[100],[100],[100],[50],[100]}
		//
		// working_conditions -> [ 3 , 3 , 1 , 3 , 1 , 2 , 0 ]
		// properties -> [ 2 , 3 , 2 , 2 , 0]
		// posture -> [ 3 , 5 , 8 ]
		// temporal_distribution -> [ 0 , 2 , 4 ]
		// score = ((vehicle & mass) + driveway_conditions(+inclination*) + (S)working_conditions +
		//  (S)properties + posture + temporal_distribution) X distance_or_duration X FEMALE X PAIR

		const distance_or_duration_array = [
			1, 1.5, 2, 2.5, 3, 3.5, 4, 5, 6, 7, 8, 9, 10, 1, 1.5, 2, 2.5, 3, 3.5, 4, 5, 6, 7, 8, 9, 10
		];

		const vehicle_mass_matrix = [
			[3, 2, 2.5, 2.5, 3, 1, 1, 1, 1, 2],
			[5, 3, 4, 3, 4, 1, 1, 1, 1, 2.5],
			[10, 6, 7, 4, 6, 2, 1.5, 1.5, 1.5, 3.5],
			[50, 12, 50, 5, 8, 3, 2, 2, 2, 4.5],
			[100, 50, 100, 7, 12, 4, 3, 2.5, 2.5, 6],
			[100, 100, 100, 12, 50, 6, 5, 4, 4, 10],
			[100, 100, 100, 50, 100, 10, 8, 7, 7, 15],
			[100, 100, 100, 100, 100, 15, 12, 10, 10, 50],
			[100, 100, 100, 100, 100, 50, 50, 50, 20, 100],
			[100, 100, 100, 100, 100, 100, 100, 100, 50, 100]
		];

		const driveway_conditions_matrix = [
			[0, 0, 0, 1, 3],
			[0, 0, 1, 2, 5],
			[0, 0, 1, 2, 5],
			[0, 1, 2, 3, 6],
			[0, 1, 2, 3, 6],
			[0, 1, 2, 3, 6],
			[0, 1, 2, 3, 6],
			[0, 1, 2, 3, 6],
			[0, 0, 0, 0, 0],
			[0, 0, 0, 0, 0]
		];

		const inclination_or_stairs_array = [5, 10, 25, 0];

		const working_conditions_array = [3, 3, 1, 3, 1, 2, 0];

		const properties_array = [2, 3, 2, 2, 0];

		const posture_array = [3, 5, 8];

		const temporal_distribution_array = [0, 2, 4];

		const distance_or_duration_value = distance_or_duration_array[kim_pp_input.distance_or_duration];

		const vehicle_mass_value = vehicle_mass_matrix[kim_pp_input.mass][kim_pp_input.vehicle];

		const driveway_conditions_value = kim_pp_input.driveway_conditions
			? driveway_conditions_matrix[kim_pp_input.vehicle][kim_pp_input.driveway_conditions]
			: 0;

		const inclination_or_stairs_value = inclination_or_stairs_array[kim_pp_input.inclination_or_stairs];

		let working_conditions_value = 0;
		let properties_value = 0;
		let i;

		// loop para somatória das opções de working_conditions:
		for (i = 0; i < kim_pp_input.working_conditions.length; i++) {
			working_conditions_value += working_conditions_array[kim_pp_input.working_conditions[i]];
		}

		// loop para somatórias das opções de properties:
		for (i = 0; i < kim_pp_input.properties.length; i++) {
			properties_value += properties_array[kim_pp_input.properties[i]];
		}

		if (working_conditions_value > 4) working_conditions_value = 4;

		if (properties_value > 4) properties_value = 4;

		const posture_value = posture_array[kim_pp_input.posture];

		const temporal_distribution_value = temporal_distribution_array[kim_pp_input.temporal_distribution];

		const score =
			(kim_pp_input.gender == 1 ? 1.3 : 1) *
			(kim_pp_input.pair == 1 ? 0.7 : 1) *
			distance_or_duration_value *
			(vehicle_mass_value +
				driveway_conditions_value +
				inclination_or_stairs_value +
				working_conditions_value +
				properties_value +
				posture_value +
				temporal_distribution_value);

		const results = {
			...kim_pp_input,
			score
		};

		return results;
	}

	async generatePDF({ file_id, locale }) {
		let kim_pp_data;
		const language = locale.substring(0, 2);
		i18n.changeLanguage(language);

		try {
			const kim_pp = await this.repository.db.KimPushPullReport.findOne({
				where: { file_id, is_active: 1 },
				include: [
					{
						model: this.repository.db.User,
						as: 'report_user',
						attributes: ['name']
					},
					{
						model: this.repository.db.File,
						as: 'file',
						include: [
							{
								model: this.repository.db.Sector,
								as: 'sector',
								attributes: ['name'],
								include: [
									{
										model: this.repository.db.Company,
										as: 'company',
										attributes: ['name'],
										include: [
											{
												model: this.repository.db.Organization,
												as: 'Organization',
												attributes: ['name']
											}
										]
									}
								]
							},
							{
								model: this.repository.db.Workstation,
								as: 'workstations',
								attributes: ['name'],
								include: [
									{
										model: this.repository.db.Line,
										as: 'line',
										attributes: ['name']
									}
								]
							}
						]
					}
				]
			}).then((data) => (kim_pp_data = data.get({ plain: true })));

			if (!kim_pp) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			let formattedReport = this.formatData(kim_pp_data, locale);
			formattedReport = this.getLabels(formattedReport);

			const payload = {
				...formattedReport,
				title: ['KIM PP Report Results'],
				language
			};

			const puppeteer = new Puppeteer();
			const html = await puppeteer.render('kim_pp', payload);

			const browser = await puppeteer.init();
			const buffer = puppeteer.mounting(browser, html);

			return buffer;
		} catch (error) {
			throw error;
		}
	}

	formatData(data, locale) {
		const formattedReport = {
			...data,
			score: data.score.toFixed(0),
			file: {
				...data.file,
				sector: {
					...data.file.sector,
					company: {
						...data.file.sector.company,
						organization: {
							...data.file.sector.company.Organization
						}
					}
				},
				workstation: {
					...data.file.workstations,
					line: {
						...data.file.workstations.line
					}
				}
			}
		};

		if (locale) {
			const previousMomentLocale = moment.locale();
			moment.locale(locale);

			formattedReport.file = {
				...formattedReport.file,
				createdAt: moment(formattedReport.file.createdAt).format('L')
			};

			formattedReport.collection_date = moment(formattedReport.collection_date).format('L');

			moment.locale(previousMomentLocale);
		}

		return formattedReport;
	}

	getLabels(kim_pp) {
		const risk =
			kim_pp.score < 20 ? 'LOW' : kim_pp.score < 50 ? 'MODERATE' : kim_pp.score < 100 ? 'HIGH' : 'VERY_HIGH';

		const labels = {
			RISK: {
				LOW: {
					value: 'LOW',
					title: 'Low',
					type: 'success'
				},
				MODERATE: {
					value: 'MODERATE',
					title: 'Moderate',
					type: 'warning'
				},
				HIGH: {
					value: 'HIGH',
					title: 'High',
					type: 'danger'
				},
				VERY_HIGH: {
					value: 'VERY_HIGH',
					title: 'Very high',
					type: 'danger'
				}
			},
			DIAGNOSTICS: {
				LOW: {
					PHYSICAL_OVERLOAD: 'Physical overload is unlikely.',
					HEALTH_CONSEQUENCES: 'No health risk is to be expected.',
					MEASURES: 'No measures need to be taken.'
				},
				MODERATE: {
					PHYSICAL_OVERLOAD: 'Physical overload is possible for less resilient persons.',
					HEALTH_CONSEQUENCES:
						'Fatigue, low-grade adaptation problems which can be compensated for during leisure time.',
					MEASURES:
						'For less resilient persons, workplace redesign and other prevention measures may be helpful.'
				},
				HIGH: {
					PHYSICAL_OVERLOAD: 'Physical overload is possible for normally resilient persons.',
					HEALTH_CONSEQUENCES:
						'Disorders (pain), possibly including dysfunctions, reversible in most cases, without morphological manifestation.',
					MEASURES: 'Workplace redesign and other prevention measures should be considered.'
				},
				VERY_HIGH: {
					PHYSICAL_OVERLOAD: 'Physical overload is likely.',
					HEALTH_CONSEQUENCES:
						'More pronounced disorders and/or dysfunctions, structural damage with pathological significance.',
					MEASURES:
						'Workplace redesign measures are necessary. Other prevention measures should be considered.'
				}
			},
			PAIR: {
				0: '1 worker',
				1: '2 workers'
			},
			GENDER: {
				0: 'Man',
				1: 'Woman'
			},
			DISTANCE: [
				'≤ 40',
				'≤ 200',
				'≤ 400',
				'≤ 800',
				'≤ 1,200',
				'≤ 1,800',
				'≤ 2,500',
				'≤ 4,200',
				'≤ 6,300',
				'≤ 8,400',
				'≤ 11,000',
				'≤ 15,000',
				'≤ 20,000'
			],
			DURATION: [
				'≤ 1',
				'≤ 5',
				'≤ 10',
				'≤ 20',
				'≤ 30',
				'≤ 45',
				'≤ 60',
				'≤ 100',
				'≤ 150',
				'≤ 210',
				'≤ 270',
				'≤ 360',
				'≤ 480'
			],
			VEHICLES: [
				'barrow1',
				'barrow2',
				'barrow3',
				'carriage1',
				'carriage2',
				'carriage3',
				'carriage3',
				'carriage4',
				'conveyor',
				'crane'
			],
			MASS: ['≤ 50', '≤ 100', '≤ 200', '≤ 300', '≤ 400', '≤ 600', '≤ 800', '≤ 1,000', '≤ 1,300', '> 1,300'],
			DRIVEWAY_CONDITIONS: {
				0: {
					description: 'Driveway completely level, smooth, solid, dry, without inclinations',
					indicator: 'success'
				},
				1: {
					description:
						'Driveway mostly smooth and level, with small damaged spots/faults, without inclinations',
					indicator: 'success'
				},
				2: {
					description: 'Mixture of cobbles, concrete, asphalt, slight inclinations, dropped kerb',
					indicator: 'warning'
				},
				3: {
					description: 'Mixture of roughly cobbled, hard sand, slight inclinations, small edges/sills',
					indicator: 'warning'
				},
				4: {
					description:
						'Earth or roughly cobbled driveway, potholes, heavy soiling, slight inclinations, landings, sills',
					indicator: 'danger'
				}
			},
			INCLINATION_OR_STAIRS: {
				0: {
					description: 'Inclinations of 2 ≤ 4° (4 ≤ 8%)',
					indicator: 'warning'
				},
				1: {
					description: 'Inclinations of 5 ≤ 10° (9 ≤ 18%)',
					indicator: 'warning'
				},
				2: {
					description: 'Stairs, inclinations > 10° (18%)',
					indicator: 'danger'
				},
				3: {
					description: 'No significant inclination',
					indicator: 'success'
				}
			},
			WORKING_CONDITIONS: {
				0: {
					description:
						'Regularly significantly increased starting forces, because transport devices sink into the ground or get wedged',
					indicator: { success: false, warning: false, danger: true }
				},
				1: {
					description: 'Frequent stops with breaking',
					indicator: { success: false, warning: false, danger: true }
				},
				2: {
					description: 'Frequent stops without breaking',
					indicator: { success: false, warning: false, danger: true }
				},
				3: {
					description: 'Many changes of direction or curves, frequent manouvering',
					indicator: { success: false, warning: false, danger: true }
				},
				4: {
					description: 'Load must be positioned precisely and stopped, driveway must be adhered to precisely',
					indicator: { success: false, warning: true, danger: false }
				},
				5: {
					description: 'Increased movement speed (approx. 1.0 ≤ 1.3 m/s)',
					indicator: { success: false, warning: true, danger: false }
				},
				6: {
					description: 'None: there are no unfavorable working conditions',
					indicator: { success: true, warning: false, danger: false }
				}
			},
			PROPERTIES: {
				0: {
					description: 'No suitable handles or construction parts for applying force',
					indicator: { success: false, warning: true, danger: false }
				},
				1: {
					description: 'No brake when driving on inclinations > 2° (> 3%)',
					indicator: { success: false, warning: false, danger: true }
				},
				2: {
					description: 'Unadjusted castors (e.g. too small on soft or uneven floor)',
					indicator: { success: false, warning: true, danger: false }
				},
				3: {
					description: 'Defective castors (worn-out, rubbing, stiff, air pressure too low)',
					indicator: { success: false, warning: true, danger: false }
				},
				4: {
					description: 'None: there are no unfavorable properties of the transport devices',
					indicator: { success: true, warning: false, danger: false }
				}
			},
			TEMPORAL_DISTRIBUTION: {
				0: 'Good',
				1: 'Restricted',
				2: 'Unfavorable'
			},
			POSTURES: {
				0: {
					value: 'posture1',
					description: [
						'Trunk upright or slightly inclined forward, no twisting',
						'Force application height can be selected freely',
						'No hindrance for the legs'
					],
					indicator: { success: true, warning: false, danger: false }
				},
				1: {
					value: 'posture2',
					description: [
						'Body inclined towards the direction of movement or slight twisting when pulling the load on one side',
						'Fixed force application height ranging from 0.9 – 1.2 m',
						'No or only slight hindrance for the legs',
						'Predominantly pulling'
					],
					indicator: { success: false, warning: true, danger: false }
				},
				2: {
					value: 'posture3',
					description: [
						'Awkward body postures caused by fixed force application height < 0.9 or > 1.2 m , lateral force application on one side or significantly obstructed view',
						'Significant hindrance for the legs',
						'Constant twisting and/or lateral inclination of the trunk'
					],
					indicator: { success: false, warning: false, danger: true }
				}
			}
		};

		let working_conditions_labels = [];
		for (let i = 0; i < kim_pp.working_conditions.length; i++) {
			working_conditions_labels[i] = labels.WORKING_CONDITIONS[kim_pp.working_conditions[i]];
		}

		let property_labels = [];
		for (let i = 0; i < kim_pp.properties.length; i++) {
			property_labels[i] = labels.PROPERTIES[kim_pp.properties[i]];
		}

		const labels_result = {
			risk: labels.RISK[risk],
			diagnostics: labels.DIAGNOSTICS[risk],
			pair: labels.PAIR[kim_pp.pair],
			gender: labels.GENDER[kim_pp.gender],
			distance: kim_pp.distance_or_duration < 13 ? labels.DISTANCE[kim_pp.distance_or_duration] : null,
			duration: kim_pp.distance_or_duration >= 13 ? labels.DURATION[kim_pp.distance_or_duration - 13] : null,
			vehicle: labels.VEHICLES[kim_pp.vehicle],
			mass: labels.MASS[kim_pp.mass],
			driveway_conditions: labels.DRIVEWAY_CONDITIONS[kim_pp.driveway_conditions],
			inclination_or_stairs: labels.INCLINATION_OR_STAIRS[kim_pp.inclination_or_stairs],
			working_conditions: working_conditions_labels,
			properties: property_labels,
			temporal_distribution: labels.TEMPORAL_DISTRIBUTION[kim_pp.temporal_distribution],
			posture: labels.POSTURES[kim_pp.posture]
		};

		const formattedReport = {
			...kim_pp,
			gender_input: kim_pp.gender,
			pair_input: kim_pp.pair,
			distance_or_duration_input: kim_pp.distance_or_duration,
			vehicle_input: kim_pp.vehicle,
			mass_input: kim_pp.mass,
			driveway_conditions_input: kim_pp.driveway_conditions,
			inclination_or_stairs_input: kim_pp.inclination_or_stairs,
			posture_input: kim_pp.posture,
			temporal_distribution_input: kim_pp.temporal_distribution,
			labels: labels_result
		};

		return formattedReport;
	}

	async countAllByRisk(params) {
		logger.info('[KimPushPull] service - countAllByRisk init', { params });
		const {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_with_user_access,
			start_date,
			end_date
		} = params;

		let user_id = '';

		if (StorageContext.getStore()?.environment === 'sandbox') {
			user_id = StorageContext.getStore().user_id;
		}

		const [data, error] = await this.repository.countAllByRisk({
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date,
			user_id
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		const result = Object.entries(data).map(([risk_name, total]) => ({ risk_name, total }));

		logger.info('[KimPushPull] service - countAllByRisk finish');
		return result;
	}
}
