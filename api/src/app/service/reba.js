import _ from 'lodash';
import config from 'config';
import moment from 'moment';
import { Puppeteer } from '../util/puppeteer/index.js';
import { REBA_RISK_NAME } from '../utils/constants.js';
import {
	i18n,
	logger,
	Storage,
	AppError,
	removeExtension,
	RESPONSE_ERROR_STATUS,
	RESPONSE_ERROR_REPORTS,
	RESPONSE_ERROR_ENTITIES,
	ERROR_RESPONSE_EXTERNAL_SERVICE
} from '../helpers/index.js';
import {
	RebaCalculator,
	FileRiskResultMapper,
	ActionPlansWithRebaScoreMapper,
	ActionPlansUniqueFileIdsMapper
} from '../entities/index.js';
import { StorageContext } from '../utils/storage_context.js';

const { REBA } = RESPONSE_ERROR_REPORTS;
const { STORAGE } = ERROR_RESPONSE_EXTERNAL_SERVICE;
const { FILE, REPORT, RANGE_RISK } = RESPONSE_ERROR_ENTITIES;
const { DATABASE_FAILED_PERFORM_QUERY } = RESPONSE_ERROR_STATUS;

export class RebaReportService {
	constructor({ repository, file_repository, range_risk_repository }) {
		this.repository = repository;
		this.storage = new Storage();
		this.file_repository = file_repository;
		this.range_risk_repository = range_risk_repository;
		this.file_risk_result_mapper = new FileRiskResultMapper();
		this.unique_file_ids_mapper = new ActionPlansUniqueFileIdsMapper();
	}

	setMaxValue = (bodyPartObject, secondsArray, bodyPart) => {
		const getAllNotes = secondsArray.map((elem) => {
			if (bodyPart === 'score_seconds') {
				const array = [bodyPartObject[elem].score_left || 0, bodyPartObject[elem].score_right || 0];
				return Math.max(...array);
			} else {
				return bodyPartObject[elem].note || 0;
			}
		});

		if (getAllNotes.length === 0) {
			return 0;
		}
		return Math.max(...getAllNotes);
	};

	getMaxScore = (allBodyScore) => {
		const bodyParts = Object.keys(allBodyScore);
		const newObject = {};

		bodyParts.forEach((bodyPart) => {
			if (!['left_hand', 'right_hand', 'squat'].find((item) => bodyPart === item)) {
				const secondsArray = Object.keys(allBodyScore[bodyPart]);
				newObject[`${bodyPart}`] = this.setMaxValue(allBodyScore[bodyPart], secondsArray, bodyPart);
			}
		});

		return newObject;
	};

	getPrefixFolder(organizationId, companyId) {
		const organization = organizationId.split('-', 5)[0];
		const company = companyId.split('-', 5)[0];
		return `${organization}-${company}`;
	}

	normalizeAngles(data = {}) {
		const object = new Object();
		Object.keys(data).map((key) => {
			object[key] = data[key][0].angles.map((x) => (x === 'null' ? null : Number(x)));
		});
		return object;
	}

	groupNotes(data = {}) {
		let result = {};
		Object.keys(data).forEach((key) => {
			if (!_.isEmpty(data[key])) {
				result[key] = Object.values(data[key]).map((object) => object.note);
			}
		});
		return result;
	}

	groupAngles(data = {}) {
		let result = {};
		Object.keys(data).forEach((key) => {
			if (!_.isEmpty(data[key])) {
				result[key] = Object.values(data[key]).map((object) => object.angle);
			}
		});
		return result;
	}

	getSizeAngles(payload) {
		let size = Object.keys(payload).reduce((acc, key, index) => {
			if (index === 0) {
				acc = payload[key].length;
				return acc;
			}

			if (payload[key].length !== acc) {
				throw new Error('Body part with invalid angles');
			}

			return acc;
		}, 0);

		return size;
	}

	async getAngles(organizationId, companyId, fileKey) {
		const storage = new Storage();
		const bucket = config.get('App.bucket');

		let keyWithoutExtension = removeExtension(fileKey);
		const prefix = this.getPrefixFolder(organizationId, companyId);

		if (!keyWithoutExtension.includes('/')) {
			keyWithoutExtension = `${prefix}/${keyWithoutExtension}`;
		}

		const key_file_reba = `${keyWithoutExtension}-reba.json`;

		const get_object_config = {
			Bucket: bucket,
			Key: key_file_reba
		};

		const data = await storage.getObject(get_object_config);

		if (!data) {
			throw new AppError(RESPONSE_ERROR_ENTITIES.FILE.DATA_NOT_FOUND);
		}

		const stream = JSON.parse(data.Body.toString('utf-8'));
		return stream;
	}

	async calculateAngles(payload) {
		logger.info('[RebaReport] service - calculateAngles init');
		const { organization_id, company_id, file_id } = payload;
		try {
			const file = await this.repository.db.File.findOne({
				where: {
					id: file_id,
					organization_id,
					company_id,
					isActive: true
				}
			});

			if (!file) {
				throw new AppError(RESPONSE_ERROR_ENTITIES.FILE.NOT_FOUND);
			}

			const storage = new Storage();
			const bucket = config.get('App.bucket');

			let keyWithoutExtension = removeExtension(file.generated_name);
			const prefix = this.getPrefixFolder(organization_id, company_id);

			if (!keyWithoutExtension.includes('/')) {
				keyWithoutExtension = `${prefix}/${keyWithoutExtension}`;
			}

			const key_file_reba = `${keyWithoutExtension}-reba.json`;

			const exists_object_config = {
				Bucket: bucket,
				Key: key_file_reba
			};

			if (await storage.existsObject(exists_object_config)) {
				return {
					status: 'warning',
					message: 'Calculation of angles has already been performed',
					data: {}
				};
			}

			const get_object_config = {
				Bucket: bucket,
				Key: `${keyWithoutExtension}.json`
			};

			const data = await storage.getObject(get_object_config);
			const stream = JSON.parse(data.Body.toString('utf-8'));
			const parts = this.normalizeAngles(stream);

			const reba = new RebaCalculator();
			const results = new Object();

			Object.keys(parts).forEach((key) => (results[key] = reba.calculationPerSecond(key, parts[key])));

			const upload_config = {
				Bucket: bucket,
				Key: key_file_reba,
				Body: JSON.stringify(results),
				ContentType: 'application/json'
			};

			await storage.putObject(upload_config);

			logger.info('[RebaReport] service - calculateAngles finish');
			return {
				status: 'success',
				message: 'Calculation done successfully',
				data: {}
			};
		} catch (error) {
			throw error;
		}
	}

	async createFileAngles(payload) {
		const { organization_id, company_id, generated_name, body } = payload;

		const bucket = config.get('App.bucket');

		let keyWithoutExtension = removeExtension(generated_name);
		const prefix = this.getPrefixFolder(organization_id, company_id);

		if (!keyWithoutExtension.includes('/')) {
			keyWithoutExtension = `${prefix}/${keyWithoutExtension}`;
		}

		const key_file_reba = `${keyWithoutExtension}-reba.json`;

		const upload_config = {
			Bucket: bucket,
			Key: key_file_reba,
			Body: JSON.stringify(body),
			ContentType: 'application/json'
		};

		await this.storage.putObject(upload_config);
	}

	async moutingPayloadFile(payload) {
		const { group_parts, size, repetition, force, coupling } = payload;

		if (!group_parts.left_knee || !group_parts.right_knee) {
			throw new AppError(REPORT.DEPRECATED_DATA_FILE);
		}

		let result = Object.keys(group_parts).reduce((prev, current) => {
			prev[current] = [];
			return prev;
		}, {});

		let score_seconds = {};

		const reba = new RebaCalculator();

		for (let i = 0; i < size; i++) {
			const {
				neck,
				trunk,
				left_upper_arm,
				right_upper_arm,
				left_lower_arm,
				right_lower_arm,
				left_knee,
				right_knee
			} = group_parts;

			score_seconds[i] = {
				score_left: null,
				score_right: null
			};

			const score_table_a_left = reba.calculateScoreA(neck[i], trunk[i], left_knee[i], force);

			const score_table_a_right = reba.calculateScoreA(neck[i], trunk[i], right_knee[i], force);

			if (!score_table_a_left || !score_table_a_right) {
				result.neck.push(null);
				result.trunk.push(null);
				result.left_upper_arm.push(null);
				result.right_upper_arm.push(null);
				result.left_lower_arm.push(null);
				result.right_lower_arm.push(null);
				result.left_knee.push(null);
				result.right_knee.push(null);
				continue;
			}

			const score_table_b_left = reba.calculateScoreB(left_upper_arm[i], left_lower_arm[i], 0, coupling);

			const score_table_b_right = reba.calculateScoreB(right_upper_arm[i], right_lower_arm[i], 0, coupling);

			if (!score_table_b_left || !score_table_b_right) {
				result.neck.push(null);
				result.trunk.push(null);
				result.left_upper_arm.push(null);
				result.right_upper_arm.push(null);
				result.left_lower_arm.push(null);
				result.right_lower_arm.push(null);
				result.left_knee.push(null);
				result.right_knee.push(null);
				continue;
			}

			const score_table_c_left = reba.calculateScoreC(score_table_a_left, score_table_b_left);

			const score_table_c_right = reba.calculateScoreC(score_table_a_right, score_table_b_right);

			const reba_score_left = reba.rebaScore(score_table_c_left, repetition);
			const reba_score_right = reba.rebaScore(score_table_c_right, repetition);

			score_seconds[i] = {
				score_left: reba_score_left,
				score_right: reba_score_right
			};
		}

		return score_seconds;
	}

	async update(payload) {
		logger.info('[RebaReport] service - update init');
		const {
			user_id,
			file_id,
			organization_id,
			company_id,
			sector_id,
			workstation_id,
			collection_date,
			force,
			coupling,
			repetition
		} = payload;
		let transaction;
		try {
			const file = await this.repository.db.File.findOne({
				where: {
					id: file_id,
					organization_id: organization_id,
					company_id: company_id,
					isActive: true
				},
				include: [
					{
						association: 'RebaReport',
						where: {
							report_user_id: user_id,
							is_active: true
						}
					}
				]
			});

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			if (!file.RebaReport) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			const {
				RebaReport: [{ id }]
			} = file.toJSON();

			await this.repository.db.RebaReport.update(
				{ repetition, force, coupling, collection_date },
				{ where: { id } },
				{ transaction }
			);

			const data = await this.getAngles(organization_id, company_id, file.generated_name);

			const group_parts = this.groupNotes(data);
			const size = this.getSizeAngles(group_parts);

			const score_seconds = await this.moutingPayloadFile({
				group_parts,
				repetition,
				coupling,
				force,
				size
			});

			const body = { ...data, score_seconds };

			const parameters_create_file = {
				generated_name: file.generated_name,
				organization_id,
				company_id,
				body
			};

			await this.createFileAngles(parameters_create_file);

			file.sector_id = sector_id;

			const workstation = await this.repository.db.Workstation.findOne({
				where: {
					id: workstation_id
				},
				attributes: ['name']
			});

			file.workstation = workstation?.name;
			file.workstation_id = workstation_id;

			this.scoreParts({ file_id, organization_id, company_id });
			await file.save({ transaction });
			await transaction.commit();

			logger.info('[RebaReport] service - update finish');
			return {
				status: 'success',
				message: 'Report updated successfully',
				data: {}
			};
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}

	async bodyPartsAngles(payload) {
		logger.info('[RebaReport] service - create init');
		const { organization_id, company_id, file_id, user_id } = payload;
		try {
			const file = await this.repository.db.File.findOne({
				where: {
					id: file_id,
					organization_id,
					company_id,
					isActive: true
				}
			});

			if (!file) {
				throw new AppError(RESPONSE_ERROR_ENTITIES.FILE.NOT_FOUND);
			}

			const storage = new Storage();
			const bucket = config.get('App.bucket');

			let keyWithoutExtension = removeExtension(file.generated_name);
			const prefix = this.getPrefixFolder(organization_id, company_id);

			if (!keyWithoutExtension.includes('/')) {
				keyWithoutExtension = `${prefix}/${keyWithoutExtension}`;
			}

			const key_file_reba = `${keyWithoutExtension}-reba.json`;

			const get_object_config = {
				Bucket: bucket,
				Key: key_file_reba
			};

			const data = await storage.getObject(get_object_config);

			if (!data) {
				throw new AppError(RESPONSE_ERROR_ENTITIES.FILE.DATA_NOT_FOUND);
			}

			const stream = JSON.parse(data.Body.toString('utf-8'));
			const result = this.groupNotes(stream);

			logger.info('[RebaReport] service - create finish');
			return result;
		} catch (error) {
			throw error;
		}
	}

	async updateComment(payload) {
		logger.info('[RebaReport] service - updateComment init');
		const { organization_id, company_id, file_id, user_id, comment } = payload;

		let transaction;
		try {
			const file = await this.repository.db.File.findOne({
				where: {
					id: file_id,
					organization_id,
					company_id,
					isActive: 1
				}
			});

			if (!file) {
				throw new AppError(RESPONSE_ERROR_ENTITIES.FILE.NOT_FOUND);
			}

			const report = await this.repository.db.RebaReport.findOne({
				where: {
					file_id,
					is_active: 1
				}
			});

			if (!report) {
				throw new AppError(RESPONSE_ERROR_ENTITIES.REPORT.NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			report.comment = comment;
			report.report_user_id = user_id;

			await report.save({ transaction });

			await transaction.commit();
			logger.info('[RebaReport] service - updateComment finish');

			return {
				status: 'success'
			};
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}

	/*
		Necessário refatorar esse serviço.
	*/
	async generatePDF({ file_id, locale }) {
		logger.info('[RebaReport] service - generatePDF init');
		let report_data;
		const language = locale.substring(0, 2);
		i18n.changeLanguage(language);

		try {
			const report = await this.repository.db.RebaReport.findOne({
				where: { file_id, is_active: 1 },
				include: [
					{
						model: this.repository.db.User,
						as: 'report_user'
					},
					{
						model: this.repository.db.File,
						as: 'file',
						include: [
							{
								model: this.repository.db.Sector,
								as: 'sector',
								attributes: ['name'],
								include: [
									{
										model: this.repository.db.Company,
										as: 'company',
										attributes: ['name'],
										include: [
											{
												model: this.repository.db.Organization,
												as: 'Organization',
												attributes: ['name']
											}
										]
									}
								]
							},
							{
								model: this.repository.db.Workstation,
								as: 'workstations',
								attributes: ['name'],
								include: [
									{
										model: this.repository.db.Line,
										as: 'line',
										attributes: ['name']
									}
								]
							}
						]
					}
				]
			}).then((data) => (report_data = data.get({ plain: true })));

			if (!report) {
				throw new AppError(RESPONSE_ERROR_ENTITIES.REPORT.NOT_FOUND);
			}

			const {
				file: { company_id, organization_id }
			} = report_data;

			const score_payload = { organization_id, company_id, file_id };

			const score = await this.score(score_payload);
			const score_parts = await this.scoreParts(score_payload);
			const worst_grade = await this.worstGrade(score_payload);
			const score_per_second = await this.scorePerSecond(score_payload);
			const riskRange = await this.riskRange();

			const formattedReport = this.formatDataForPDF(report_data, locale);
			const labels = this.getLabelsForPDF(formattedReport);

			const payload = {
				title: ['REBA Report Results'],
				data: { score, score_parts, score_per_second, worst_grade, riskRange },
				labels,
				...formattedReport,
				language
			};

			const puppeteer = new Puppeteer();
			const html = await puppeteer.render('reba', payload);

			const browser = await puppeteer.init();
			const buffer = puppeteer.mounting(browser, html);

			logger.info('[RebaReport] service - generatePDF finish');
			return buffer;
		} catch (error) {
			throw error;
		}
	}

	formatDataForPDF(report, locale) {
		const previousMomentLocale = moment.locale();
		moment.locale(locale);

		const formattedReport = {
			...report,
			file: {
				...report.file,
				sector: {
					...report.file.sector,
					company: {
						...report.file.sector.company,
						organization: {
							...report.file.sector.company.Organization
						}
					}
				},
				workstation: {
					...report.file.workstations,
					line: {
						...report.file.workstations.line
					}
				}
			}
		};

		formattedReport.collection_date = moment(formattedReport.collection_date).format('L');

		moment.locale(previousMomentLocale);
		return formattedReport;
	}

	getLabelsForPDF({ coupling, force }) {
		const labels = {
			coupling: {
				1: 'Good',
				2: 'Fair',
				3: 'Poor',
				4: 'Unacceptable'
			},
			force: {
				1: 'Load less than 5kg (intermittent)',
				2: 'Load from 5kg to 20kg (intermittent)',
				3: 'Load from 5kg to 20kg (static or repetitive)',
				4: 'Bigger load 20kg of repetitive load or blows'
			}
		};

		return {
			coupling: labels.coupling[coupling],
			force: labels.force[force]
		};
	}

	async scoreParts({ organization_id, company_id, file_id }) {
		logger.info('[RebaReport] service - scoreParts init');
		let transaction;
		try {
			const file = await this.repository.db.File.findOne({
				where: {
					id: file_id,
					organization_id,
					company_id,
					isActive: true
				},
				include: [
					{
						association: 'reba'
					}
				]
			});

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			if (!file.reba) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			const storage = new Storage();
			const bucket = config.get('App.bucket');

			let key_without_extension = removeExtension(file.generated_name);
			const prefix = this.getPrefixFolder(organization_id, company_id);

			if (!key_without_extension.includes('/')) {
				key_without_extension = `${prefix}/${key_without_extension}`;
			}

			const key_file_reba = `${key_without_extension}-reba.json`;

			const payload_storage = {
				Bucket: bucket,
				Key: key_file_reba
			};

			const data = await storage.getObject(payload_storage);

			const stream = JSON.parse(data.Body.toString('utf-8'));
			const mod_stream = this.getMaxScore(stream);

			const reba_id = file.reba.id;

			transaction = await this.repository.db.sequelize.transaction();

			const reba_report = await this.repository.db.RebaReport.findOne({
				where: {
					id: reba_id,
					is_active: true
				}
			});

			if (!reba_report) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			await reba_report.update(mod_stream, { transaction });

			const only_parts = [
				'neck',
				'trunk',
				'left_knee',
				'right_knee',
				'left_upper_arm',
				'right_upper_arm',
				'left_lower_arm',
				'right_lower_arm'
			];

			let notes = {};

			for (let part in stream) {
				if (only_parts.includes(part)) {
					let total = [0, 0, 0, 0, 0];
					Object.keys(stream[part]).forEach((second) => {
						const { note } = stream[part][second];
						note === null && (total[0] = ++total[0]);
						note === 1 && (total[1] = ++total[1]);
						note === 2 && (total[2] = ++total[2]);
						note === 3 && (total[3] = ++total[3]);
						note === 4 && (total[4] = ++total[4]);
					});
					notes[part] = total;
				}
			}

			await transaction.commit();

			logger.info('[RebaReport] service - scoreParts finish');
			return notes;
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}

	async scorePerSecond(payload) {
		logger.info('[RebaReport] service - scorePerSecond init');
		const { organization_id, company_id, file_id } = payload;
		try {
			const file = await this.repository.db.File.findOne({
				where: {
					id: file_id,
					organization_id,
					company_id,
					isActive: true
				},
				include: [
					{
						association: 'RebaReport'
					}
				]
			});

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			if (!file.RebaReport) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			const storage = new Storage();
			const bucket = config.get('App.bucket');

			let key_without_extension = removeExtension(file.generated_name);
			const prefix = this.getPrefixFolder(organization_id, company_id);

			if (!key_without_extension.includes('/')) {
				key_without_extension = `${prefix}/${key_without_extension}`;
			}

			const key_file_reba = `${key_without_extension}-reba.json`;

			const payload_storage = {
				Bucket: bucket,
				Key: key_file_reba
			};

			const data = await storage.getObject(payload_storage);

			if (!data) {
				throw new AppError(STORAGE.FAIL_DOWNLOAD_DATA);
			}

			const stream = JSON.parse(data.Body.toString('utf-8'));

			const only_parts = [
				'neck',
				'trunk',
				'left_knee',
				'right_knee',
				'left_upper_arm',
				'right_upper_arm',
				'left_lower_arm',
				'right_lower_arm',
				'score_seconds'
			];

			let notes = {};

			for (let part in stream) {
				only_parts.includes(part) && (notes[part] = stream[part]);
			}

			logger.info('[RebaReport] service - scorePerSecond finish');
			return notes;
		} catch (error) {
			throw error;
		}
	}

	async score(payload) {
		logger.info('[RebaReport] service - score init');
		const { organization_id, company_id, file_id } = payload;
		try {
			const file = await this.repository.db.File.findOne({
				where: {
					id: file_id,
					organization_id,
					company_id,
					isActive: true
				},
				include: [
					{
						association: 'RebaReport'
					}
				]
			});

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			if (!file.RebaReport) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			const storage = new Storage();
			const bucket = config.get('App.bucket');

			let key_without_extension = removeExtension(file.generated_name);
			const prefix = this.getPrefixFolder(organization_id, company_id);

			if (!key_without_extension.includes('/')) {
				key_without_extension = `${prefix}/${key_without_extension}`;
			}

			const key_file_reba = `${key_without_extension}-reba.json`;

			const payload_storage = {
				Bucket: bucket,
				Key: key_file_reba
			};

			const data = await storage.getObject(payload_storage);
			const stream = JSON.parse(data.Body.toString('utf-8'));

			const { score_seconds } = stream || {};

			let left_low = 0,
				left_medium = 0,
				left_high = 0,
				left_very_high = 0,
				left_not_identified = 0;

			let right_low = 0,
				right_medium = 0,
				right_high = 0,
				right_very_high = 0,
				right_not_identified = 0;

			if (score_seconds) {
				Object.keys(score_seconds).forEach((second) => {
					const { score_left, score_right } = score_seconds[second];
					score_left >= 2 && score_left <= 3 && left_low++;
					score_left >= 4 && score_left <= 7 && left_medium++;
					score_left >= 8 && score_left <= 10 && left_high++;
					score_left > 11 && left_very_high++;
					score_left === null && left_not_identified++;

					score_right >= 2 && score_right <= 3 && right_low++;
					score_right >= 4 && score_right <= 7 && right_medium++;
					score_right >= 8 && score_right <= 10 && right_high++;
					score_right > 11 && right_very_high++;
					score_right === null && right_not_identified++;
				});
			}

			logger.info('[RebaReport] service - score finish');
			return {
				left: [left_not_identified, left_low, left_medium, left_high, left_very_high],
				right: [right_not_identified, right_low, right_medium, right_high, right_very_high]
			};
		} catch (error) {
			throw error;
		}
	}

	async worstGrade(payload) {
		logger.info('[RebaReport] service - score init');
		const { organization_id, company_id, file_id } = payload;
		try {
			const file = await this.repository.db.File.findOne({
				where: {
					id: file_id,
					organization_id,
					company_id,
					isActive: true
				},
				include: [
					{
						association: 'RebaReport'
					}
				]
			});

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			if (!file.RebaReport) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			const storage = new Storage();
			const bucket = config.get('App.bucket');

			let key_without_extension = removeExtension(file.generated_name);
			const prefix = this.getPrefixFolder(organization_id, company_id);

			if (!key_without_extension.includes('/')) {
				key_without_extension = `${prefix}/${key_without_extension}`;
			}

			const key_file_reba = `${key_without_extension}-reba.json`;

			const payload_storage = {
				Bucket: bucket,
				Key: key_file_reba
			};

			const data = await storage.getObject(payload_storage);

			const stream = JSON.parse(data.Body.toString('utf-8'));
			const { score_seconds } = stream || {};

			let worst_grade = 0;
			let time = 0;

			if (!score_seconds) {
				throw new AppError(REBA.CALCULATION_SECOND_NOT_FOUND);
			}

			Object.keys(score_seconds).forEach((second) => {
				const { score_left, score_right } = score_seconds[second];
				let major = Math.max(score_left, score_right);

				if (major > worst_grade) {
					worst_grade = major;
					time = second;
				}
			});

			return { worst_grade, time: moment.utc(time * 1000).format('HH:mm:ss') };
		} catch (error) {
			throw error;
		}
	}

	async riskRange() {
		logger.info('[RebaReport] service - riskRange init');
		try {
			const risk_range = await this.repository.db.RangeRisk.findOne({
				where: {
					name: 'REBA',
					standard: true,
					is_active: true
				},
				attributes: ['generated_name']
			});

			if (!risk_range) {
				throw new AppError(RANGE_RISK.NOT_FOUND);
			}

			const { generated_name } = risk_range;

			const config = { Key: generated_name, Bucket: 'kinebot-statics' };

			const data = await this.storage.getObject(config);

			if (!data) {
				throw new AppError(RANGE_RISK.FAIL_DOWNLOAD_DATA);
			}

			const stream = JSON.parse(data.Body.toString('utf-8'));

			logger.info('[RebaReport] service - riskRange init');
			return stream;
		} catch (error) {
			throw error;
		}
	}

	async getActionPlansRebaScores({ organization_id, company_id, sector_id }) {
		logger.info('[RebaReport] service - getActionPlansRebaScores init');
		const action_plans = await this.repository.findFilesActionPlans({ organization_id, company_id, sector_id });
		const file_ids = this.unique_file_ids_mapper.mapThroughActionPlansAndSetUniqueFileIds(action_plans);
		const files = await this.#getFilesForEachUniqueFileId(file_ids);
		const action_plans_mapper = new ActionPlansWithRebaScoreMapper({
			files,
			company_id,
			action_plans,
			organization_id
		});
		const { action_plans_reba_scores, files_without_reba_json } = await action_plans_mapper.setActionPlans();
		logger.info('[RebaReport] service - getActionPlansRebaScores finish');
		return {
			files_without_reba_json,
			action_plans_reba_scores
		};
	}

	getNormalizeExposuresScore(data = []) {
		/* Necessário correção da nota do relatório reba no banco de dados. */
		return data?.map((item) => {
			if (item.risk > 0) {
				return { ...item, risk: item.risk - 1 };
			}
			return item;
		});
	}

	async getExposuresScore(params) {
		logger.info('[RebaReport] service - getExposuresScore init', { params });
		const { organization_id, company_id, sector_id, line_id, workstation_id, companies_ids, start_date, end_date } =
			params;

		let user_id = '';

		if (StorageContext.getStore()?.environment === 'sandbox') {
			user_id = StorageContext.getStore().user_id;
		}

		const range_risk = await this.getRangeRisk();

		const [data, error] = await this.repository.getExposuresScore({
			range_risk_id: range_risk.id,
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_ids,
			start_date,
			end_date,
			user_id
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		const payload = this.getNormalizeExposuresScore(data);
		const result = this.#formatExposureScore(payload);

		logger.info('[RebaReport] service - getExposuresScore finish');
		return result;
	}

	async getBodySideExposuresScore(params) {
		logger.info('[RebaReport] service - getBodySideExposuresScore init', { params });
		const { organization_id, company_id, sector_id, line_id, workstation_id, companies_ids, start_date, end_date } =
			params;

		let user_id = '';

		if (StorageContext.getStore()?.environment === 'sandbox') {
			user_id = StorageContext.getStore().user_id;
		}

		const range_risk = await this.getRangeRisk();

		const [data, error] = await this.repository.getBodySideExposuresScore({
			range_risk_id: range_risk.id,
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_ids,
			start_date,
			end_date,
			user_id
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		const result = this.#formatExposureScore(data);

		logger.info('[RebaReport] service - getBodySideExposuresScore finish');
		return result;
	}

	async createFileRiskResults({ group_parts, file_id }, options = {}) {
		logger.info('[RebaReport] service - createFileRiskResults init');

		const body_parts_percentages = this.file_risk_result_mapper.getPercentages(group_parts);

		const body_parts = await this.repository.db.BodyPart.findAll();
		const range_risk = await this.getRangeRisk();

		const file_risk_results_to_create = [];

		Object.keys(body_parts_percentages).forEach((body_part) => {
			const body_part_found = body_parts.find((bp) => bp.name === body_part);

			if (!body_part_found) {
				return;
			}

			Object.keys(body_parts_percentages[body_part]).forEach((risk) => {
				const percentage = body_parts_percentages[body_part][risk];

				file_risk_results_to_create.push({
					body_part_id: body_part_found.id,
					range_risk_id: range_risk.id,
					percentage,
					file_id,
					risk: risk === 'null' ? null : risk
				});
			});
		});

		const [created_file_risk_results, error_risks_results] = await this.file_repository.setRiskResults(
			file_risk_results_to_create,
			options
		);

		if (error_risks_results) {
			logger.error(`${error_risks_results.message}, stack trace - ${error_risks_results.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[RebaReport] service - createFileRiskResults finish');
		return created_file_risk_results;
	}

	async getRangeRisk() {
		logger.info('[RebaReport] service - getRangeRisk init');
		const [range_risk, error] = await this.range_risk_repository.findOneByName(REBA_RISK_NAME);

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (!range_risk) {
			throw new AppError(RANGE_RISK.NOT_FOUND);
		}

		logger.info('[RebaReport] service - getRangeRisk finish');
		return range_risk;
	}

	#formatExposureScore(data) {
		const grouped_data = data.reduce((acc, item) => {
			const { body_part, body_side, risk, seconds } = item;
			const name = body_part || body_side;

			if (!acc[name]) {
				acc[name] = {
					name,
					total: 0,
					data: [
						{
							risk: null,
							seconds: 0
						},
						{
							risk: 0,
							seconds: 0
						},
						{
							risk: 1,
							seconds: 0
						},
						{
							risk: 2,
							seconds: 0
						},
						{
							risk: 3,
							seconds: 0
						}
					]
				};
			}

			if (risk === null) {
				acc[name].data[0].seconds = seconds;
				acc[name].total += seconds;
			}

			if (risk === 0) {
				acc[name].data[1].seconds = seconds;
				acc[name].total += seconds;
			}

			if (risk === 1) {
				acc[name].data[2].seconds = seconds;
				acc[name].total += seconds;
			}

			if (risk === 2) {
				acc[name].data[3].seconds = seconds;
				acc[name].total += seconds;
			}

			if (risk === 3) {
				acc[name].data[4].seconds = seconds;
				acc[name].total += seconds;
			}

			return acc;
		}, {});

		const formatted_data = Object.values(grouped_data).map(({ name, total, data }) => {
			const data_with_percentages = data.map(({ risk, seconds }) => ({
				risk,
				percentage: this.#getPercentage(seconds, total)
			}));

			// const fixed_data = this.#fixPercentages(data_with_percentages);

			return {
				name,
				total_seconds: total,
				// data: fixed_data
				data: data_with_percentages
			};
		});

		return formatted_data;
	}

	// #fixPercentages(data) {
	// 	const sorted_data = [...data].sort((a, b) => b.percentage - a.percentage);
	// 	const total_percentage = sorted_data.reduce((sum, item) => sum + item.percentage, 0);
	// 	const difference = 100 - total_percentage;

	// 	if (difference !== 0) {
	// 		sorted_data[0].percentage += difference;
	// 	}

	// 	return sorted_data;
	// }

	#getPercentage(seconds, total) {
		return Number(Number((seconds / total) * 100).toFixed(2));
	}

	async #getFilesForEachUniqueFileId(file_ids) {
		const file_promises = file_ids.map((file_id) => this.#getFileWithWorkstationAndReba(file_id));
		const files = await Promise.all(file_promises);
		return files.filter((file) => file).map((file) => file.get({ plain: true }));
	}

	#getFileWithWorkstationAndReba(file_id) {
		const include_workstation = {
			association: 'workstations',
			include: [
				{
					association: 'line'
				}
			]
		};
		const include_reba = {
			association: 'reba',
			attributes: ['score_seconds'],
			where: {
				is_active: true
			}
		};
		return this.file_repository.findByPk(file_id, {
			include: [include_workstation, include_reba]
		});
	}

	async show(params) {
		logger.info('[RebaReport] service - show init', { params });
		const { organization_id, company_id, file_id } = params;

		const file = await this.repository.db.File.findOne({
			where: {
				id: file_id,
				organization_id,
				company_id,
				isActive: true
			}
		});

		if (!file) {
			throw new AppError(FILE.NOT_FOUND);
		}

		const report = await this.repository.db.RebaReport.findOne({
			where: {
				file_id,
				is_active: 1
			},
			attributes: [
				'id',
				'repetition',
				'force',
				'coupling',
				'comment',
				'collection_date',
				'trunk',
				'neck',
				'left_lower_arm',
				'right_lower_arm',
				'left_upper_arm',
				'right_upper_arm',
				'left_knee',
				'right_knee',
				'left_ankle',
				'right_ankle',
				'hip',
				'score_seconds'
			],
			raw: true
		});

		if (!report) {
			logger.info('[RebaReport] service - show finish');
			return { message: 'This file has not been analyzed yet.', status: 'failed' };
		}

		const renamedKeys = {
			id: report.id,
			repetition: report.repetition,
			force: report.force,
			coupling: report.coupling,
			comment: report.comment,
			collection_date: report.collection_date,
			body_parts: {
				trunk: report.trunk,
				neck: report.neck,
				left_lower_arm: report.left_lower_arm,
				right_lower_arm: report.right_lower_arm,
				left_upper_arm: report.left_upper_arm,
				right_upper_arm: report.right_upper_arm,
				left_knee: report.left_knee,
				right_knee: report.right_knee,
				left_ankle: report.left_ankle,
				right_ankle: report.right_ankle,
				hip: report.hip,
				score_seconds: report.score_seconds
			}
		};

		logger.info('[RebaReport] service - show finish');
		return renamedKeys;
	}

	async create(payload) {
		logger.info('[RebaReport] service - create init');
		const {
			user_id,
			file_id,
			organization_id,
			company_id,
			sector_id,
			workstation_id,
			collection_date,
			force,
			coupling,
			repetition
		} = payload;
		let transaction;
		try {
			const file = await this.repository.db.File.findOne({
				where: {
					id: file_id,
					organization_id,
					company_id,
					isActive: true
				}
			});

			if (!file) {
				throw new AppError(RESPONSE_ERROR_ENTITIES.FILE.NOT_FOUND);
			}

			const existing_reba_report = await this.repository.db.RebaReport.findOne({
				where: {
					file_id: file.id,
					is_active: true
				}
			});

			if (existing_reba_report) {
				throw new AppError(REPORT.ALREADY_CREATED);
			}

			transaction = await this.repository.db.sequelize.transaction();

			const created = await this.repository.db.RebaReport.create(
				{
					file_id: file.id,
					report_user_id: user_id,
					repetition,
					coupling,
					force,
					collection_date
				},
				{ transaction }
			);

			if (!created) {
				throw new AppError(REPORT.FAIL_CREATE);
			}

			const data = await this.getAngles(organization_id, company_id, file.generated_name);

			const group_parts = this.groupNotes(data);
			const size = this.getSizeAngles(group_parts);

			const score_seconds = await this.moutingPayloadFile({
				group_parts,
				repetition,
				coupling,
				force,
				size
			});

			await this.createFileRiskResults(
				{
					group_parts,
					file_id: file.id
				},
				{ transaction }
			);

			const body = { ...data, score_seconds };

			const parameters_create_file = {
				generated_name: file.generated_name,
				organization_id,
				company_id,
				body
			};

			await this.createFileAngles(parameters_create_file);

			file.sector_id = sector_id;

			const workstation = await this.repository.db.Workstation.findOne({
				where: {
					id: workstation_id
				},
				attributes: ['name']
			});

			file.workstation = workstation?.name;
			file.workstation_id = workstation_id;

			await file.save({ transaction });
			await transaction.commit();

			await this.scoreParts({ organization_id, company_id, file_id });

			logger.info('[RebaReport] service - create finish');
			return {
				status: 'success',
				message: 'Report created successfully',
				data: {}
			};
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}
}
