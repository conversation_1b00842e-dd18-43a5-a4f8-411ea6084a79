import _ from 'lodash';
import config from 'config';
import moment from 'moment';
import { SeraSummary } from '../entities/index.js';
import { StorageContext } from '../utils/storage_context.js';
import { AppError, logger, Util, RESPONSE_ERROR_STATUS } from '../helpers/index.js';
import { ERROR_RESPONSE_ENTITIES_ENUM, ERROR_RESPONSE_ENUM } from '../util/enum.js';

const { getPrefix } = Util;
const bucket = config.get('App.bucket');
const { DATABASE_FAILED_PERFORM_QUERY } = RESPONSE_ERROR_STATUS;

export class SeraSummaryService {
	constructor({
		repository,
		sera_summary_review_repository,
		action_plan_repository,
		user_repository,
		file_repository
	}) {
		this.repository = repository;
		this.sera_summary = new SeraSummary();
		this.user_repository = user_repository;
		this.file_repository = file_repository;
		this.action_plan_repository = action_plan_repository;
		this.sera_summary_review_repository = sera_summary_review_repository;
	}

	async create(params) {
		logger.info('[SeraSummary] service - create init', { params });
		const { evaluator_id, report_name, cycle_id, user_id, files } = params;
		let transaction = await this.repository.db.sequelize.transaction();
		try {
			const sera_summary = await this.repository.create(
				{
					evaluator_id,
					report_name,
					cycle_id,
					user_id
				},
				{
					transaction
				}
			);

			if (!sera_summary) {
				throw new AppError(ERROR_RESPONSE_ENUM.INTERNAL_SERVER_ERROR);
			}

			const file_records_list = files.map((file_id) => ({
				sera_summary_id: sera_summary.id,
				file_id: file_id
			}));

			await this.repository.db.SeraSummaryFiles.bulkCreate(file_records_list, { transaction });

			const sera_summary_review = await this.sera_summary_review_repository.create(
				{
					sera_summary_id: sera_summary.id,
					user_id,
					review: 0
				},
				{
					transaction
				}
			);

			if (!sera_summary_review) {
				throw new AppError(ERROR_RESPONSE_ENUM.INTERNAL_SERVER_ERROR);
			}

			await transaction.commit();

			const created_sera_summary = sera_summary.toJSON();
			const created_sera_summary_review = sera_summary_review.toJSON();

			logger.info('[SeraSummary] service - create success');
			return {
				...created_sera_summary,
				sera_summary_review_id: created_sera_summary_review?.id
			};
		} catch (error) {
			logger.error('[SeraSummary] service - create error', { error });
			transaction && (await transaction.rollback());
			throw error;
		} finally {
			logger.info('[SeraSummary] service - create finish');
		}
	}

	async update(params) {
		logger.info('[SeraSummary] service - update init', { params });
		let transaction;
		try {
			const sera_summary = await this.repository.findByPk(params.id);

			if (!sera_summary) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.SERA_SUMMARY.NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			await this.repository.update(
				{
					...params
				},
				{
					where: {
						id: params.id
					},
					transaction
				}
			);

			const { files } = params;

			const sera_summary_files = await this.repository.db.SeraSummaryFiles.findAll({
				where: {
					sera_summary_id: sera_summary.id
				},
				paranoid: false
			});

			for (const file of sera_summary_files) {
				const exists_file = files.includes(file.file_id);

				if (!exists_file) {
					await file.destroy({ transaction });
				}

				if (exists_file && file.deleted_at) {
					await file.restore({ transaction });
				}
			}

			for (const file of files) {
				const exists_file = sera_summary_files.some((f) => f.file_id === file);
				if (!exists_file) {
					await this.repository.db.SeraSummaryFiles.create(
						{
							sera_summary_id: sera_summary.id,
							file_id: file
						},
						{ transaction }
					);
				}
			}

			await transaction.commit();
			const existing_sera_summary = await this.repository.findByPk(params.id);
			const updated_sera_summary = existing_sera_summary.toJSON();

			const sera_summary_reviews = await this.sera_summary_review_repository.findAllByForeignKey({
				where: {
					sera_summary_id: updated_sera_summary.id
				},
				order: [['review', 'DESC']]
			});

			const json_sera_summary_review = sera_summary_reviews.map((review) => review.toJSON());
			const last_review = json_sera_summary_review[0];

			logger.info('[SeraSummary] service - update success');
			return {
				...updated_sera_summary,
				sera_summary_review_id: last_review.id
			};
		} catch (error) {
			logger.error('[SeraSummary] service - update error', { error });
			transaction && (await transaction?.rollback());
			throw error;
		} finally {
			logger.info('[SeraSummary] service - update finish');
		}
	}

	async show(id, company_id) {
		logger.info('[SeraSummary] service - show init');
		const { include_sera_summary_review, include_cycle, include_evaluator } =
			this.sera_summary.setSeraSummaryInclude(company_id);
		const existing_sera_summary = await this.repository.findByPk(id, {
			include: [include_sera_summary_review, include_cycle, include_evaluator],
			attributes: [
				'id',
				'report_name',
				'status',
				'comment',
				'evaluator_id',
				'cycle_id',
				'created_at',
				'updated_at'
			],
			order: [['review', 'review', 'DESC']]
		});

		if (!existing_sera_summary) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.SERA_SUMMARY.NOT_FOUND);
		}

		const json_sera_summary = existing_sera_summary.toJSON();
		const sera_summary_reviews = await this.sera_summary_review_repository.findAllByForeignKey({
			where: {
				sera_summary_id: json_sera_summary.id
			},
			order: [['review', 'DESC']]
		});
		const sera_summary_review_json = sera_summary_reviews.map((review) => review.toJSON());
		const last_review = sera_summary_review_json[0];

		const mapped_sera_summary = this.sera_summary.mapSeraSummary(existing_sera_summary);
		logger.info('[SeraSummary] service - show finish');
		return {
			...mapped_sera_summary,
			sera_summary_review_id: last_review.id
		};
	}

	async delete(id) {
		logger.info('[SeraSummary] service - delete init');
		let transaction;
		try {
			const existing_sera_summary = await this.repository.findByPk(id);

			if (!existing_sera_summary) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.SERA_SUMMARY.NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();
			await this.repository.delete(
				{
					where: {
						id
					}
				},
				transaction
			);
			await transaction.commit();
			const deleted_sera_summary = await this.repository.findByPk(id, { paranoid: false });
			logger.info('[SeraSummary] service - delete finish');
			return deleted_sera_summary;
		} catch (error) {
			logger.error('[SeraSummary] service - delete error', { error });
			await transaction?.rollback();
			throw error;
		}
	}

	async fetchResults({ sera_summary_id, company_id, review }) {
		logger.info('[SeraSummary] service - fetchResults init');
		const { include_cycle, include_evaluator, include_sera_responsible, include_sera_summary_review } =
			this.sera_summary.setSeraResultInclude(company_id);
		const existing_sera_summary = await this.repository.findByPk(sera_summary_id, {
			include: [include_sera_summary_review, include_cycle, include_evaluator, include_sera_responsible],
			attributes: [
				'id',
				'report_name',
				'status',
				'comment',
				'evaluator_id',
				'cycle_id',
				'created_at',
				'updated_at'
			],
			order: [['review', 'review', 'DESC']]
		});

		if (!existing_sera_summary) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.SERA_SUMMARY.NOT_FOUND);
		}

		const sera = existing_sera_summary.toJSON();

		const selected_review_summaries = sera.review
			.filter(({ review: version }) => version === review)
			.map((item) => item.id);

		const action_plan_options = this.sera_summary.setSeraResultActionPlanInclude(selected_review_summaries);

		const [action_plans, action_plans_error] = await this.action_plan_repository.findAll({}, action_plan_options);

		if (action_plans_error) {
			logger.error(`${action_plans_error.message}, stack trace - ${action_plans_error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		sera.action_plans = action_plans.map((action_plan) => action_plan.toJSON());

		const mapped_results = this.sera_summary.mapResults(sera, review);
		logger.info('[SeraSummary] service - fetchResults finish');
		return mapped_results;
	}

	async findAllForSeraSummaryList(params) {
		logger.info('[SeraSummary] service - findAllForSeraSummaryList init', { params });
		const { company_id, limit, offset, filter } = params;

		const filter_by = this.sera_summary.setSeraSummaryListFilter(filter);

		const {
			include_sera_summary_review,
			include_cycle,
			include_evaluator,
			include_workstation,
			include_sera_summary_files
		} = this.sera_summary.setSeraSummaryInclude(company_id, filter_by);

		let find_all = {
			include: [include_sera_summary_review, include_cycle, include_evaluator, include_sera_summary_files],
			where: {},
			distinct: true,
			attributes: ['id', 'report_name', 'status', 'comment', 'created_at', 'updated_at'],
			order: [['review', 'review', 'DESC']],
			limit,
			offset: limit * offset
		};

		if (filter_by.report_name) {
			_.set(find_all, 'where', {
				...find_all.where,
				...filter_by.report_name
			});
		}

		if (filter_by.created_at) {
			_.set(find_all, 'where', {
				...find_all.where,
				...filter_by.created_at
			});
		}

		if (filter_by.status) {
			_.set(find_all, 'where', {
				...find_all.where,
				...filter_by.status
			});
		}

		if (StorageContext.getStore()?.environment === 'sandbox') {
			_.set(find_all, 'where', {
				...find_all.where,
				user_id: StorageContext.getStore().user_id
			});
		}

		const existing_sera_summary = await this.repository.findAndCountAll(find_all);
		const sera_summaries_rows = existing_sera_summary?.rows.map((data) => data.toJSON());

		const mapped_summary_rows = await Promise.all(
			sera_summaries_rows?.map(async (summary) => {
				const include_task = {
					association: 'task',
					required: true,
					include: [
						{
							association: 'sera_report',
							required: true,
							include: [
								{
									association: 'review',
									required: true,
									where: {
										sera_summary_id: summary.id
									}
								}
							]
						}
					]
				};

				const file = await this.file_repository.findAll({
					attributes: ['id', 'original_name', 'generated_name', 'workstation_id'],
					where: {
						is_active: true
					},
					include: [include_workstation, include_task]
				});

				const file_list = file.map((data) => data.toJSON());
				const updated = { ...summary, file: file_list };
				return updated;
			})
		);

		const rows = this.sera_summary.mapSeraSummariesList(mapped_summary_rows, company_id);

		logger.info('[SeraSummary] service - findAllForSeraSummaryList finish');
		return { rows: rows, count: rows.length };
	}

	async index() {
		logger.info('[SeraSummary] service - index init');
		const existing_sera_summary = await this.repository.findAll();
		logger.info('[SeraSummary] service - index finish');
		return existing_sera_summary;
	}

	async downloadPDF(payload) {
		logger.info('[SeraSummary] service - downloadPDF init');
		const { organization_id, company_id, sera_summary_id, locale, user_id, review } = payload;

		const { include_cycle, include_sera_summary_review, include_evaluator, include_sera_responsible } =
			this.sera_summary.setSeraResultInclude(company_id);
		const existing_user = await this.user_repository.findByPk(user_id, {
			where: {
				is_active: true
			},
			attributes: ['id', 'name']
		});
		const existing_sera_summary = await this.repository.findByPk(sera_summary_id, {
			include: [include_sera_summary_review, include_cycle, include_evaluator, include_sera_responsible],
			attributes: [
				'id',
				'report_name',
				'status',
				'comment',
				'evaluator_id',
				'cycle_id',
				'created_at',
				'updated_at'
			],
			order: [['review', 'review', 'DESC']]
		});

		if (!existing_sera_summary) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.SERA_SUMMARY.NOT_FOUND);
		}

		const sera = existing_sera_summary.toJSON();

		const selected_review_summaries = sera.review
			.filter(({ review: version }) => version === review)
			.map((item) => item.id);

		const action_plan_options = this.sera_summary.setSeraResultActionPlanInclude(selected_review_summaries);

		const [action_plans, action_plans_error] = await this.action_plan_repository.findAll({}, action_plan_options);

		if (action_plans_error) {
			logger.error(`${action_plans_error.message}, stack trace - ${action_plans_error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		sera.action_plans = action_plans.map((action_plan) => action_plan.toJSON());

		const mapped_results = this.sera_summary.mapResults(sera, review);
		const [locale_language] = locale.split('-');
		this.#mapForPDF(mapped_results, locale_language);
		const prefix = getPrefix(organization_id, company_id);
		const file_key = this.sera_summary.formatFileKey(sera.report_name, prefix, 'sera-summary');
		const target_file = `sera-summary-${sera.report_name}.pdf`;
		const language = locale.replace('-', '_');
		const config = {
			functionName: 'pdf_container',
			data: {
				...mapped_results,
				prefix,
				bucket,
				file_key,
				language,
				id: sera_summary_id,
				comment: sera.comment,
				report_type: 'john-deere',
				type: 'john-deere/sera-result',
				technical_manager: existing_user.name,
				title: 'Safety Ergonomic Risk Assessment (S.E.R.A.)'
			}
		};
		const key = await this.sera_summary.generatedPDF(config);
		const url = await this.sera_summary.generateDownloadURL({
			fileName: target_file,
			Bucket: bucket,
			Key: key
		});
		logger.info('[SeraSummary] service - downloadPDF finish');
		return { url };
	}

	#mapForPDF(mapped_results, locale_language) {
		const { informations, task_summaries, reviews_history } = mapped_results;
		mapped_results.informations = {
			...informations,
			evaluator_id: informations.evaluator_id ?? '-',
			evaluator_name: informations.evaluator_name ?? '-',
			responsible_user: informations.responsible_user ?? '-',
			last_review_date: moment(informations.last_review_date).locale(locale_language).format('L'),
			original_version_date: moment(informations.original_version_date).locale(locale_language).format('L')
		};
		mapped_results.task_summaries = task_summaries.map((task_summary) => ({
			...task_summary,
			created_at: moment(task_summary.created_at).locale(locale_language).format('L'),
			reviewed_at: moment(task_summary.reviewed_at).locale(locale_language).format('L'),
			evaluator_name: task_summary.evaluator_name ?? '-',
			reviewer_name: task_summary.reviewer_name ?? '-'
		}));
		mapped_results.reviews_history =
			reviews_history?.map((review) => ({
				...review,
				created_at: moment(review.created_at).locale(locale_language).format('L')
			})) ?? null;
	}
}
