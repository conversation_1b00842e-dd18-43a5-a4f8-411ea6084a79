import config from 'config';
import sequelize from 'sequelize';
import { v4 as uuidv4 } from 'uuid';
import { AVATAR_DEFAULT } from '../../util/enum.js';
import { LexoRank } from '../../entities/ActionPlan/LexoRank.js';
import {
	RISK_RPN_MAX,
	RISK_RPN_COLORS,
	PDF_FUNCTION_NAME,
	NOTIFICATION_TYPES,
	NOTIFICATION_METHODS,
	ACTION_PLAN_PDF_SECTIONS,
	ACTION_PLAN_HISTORY_TYPES,
	ACTION_PLAN_MAX_ATTACHMENTS,
	ACTION_PLAN_ORIGIN_TABLE_NAME
} from '../../utils/constants.js';
import {
	i18n,
	logger,
	Lambda,
	Storage,
	AppError,
	HelpersUtil,
	Notification,
	RESPONSE_STATUS,
	RESPONSE_ENTITIES,
	RESPONSE_ERROR_STATUS,
	RESPONSE_ERROR_ENTITIES,
	DEFAULT_BUSINESS_INFORMATION,
	ERROR_RESPONSE_EXTERNAL_SERVICE
} from '../../helpers/index.js';
import { StorageContext } from '../../utils/storage_context.js';

const bucket = config.get('App.bucket');
const { STORAGE, SNS } = ERROR_RESPONSE_EXTERNAL_SERVICE;
const { DATABASE_FAILED_PERFORM_QUERY, DATABASE_FAILED_SAVE_DATA } = RESPONSE_ERROR_STATUS;
const { ACTION_PLAN, USER, FILE, ACTION_PLAN_COMMENT, NOTIFICATION } = RESPONSE_ERROR_ENTITIES;

export class ActionPlanV2Service {
	constructor({
		repository,
		user_repository,
		file_repository,
		notification_repository,
		ergonomic_tool_repository,
		business_information_repository,
		custom_report_step_key_result_repository,
		custom_report_sub_step_key_result_repository,
		action_plan_task_repository,
		custom_report_step_key_repository,
		preliminary_analysis_step_repository,
		custom_report_sub_step_key_repository
	}) {
		this.lambda = new Lambda();
		this.storage = new Storage();
		this.lexo_rank = new LexoRank();
		this.helpers_util = new HelpersUtil();
		this.notification = new Notification();
		this.repository = repository;
		this.file_repository = file_repository;
		this.user_repository = user_repository;
		this.notification_repository = notification_repository;
		this.ergonomic_tool_repository = ergonomic_tool_repository;
		this.business_information_repository = business_information_repository;
		this.custom_report_step_key_result_repository = custom_report_step_key_result_repository;
		this.custom_report_sub_step_key_result_repository = custom_report_sub_step_key_result_repository;
		this.action_plan_task_repository = action_plan_task_repository;
		this.custom_report_step_key_repository = custom_report_step_key_repository;
		this.preliminary_analysis_step_repository = preliminary_analysis_step_repository;
		this.custom_report_sub_step_key_repository = custom_report_sub_step_key_repository;
	}

	async index(params) {
		logger.info('[ActionPlan] service - index init', { params });
		const {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			activity_id,
			companies_with_user_access,
			title,
			status,
			responsible_id,
			start_date,
			end_date,
			priority,
			investment_range,
			due_date_start,
			due_date_end,
			origin_name,
			sort,
			limit,
			offset
		} = params;

		const payload = {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			activity_id,
			companies_with_user_access,
			title,
			origin_name,
			status,
			responsible_id,
			start_date,
			end_date,
			priority,
			investment_range,
			due_date_start,
			due_date_end
		};

		const promises = [
			this.repository.index({ ...payload, sort, limit, offset }),
			this.repository.countAll(payload)
		];

		const [[data, error_find], [total, error_count]] = await Promise.all(promises);

		if (error_find) {
			logger.error(`${error_find.message}, stack trace - ${error_find.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (error_count) {
			logger.error(`${error_count.message}, stack trace - ${error_count.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		const enriched_data = await this.#enrichActionPlansWithCorrectScoresFromList(data);

		logger.info('[ActionPlan] service - index finish');
		return { offset, limit, total, data: enriched_data };
	}

	async findAndCountAllForBoard(params) {
		logger.info('[ActionPlan] service - findAndCountAllForBoard init', { params });
		let user_id = '';

		if (StorageContext.getStore()?.environment === 'sandbox') {
			user_id = StorageContext.getStore().user_id;
		}

		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			activity_id,
			title,
			status,
			responsible_id,
			start_date,
			end_date,
			priority,
			investment_range,
			due_date_start,
			due_date_end,
			origin_name,
			sort,
			limit,
			offset
		} = params;

		const payload = {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			activity_id,
			companies_with_user_access,
			title,
			origin_name,
			status,
			responsible_id,
			start_date,
			end_date,
			priority,
			investment_range,
			due_date_start,
			due_date_end,
			user_id
		};

		const promises = [
			this.repository.findAllForBoard({ ...payload, sort, limit, offset }),
			this.repository.countAll(payload)
		];

		const [[data, error_find], [total, error_count]] = await Promise.all(promises);

		if (error_find) {
			logger.error(`${error_find.message}, stack trace - ${error_find.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (error_count) {
			logger.error(`${error_count.message}, stack trace - ${error_count.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		const enriched_data = await this.#enrichActionPlansWithCorrectScoresFromList(data);

		logger.info('[ActionPlan] service - findAndCountAllForBoard finish');
		return { offset, limit, total, data: enriched_data };
	}

	async getOriginOptions() {
		logger.info('[ActionPlan] service - getOriginOptions init');

		const [data, error] = await this.repository.getOriginOptions();

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[ActionPlan] service - getOriginOptions finish');
		return data;
	}

	async getReportOrigins(params) {
		logger.info('[ActionPlan] service - getReportOrigins init', { params });
		const { workstation_id, report_name, limit, offset } = params;

		const [result, error] = await this.repository.getReportOrigins({ workstation_id, report_name, limit, offset });

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[ActionPlan] service - getReportOrigins finish');
		return result;
	}

	async countAll(params) {
		logger.info('[ActionPlan] service - countAll init', { params });
		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			activity_id,
			title,
			status,
			responsible_id,
			start_date,
			end_date,
			priority,
			investment_range,
			due_date_start,
			due_date_end,
			origin_name
		} = params;

		const [total, error] = await this.repository.countAll({
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			activity_id,
			companies_with_user_access,
			title,
			origin_name,
			status,
			responsible_id,
			start_date,
			end_date,
			priority,
			investment_range,
			due_date_start,
			due_date_end
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[ActionPlan] service - countAll finish');
		return { total };
	}

	async create(params) {
		logger.info('[ActionPlan] service - create init', { params });
		let transaction, action_plan_origin_id, score, file_id, origin_workstation_id;
		const {
			user_id,
			company_id,
			organization_id,
			workstation_id,
			activity_id,
			title,
			description,
			due_date,
			responsible_user_id,
			investment_range,
			priority,
			status,
			origin,
			tasks,
			attachments,
			related_reports,
			language
		} = params;
		try {
			const [author, author_error] = await this.user_repository.findByPk(user_id, {
				attributes: ['name', 'email']
			});

			if (author_error) {
				logger.error(`${author_error.message}, stack trace - ${author_error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			if (!author) {
				throw new AppError(USER.NOT_FOUND);
			}

			const [responsible, responsible_error] = await this.user_repository.findByPk(responsible_user_id, {
				attributes: ['id', 'name', 'email']
			});

			if (responsible_error) {
				logger.error(`${responsible_error.message}, stack trace - ${responsible_error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			if (!responsible) {
				throw new AppError(USER.NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			if (origin) {
				const action_plan_origin = await this.createOrigin(origin, { transaction });

				file_id = origin.file_id;
				score = action_plan_origin.score;
				action_plan_origin_id = action_plan_origin.id;
				origin_workstation_id = action_plan_origin.workstation_id;
			}

			const lexo_rank = await this.generateLexoRank(status);

			const [created_action_plan, action_plan_error] = await this.repository.create(
				{
					score,
					user_id,
					file_id,
					company_id,
					organization_id,
					workstation_id: workstation_id || origin_workstation_id,
					activity_id,
					title,
					description,
					due_date,
					responsible_user_id,
					investment_range,
					priority,
					status,
					lexo_rank,
					action_plan_origin_id
				},
				{
					transaction
				}
			);

			if (action_plan_error) {
				logger.error(`${action_plan_error.message}, stack trace - ${action_plan_error.stack}`);
				throw new AppError(DATABASE_FAILED_SAVE_DATA);
			}

			const action_plan = created_action_plan.toJSON();

			await this.createHistory(
				{
					type: ACTION_PLAN_HISTORY_TYPES.CREATE,
					action_plan_id: action_plan.id,
					user_id
				},
				{ transaction }
			);

			if (attachments?.length) {
				await this.setAttachments({ action_plan_id: action_plan.id, user_id, attachments }, { transaction });
			}

			if (tasks?.length) {
				await Promise.all(
					tasks.map((task) =>
						this.createTask({ ...task, action_plan_id: action_plan.id, user_id }, { transaction })
					)
				);
			}

			if (related_reports?.length) {
				await Promise.all(
					related_reports.map((report) =>
						this.addRelatedReport({ ...report, action_plan_id: action_plan.id, user_id }, { transaction })
					)
				);
			}

			await transaction.commit();

			const notification_content = {
				action_plan_url: this.getActionPlanUrl(),
				action_plan_title: action_plan.title,
				responsible_name: responsible.name,
				owner_name: author.name
			};

			await this.sendNotification({
				type: NOTIFICATION_TYPES.ACTION_PLAN.RESPONSIBLE_CREATED,
				user: responsible,
				content: notification_content,
				language
			});

			logger.info('[ActionPlan] service - create success');
			return {
				status: RESPONSE_STATUS.SUCCESS,
				message: RESPONSE_ENTITIES.ACTION_PLAN.CREATED,
				data: { ...action_plan, company_id }
			};
		} catch (error) {
			logger.error('[ActionPlan] service - create error');
			transaction && (await transaction?.rollback());
			throw error;
		} finally {
			logger.info('[ActionPlan] service - create finish');
		}
	}

	async createOrigin(params, options) {
		logger.info('[ActionPlan] service - createOrigin init', { params });
		const { table_name, column_id, file_id } = params;

		const file = await this.file_repository.findByPk(file_id);

		if (!file) {
			throw new AppError(FILE.NOT_FOUND);
		}

		const [report_origin, report_origin_error] = await this.repository.getReportOrigin({
			table_name,
			column_id,
			file_id
		});

		if (report_origin_error) {
			logger.error(`${report_origin_error.message}, stack trace - ${report_origin_error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (!report_origin) {
			throw new AppError(ACTION_PLAN.ORIGIN_REPORT_NOT_FOUND);
		}

		const [action_plan_origin, ap_origin_error] = await this.repository.createOrigin(
			{
				table_name,
				column_id,
				origin_name: report_origin.origin_name
			},
			options
		);

		if (ap_origin_error) {
			logger.error(`${ap_origin_error.message}, stack trace - ${ap_origin_error.stack}`);
			throw new AppError(DATABASE_FAILED_SAVE_DATA);
		}

		logger.info('[ActionPlan] service - createOrigin finish');
		return {
			id: action_plan_origin[0].id,
			score: report_origin.score,
			workstation_id: file.workstation_id
		};
	}

	async createFromReport(params) {
		logger.info('[ActionPlan] service - createFromReport init', { params });
		const {
			user_id,
			company_id,
			organization_id,
			title,
			description,
			due_date,
			responsible_user_id,
			investment_range,
			priority,
			status,
			tasks,
			file_id,
			sera_summary_review_id,
			custom_report_step_key_id,
			custom_report_sub_step_key_id
		} = params;

		const origin = this.getOriginFromParams({
			file_id,
			sera_summary_review_id,
			custom_report_step_key_id,
			custom_report_sub_step_key_id
		});

		const result = await this.create({
			user_id,
			company_id,
			organization_id,
			title,
			description,
			due_date,
			responsible_user_id,
			investment_range,
			priority,
			status,
			tasks,
			origin
		});

		logger.info('[ActionPlan] service - createFromReport finish');
		return result;
	}

	async update(params) {
		logger.info('[ActionPlan] service - update init', { params });
		const {
			id,
			title,
			description,
			due_date,
			responsible_user_id,
			investment_range,
			investment_value,
			priority,
			status,
			user_id,
			language
		} = params;
		let transaction;
		try {
			const [action_plan, error_action_plan] = await this.repository.findOne({ id });

			if (error_action_plan) {
				logger.error(`${error_action_plan.message}, stack trace - ${error_action_plan.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			if (!action_plan) {
				throw new AppError(ACTION_PLAN.NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			const changed_fields = await this.getChangedFieldsHistoryType(action_plan, {
				title,
				description,
				due_date,
				responsible_user_id,
				investment_range,
				investment_value,
				priority,
				status
			});

			await action_plan.update(
				{
					title,
					description,
					due_date,
					responsible_user_id,
					investment_range,
					investment_value,
					priority,
					status
				},
				{
					transaction
				}
			);

			const promises = changed_fields.map((type) =>
				this.createHistory({ action_plan_id: id, type, user_id }, { transaction })
			);

			await Promise.all(promises);
			await transaction.commit();

			const status_changed = changed_fields.some((field) => field === ACTION_PLAN_HISTORY_TYPES.STATUS_CHANGED);

			if (status_changed) {
				const [user, error_user] = await this.user_repository.findByPk(user_id, {
					attributes: ['name', 'email']
				});

				if (error_user) {
					logger.error(`${error_user.message}, stack trace - ${error_user.stack}`);
					throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
				}

				if (!user) {
					throw new AppError(USER.NOT_FOUND);
				}

				const [author, error_author] = await this.user_repository.findByPk(action_plan.user_id, {
					attributes: ['id', 'name', 'email']
				});

				if (error_author) {
					logger.error(`${error_author.message}, stack trace - ${error_author.stack}`);
					throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
				}

				if (!author) {
					throw new AppError(USER.NOT_FOUND);
				}

				const notification_content = {
					user_name: user.name,
					responsible_name: author.name,
					action_plan_title: action_plan.title,
					action_plan_url: this.getActionPlanUrl()
				};

				await this.sendNotification({
					user: author,
					language: language,
					type: NOTIFICATION_TYPES.ACTION_PLAN.AUTHOR_STATUS_CHANGED,
					content: notification_content
				});
			}

			logger.info('[ActionPlan] service - update success');
			return {
				status: RESPONSE_STATUS.SUCCESS,
				message: RESPONSE_ENTITIES.ACTION_PLAN.UPDATED,
				data: action_plan
			};
		} catch (error) {
			logger.error('[ActionPlan] service - update error');
			transaction && (await transaction?.rollback());
			throw error;
		} finally {
			logger.info('[ActionPlan] service - update finish');
		}
	}

	async findOne(params) {
		logger.info('[ActionPlan] service - findOne init', { params });
		const { id, company_id, organization_id } = params;

		const [action_plan, action_plan_error] = await this.repository.findByPk(id, { company_id, organization_id });

		if (action_plan_error) {
			logger.error(`${action_plan_error.message}, stack trace - ${action_plan_error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (!action_plan) {
			throw new AppError(ACTION_PLAN.NOT_FOUND);
		}

		let enriched_action_plan = action_plan;
		if (action_plan.file_id && action_plan.action_plan_origin) {
			const [enriched] = await this.#enrichActionPlansWithCorrectScores([action_plan], action_plan.file_id);
			enriched_action_plan = enriched;
		}

		logger.info('[ActionPlan] service - findOne finish');
		return enriched_action_plan;
	}

	async findOneFromReport(params) {
		logger.info('[ActionPlan] service - findOneFromReport init', { params });
		const {
			id,
			file_id,
			company_id,
			organization_id,
			sera_summary_review_id,
			custom_report_step_key_id,
			custom_report_sub_step_key_id
		} = params;

		const { table_name, column_id } = this.getOriginFromParams({
			sera_summary_review_id,
			custom_report_step_key_id,
			custom_report_sub_step_key_id
		});

		const action_plan_params = { file_id };

		if (id) {
			action_plan_params.id = id;
		}

		const [action_plan, action_plan_error] = await this.repository.findOne(action_plan_params, {
			attributes: ['id'],
			include: {
				association: 'action_plan_origin',
				where: { table_name, column_id }
			}
		});

		if (action_plan_error) {
			logger.error(`${action_plan_error.message}, stack trace - ${action_plan_error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (!action_plan) {
			throw new AppError(ACTION_PLAN.NOT_FOUND);
		}

		const [result, error] = await this.repository.findByPk(action_plan.id, { company_id, organization_id });

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (!result) {
			throw new AppError(ACTION_PLAN.NOT_FOUND);
		}

		let enriched_result = result;
		if (result.file_id && result.action_plan_origin) {
			const [enriched] = await this.#enrichActionPlansWithCorrectScores([result], result.file_id);
			enriched_result = enriched;
		}

		logger.info('[ActionPlan] service - findOneFromReport finish');
		return enriched_result;
	}

	async findAllFromReport(params) {
		logger.info('[ActionPlan] service - findOneFromReport init', { params });
		const { file_id, sera_summary_review_id, custom_report_step_key_id, custom_report_sub_step_key_id } = params;

		const { table_name, column_id } = this.getOriginFromParams({
			sera_summary_review_id,
			custom_report_step_key_id,
			custom_report_sub_step_key_id
		});

		const [data, error] = await this.repository.findAll(
			{ file_id },
			{
				attributes: ['id', 'title'],
				include: [
					{
						association: 'action_plan_origin',
						where: { table_name, column_id },
						required: true,
						attributes: []
					},
					{
						association: 'file',
						required: true,
						attributes: ['company_id', 'organization_id']
					}
				]
			}
		);

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		const result = data.map((item) => ({
			id: item.id,
			title: item.title,
			company_id: item.file.company_id,
			organization_id: item.file.organization_id
		}));

		logger.info('[ActionPlan] service - findOneFromReport finish');
		return result;
	}

	async setRelatedReports(params) {
		logger.info('[ActionPlan] service - setRelatedReports init', { params });
		const { action_plan_id, user_id, related_reports } = params;
		let transaction;
		try {
			const [existing_related_reports, existing_related_reports_error] = await this.repository.getRelatedReports(
				{
					action_plan_id
				},
				{
					include: {
						association: 'ergonomic_tool',
						attributes: ['name', 'title', 'subtitle']
					}
				}
			);

			if (existing_related_reports_error) {
				logger.error(
					`${existing_related_reports_error.message}, stack trace - ${existing_related_reports_error.stack}`
				);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			transaction = await this.repository.db.sequelize.transaction();

			const related_reports_to_remove = existing_related_reports.filter(
				(existing_report) =>
					!related_reports.some((s) => s.ergonomic_tool_id === existing_report.ergonomic_tool_id)
			);

			await Promise.all(
				related_reports_to_remove.map((related_report) =>
					this.removeRelatedReport({ related_report, user_id, action_plan_id }, { transaction })
				)
			);

			const related_reports_to_add = related_reports.filter(
				(related_report) =>
					!existing_related_reports.some((s) => s.ergonomic_tool_id === related_report.ergonomic_tool_id)
			);

			await Promise.all(
				related_reports_to_add.map((related_report) =>
					this.addRelatedReport({ ...related_report, action_plan_id, user_id }, { transaction })
				)
			);

			await transaction.commit();

			return {
				status: RESPONSE_STATUS.SUCCESS
			};
		} catch (error) {
			logger.error('[ActionPlan] service - setRelatedReports error');
			transaction && (await transaction?.rollback());
			throw error;
		} finally {
			logger.info('[ActionPlan] service - setRelatedReports finish');
		}
	}

	async addRelatedReport(params, options) {
		logger.info('[ActionPlan] service - addRelatedReport init', { params });
		const { action_plan_id, user_id, report_id, ergonomic_tool_id } = params;

		const [ergonomic_tool, ergonomic_tool_error] = await this.ergonomic_tool_repository.findByPk(
			ergonomic_tool_id,
			options
		);

		if (ergonomic_tool_error) {
			logger.error(`${ergonomic_tool_error.message}, stack trace - ${ergonomic_tool_error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (!ergonomic_tool) {
			throw new AppError(RESPONSE_ERROR_ENTITIES.ERGONOMIC_TOOL.NOT_FOUND);
		}

		const [created_related_report, related_report_error] = await this.repository.createRelatedReport(
			{ action_plan_id, user_id, report_id, ergonomic_tool_id },
			options
		);

		if (related_report_error) {
			logger.error(`${related_report_error.message}, stack trace - ${related_report_error.stack}`);
			throw new AppError(DATABASE_FAILED_SAVE_DATA);
		}

		await this.createHistory(
			{
				action_plan_id,
				user_id,
				type: ACTION_PLAN_HISTORY_TYPES.LINKED_TOOL,
				description: ergonomic_tool.subtitle || ergonomic_tool.title
			},
			options
		);

		logger.info('[ActionPlan] service - addRelatedReport finish');
		return created_related_report;
	}

	async removeRelatedReport(params, options) {
		logger.info('[ActionPlan] service - removeRelatedReport init', { params });
		const { related_report, user_id, action_plan_id } = params;

		await related_report.destroy(options);

		await this.createHistory(
			{
				action_plan_id,
				user_id,
				type: ACTION_PLAN_HISTORY_TYPES.UNLINKED_TOOL,
				description: related_report?.ergonomic_tool?.subtitle || related_report?.ergonomic_tool?.title
			},
			options
		);

		logger.info('[ActionPlan] service - removeRelatedReports finish');
		return true;
	}

	async getAttachments(params) {
		logger.info('[ActionPlan] service - getAttachments init');
		const { action_plan_id } = params;
		try {
			const [data, error] = await this.repository.getAttachments(
				{
					action_plan_id
				},
				{
					include: {
						association: 'user',
						attributes: ['name']
					}
				}
			);

			if (error) {
				logger.error(`${error.message}, stack trace - ${error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			const result = await Promise.all(
				data.map(async (item) => {
					const attachment = item.toJSON();

					if (this.#attachmentIsImage(attachment.file_name)) {
						attachment.url = await this.storage.getSignedUrl({
							Key: attachment.location,
							Bucket: bucket
						});
					}

					return attachment;
				})
			);

			logger.info('[ActionPlan] service - getAttachments success');
			return result;
		} catch (error) {
			logger.error('[ActionPlan] service - getAttachments error');
			throw error;
		} finally {
			logger.info('[ActionPlan] service - getAttachments finish');
		}
	}

	async addAttachment(params) {
		logger.info('[ActionPlan] service - addAttachment init', { params });
		const { organization_id, company_id, content_type, file_name, size, user_id, action_plan_id } = params;

		let transaction;
		try {
			const [count, count_error] = await this.repository.countAttachments({ action_plan_id });

			if (count_error) {
				logger.error(`${count_error.message}, stack trace - ${count_error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			if (count >= ACTION_PLAN_MAX_ATTACHMENTS) {
				throw new AppError(ACTION_PLAN.MAX_ATTACHMENTS_REACHED);
			}

			const [author, author_error] = await this.user_repository.findByPk(user_id, { attributes: ['name'] });

			if (author_error) {
				logger.error(`${author_error.message}, stack trace - ${author_error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			const { url, location } = await this.createUrlSigned({
				organization_id,
				company_id,
				content_type,
				file_name
			});

			transaction = await this.repository.db.sequelize.transaction();

			const [attachment, attachment_error] = await this.repository.createAttachment({
				action_plan_id,
				file_name,
				location,
				user_id,
				size,
				url
			});

			if (attachment_error) {
				logger.error(`${attachment_error.message}, stack trace - ${attachment_error.stack}`);
				throw new AppError(DATABASE_FAILED_SAVE_DATA);
			}

			if (action_plan_id) {
				await this.createHistory(
					{
						action_plan_id,
						user_id,
						type: ACTION_PLAN_HISTORY_TYPES.ATTACHMENT_ADDED,
						description: file_name
					},
					{ transaction }
				);
			}

			const result = attachment.toJSON();
			result.url = url;
			result.user = author;

			await transaction.commit();

			logger.info('[ActionPlan] service - addAttachment success');
			return result;
		} catch (error) {
			logger.error('[ActionPlan] service - addAttachment error');
			transaction && (await transaction?.rollback());
			throw error;
		} finally {
			logger.info('[ActionPlan] service - addAttachment finish');
		}
	}

	async updateAttachment(params) {
		logger.info('[ActionPlan] service - updateAttachment init', { params });
		const { action_plan_attachment_id, description } = params;

		const [attachment, attachment_error] = await this.repository.getAttachment({
			id: action_plan_attachment_id
		});

		if (attachment_error) {
			logger.error(`${attachment_error.message}, stack trace - ${attachment_error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (!attachment) {
			throw new AppError(ACTION_PLAN.ATTACHMENT_NOT_FOUND);
		}

		await attachment.update({ description });

		logger.info('[ActionPlan] service - updateAttachment finish');
		return {
			status: RESPONSE_STATUS.SUCCESS,
			message: RESPONSE_ENTITIES.ACTION_PLAN.ATTACHMENT_UPDATED,
			data: {
				id: attachment.id
			}
		};
	}

	async removeAttachment(params) {
		logger.info('[ActionPlan] service - removeAttachment init', { params });
		const { action_plan_attachment_id, user_id } = params;

		let transaction;
		try {
			const [attachment, attachment_error] = await this.repository.getAttachment({
				id: action_plan_attachment_id
			});

			if (attachment_error) {
				logger.error(`${attachment_error.message}, stack trace - ${attachment_error.stack}`);
				throw new AppError(DATABASE_FAILED_SAVE_DATA);
			}

			if (!attachment) {
				throw new AppError(ACTION_PLAN.ATTACHMENT_NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			if (attachment.action_plan_id) {
				await this.createHistory(
					{
						user_id,
						action_plan_id: attachment.action_plan_id,
						type: ACTION_PLAN_HISTORY_TYPES.EVIDENCE_REMOVED,
						description: attachment.file_name
					},
					{ transaction }
				);
			}

			await attachment.destroy({ transaction });

			await transaction.commit();

			logger.info('[ActionPlan] service - removeAttachment success');
			return {
				status: RESPONSE_STATUS.SUCCESS,
				message: RESPONSE_ENTITIES.ACTION_PLAN.ATTACHMENT_REMOVED,
				data: {
					id: attachment.id
				}
			};
		} catch (error) {
			logger.error('[ActionPlan] service - removeAttachment error');
			transaction && (await transaction?.rollback());
			throw error;
		} finally {
			logger.info('[ActionPlan] service - removeAttachment finish');
		}
	}

	async setAttachments(params, options) {
		logger.info('[ActionPlan] service - setAttachments init', { params });
		const { action_plan_id, user_id, attachments: attachments_ids } = params;

		const [data, attachments_error] = await this.repository.updateAttachments(
			{ action_plan_id },
			{ id: attachments_ids },
			options
		);

		if (attachments_error) {
			logger.error(`${attachments_error.message}, stack trace - ${attachments_error.stack}`);
			throw new AppError(DATABASE_FAILED_SAVE_DATA);
		}

		if (!data) {
			throw new AppError(DATABASE_FAILED_SAVE_DATA);
		}

		const [attachments_data, attachments_data_error] = await this.repository.getAttachments(
			{
				id: attachments_ids
			},
			options
		);

		if (attachments_data_error) {
			logger.error(`${attachments_data_error.message}, stack trace - ${attachments_data_error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		const history_promises = attachments_data.map((attachment) =>
			this.createHistory(
				{
					action_plan_id,
					user_id,
					type: ACTION_PLAN_HISTORY_TYPES.ATTACHMENT_ADDED,
					description: attachment.file_name
				},
				options
			)
		);

		await Promise.all(history_promises);

		logger.info('[ActionPlan] service - setAttachments finish');
		return attachments_data;
	}

	async getTasks(params) {
		logger.info('[ActionPlan] service - getTasks init');
		const { action_plan_id, is_completed } = params;

		const [data, error] = await this.repository.getTasks(
			{
				is_completed,
				action_plan_id
			},
			{
				include: [
					{
						association: 'responsible_user',
						attributes: ['name']
					},
					{
						association: 'user',
						attributes: ['name']
					}
				],
				order: [
					[sequelize.fn('COALESCE', sequelize.col('due_date'), sequelize.literal('9999999999')), 'ASC'],
					['due_date', 'ASC']
				]
			}
		);

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[ActionPlan] service - getTasks finish');
		return data;
	}

	async addTask(params) {
		logger.info('[ActionPlan] service - addTask init', { params });
		const { action_plan_id, user_id, title, due_date, responsible_user_id, is_completed } = params;
		let transaction;
		try {
			const [action_plan, action_plan_error] = await this.repository.findOne({ id: action_plan_id });

			if (action_plan_error) {
				logger.error(`${action_plan_error.message}, stack trace - ${action_plan_error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			if (!action_plan) {
				throw new AppError(ACTION_PLAN.NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			const data = await this.createTask(
				{
					title,
					user_id,
					due_date,
					is_completed,
					action_plan_id,
					responsible_user_id
				},
				{ transaction }
			);

			await transaction.commit();

			logger.info('[ActionPlan] service - addTask success');
			return {
				status: RESPONSE_STATUS.SUCCESS,
				message: RESPONSE_ENTITIES.ACTION_PLAN.TASK_CREATED,
				data: data
			};
		} catch (error) {
			transaction && (await transaction?.rollback());
			logger.error('[ActionPlan] service - addTask error');
			throw error;
		} finally {
			logger.info('[ActionPlan] service - addTask finish');
		}
	}

	async createTask(params, options) {
		logger.info('[ActionPlan] service - createTask init', { params });
		const { user_id, title, due_date, responsible_user_id, is_completed = false, action_plan_id } = params;

		const [created_task, task_error] = await this.repository.createTask(
			{
				title,
				due_date,
				responsible_user_id,
				user_id,
				action_plan_id,
				is_completed
			},
			options
		);

		if (task_error) {
			logger.error(`${task_error.message}, stack trace - ${task_error.stack}`);
			throw new AppError(DATABASE_FAILED_SAVE_DATA);
		}

		await this.createHistory(
			{
				action_plan_id,
				user_id,
				type: ACTION_PLAN_HISTORY_TYPES.TASK_ADDED,
				description: title
			},
			options
		);

		logger.info('[ActionPlan] service - createTask finish');
		return created_task;
	}

	async removeTask(params) {
		logger.info('[ActionPlan] service - removeTask init', { params });
		const { action_plan_task_id, user_id } = params;
		let transaction;
		try {
			const [task, task_error] = await this.repository.getTask({
				id: action_plan_task_id
			});

			if (task_error) {
				logger.error(`${task_error.message}, stack trace - ${task_error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			if (!task) {
				throw new AppError(ACTION_PLAN.TASK_NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			await task.destroy({ transaction });

			await this.createHistory(
				{
					user_id,
					description: task.title,
					action_plan_id: task.action_plan_id,
					type: ACTION_PLAN_HISTORY_TYPES.TASK_REMOVED
				},
				{ transaction }
			);

			await transaction.commit();

			logger.info('[ActionPlan] service - removeTask success');
			return {
				status: RESPONSE_STATUS.SUCCESS,
				message: RESPONSE_ENTITIES.ACTION_PLAN.TASK_REMOVED,
				data: {
					id: action_plan_task_id
				}
			};
		} catch (error) {
			transaction && (await transaction?.rollback());
			logger.error('[ActionPlan] service - removeTask error');
			throw error;
		} finally {
			logger.info('[ActionPlan] service - removeTask finish');
		}
	}

	async updateTask(params) {
		logger.info('[ActionPlan] service - updateTask init', { params });
		const { action_plan_task_id, user_id, title, due_date, responsible_user_id } = params;
		let transaction;
		try {
			const [task, task_error] = await this.repository.getTask({
				id: action_plan_task_id
			});

			if (task_error) {
				logger.error(`${task_error.message}, stack trace - ${task_error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			if (!task) {
				throw new AppError(ACTION_PLAN.TASK_NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			const payload = { action_plan_id: task.action_plan_id, user_id, description: title };

			if (title !== task.title) {
				await this.createHistory(
					{ ...payload, type: ACTION_PLAN_HISTORY_TYPES.TASK_CHANGED, description: task.title },
					{ transaction }
				);
			}

			if (due_date !== task.due_date) {
				await this.createHistory(
					{ ...payload, type: ACTION_PLAN_HISTORY_TYPES.DUE_DATE_CHANGED },
					{ transaction }
				);
			}

			if (responsible_user_id !== task.responsible_user_id) {
				await this.createHistory(
					{ ...payload, type: ACTION_PLAN_HISTORY_TYPES.RESPONSIBLE_CHANGED },
					{ transaction }
				);
			}

			await task.update({ title, due_date, responsible_user_id }, { transaction });
			await transaction.commit();

			logger.info('[ActionPlan] service - updateTask success');
			return {
				status: RESPONSE_STATUS.SUCCESS,
				message: RESPONSE_ENTITIES.ACTION_PLAN.TASK_UPDATED,
				data: task
			};
		} catch (error) {
			transaction && (await transaction?.rollback());
			logger.error('[ActionPlan] service - updateTask error');
			throw error;
		} finally {
			logger.info('[ActionPlan] service - updateTask finish');
		}
	}

	async getTaskAttachments(params) {
		logger.info('[ActionPlan] service - getTaskAttachments init');
		const { action_plan_task_id } = params;
		try {
			const [data, error] = await this.repository.getTaskAttachments(
				{
					action_plan_task_id: action_plan_task_id
				},
				{
					include: {
						association: 'user',
						attributes: ['name']
					}
				}
			);

			if (error) {
				logger.error(`${error.message}, stack trace - ${error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			const result = await Promise.all(
				data.map(async (item) => {
					const attachment = item.toJSON();

					if (this.#attachmentIsImage(attachment.file_name)) {
						attachment.url = await this.storage.getSignedUrl({
							Key: attachment.location,
							Bucket: bucket
						});
					}

					return attachment;
				})
			);

			logger.info('[ActionPlan] service - getTaskAttachments success');
			return result;
		} catch (error) {
			logger.error('[ActionPlan] service - getTaskAttachments error');
			throw error;
		} finally {
			logger.info('[ActionPlan] service - getTaskAttachments finish');
		}
	}

	async addTaskAttachment(params) {
		logger.info('[ActionPlan] service - addTaskAttachment init', { params });
		const { organization_id, company_id, content_type, file_name, size, user_id, action_plan_task_id } = params;
		let transaction, action_plan_id;
		try {
			const [count, count_error] = await this.repository.countTaskAttachments({ action_plan_task_id });

			if (count_error) {
				logger.error(`${count_error.message}, stack trace - ${count_error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			if (count >= ACTION_PLAN_MAX_ATTACHMENTS) {
				throw new AppError(ACTION_PLAN.MAX_ATTACHMENTS_REACHED);
			}

			const [author, author_error] = await this.user_repository.findByPk(user_id, { attributes: ['name'] });

			if (author_error) {
				logger.error(`${author_error.message}, stack trace - ${author_error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			const { url, location } = await this.createUrlSigned({
				organization_id,
				company_id,
				content_type,
				file_name
			});

			transaction = await this.repository.db.sequelize.transaction();

			if (action_plan_task_id) {
				const [task, task_error] = await this.repository.getTask(
					{
						id: action_plan_task_id
					},
					{
						attributes: ['action_plan_id']
					}
				);

				if (task_error) {
					logger.error(`${task_error.message}, stack trace - ${task_error.stack}`);
					throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
				}

				if (!task) {
					throw new AppError(ACTION_PLAN.TASK_NOT_FOUND);
				}

				action_plan_id = task.action_plan_id;
			}

			const [task_attachment, task_attachment_error] = await this.repository.createTaskAttachment(
				{
					action_plan_task_id,
					file_name,
					location,
					user_id,
					size,
					url
				},
				{ transaction }
			);

			if (task_attachment_error) {
				logger.error(`${task_attachment_error.message}, stack trace - ${task_attachment_error.stack}`);
				throw new AppError(DATABASE_FAILED_SAVE_DATA);
			}

			if (action_plan_id) {
				await this.createHistory(
					{
						action_plan_id,
						user_id,
						type: ACTION_PLAN_HISTORY_TYPES.EVIDENCE_ADDED,
						description: file_name
					},
					{ transaction }
				);
			}

			const result = task_attachment.toJSON();
			result.url = url;
			result.user = author;

			await transaction.commit();

			logger.info('[ActionPlan] service - addTaskAttachment success');
			return result;
		} catch (error) {
			logger.error('[ActionPlan] service - addTaskAttachment error');
			transaction && (await transaction?.rollback());
			throw error;
		} finally {
			logger.info('[ActionPlan] service - addTaskAttachment finish');
		}
	}

	async updateTaskAttachment(params) {
		logger.info('[ActionPlan] service - updateTaskAttachment init');
		const { action_plan_task_attachment_id, description } = params;

		const [task_attachment, task_attachment_error] = await this.repository.getTaskAttachment({
			id: action_plan_task_attachment_id
		});

		if (task_attachment_error) {
			logger.error(`${task_attachment_error.message}, stack trace - ${task_attachment_error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (!task_attachment) {
			throw new AppError(ACTION_PLAN.EVIDENCE_NOT_FOUND);
		}

		await task_attachment.update({ description });

		logger.info('[ActionPlan] service - updateTaskAttachment finish');
		return {
			status: RESPONSE_STATUS.SUCCESS,
			message: RESPONSE_ENTITIES.ACTION_PLAN.EVIDENCE_UPDATED,
			data: {
				id: task_attachment.id
			}
		};
	}

	async removeTaskAttachment(params) {
		logger.info('[ActionPlan] service - removeTaskAttachment init', { params });
		const { action_plan_task_attachment_id, user_id } = params;

		let transaction, action_plan_id;
		try {
			const [task_attachment, task_attachment_error] = await this.repository.getTaskAttachment({
				id: action_plan_task_attachment_id
			});

			if (task_attachment_error) {
				logger.error(`${task_attachment_error.message}, stack trace - ${task_attachment_error.stack}`);
				throw new AppError(DATABASE_FAILED_SAVE_DATA);
			}

			if (!task_attachment) {
				throw new AppError(ACTION_PLAN.EVIDENCE_NOT_FOUND);
			}

			if (task_attachment.action_plan_task_id) {
				const [task, task_error] = await this.repository.getTask(
					{
						id: task_attachment.action_plan_task_id
					},
					{
						attributes: ['action_plan_id']
					}
				);

				if (task_error) {
					logger.error(`${task_error.message}, stack trace - ${task_error.stack}`);
					throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
				}

				if (!task) {
					throw new AppError(ACTION_PLAN.TASK_NOT_FOUND);
				}

				action_plan_id = task.action_plan_id;
			}

			transaction = await this.repository.db.sequelize.transaction();

			if (action_plan_id) {
				await this.createHistory(
					{
						action_plan_id,
						user_id,
						type: ACTION_PLAN_HISTORY_TYPES.EVIDENCE_REMOVED,
						description: task_attachment.file_name
					},
					{ transaction }
				);
			}

			await task_attachment.destroy({ transaction });

			await transaction.commit();

			logger.info('[ActionPlan] service - removeTaskAttachment success');
			return {
				status: RESPONSE_STATUS.SUCCESS,
				message: RESPONSE_ENTITIES.ACTION_PLAN.EVIDENCE_REMOVED,
				data: {
					id: task_attachment.id
				}
			};
		} catch (error) {
			logger.error('[ActionPlan] service - removeTaskAttachment error');
			transaction && (await transaction?.rollback());
			throw error;
		} finally {
			logger.info('[ActionPlan] service - removeTaskAttachment finish');
		}
	}

	async setTaskAttachments(params, options) {
		logger.info('[ActionPlan] service - setTaskAttachments init', { params });
		const { action_plan_id, action_plan_task_id, user_id, attachments: attachments_ids } = params;

		const [data, attachments_error] = await this.repository.updateTaskAttachments(
			{ action_plan_task_id },
			{ id: attachments_ids },
			options
		);

		if (attachments_error) {
			logger.error(`${attachments_error.message}, stack trace - ${attachments_error.stack}`);
			throw new AppError(DATABASE_FAILED_SAVE_DATA);
		}

		if (!data) {
			throw new AppError(DATABASE_FAILED_SAVE_DATA);
		}

		const [attachments_data, attachments_data_error] = await this.repository.getTaskAttachments(
			{
				id: attachments_ids
			},
			options
		);

		if (attachments_data_error) {
			logger.error(`${attachments_data_error.message}, stack trace - ${attachments_data_error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		const history_promises = attachments_data.map((attachment) =>
			this.createHistory(
				{
					action_plan_id,
					user_id,
					type: ACTION_PLAN_HISTORY_TYPES.EVIDENCE_ADDED,
					description: attachment.file_name
				},
				options
			)
		);

		await Promise.all(history_promises);

		logger.info('[ActionPlan] service - setTaskAttachments finish');
		return attachments_data;
	}

	async getRelatedReports({ action_plan_id }) {
		logger.info('[ActionPlan] service - getRelatedReports init', { params: action_plan_id });
		const [existing_action_plan, action_plan_error] = await this.repository.findOne(
			{ id: action_plan_id },
			{
				include: [
					{
						association: 'action_plan_origin',
						required: false
					},
					{
						association: 'action_plan_related_reports',
						required: false
					}
				]
			}
		);

		if (action_plan_error) {
			logger.error(`${action_plan_error.message}, stack trace - ${action_plan_error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (!existing_action_plan) {
			throw new AppError(ACTION_PLAN.NOT_FOUND);
		}

		const action_plan = existing_action_plan.toJSON();

		const [related_tools, related_tools_error] = await this.repository.getRelatedTools(
			action_plan.action_plan_origin || {}
		);

		if (related_tools_error) {
			logger.error(`${related_tools_error.message}, stack trace - ${related_tools_error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		let checked_related_reports = [];

		checked_related_reports = action_plan.action_plan_related_reports.map((report) => ({
			report_id: report.report_id,
			ergonomic_tool_id: report.ergonomic_tool_id
		}));

		const result = related_tools.map((tool) => ({
			report_id: tool.has_report ? tool.file_id : null,
			ergonomic_tool_id: tool.id,
			checked: checked_related_reports.some((report) => report.ergonomic_tool_id === tool.id),
			ergonomic_tool: {
				name: tool.name,
				title: tool.title,
				subtitle: tool.subtitle,
				description: tool.description
			}
		}));

		logger.info('[ActionPlan] service - getRelatedReports finish');
		return result;
	}

	async getHistory(params) {
		logger.info('[ActionPlan] service - getHistory init', { params });
		const { company_id, action_plan_id } = params;

		const [result, error] = await this.repository.getHistory({ company_id, action_plan_id });

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[ActionPlan] service - getHistory finish');
		return result;
	}

	async createHistory(params, options) {
		logger.info('[ActionPlan] service - createHistory init', { params });
		const { action_plan_id, user_id, type, description } = params;

		const [history_type, history_type_error] = await this.repository.getHistoryTypeByName(type);

		if (history_type_error) {
			logger.error(`${history_type_error.message}, stack trace - ${history_type_error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (!history_type) {
			throw new AppError(ACTION_PLAN.HISTORY_TYPE_NOT_FOUND);
		}

		const [history, history_error] = await this.repository.createHistory(
			{
				action_plan_history_type_id: history_type.id,
				action_plan_id,
				description,
				user_id
			},
			options
		);

		if (history_error) {
			logger.error(`${history_error.message}, stack trace - ${history_error.stack}`);
			throw new AppError(ACTION_PLAN.HISTORY_FAIL_CREATE);
		}

		logger.info('[ActionPlan] service - createHistory finish');
		return history;
	}

	async createUrlSigned(params) {
		logger.info('[ActionPlan] service - createUrlSigned init', { params });
		const { organization_id, company_id, content_type, file_name } = params;

		const folder = Storage.getFolder({
			organizationId: organization_id,
			companyId: company_id
		});

		const generated_name = this.#generateFileName(file_name);
		const location = `${folder}/${generated_name}`;

		const { url } = await this.storage.createSignatureUpload({
			bucket,
			fileName: location,
			contentType: content_type
		});

		if (!url) {
			throw new AppError(STORAGE.FAIL_CREATE_SIGNATURE);
		}

		logger.info('[ActionPlan] service - createUrlSigned finish');
		return { url, location };
	}

	async getChangedFieldsHistoryType(existing_action_plan, params) {
		const types = {
			title: ACTION_PLAN_HISTORY_TYPES.TITLE_CHANGED,
			description: ACTION_PLAN_HISTORY_TYPES.DESCRIPTION_CHANGED,
			due_date: ACTION_PLAN_HISTORY_TYPES.DUE_DATE_CHANGED,
			responsible_user_id: ACTION_PLAN_HISTORY_TYPES.RESPONSIBLE_CHANGED,
			investment_range: ACTION_PLAN_HISTORY_TYPES.INVESTMENT_CHANGED,
			investment_value: ACTION_PLAN_HISTORY_TYPES.INVESTMENT_CHANGED,
			priority: ACTION_PLAN_HISTORY_TYPES.PRIORITY_CHANGED,
			status: ACTION_PLAN_HISTORY_TYPES.STATUS_CHANGED
		};

		const fields = [
			'title',
			'description',
			'due_date',
			'responsible_user_id',
			'investment_range',
			'investment_value',
			'priority',
			'status'
		];

		const changed_fields = fields.filter((field) => String(existing_action_plan[field]) !== String(params[field]));
		const history_types = changed_fields.map((field) => types[field]);

		return history_types;
	}

	async updateRank(params) {
		logger.info('[ActionPlan] service - updateRank init', { params });
		const { id, status, lexo_rank, is_ordered, user_id, language } = params;
		let transaction;
		let new_lexo_rank = lexo_rank;
		try {
			const [action_plan, error_action_plan] = await this.repository.findOne({ id });

			if (error_action_plan) {
				logger.error(`${error_action_plan.message}, stack trace - ${error_action_plan.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			if (!action_plan) {
				throw new AppError(ACTION_PLAN.NOT_FOUND);
			}

			if (is_ordered) {
				new_lexo_rank = await this.generateLexoRank(status);
			}

			transaction = await this.repository.db.sequelize.transaction();

			const payload = {
				id,
				status,
				is_ordered,
				lexo_rank: new_lexo_rank
			};

			const status_changed = status !== action_plan.status;

			const [updated, error] = await this.repository.updateRank(payload, { transaction });

			if (error) {
				logger.error(`${error.message}, stack trace - ${error.stack}`);
				throw new AppError(DATABASE_FAILED_SAVE_DATA);
			}

			if (!updated) {
				throw new AppError(ACTION_PLAN.FAIL_UPDATE);
			}

			if (status_changed) {
				const [user, error_user] = await this.user_repository.findByPk(user_id, {
					attributes: ['name', 'email']
				});

				if (error_user) {
					logger.error(`${error_user.message}, stack trace - ${error_user.stack}`);
					throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
				}

				if (!user) {
					throw new AppError(USER.NOT_FOUND);
				}

				const [author, error_author] = await this.user_repository.findByPk(action_plan.user_id, {
					attributes: ['id', 'name', 'email']
				});

				if (error_author) {
					logger.error(`${error_author.message}, stack trace - ${error_author.stack}`);
					throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
				}

				if (!author) {
					throw new AppError(USER.NOT_FOUND);
				}

				const notification_content = {
					user_name: user.name,
					responsible_name: author.name,
					action_plan_title: action_plan.title,
					action_plan_url: this.getActionPlanUrl()
				};

				await this.sendNotification({
					user: author,
					language: language,
					content: notification_content,
					type: NOTIFICATION_TYPES.ACTION_PLAN.AUTHOR_STATUS_CHANGED
				});
			}

			await transaction.commit();

			logger.info('[ActionPlan] service - updateRank finish');
			return {
				status: RESPONSE_STATUS.SUCCESS,
				message: RESPONSE_ENTITIES.ACTION_PLAN.UPDATED,
				data: { id }
			};
		} catch (error) {
			logger.error('[ActionPlan] service - updateRank error');
			transaction && (await transaction?.rollback());
			throw error;
		} finally {
			logger.info('[ActionPlan] service - updateRank finish');
		}
	}

	async delete({ ids, user_id }) {
		logger.info('[ActionPlan] service - delete init', { params: { ids, user_id } });
		let transaction;
		try {
			transaction = await this.repository.db.sequelize.transaction();

			const [result, error] = await this.repository.delete({ ids, user_id }, { transaction });

			if (error) {
				logger.error(`${error.message}, stack trace - ${error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			if (result !== ids.length) {
				throw new AppError(ACTION_PLAN.FAIL_DELETE);
			}

			await Promise.all(
				ids.map((action_plan_id) =>
					this.createHistory(
						{
							user_id,
							action_plan_id,
							type: ACTION_PLAN_HISTORY_TYPES.DELETED
						},
						{ transaction }
					)
				)
			);

			await transaction.commit();

			logger.info('[ActionPlan] service - delete success');
			return {
				status: RESPONSE_STATUS.SUCCESS,
				message: RESPONSE_ENTITIES.ACTION_PLAN.DELETED,
				data: {
					ids,
					affected_rows: result
				}
			};
		} catch (error) {
			logger.error('[ActionPlan] service - delete error');
			transaction && (await transaction?.rollback());
			throw error;
		} finally {
			logger.info('[ActionPlan] service - delete finish');
		}
	}

	async getComments(params) {
		logger.info('[ActionPlan] service - getComments init');
		const { company_id, action_plan_id, limit, offset } = params;

		const promises = [
			this.repository.getComments({ company_id, action_plan_id, limit, offset }),
			this.repository.countAllComments({ company_id, action_plan_id })
		];

		const [[comments, error_find], [total, error_count]] = await Promise.all(promises);

		if (error_find) {
			logger.error(`${error_find.message}, stack trace - ${error_find.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (error_count) {
			logger.error(`${error_count.message}, stack trace - ${error_count.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[ActionPlan] service - getComments finish');
		return { limit, offset, total, comments };
	}

	async createComment(params) {
		logger.info('[ActionPlan] service - createComment init');
		const { organization_id, company_id, action_plan_id, user_id, description } = params;
		let transaction;
		try {
			const [action_plan, action_plan_find_error] = await this.repository.findByPk(action_plan_id, {
				organization_id,
				company_id
			});

			if (action_plan_find_error) {
				logger.error(`${action_plan_find_error.message}, stack trace - ${action_plan_find_error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			if (!action_plan) {
				throw new AppError(ACTION_PLAN.NOT_FOUND);
			}

			const [responsible, find_user_error] = await this.user_repository.findByPk(user_id);

			if (find_user_error) {
				logger.error(`${find_user_error.message}, stack trace - ${find_user_error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			if (!responsible) {
				throw new AppError(USER.NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			const [comment, error] = await this.repository.createComment(
				{ action_plan_id, user_id, description },
				{ transaction }
			);

			if (error) {
				logger.error(`${error.message}, stack trace - ${error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			if (!comment) {
				throw new AppError(ACTION_PLAN_COMMENT.FAIL_CREATE);
			}

			const history = await this.createHistory({
				action_plan_id,
				type: ACTION_PLAN_HISTORY_TYPES.COMMENT_ADDED,
				description: this.getCommentHistoryDescription(description),
				user_id
			});

			if (!history) {
				throw new AppError(ACTION_PLAN.HISTORY_FAIL_CREATE);
			}

			await transaction.commit();

			logger.info('[ActionPlan] service - createComment success');
			return comment;
		} catch (error) {
			transaction && (await transaction.rollback());
			logger.error('[ActionPlan] service - createComment error');
			throw error;
		} finally {
			logger.info('[ActionPlan] service - createComment finish');
		}
	}

	async updateComment(params) {
		logger.info('[ActionPlan] service - updateComments init');
		const { action_plan_comment_id, user_id, description } = params;
		let transaction;
		try {
			const [comment, error] = await this.repository.findCommentByPk(action_plan_comment_id);

			if (error) {
				logger.error(`${error.message}, stack trace - ${error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			if (!comment) {
				throw new AppError(ACTION_PLAN_COMMENT.NOT_FOUND);
			}

			if (comment.user_id !== user_id) {
				throw new AppError(ACTION_PLAN_COMMENT.NOT_AUTHORIZED);
			}

			transaction = await this.repository.db.sequelize.transaction();

			const history_description = this.getCommentHistoryDescription(comment.description);

			comment.description = description;
			await comment.save({ transaction });

			const history = await this.createHistory({
				action_plan_id: comment.action_plan_id,
				type: ACTION_PLAN_HISTORY_TYPES.COMMENT_CHANGED,
				description: history_description,
				user_id
			});

			if (!history) {
				throw new AppError(ACTION_PLAN.HISTORY_FAIL_CREATE);
			}

			await transaction.commit();

			logger.info('[ActionPlan] service - updateComments finish');
			return {
				status: RESPONSE_STATUS.SUCCESS,
				message: RESPONSE_ENTITIES.ACTION_PLAN.COMMENT_UPDATED,
				data: { id: action_plan_comment_id }
			};
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}

	async deleteComment(params) {
		logger.info('[ActionPlan] service - deleteComments init');
		const { action_plan_comment_id, user_id } = params;
		let transaction;
		try {
			const [comment, error] = await this.repository.findCommentByPk(action_plan_comment_id);

			if (error) {
				logger.error(`${error.message}, stack trace - ${error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			if (!comment) {
				throw new AppError(ACTION_PLAN_COMMENT.NOT_FOUND);
			}

			if (comment.user_id !== user_id) {
				throw new AppError(ACTION_PLAN_COMMENT.NOT_AUTHORIZED);
			}

			const { action_plan_id, description } = comment;

			transaction = await this.repository.db.sequelize.transaction();

			await comment.destroy({ transaction });

			const history = await this.createHistory({
				action_plan_id: action_plan_id,
				type: ACTION_PLAN_HISTORY_TYPES.COMMENT_REMOVED,
				description: this.getCommentHistoryDescription(description),
				user_id
			});

			if (!history) {
				throw new AppError(ACTION_PLAN.HISTORY_FAIL_CREATE);
			}

			await transaction.commit();

			logger.info('[ActionPlan] service - deleteComments success');
			return {
				status: RESPONSE_STATUS.SUCCESS,
				message: RESPONSE_ENTITIES.ACTION_PLAN.COMMENT_DELETED,
				data: { id: action_plan_comment_id }
			};
		} catch (error) {
			logger.error('[ActionPlan] service - deleteComments error');
			transaction && (await transaction?.rollback());
			throw error;
		} finally {
			logger.info('[ActionPlan] service - deleteComments finish');
		}
	}

	async getPDF(params) {
		logger.info('[ActionPlan] service - getPDF init', { params });
		const { id, company_id, organization_id, sections, locale } = params;

		let tasks, history, comments, attachments;
		const include_sections = this.getPDFSections(sections);

		const action_plan = await this.findOne({ id, company_id, organization_id });

		if (include_sections.image_attachments || include_sections.other_attachments) {
			attachments = await this.getAttachments({ action_plan_id: id });
		}

		if (include_sections.tasks) {
			const { completed_tasks, uncompleted_tasks } = include_sections;
			const is_completed = completed_tasks && uncompleted_tasks ? undefined : completed_tasks;

			tasks = await this.getTasks({ action_plan_id: id, is_completed });

			if (include_sections.attachments) {
				tasks = await Promise.all(
					tasks.map(async (task) => {
						const result = task.toJSON();
						result.attachments = await this.getTaskAttachments({ action_plan_task_id: task.id });

						return result;
					})
				);
			}
		}

		if (include_sections.history) {
			history = await this.getHistory({ action_plan_id: id, company_id });
		}

		if (include_sections.comments) {
			const comments_data = await this.getComments({ action_plan_id: id, company_id });

			comments = comments_data.comments;
		}

		const business_informations = await this.getBusinessInformations({ organization_id, company_id });

		const result = this.formatPDFData({
			business_informations,
			organization_id,
			include_sections,
			action_plan,
			attachments,
			company_id,
			comments,
			history,
			locale,
			tasks
		});

		const key = await this.generatedPDF({
			functionName: PDF_FUNCTION_NAME,
			data: result
		});
		const url = await this.generateDownloadURL({ fileName: result.file_key, Bucket: bucket, Key: key });

		logger.info('[ActionPlan] service - getPDF finish');
		return url;
	}

	async getBusinessInformations(params) {
		logger.info('[PreliminaryAnalysis] service - getBusinessInformations init');
		const { organization_id, company_id } = params;

		let business_informations = await this.business_information_repository.findOne({
			where: { company_id }
		});

		if (!business_informations) {
			business_informations = await this.business_information_repository.findOne({
				where: { organization_id }
			});
		}

		if (!business_informations) {
			business_informations = DEFAULT_BUSINESS_INFORMATION;
		}

		logger.info('[PreliminaryAnalysis] service - getBusinessInformations finish');
		return business_informations;
	}

	async generatedPDF(data) {
		const { key } = await this.lambda.call(data);

		if (!key) {
			throw new AppError(RESPONSE_ERROR_ENTITIES.REPORT.FAIL_CREATE);
		}

		return key;
	}

	async generateDownloadURL({ fileName, Bucket, Key }) {
		const url = await this.storage.createSignatureDownload({ fileName, Bucket, Key });

		if (!url) {
			throw new AppError(RESPONSE_ERROR_ENTITIES.FILE.FAILED_CREATE_DOWNLOAD_URL);
		}

		return url;
	}

	getCommentHistoryDescription(description) {
		if (description.length <= 10) {
			return description;
		}

		const abbreviated = description.slice(0, 9) + '...';
		return abbreviated;
	}

	getPDFSections(sections) {
		const image_attachments = sections.includes(ACTION_PLAN_PDF_SECTIONS.IMAGE_ATTACHMENTS);
		const other_attachments = sections.includes(ACTION_PLAN_PDF_SECTIONS.OTHER_ATTACHMENTS);
		const uncompleted_tasks = sections.includes(ACTION_PLAN_PDF_SECTIONS.UNCOMPLETED_TASKS);
		const completed_tasks = sections.includes(ACTION_PLAN_PDF_SECTIONS.COMPLETED_TASKS);
		const responsible = sections.includes(ACTION_PLAN_PDF_SECTIONS.RESPONSIBLE);
		const investment = sections.includes(ACTION_PLAN_PDF_SECTIONS.INVESTMENT);
		const due_date = sections.includes(ACTION_PLAN_PDF_SECTIONS.DUE_DATE);
		const priority = sections.includes(ACTION_PLAN_PDF_SECTIONS.PRIORITY);
		const comments = sections.includes(ACTION_PLAN_PDF_SECTIONS.COMMENTS);
		const history = sections.includes(ACTION_PLAN_PDF_SECTIONS.HISTORY);
		const attachments = image_attachments || other_attachments;
		const tasks = uncompleted_tasks || completed_tasks;

		return {
			image_attachments,
			other_attachments,
			uncompleted_tasks,
			completed_tasks,
			responsible,
			attachments,
			investment,
			due_date,
			priority,
			comments,
			history,
			tasks
		};
	}

	formatPDFData(params) {
		const {
			locale,
			action_plan,
			attachments,
			tasks,
			organization_id,
			company_id,
			include_sections,
			history,
			comments,
			business_informations
		} = params;

		const prefix = this.helpers_util.getPrefix(organization_id, company_id);
		const prefix_id = action_plan.id.split('-')[0];
		const file_key = `${prefix}/action-plan/${prefix_id}-${action_plan.title}.pdf`;
		const language = locale.replace('-', '_');

		let images, other_attachments, task_list, mapped_history, mapped_comments;

		if (attachments) {
			if (include_sections.image_attachments) {
				images = this.#formatImageAttachments(
					attachments.filter((attachment) => this.#attachmentIsImage(attachment.file_name)),
					locale
				);
			}
			if (include_sections.other_attachments) {
				other_attachments = this.#formatOtherAttachments(
					attachments.filter((attachment) => !this.#attachmentIsImage(attachment.file_name)),
					locale
				);
			}
		}

		if (tasks) {
			task_list = tasks.map((task) =>
				this.#formatTask(
					{
						task,
						image_attachments: include_sections.image_attachments,
						other_attachments: include_sections.other_attachments
					},
					locale
				)
			);
		}

		if (history) {
			mapped_history = this.#formatHistory(history, locale);
		}

		if (comments) {
			mapped_comments = this.#formatComments(comments, locale);
		}

		const data = {
			id: action_plan.id,
			type: 'john-deere/action-plan',
			report_type: 'john-deere',
			title: 'Action Plan',
			file_key,
			language,
			bucket,
			prefix,
			organization: this.#formatOrganization({
				business_informations,
				company: action_plan.company,
				organization: action_plan.organization
			}),
			informations: {
				organization_name: action_plan.organization.name,
				company_name: action_plan.company.name,
				sector_name: action_plan.sector.name,
				workstation_name: action_plan.workstation.name,
				line_name: action_plan.line.name,
				activity_name: action_plan.activity?.name,
				author_name: action_plan.author.name,
				creation_date: this.#formatDate(action_plan.created_at, locale),
				origin: action_plan.origin?.name,
				score: action_plan.score,
				score_color: this.#getScoreColor(action_plan.score),
				status: this.#formatStatus(action_plan.status),
				reference_date: this.#formatDate(new Date(), locale)
			},
			action_details: {
				responsible: include_sections.responsible ? action_plan.responsible.name : undefined,
				due_date: include_sections.due_date ? this.#formatDate(action_plan.due_date, locale) : undefined,
				priority: include_sections.priority ? action_plan.priority : undefined,
				investment: include_sections.investment
					? this.#formatNumber(action_plan.investment_value, locale)
					: undefined,
				description: action_plan.description
			},
			images,
			other_attachments,
			task_list,
			history: mapped_history,
			comments: mapped_comments
		};

		return data;
	}

	#getScoreColor(score) {
		if (!score) {
			return null;
		}

		if (score <= RISK_RPN_MAX.ACCEPTABLE) {
			return RISK_RPN_COLORS.ACCEPTABLE;
		}

		if (score <= RISK_RPN_MAX.MODERATE) {
			return RISK_RPN_COLORS.MODERATE;
		}

		if (score <= RISK_RPN_MAX.HIGH) {
			return RISK_RPN_COLORS.HIGH;
		}

		if (score <= RISK_RPN_MAX.VERY_HIGH) {
			return RISK_RPN_COLORS.VERY_HIGH;
		}

		if (score <= RISK_RPN_MAX.SERIOUS) {
			return RISK_RPN_COLORS.SERIOUS;
		}

		return RISK_RPN_COLORS.DEFAULT;
	}

	#formatOrganization({ business_informations, company, organization }) {
		const url_logo = company?.url_logo || organization?.url_logo || AVATAR_DEFAULT;

		return {
			company_name: company.name,
			fantasy_name: business_informations.fantasy_name,
			cnpj: business_informations.cnpj,
			address: business_informations.address,
			city: business_informations.city,
			state: business_informations.state,
			zipcode: business_informations.zipcode,
			district: business_informations.district,
			organization_name: organization.name,
			organization_logo: url_logo
		};
	}

	#formatNumber(value, locale) {
		return Number(value).toLocaleString(locale, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
	}

	#formatStatus(status) {
		const statuses = {
			'TO DO': 'To do',
			DOING: 'Doing',
			DONE: 'Done'
		};

		return statuses[status];
	}

	#formatComments(comments, locale) {
		return comments.map((comment) => ({
			date: this.#formatDate(comment.updated_at, locale),
			comment: comment.description,
			user: comment.user.name
		}));
	}

	#formatHistory(history, locale) {
		return history.map((item) => ({
			date: this.#formatDate(item.created_at, locale),
			description: item.description,
			user: item.author,
			type: item.type
		}));
	}

	#formatOtherAttachments(attachments, locale) {
		const data = attachments.map((attachment) => {
			const file_name = attachment.file_name.split('.').slice(0, -1).join('.');
			const file_extension = attachment.file_name.split('.').pop();
			return {
				name: file_name,
				file_type: file_extension,
				uploaded_at: this.#formatDate(attachment.created_at, locale),
				uploaded_by: attachment.user.name
			};
		});

		return data;
	}

	#formatImageAttachments(attachments, locale) {
		const data = attachments.map((attachment) => ({
			description: attachment.description,
			url: attachment.url,
			uploaded_at: this.#formatDate(attachment.created_at, locale),
			uploaded_by: attachment.user.name,
			name: attachment.file_name
		}));

		return data;
	}

	#formatTask({ task, image_attachments, other_attachments }, locale) {
		const data = {
			name: task?.title,
			status: task.is_completed ? 'Completed' : 'Uncompleted',
			responsible: task?.user,
			due_date: this.#formatDate(task.due_date, locale)
		};

		if (!task.attachments) {
			return data;
		}

		if (image_attachments) {
			data.images = this.#formatImageAttachments(
				task.attachments.filter((attachment) => this.#attachmentIsImage(attachment.file_name)),
				locale
			);
		}

		if (other_attachments) {
			data.other_attachments = this.#formatOtherAttachments(
				task.attachments.filter((attachment) => !this.#attachmentIsImage(attachment.file_name)),
				locale
			);
		}

		return data;
	}

	#formatDate(date, locale) {
		return new Date(date).toLocaleDateString(locale, {
			day: '2-digit',
			month: '2-digit',
			year: 'numeric'
		});
	}

	#attachmentIsImage(file_name) {
		return file_name.endsWith('.png') || file_name.endsWith('.jpg') || file_name.endsWith('.jpeg');
	}

	#generateFileName(name) {
		const random_id = uuidv4();
		const nomenclature = random_id.split('-', 5)[0];
		return `${nomenclature}-${name}`;
	}

	getOriginFromParams(params) {
		const { sera_summary_review_id, custom_report_step_key_id, custom_report_sub_step_key_id, file_id } = params;

		let table_name, column_id;

		if (sera_summary_review_id) {
			table_name = ACTION_PLAN_ORIGIN_TABLE_NAME.SERA_SUMMARY_REVIEW;
			column_id = sera_summary_review_id;
		}

		if (custom_report_step_key_id) {
			table_name = ACTION_PLAN_ORIGIN_TABLE_NAME.CUSTOM_REPORT_STEP_KEY;
			column_id = custom_report_step_key_id;
		}

		if (custom_report_sub_step_key_id) {
			table_name = ACTION_PLAN_ORIGIN_TABLE_NAME.CUSTOM_REPORT_SUB_STEP_KEY;
			column_id = custom_report_sub_step_key_id;
		}

		return { table_name, column_id, file_id };
	}

	async generateLexoRank(status) {
		logger.info('[ActionPlan] service - generateLexoRank init');
		const [action_plan, action_plan_error] = await this.repository.getFistActionPlan(status);

		if (action_plan_error) {
			logger.error(`${action_plan_error.message}, stack trace - ${action_plan_error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		const lexo_rank = action_plan ? this.lexo_rank.generate('', action_plan.lexo_rank) : this.lexo_rank.generate();

		logger.info('[ActionPlan] service - generateLexoRank finish');
		return lexo_rank;
	}

	async setUserPreferenceColumns(params) {
		logger.info('[ActionPlan] service - setUserPreferenceColumns init', { params });
		const { columns, user_id } = params;
		let transaction;
		try {
			const [column_fields, column_fields_error] = await this.repository.getColumnFieldsByNames(columns);

			if (column_fields_error) {
				logger.error(`${column_fields_error.message}, stack trace - ${column_fields_error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			const [user_preference, user_preference_error] = await this.repository.getUserPreferenceColumns(user_id);

			if (user_preference_error) {
				logger.error(`${user_preference_error.message}, stack trace - ${user_preference_error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			const column_ids = column_fields.map((field) => field.id);

			const records_to_create = column_ids.map((column_id) => ({
				user_id,
				action_plan_column_field_id: column_id
			}));

			transaction = await this.repository.db.sequelize.transaction();

			const [, error] = user_preference.length
				? await this.repository.updateUserPreferenceColumns(
						{ records_to_create: records_to_create, user_id },
						{ transaction }
					)
				: await this.repository.createUserPreferenceColumns(
						{ records_to_create: records_to_create },
						{ transaction }
					);

			if (error) {
				logger.error(`${error.message}, stack trace - ${error.stack}`);
				throw new AppError(DATABASE_FAILED_SAVE_DATA);
			}

			await transaction.commit();

			logger.info('[ActionPlan] service - setUserPreferenceColumns success');
			return {
				status: RESPONSE_STATUS.SUCCESS,
				message: RESPONSE_ENTITIES.ACTION_PLAN.PREFERENCES_UPDATED,
				data: { columns: column_ids, user_id }
			};
		} catch (error) {
			logger.error('[ActionPlan] service - setUserPreferenceColumns error');
			transaction && (await transaction?.rollback());
			throw error;
		} finally {
			logger.info('[ActionPlan] service - setUserPreferenceColumns finish');
		}
	}

	async getUserPreferenceColumns(params) {
		logger.info('[ActionPlan] service - getUserPreferenceColumns init', { params });
		const { user_id } = params;

		const [user_preference, user_preference_error] = await this.repository.getUserPreferenceColumns(user_id);

		if (user_preference_error) {
			logger.error(`${user_preference_error.message}, stack trace - ${user_preference_error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[ActionPlan] service - getUserPreferenceColumns success');
		return user_preference;
	}

	async bulkUpdate(params) {
		logger.info('[ActionPlan] service - bulkUpdate init', { params });
		const { ids, field } = params;
		let transaction = await this.repository.db.sequelize.transaction();
		try {
			const [affected_rows, error] = await this.repository.bulkUpdate(
				{
					ids,
					field
				},
				{
					transaction
				}
			);

			if (error) {
				logger.error(`${error.message}, stack trace - ${error.stack}`);
				throw new AppError(DATABASE_FAILED_SAVE_DATA);
			}

			await transaction.commit();

			logger.info('[ActionPlan] service - bulkUpdate success');
			return {
				status: RESPONSE_STATUS.SUCCESS,
				message: RESPONSE_ENTITIES.ACTION_PLAN.UPDATED,
				data: { affected_rows }
			};
		} catch (error) {
			logger.error('[ActionPlan] service - bulkUpdate error');
			await transaction?.rollback();
			throw error;
		} finally {
			logger.info('[ActionPlan] service - bulkUpdate finish');
		}
	}

	async getAttachmentDownloadUrl(params) {
		logger.info('[ActionPlan] service - getAttachmentDownloadUrl init', { params });
		const { action_plan_attachment_id } = params;

		const [attachment, error] = await this.repository.getAttachment({
			id: action_plan_attachment_id
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (!attachment) {
			throw new AppError(ACTION_PLAN.ATTACHMENT_NOT_FOUND);
		}

		const url = await this.storage.createSignatureDownload({
			Key: attachment.location,
			Bucket: bucket,
			fileName: attachment.file_name
		});

		logger.info('[ActionPlan] service - getAttachmentDownloadUrl success');
		return url;
	}

	async getTaskAttachmentDownloadUrl(params) {
		logger.info('[ActionPlan] service - getTaskAttachmentDownloadUrl init', { params });
		const { action_plan_task_attachment_id } = params;

		const [attachment, error] = await this.repository.getTaskAttachment({
			id: action_plan_task_attachment_id
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (!attachment) {
			throw new AppError(ACTION_PLAN.EVIDENCE_NOT_FOUND);
		}

		const url = await this.storage.createSignatureDownload({
			Key: attachment.location,
			Bucket: bucket,
			fileName: attachment.file_name
		});

		logger.info('[ActionPlan] service - getTaskAttachmentDownloadUrl success');
		return url;
	}

	async getUserPreferenceNotification(params) {
		logger.info('[ActionPlan] service - getUserPreferenceNotification init', { params });
		const { user_id } = params;

		const notification_type_names = Object.values(NOTIFICATION_TYPES.ACTION_PLAN);

		const [data, error] = await this.notification_repository.getUserPreferences(
			{ user_id },
			{
				include: {
					association: 'notification_type',
					attributes: ['name'],
					required: true,
					where: { name: notification_type_names }
				}
			}
		);

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		const result = data.map((item) => item.notification_type.name);

		logger.info('[ActionPlan] service - getUserPreferenceNotification success');
		return result;
	}

	async setUserPreferenceNotification(params) {
		logger.info('[ActionPlan] service - setUserPreferenceNotification init', { params });
		const { user_id, preferences } = params;
		let transaction;
		try {
			const [email_notification_method, email_notification_method_error] =
				await this.notification_repository.getNotificationMethod({
					method: NOTIFICATION_METHODS.EMAIL
				});

			if (email_notification_method_error) {
				logger.error(
					`${email_notification_method_error.message}, stack trace - ${email_notification_method_error.stack}`
				);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			const [notification_types, notification_types_error] =
				await this.notification_repository.getNotificationTypes({
					name: preferences
				});

			if (notification_types_error) {
				logger.error(`${notification_types_error.message}, stack trace - ${notification_types_error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			const notification_type_names = Object.values(NOTIFICATION_TYPES.ACTION_PLAN);

			const [existing_preferences, existing_preferences_error] =
				await this.notification_repository.getUserPreferences(
					{
						user_id
					},
					{
						include: {
							association: 'notification_type',
							attributes: ['name'],
							required: true,
							where: {
								name: notification_type_names
							}
						}
					}
				);

			if (existing_preferences_error) {
				logger.error(
					`${existing_preferences_error.message}, stack trace - ${existing_preferences_error.stack}`
				);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			const preferences_to_create = notification_types
				.filter(
					(preference) =>
						!existing_preferences.some((existing) => existing.notification_type_id === preference.id)
				)
				.map((preference) => ({
					user_id,
					notification_type_id: preference.id,
					notification_method_id: email_notification_method.id
				}));

			const preferences_to_delete = existing_preferences
				.filter((existing) => !preferences.includes(existing.notification_type.name))
				.map((existing) => existing.notification_type_id);

			transaction = await this.repository.db.sequelize.transaction();

			if (preferences_to_delete.length) {
				const [, deleted_preferences_error] = await this.notification_repository.deleteUserPreferences(
					{
						user_id,
						notification_type_id: preferences_to_delete,
						notification_method_id: email_notification_method.id
					},
					{ transaction }
				);

				if (deleted_preferences_error) {
					logger.error(
						`${deleted_preferences_error.message}, stack trace - ${deleted_preferences_error.stack}`
					);
					throw new AppError(DATABASE_FAILED_SAVE_DATA);
				}
			}

			if (preferences_to_create.length) {
				const [, created_preferences_error] = await this.notification_repository.createUserPreferences(
					preferences_to_create,
					{ transaction }
				);

				if (created_preferences_error) {
					logger.error(
						`${created_preferences_error.message}, stack trace - ${created_preferences_error.stack}`
					);
					throw new AppError(DATABASE_FAILED_SAVE_DATA);
				}
			}

			await transaction.commit();

			logger.info('[ActionPlan] service - setUserPreferenceNotification success');
			return {
				status: RESPONSE_STATUS.SUCCESS,
				message: RESPONSE_ENTITIES.ACTION_PLAN.PREFERENCES_UPDATED,
				data: { preferences }
			};
		} catch (error) {
			logger.error('[ActionPlan] service - setUserPreferenceNotification error');
			transaction && (await transaction?.rollback());
			throw error;
		} finally {
			logger.info('[ActionPlan] service - setUserPreferenceNotification finish');
		}
	}

	async sendNotification(params) {
		logger.info('[ActionPlan] service - sendNotification init', { params });
		const { type, user, language, content } = params;

		const [notification_type, notification_type_error] = await this.notification_repository.getNotificationType({
			name: type
		});

		if (notification_type_error) {
			logger.error(`${notification_type_error.message}, stack trace - ${notification_type_error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (!notification_type) {
			throw new AppError(NOTIFICATION.TYPE_NOT_FOUND, { type });
		}

		const [user_preferences, user_preferences_error] = await this.notification_repository.getUserPreferences(
			{ notification_type_id: notification_type.id, user_id: user.id },
			{ include: { association: 'notification_method', attributes: ['id', 'method'] } }
		);

		if (user_preferences_error) {
			logger.error(`${user_preferences_error.message}, stack trace - ${user_preferences_error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		const [methods, methods_error] = await this.notification_repository.getNotificationMethods({});

		if (methods_error) {
			logger.error(`${methods_error.message}, stack trace - ${methods_error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		const email_method = methods.find((item) => item.method === NOTIFICATION_METHODS.EMAIL);

		const send_email = user_preferences.some(
			(preference) => preference.notification_method.method === NOTIFICATION_METHODS.EMAIL
		);

		if (send_email) {
			const payload = { user, language, content, notification_type, email_method };
			await this.sendEmail(payload);
		}

		logger.info('[ActionPlan] service - sendNotification finish');
		return { web: false, email: send_email };
	}

	async sendEmail(params) {
		logger.info('[ActionPlan] service - sendEmail init', { params });
		const { user, language, content, notification_type, email_method } = params;

		const template = this.getTemplate(notification_type.name);

		const email_notification = await this.notification.sendEmail({
			language,
			email: user.email,
			content_mail: content,
			subject: await this.getSubject(notification_type.title, language),
			template
		});

		if (!email_notification) {
			throw new AppError(SNS.FAIL_SEND_MESSAGE);
		}

		const [notification_created, notification_created_error] = await this.notification_repository.create({
			notification_type_id: notification_type.id,
			notification_method_id: email_method.id,
			user_id: user.id
		});

		if (notification_created_error) {
			logger.error(`${notification_created_error.message}, stack trace - ${notification_created_error.stack}`);
			throw new AppError(DATABASE_FAILED_SAVE_DATA);
		}

		if (!notification_created) {
			throw new AppError(NOTIFICATION.FAIL_CREATE);
		}

		logger.info('[ActionPlan] service - sendEmail finish');
		return notification_created;
	}

	getTemplate(notification_type) {
		const template_mapper = {
			create_action: 'create_action',
			author_action_plan_status_changed: 'update_action',
			author_action_plan_task_completed: 'action_completed_task'
		};

		return template_mapper[notification_type];
	}

	getActionPlanUrl() {
		logger.info('[ActionPlan] service - sendEmail init');

		const environment_url = config.get('url');
		const url = `${environment_url}/action-plans`;

		logger.info('[ActionPlan] service - sendEmail finish');
		return url;
	}

	async getSubject(notification_type, language) {
		logger.info('[ActionPlan] service - getSubject init');

		const subject_mapper = {
			'Action plan task completed': 'Kinebot - Action plan task completed',
			'New action plan created': 'Kinebot - New action plan created',
			'Action plan status changed': 'Kinebot - Action plan status changed'
		};

		await i18n.changeLanguage(language);
		const subject_translated = i18n.t(subject_mapper[notification_type]);

		logger.info('[ActionPlan] service - getSubject finish');
		return subject_translated;
	}

	async setTaskCompleted(params) {
		logger.info('[ActionPlan] service - setTaskCompleted init', { params });
		const { organization_id, company_id, action_plan_task_id, user_id, language } = params;
		let transaction;
		try {
			const [task, task_error] = await this.repository.getTask({ id: action_plan_task_id });

			if (task_error) {
				logger.error(`${task_error.message}, stack trace - ${task_error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			if (!task) {
				throw new AppError(ACTION_PLAN.TASK_NOT_FOUND);
			}

			const [action_plan, action_plan_error] = await this.repository.findByPk(task.action_plan_id, {
				organization_id,
				company_id
			});

			if (action_plan_error) {
				logger.error(`${action_plan_error.message}, stack trace - ${action_plan_error.stack}`);
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			if (!action_plan) {
				throw new AppError(ACTION_PLAN.NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			const is_completed = !task.is_completed;

			await task.update({ is_completed }, { transaction });

			const history_type = is_completed
				? ACTION_PLAN_HISTORY_TYPES.TASK_COMPLETED
				: ACTION_PLAN_HISTORY_TYPES.TASK_UNCOMPLETED;

			await this.createHistory(
				{
					action_plan_id: task.action_plan_id,
					type: history_type,
					title: task.title,
					user_id
				},
				{ transaction }
			);

			await transaction.commit();

			if (history_type === ACTION_PLAN_HISTORY_TYPES.TASK_COMPLETED) {
				const [user, user_error] = await this.user_repository.findByPk(user_id, {
					attributes: ['name', 'email']
				});

				if (user_error) {
					logger.error(`${user_error.message}, stack trace - ${user_error.stack}`);
					throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
				}

				if (!user) {
					throw new AppError(USER.NOT_FOUND);
				}

				const [author, author_error] = await this.user_repository.findByPk(action_plan.user_id, {
					attributes: ['id', 'name', 'email']
				});

				if (author_error) {
					logger.error(`${author_error.message}, stack trace - ${author_error.stack}`);
					throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
				}

				if (!author) {
					throw new AppError(USER.NOT_FOUND);
				}

				const notification_content = {
					responsible_name: author.name,
					action_plan_title: action_plan.title,
					action_plan_task_title: task.title,
					user_name: user.name,
					action_plan_url: this.getActionPlanUrl()
				};

				await this.sendNotification({
					type: NOTIFICATION_TYPES.ACTION_PLAN.AUTHOR_TASK_COMPLETED,
					user: author,
					content: notification_content,
					language
				});
			}

			logger.info('[ActionPlan] service - setTaskCompleted success');
			return {
				status: RESPONSE_STATUS.SUCCESS,
				message: RESPONSE_ENTITIES.ACTION_PLAN.TASK_UPDATED,
				data: {
					id: action_plan_task_id,
					is_completed
				}
			};
		} catch (error) {
			transaction && (await transaction?.rollback());
			logger.error('[ActionPlan] service - setTaskCompleted error');
			throw error;
		} finally {
			logger.info('[ActionPlan] service - setTaskCompleted finish');
		}
	}

	async getReportsActionPlansCount(params) {
		logger.info('[ActionPlan] service - getReportsActionPlansCount init', { params });
		try {
			const [data, error] = await this.repository.getReportsActionPlansCount(params);

			if (error) {
				logger.error('[ActionPlan] service - getReportsActionPlansCount error', { error });
				throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
			}

			const result = {
				count: Number(data?.total)
			};

			logger.info('[ActionPlan] service - getReportsActionPlansCount success');
			return result;
		} catch (error) {
			logger.error('[ActionPlan] service - getReportsActionPlansCount error', { error });
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		} finally {
			logger.info('[ActionPlan] service - getReportsActionPlansCount finish');
		}
	}

	async getReportsActionPlansOriginCount(params) {
		logger.info('[ActionPlan] service - getReportsActionPlansOriginCount init', { params });
		const [data, error] = await this.repository.getReportsActionPlansOriginCount(params);

		if (error) {
			logger.error('[ActionPlan] service - getReportsActionPlansOriginCount error', error);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		return data;
	}

	async #enrichActionPlansWithCorrectScoresFromList(action_plans) {
		if (!action_plans || action_plans.length === 0) {
			return action_plans;
		}

		const file_groups = {};
		const action_plans_without_file_id = [];

		action_plans.forEach((action_plan) => {
			const file_id = action_plan.file_id;
			if (file_id) {
				if (!file_groups[file_id]) {
					file_groups[file_id] = [];
				}
				file_groups[file_id].push(action_plan);
			} else {
				action_plans_without_file_id.push(action_plan);
			}
		});

		const enriched_groups = await Promise.all(
			Object.entries(file_groups).map(async ([file_id, plans]) => {
				return await this.#enrichActionPlansWithCorrectScores(plans, file_id);
			})
		);

		const all_enriched = enriched_groups.flat();
		return [...all_enriched, ...action_plans_without_file_id];
	}

	async #enrichActionPlansWithCorrectScores(action_plans, file_id) {
		const plain_action_plans =
			Array.isArray(action_plans) && action_plans.length > 0 && typeof action_plans[0].get !== 'function'
				? action_plans
				: action_plans.map((action_plan) => action_plan.get({ plain: true }));

		const sub_step_key_ids = [];
		const step_key_ids = [];

		plain_action_plans.forEach((action_plan) => {
			const origin = action_plan.action_plan_origin;
			if (origin) {
				if (origin.table_name === 'custom_report_sub_step_keys') {
					sub_step_key_ids.push(origin.column_id);
				} else if (origin.table_name === 'custom_report_step_keys') {
					step_key_ids.push(origin.column_id);
				}
			}
		});

		const score_maps = await this.#fetchScoresInBatch(sub_step_key_ids, step_key_ids, file_id);

		return plain_action_plans.map((action_plan) => {
			const origin = action_plan.action_plan_origin;
			if (origin) {
				const score_key = `${origin.table_name}_${origin.column_id}`;
				const correct_score = score_maps[score_key];
				if (correct_score !== undefined) {
					action_plan.score = correct_score;
				}
			}
			return action_plan;
		});
	}

	async #fetchScoresInBatch(sub_step_key_ids, step_key_ids, file_id) {
		const score_maps = {};

		try {
			if (sub_step_key_ids.length > 0) {
				const sub_step_results = await this.custom_report_sub_step_key_result_repository.findAll({
					where: {
						custom_report_sub_step_key_id: sub_step_key_ids
					},
					include: [
						{
							association: 'custom_report_result',
							where: {
								file_id: file_id
							},
							required: true
						}
					]
				});

				if (sub_step_results) {
					sub_step_results.forEach((result) => {
						const key = `custom_report_sub_step_keys_${result.custom_report_sub_step_key_id}`;
						score_maps[key] = result.score || 0;
					});
				}
			}

			if (step_key_ids.length > 0) {
				const step_results = await this.custom_report_step_key_result_repository.findAll({
					where: {
						custom_report_step_key_id: step_key_ids
					},
					include: [
						{
							association: 'custom_report_result',
							where: {
								file_id: file_id
							},
							required: true
						}
					]
				});

				if (step_results) {
					step_results.forEach((result) => {
						const key = `custom_report_step_keys_${result.custom_report_step_key_id}`;
						score_maps[key] = result.score || 0;
					});
				}
			}
		} catch (error) {
			logger.error(`[ActionPlan] service - #fetchScoresInBatch error: ${error.message}`);
		}

		return score_maps;
	}

	async countAllFromReportsDelayed(params) {
		logger.info('[ActionPlan] service - countAllFromReportsDelayed init', { params });
		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date
		} = params;

		const payload = {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date
		};

		const promises = [
			this.repository.countAllFromReports(payload),
			this.repository.countAllFromReports({ ...payload, delayed: true })
		];

		const [[total, error_total], [delayed, error_delayed]] = await Promise.all(promises);

		if (error_total) {
			logger.error(`${error_total.message}, stack trace - ${error_total.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (error_delayed) {
			logger.error(`${error_delayed.message}, stack trace - ${error_delayed.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[ActionPlan] service - countAllFromReportsDelayed finish');
		return { total, delayed };
	}

	async countAllFromReportsByStatus(params) {
		logger.info('[ActionPlan] service - countAllFromReportsByStatus init', { params });
		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date
		} = params;

		const payload = {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date
		};

		const promises = [
			this.repository.countAllFromReportsByStatus(payload),
			this.repository.countAllFromReportsByStatus({ ...payload, delayed: true })
		];

		const [[total, error_total], [delayed, error_delayed]] = await Promise.all(promises);

		if (error_total) {
			logger.error(`${error_total.message}, stack trace - ${error_total.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (error_delayed) {
			logger.error(`${error_delayed.message}, stack trace - ${error_delayed.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[ActionPlan] service - countAllFromReportsByStatus finish');
		return { total, delayed };
	}
}
