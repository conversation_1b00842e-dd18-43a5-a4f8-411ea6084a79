import _ from 'lodash';
import { <PERSON>Report, <PERSON>wa<PERSON><PERSON><PERSON>apper, <PERSON><PERSON><PERSON><PERSON> } from '../entities/index.js';
import {
	CUSTOM_REPORT_NAMES,
	CUSTOM_REPORT_SORT_ORDER,
	CUSTOM_REPORT_ACTION_TYPE
} from '../util/constants-custom-report.js';
import {
	logger,
	AppError,
	ENUM_STATUS_FILE,
	RESPONSE_ERROR_STATUS,
	RESPONSE_ERROR_ENTITIES,
	WORKER_SELF_EVALUATION_ENUM
} from '../helpers/index.js';
import { StorageContext } from '../utils/storage_context.js';

const IS_SERA_CUSTOM_REPORT = 'SERA';
const IS_BERA_CUSTOM_REPORT = 'BERA';

const { DATABASE_FAILED_PERFORM_QUERY } = RESPONSE_ERROR_STATUS;
const { USER, FILE, CO<PERSON><PERSON>Y, ORGANI<PERSON>ATION, CUSTOM_REPORT, WORKER_SELF_EVALUATION, CUSTOM_REPORT_RESULT } =
	RESPONSE_ERROR_ENTITIES;

export class CustomReportService {
	#file_join_mapper;

	constructor({
		repository,
		file_repository,
		user_repository,
		company_repository,
		organization_repository,
		bera_job_summary_repository,
		custom_report_step_repository,
		custom_report_result_repository,
		custom_report_review_repository,
		characteristic_result_repository,
		work_condition_result_repository,
		worker_self_evaluation_repository,
		custom_report_step_key_result_repository,
		custom_report_sub_step_key_result_repository,
		custom_report_step_key_additional_item_result_repository
	}) {
		this.repository = repository;
		this.user_repository = user_repository;
		this.file_repository = file_repository;
		this.custom_report = new CustomReport();
		this.#file_join_mapper = new FileJoiner();
		this.company_repository = company_repository;
		this.organization_repository = organization_repository;
		this.bera_job_summary_repository = bera_job_summary_repository;
		this.custom_report_step_repository = custom_report_step_repository;
		this.custom_report_result_repository = custom_report_result_repository;
		this.custom_report_review_repository = custom_report_review_repository;
		this.work_condition_result_repository = work_condition_result_repository;
		this.characteristic_result_repository = characteristic_result_repository;
		this.worker_self_evaluation_repository = worker_self_evaluation_repository;
		this.custom_report_step_key_result_repository = custom_report_step_key_result_repository;
		this.custom_report_sub_step_key_result_repository = custom_report_sub_step_key_result_repository;
		this.custom_report_step_key_additional_item_result_repository =
			custom_report_step_key_additional_item_result_repository;
	}

	async findById(id) {
		logger.info('[CustomReport] service - findById init', { params: { id } });
		const data = await this.repository.findByPk(id);

		if (!data) {
			throw new AppError(CUSTOM_REPORT.NOT_FOUND);
		}

		logger.info('[CustomReport] service - findById finish');
		return data;
	}

	async findByName(name) {
		logger.info('[CustomReport] service - findByName init', { params: { name } });
		const data = await this.repository.findOne({
			where: { name }
		});

		if (!data) {
			throw new AppError(CUSTOM_REPORT.NOT_FOUND);
		}

		logger.info('[CustomReport] service - findByName finish');
		return data;
	}

	async findByAcronym(acronym) {
		logger.info('[CustomReport] service - findByAcronym init', { params: { acronym } });
		const data = await this.repository.findOne({
			where: {
				acronym
			}
		});

		if (!data) {
			throw new AppError(CUSTOM_REPORT.NOT_FOUND);
		}

		logger.info('[CustomReport] service - findByAcronym finish');
		return data;
	}

	async findAll() {
		logger.info('[CustomReport] service - findAll init');
		const existing_custom_reports = await this.repository.findAll();
		const sorted_custom_reports = existing_custom_reports.sort((a, b) => {
			return CUSTOM_REPORT_SORT_ORDER.indexOf(a.name) - CUSTOM_REPORT_SORT_ORDER.indexOf(b.name);
		});
		logger.info('[CustomReport] service - findAll finish');
		return sorted_custom_reports;
	}

	async findFilesByCustomReportId(payload) {
		logger.info('[CustomReport] service - findFilesByCustomReportId init');
		const {
			custom_report_id,
			bera_job_summary_id,
			sera_summary_id,
			report_type,
			organization_id,
			company_id,
			limit,
			offset,
			file_name
		} = payload;

		const existing_custom_report = await this.repository.findOne({
			where: {
				id: custom_report_id
			},
			attributes: ['id', 'name', 'updated_at']
		});

		const include_custom_report = {
			association: 'custom_report',
			where: {
				id: existing_custom_report.id
			},
			required: true
		};

		const custom_report_json = existing_custom_report.toJSON();
		const include_task = {
			association: 'task',
			required: false
		};

		const include_sector = {
			association: 'sector',
			required: false,
			where: {
				company_id
			},
			attributes: ['id', 'name']
		};

		const include_line = {
			association: 'line',
			required: false,
			attributes: ['id', 'name'],
			include: [include_sector]
		};

		const include_workstation = {
			association: 'workstations',
			required: false,
			attributes: ['id', 'name'],
			include: [include_line]
		};

		const find_all = {
			where: {
				is_active: true,
				organization_id,
				company_id,
				status: ENUM_STATUS_FILE.PROCESSED
			},
			distinct: true,
			attributes: ['id', 'original_name', 'generated_name', 'updated_at'],
			include: [include_custom_report, include_task, include_workstation],
			limit,
			offset: offset * limit
		};

		if (StorageContext.getStore()?.environment === 'sandbox') {
			logger.info('[CustomReport] service - findFilesByCustomReportId - find_all', {
				user_id: StorageContext.getStore().user_id
			});
			_.set(find_all, 'where', {
				...find_all.where,
				user_id: StorageContext.getStore().user_id
			});
		}

		if (report_type === IS_SERA_CUSTOM_REPORT) {
			const { sera_report } = this.custom_report.setSeraSummaryInclude(sera_summary_id);
			_.set(include_task, 'include', sera_report);
			if (sera_summary_id) {
				_.set(find_all, 'limit', 100);
			}
		}

		if (file_name) {
			_.set(find_all, 'where', {
				...find_all.where,
				...this.custom_report.filterByFileName(file_name)
			});
		}

		if (bera_job_summary_id || report_type === IS_BERA_CUSTOM_REPORT) {
			const { bera_report, kim_mho, kim_push_pull, niosh, strain_index } =
				this.custom_report.setBeraSummaryInclude();

			find_all.include.unshift(bera_report);
			find_all.include.push(niosh, strain_index, kim_mho, kim_push_pull);

			const include_bera_job_summary_files = {
				association: 'bera_job_summary_files',
				required: false,
				attributes: ['id', 'bera_job_summary_id', 'file_id']
			};

			find_all.include.push(include_bera_job_summary_files);

			const existing_bera_job_summary_result = await this.bera_job_summary_repository.findByPk(
				bera_job_summary_id,
				{
					include: [bera_report]
				}
			);

			if (existing_bera_job_summary_result?.overall_score > 0) {
				_.set(bera_report, 'required', true);
				_.set(find_all, 'limit', existing_bera_job_summary_result?.bera_report.length);
			}

			_.set(find_all, 'where', {
				...find_all.where,
				...this.custom_report.filterToBeraFiles(
					existing_bera_job_summary_result?.bera_report,
					bera_job_summary_id
				)
			});

			find_all.distinct = true;
			find_all.subQuery = false;
		}

		logger.info('[CustomReport] service - findFilesByCustomReportId - find_all', { find_all });
		const existing_custom_report_files = await this.file_repository.findAndCountAll(find_all);

		const unique_files = _.uniqBy(existing_custom_report_files.rows, 'id');
		const mapped_custom_report_files = unique_files.map((file) => {
			const plain = file.toJSON();
			if (
				(bera_job_summary_id || report_type === IS_BERA_CUSTOM_REPORT) &&
				Array.isArray(plain.bera_job_summary_files)
			) {
				plain.bera_job_summary_files = plain.bera_job_summary_files[0] || null;
			}

			return {
				...plain,
				task_id: plain.task ? plain.task[0]?.id : undefined,
				task_name: plain.task ? (plain.task[0]?.description ?? plain.task[0]?.name) : undefined,
				CustomReportsFiles: plain.custom_report[0]?.CustomReportsFiles
			};
		});

		if (sera_summary_id) {
			const rows = mapped_custom_report_files.filter((file) =>
				file.task[0]?.sera_report?.find((report) => report.review?.sera_summary_id === sera_summary_id)
			);
			if (rows.length > 0) {
				return {
					...custom_report_json,
					files: {
						count: rows.length,
						rows
					}
				};
			}
		}

		logger.info('[CustomReport] service - findFilesByCustomReportId - mapped_custom_report_files', {
			mapped_custom_report_files
		});

		logger.info('[CustomReport] service - findFilesByCustomReportId finish');
		return {
			...custom_report_json,
			files: {
				count: existing_custom_report_files.count,
				rows: mapped_custom_report_files
			}
		};
	}

	async getReportsToFillOut({
		user_id,
		file_id,
		company_id,
		action_type,
		organization_id,
		custom_report_id,
		custom_report_name,
		original_custom_report_result_id
	}) {
		logger.info('[CustomReport] service - getReportsToFillOut init');
		await this.#getUserById(user_id);
		await this.#getCompanyById(company_id);
		await this.#getOrganizationById(organization_id);

		const result = await this.#getCustomReportResultOrLastReviewResult({
			file_id,
			custom_report_id,
			original_custom_report_result_id
		});
		const file = await this.#getFileWithHierarchyAndToolsById(file_id, result, custom_report_name);
		const options = this.#defineGetReportOptions(result?.id);
		const existing_custom_report = await this.#getReportById(custom_report_id, options);
		const custom_report = existing_custom_report.get({ plain: true });
		const initial_values_mapper = new EwaD86Mapper();
		const nothing_stressful = await this.#findWorkerSelfEvaluationByName(
			WORKER_SELF_EVALUATION_ENUM.NOTHING_STRESSFUL
		);
		const previous_step_key_result_scores = await this.#findPreviousStepKeyResults(result);

		logger.info('teste', { file: JSON.stringify(file) });

		const response = await initial_values_mapper.setResponseInitialValue({
			file,
			result,
			file_id,
			custom_report,
			nothing_stressful,
			previous_step_key_result_scores
		});

		const step_keys = custom_report.step.flatMap(({ step_key }) => step_key);
		const sub_step_keys = step_keys
			.flatMap(({ sub_step_keys }) => sub_step_keys)
			.filter((sub_step_key) => sub_step_key);

		let steps_initial_values = initial_values_mapper.setStepsInitialValues({
			step_keys,
			sub_step_keys,
			steps: custom_report.step
		});

		let current_step;
		if (result) {
			current_step = await this.custom_report_step_repository.findByPk(result.current_step_id);
		}

		const is_last_step = current_step?.sequence === response.total_steps - 1;
		const is_completed = result?.worst_score > 0 && is_last_step;
		const is_creating_review = result && action_type === CUSTOM_REPORT_ACTION_TYPE.REVIEW && is_completed;
		const is_editing = result && (action_type === CUSTOM_REPORT_ACTION_TYPE.REPORT || !is_completed);
		const has_action_type = !!action_type;

		if ((result && !has_action_type) || is_editing) {
			const { additional_item_results_for_step_key_and_additional_item_result_id } =
				await this.#setResponseAdditionalItemsResults(result, response);

			steps_initial_values = await this.#setStepKeysResultByStep({
				step_keys,
				sub_step_keys,
				steps_initial_values,
				initial_values_mapper,
				steps: custom_report.step,
				custom_report_result_id: result.id,
				additional_item_results_for_step_key_and_additional_item_result_id
			});

			const workstation_id = file.workstations.id;
			response.comment = result.comment;
			response.current_step = current_step.sequence;
			response.is_completed = result.worst_score > 0;
			response.current_step_name = current_step.name;
			await this.#setResultWorkCondition({ workstation_id, response, initial_values_mapper });
			await this.#setResultCharacteristic({ workstation_id, response, initial_values_mapper, nothing_stressful });
		}

		if (is_creating_review) {
			response.file_id = undefined;
		}

		logger.info('[CustomReport] service - getReportsToFillOut finish');
		return {
			...response,
			...steps_initial_values
		};
	}

	async getWorstScores(payload) {
		logger.info('[CustomReport] service - getWorstScores init');
		const {
			custom_report_id,
			created_at_start,
			organization_id,
			created_at_end,
			workstation_id,
			companies_ids,
			company_id,
			sector_id,
			line_id,
			offset,
			limit
		} = payload;

		const custom_report = await this.repository.findByPk(custom_report_id);

		if (!custom_report) {
			throw new AppError(CUSTOM_REPORT.NOT_FOUND);
		}

		const [result, error] = await this.repository.getHierarchyWorstScore({
			custom_report_name: custom_report.name,
			custom_report_id,
			created_at_start,
			organization_id,
			created_at_end,
			workstation_id,
			companies_ids,
			company_id,
			sector_id,
			line_id,
			offset,
			limit
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		const { rows, total, entity } = result;
		const mapped_rows = rows.map((m) => this.#getWorstRiskMapper(m, entity));

		logger.info('[CustomReport] service - getWorstScores finish');
		return {
			rows: mapped_rows,
			entity,
			offset,
			limit,
			total
		};
	}

	#getWorstRiskMapper(row, entity) {
		if (entity === 'file') {
			const name_without_extension = row.name.split('.').slice(0, -1).join('.');
			return {
				...row,
				name: name_without_extension
			};
		}

		return row;
	}

	async #findPreviousStepKeyResults(result) {
		let previous_step_key_result_rpns = null;

		if (result?.previous_custom_report_result) {
			const step_key_results = await this.custom_report_step_key_result_repository.findAll({
				where: {
					custom_report_result_id: result.previous_custom_report_result.id
				}
			});

			previous_step_key_result_rpns = step_key_results.reduce((scores, current_step_key_result) => {
				const step_key_result_scores = {
					score: current_step_key_result.score,
					result: current_step_key_result.result
				};

				return {
					...scores,
					[current_step_key_result.custom_report_step_key_id]: step_key_result_scores
				};
			}, {});
		}
		return previous_step_key_result_rpns;
	}

	async #getCustomReportResultOrLastReviewResult({ file_id, custom_report_id, original_custom_report_result_id }) {
		if (!original_custom_report_result_id && !file_id) {
			return undefined;
		}

		if (original_custom_report_result_id) {
			const last_review = await this.custom_report_review_repository.findLastReviewByOriginalResultId(
				original_custom_report_result_id
			);
			const last_custom_report_result = await this.#getCustomReportResultById(
				last_review.custom_report_result_id
			);
			const previous_custom_report_result = await this.#getPreviousResult(
				last_review.version,
				original_custom_report_result_id
			);

			return {
				...last_custom_report_result.get({ plain: true }),
				previous_custom_report_result
			};
		}

		const custom_report_result = await this.custom_report_result_repository.findOne({
			where: {
				file_id,
				custom_report_id
			}
		});

		return custom_report_result?.get({ plain: true });
	}

	async #getPreviousResult(current_version, original_custom_report_result_id) {
		let previous_custom_report_result = null;

		if (current_version > 1) {
			const previous_review = await this.custom_report_review_repository.findOne({
				where: {
					original_custom_report_result_id,
					version: current_version - 1
				}
			});
			const custom_report_result_response = await this.#getCustomReportResultById(
				previous_review.custom_report_result_id
			);

			previous_custom_report_result = custom_report_result_response.get({ plain: true });
		}

		return previous_custom_report_result;
	}

	async #getCustomReportResultById(custom_report_result_id) {
		const custom_report_result = await this.custom_report_result_repository.findByPk(custom_report_result_id);

		if (!custom_report_result) {
			throw new AppError(CUSTOM_REPORT_RESULT.NOT_FOUND);
		}

		return custom_report_result;
	}

	async #setResponseAdditionalItemsResults(result, response) {
		const additional_items_results = await this.#getAllAdditionalItemsResults(result);

		const {
			additional_item_results_for_step_key,
			additional_item_results_for_step_key_and_additional_item_result_id
		} = this.#setAdditionalItemResultsForStepKey(additional_items_results);

		this.#updateStepKeysAdditionalItemsResponse(response, additional_item_results_for_step_key);

		return {
			additional_item_results_for_step_key,
			additional_item_results_for_step_key_and_additional_item_result_id
		};
	}

	#updateStepKeysAdditionalItemsResponse(response, additional_item_results_for_step_key) {
		response.steps = response.steps.map((step) => {
			const mapped_step_keys = step.step_keys?.map((step_key) => {
				return {
					...step_key,
					additional_items: step_key?.additional_items?.map((additional_item) => ({
						...additional_item,
						results: additional_item_results_for_step_key[step_key.id] ?? []
					}))
				};
			});
			return {
				...step,
				step_keys: mapped_step_keys
			};
		});
	}

	#setAdditionalItemResultsForStepKey(additional_items_results) {
		const additional_item_results_for_step_key = additional_items_results?.reduce(
			(additional_item_result_for_step_key, current_additional_item_result) => {
				const step_key_id = current_additional_item_result.step_key_additional_item.custom_report_step_key_id;
				const previous_result = additional_item_result_for_step_key[step_key_id] ?? [];
				const updated_result = [...previous_result];
				const new_result = {
					...current_additional_item_result,
					additional_item_result_id: current_additional_item_result.id,
					description: current_additional_item_result.option_1?.description
				};
				updated_result.push(new_result);
				return {
					...additional_item_result_for_step_key,
					[step_key_id]: updated_result
				};
			},
			{}
		);

		const additional_item_results_for_step_key_and_additional_item_result_id = additional_items_results?.reduce(
			(additional_item_result_for_step_key, current_additional_item_result) => {
				const step_key_id = current_additional_item_result.step_key_additional_item.custom_report_step_key_id;
				const new_result = {
					[current_additional_item_result.step_key_additional_item.additional_item_id]: {
						...current_additional_item_result,
						additional_item_result_id: current_additional_item_result.id
					}
				};
				return {
					...additional_item_result_for_step_key,
					[step_key_id]: new_result
				};
			},
			{}
		);

		return {
			additional_item_results_for_step_key,
			additional_item_results_for_step_key_and_additional_item_result_id
		};
	}

	async #getAllAdditionalItemsResults(result) {
		const additional_items_results_response =
			await this.custom_report_step_key_additional_item_result_repository.findAll({
				where: {
					custom_report_result_id: result.id
				},
				include: [
					{
						association: 'step_key_additional_item',
						attributes: ['additional_item_id', 'custom_report_step_key_id']
					},
					{
						association: 'option_1',
						attributes: ['description']
					}
				]
			});

		const additional_items_results = additional_items_results_response?.map((result) =>
			result?.get({ plain: true })
		);
		return additional_items_results;
	}

	async #setStepKeysResultByStep({
		steps,
		step_keys,
		sub_step_keys,
		steps_initial_values,
		initial_values_mapper,
		custom_report_result_id,
		additional_item_results_for_step_key_and_additional_item_result_id
	}) {
		const sub_step_keys_ids = sub_step_keys.map(({ id }) => id);
		const step_keys_ids = step_keys.map(({ id }) => id);
		const custom_report_sub_step_keys_results = await this.#getSubStepKeysResultsBySubStepKeyIds({
			sub_step_keys_ids,
			custom_report_result_id
		});
		const custom_report_step_keys_results = await this.#getStepKeysResultsByStepKeyIds({
			step_keys_ids,
			custom_report_result_id
		});
		const step_keys_results_by_step = initial_values_mapper.mapStepKeysResultsByStep({
			custom_report_step_keys_results,
			custom_report_sub_step_keys_results,
			additional_item_results_for_step_key_and_additional_item_result_id
		});
		return steps.reduce((initial_values, current_step) => {
			const { id } = current_step;
			return {
				...initial_values,
				[id]: {
					...steps_initial_values[id],
					...step_keys_results_by_step[id]
				}
			};
		}, {});
	}

	async #getSubStepKeysResultsBySubStepKeyIds({ sub_step_keys_ids, custom_report_result_id }) {
		return await this.custom_report_sub_step_key_result_repository.findAll({
			attributes: [
				'id',
				'score',
				'result',
				'exposure_id',
				'severity_id',
				'risk_damage_id',
				'risk_category_id',
				'vulnerability_id',
				'risk_description_id',
				'custom_report_sub_step_key_id'
			],
			include: [
				{
					association: 'sub_step_key',
					attributes: ['id'],
					include: [
						{
							association: 'step_key',
							attributes: ['id']
						}
					]
				}
			],
			where: {
				custom_report_result_id,
				custom_report_sub_step_key_id: sub_step_keys_ids
			}
		});
	}

	async #getStepKeysResultsByStepKeyIds({ step_keys_ids, custom_report_result_id }) {
		return this.custom_report_step_key_result_repository.findAll({
			attributes: [
				'id',
				'score',
				'result',
				'exposure_id',
				'severity_id',
				'risk_damage_id',
				'risk_category_id',
				'vulnerability_id',
				'risk_description_id',
				'custom_report_step_key_id'
			],
			include: [
				{
					association: 'step_key',
					attributes: ['id'],
					include: [
						{
							association: 'step',
							attributes: ['id']
						}
					]
				}
			],
			where: {
				custom_report_result_id,
				custom_report_step_key_id: step_keys_ids
			}
		});
	}

	#defineGetReportOptions(custom_report_result_id) {
		return {
			attributes: ['id', 'name', 'description', 'acronym'],
			include: [
				{
					association: 'step',
					attributes: ['id', 'name', 'description', 'sequence'],
					include: [
						{
							association: 'step_key',
							attributes: ['id', 'name', 'description', 'sequence', 'custom_report_step_id'],
							include: [
								{
									association: 'sub_step_keys',
									required: false,
									attributes: ['id', 'name', 'description', 'sequence', 'custom_report_step_key_id'],
									include: [
										{
											association: 'custom_report_sub_step_key_result',
											required: false,
											where: {
												custom_report_result_id: custom_report_result_id ?? ''
											},
											attributes: ['id', 'result']
										}
									]
								},
								{
									association: 'custom_report_step_key_result',
									required: false,
									where: {
										custom_report_result_id: custom_report_result_id ?? ''
									},
									attributes: ['id', 'result']
								},
								{
									association: 'ergonomic_tool',
									required: false,
									attributes: ['id', 'name', 'description', 'title', 'subtitle']
								},
								{
									association: 'additional_items',
									required: false,
									attributes: [
										'id',
										'name',
										'title_1',
										'unit_1',
										'title_2',
										'unit_2',
										'title_3',
										'unit_3',
										'title_4',
										'unit_4',
										'title_5',
										'unit_5',
										'title_6',
										'unit_6'
									],
									include: [
										{
											association: 'additional_item_type',
											required: false,
											attributes: ['id', 'type']
										}
									]
								}
							]
						}
					]
				}
			],
			order: [
				['step', 'sequence', 'ASC'],
				['step', 'step_key', 'sequence', 'ASC'],
				['step', 'step_key', 'sub_step_keys', 'sequence', 'ASC']
			]
		};
	}

	async #getUserById(user_id) {
		const user = await this.user_repository.findByPk(user_id);
		if (!user) {
			throw new AppError(USER.NOT_FOUND);
		}
		return user;
	}

	async #getOrganizationById(organization_id) {
		const organization = await this.organization_repository.findByPk(organization_id);
		if (!organization) {
			throw new AppError(ORGANIZATION.NOT_FOUND);
		}
		return organization;
	}

	async #getCompanyById(company_id) {
		const company = await this.company_repository.findByPk(company_id);
		if (!company) {
			throw new AppError(COMPANY.NOT_FOUND);
		}
		return company;
	}

	async #getFileWithHierarchyAndToolsById(file_id, result, custom_report_name) {
		let file = null;

		const file_response = await this.file_repository.findByPk(file_id ?? result?.file_id, {
			attributes: [
				'id',
				'createdAt',
				'generated_name',
				'original_name',
				'duration',
				'organization_id',
				'company_id'
			],
			where: {
				isActive: true
			},
			include: [
				this.#file_join_mapper.includeAllHierarchy(),
				{
					required: false,
					association: 'reba',
					attributes: ['id', 'file_id'],
					where: {
						is_active: true
					}
				},
				{
					required: false,
					association: 'niosh',
					attributes: ['id', 'file_id'],
					where: {
						is_active: true
					}
				},
				{
					required: false,
					association: 'kim_mho',
					attributes: ['id', 'file_id'],
					where: {
						is_active: true
					}
				},
				{
					required: false,
					association: 'angle_time',
					attributes: ['id', 'file_id'],
					where: {
						is_active: true
					}
				},
				{
					required: false,
					association: 'strain_index',
					attributes: ['id', 'file_id'],
					where: {
						is_active: true
					}
				},
				{
					required: false,
					association: 'kim_push_pull',
					attributes: ['id', 'file_id'],
					where: {
						is_active: true
					}
				},
				{
					required: false,
					association: 'liberty_mutual',
					attributes: ['id', 'file_id']
				}
			]
		});

		if (!file_response && custom_report_name === CUSTOM_REPORT_NAMES.EWA_D86) {
			throw new AppError(FILE.NOT_FOUND);
		}

		if (file_response) {
			file = file_response.get({ plain: true });
		}

		return file;
	}

	async #getReportById(report_id, options) {
		const report = await this.repository.findByPk(report_id, {
			...options
		});
		if (!report) {
			throw new AppError(CUSTOM_REPORT.NOT_FOUND);
		}
		return report;
	}

	async #setResultWorkCondition({ workstation_id, response, initial_values_mapper }) {
		const work_conditions = await this.#getWorkConditionForResultByWorkstationId(workstation_id);
		const mapped_work_conditions = initial_values_mapper.mapWorkConditionsResult(work_conditions);
		response.work_conditions = mapped_work_conditions;
	}

	async #getWorkConditionForResultByWorkstationId(workstation_id) {
		return this.work_condition_result_repository.findOne({
			where: {
				workstation_id
			},
			attributes: [
				'id',
				'work_schedule',
				'place_description',
				'expected_task_description',
				'performed_task_description'
			]
		});
	}

	async #setResultCharacteristic({ workstation_id, response, initial_values_mapper, nothing_stressful }) {
		const characteristic = await this.#getCharacteristicForResultByWorkstationId(workstation_id);
		let mapped_characteristic = initial_values_mapper.mapCharacteristicsResult(characteristic, nothing_stressful);
		response.characteristics = mapped_characteristic;
	}

	async #getCharacteristicForResultByWorkstationId(workstation_id) {
		return this.characteristic_result_repository.findOne({
			where: {
				workstation_id
			},
			attributes: [
				'id',
				'working_population_men',
				'working_population_women',
				'total_working_population',
				'working_population_others',
				'particularities_description',
				'worker_verbalization_description'
			],
			include: [
				{
					association: 'worker_self_evaluation',
					attributes: ['id', 'name', 'description']
				}
			]
		});
	}

	async #findWorkerSelfEvaluationByName(name) {
		const worker_self_evaluation = await this.worker_self_evaluation_repository.findOne({
			where: {
				name
			}
		});
		if (!worker_self_evaluation) {
			throw new AppError(WORKER_SELF_EVALUATION.NOT_FOUND);
		}
		return worker_self_evaluation;
	}

	async findAllTypes(params) {
		logger.info('[CustomReport] service - findAllTypes init', { params });
		const { companies_with_user_access, selected_company_id } = params;

		if (selected_company_id && !companies_with_user_access.includes(selected_company_id)) {
			throw new AppError(COMPANY.NOT_AUTHORIZED);
		}

		if (!companies_with_user_access.length) {
			throw new AppError(COMPANY.NOT_AUTHORIZED);
		}

		const [data, error] = await this.repository.findAllTypes(params);

		if (error) {
			logger.error('[CustomReport] service - findAllTypes error', { error });
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[CustomReport] service - findAllTypes success');
		return data;
	}
}
