import { ERROR_RESPONSE_ENTITIES_ENUM } from '../util/enum.js';
import { AppError } from '../helpers/errors.js';
import { logger } from '../helpers/logger.js';

export class StressLevelService {
	constructor({ repository, custom_report_step_key_repository }) {
		this.repository = repository;
		this.custom_report_step_key_repository = custom_report_step_key_repository;
	}

	async findById(id) {
		logger.info('[StressLevel] service - findById init');
		try {
			const existing_stress_level = await this.repository.findByPk(id);

			if (!existing_stress_level) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.STRESS_LEVEL.NOT_FOUND);
			}
			logger.info('[StressLevel] service - findById finish');
			return existing_stress_level;
		} catch (error) {
			logger.error('[StressLevel] service - findById error', { error });
			throw error;
		}
	}

	async findAllByStepKeyId(custom_report_step_key_id) {
		logger.info('[StressLevel] service - findAllByStepKeyId init');
		let order = [['score', 'ASC']];
		const custom_report_step_key = await this.custom_report_step_key_repository.findByPk(custom_report_step_key_id);
		if (custom_report_step_key.name === 'lifting') {
			order = [
				['description', 'ASC'],
				['score', 'ASC']
			];
		}
		const stress_levels = await this.repository.findAllByForeignKey({
			where: {
				custom_report_step_key_id
			},
			order
		});
		logger.info('[StressLevel] service - findAllByStepKeyId finish');
		return stress_levels;
	}
}
