import moment from 'moment';
import { Puppeteer } from '../util/puppeteer/index.js';
import { StorageContext } from '../utils/storage_context.js';
import { StrainIndexCalculator } from '../entities/index.js';
import { formatDecimalSeparator, getWorkspace } from '../util/index.js';
import {
	i18n,
	Socket,
	logger,
	AppError,
	SOCKET_EVENTS,
	RESPONSE_ERROR_STATUS,
	RESPONSE_ERROR_ENTITIES
} from '../helpers/index.js';

const PERCENTAGE_VALUE = 100;
const ONE_MINUTE_IN_SECONDS = 60;
const { STRAIN_INDEX_REPORT } = SOCKET_EVENTS;
const { REPORT, FILE, WORKSTATION } = RESPONSE_ERROR_ENTITIES;
const { DATABASE_FAILED_PERFORM_QUERY } = RESPONSE_ERROR_STATUS;

export class StrainIndexReportService {
	constructor({ repository, file_repository, workstation_repository }) {
		this.repository = repository;
		this.file_repository = file_repository;
		this.workstation_repository = workstation_repository;
	}

	async show(file_id) {
		logger.info('[StrainIndexReport] service - show init');
		const report = await this.repository.findOne({
			where: {
				file_id,
				is_active: true
			},
			raw: true
		});

		if (!report) {
			logger.info('[StrainIndexReport] service - show finish');
			return { message: 'This file has not been analyzed yet.', status: 'failed' };
		}

		logger.info('[StrainIndexReport] service - show finish');
		return report;
	}

	async create({ report_input, user_id }) {
		logger.info('[StrainIndexReport] service - create init');
		const { file_id } = report_input;

		let transaction;

		try {
			let file = await this.file_repository.findOne({
				where: {
					id: file_id,
					is_active: 1
				}
			});

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			const report = await this.repository.findOne({
				where: {
					file_id,
					is_active: true
				}
			});

			if (report) {
				throw new AppError(REPORT.ALREADY_CREATED);
			}

			const workstation = await this.workstation_repository.findOne({
				where: {
					id: report_input.workstation_id
				},
				attributes: ['name']
			});

			if (!workstation) {
				throw new AppError(WORKSTATION.NOT_FOUND);
			}

			const strain_index = new StrainIndexCalculator(report_input);
			const report_results = strain_index.calculateResults();

			const payload = {
				...report_input,
				...report_results,
				report_user_id: user_id
			};

			transaction = await this.repository.db.sequelize.transaction();

			const created = await this.repository.create(payload, {
				transaction
			});

			if (!created) {
				throw new AppError(REPORT.FAIL_CREATE);
			}

			const result = created.get({ plain: true });

			file.workstation = workstation?.name;
			file.sector_id = report_input.sector_id;
			file.workstation_id = report_input.workstation_id;

			await file.save({ transaction });
			await transaction.commit();

			this.#sendDataToSocket(file, created);

			logger.info('[StrainIndexReport] service - create finish');
			return result;
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async update({ report_input, user_id }) {
		logger.info('[StrainIndexReport] service - update init');
		const { report_id, file_id } = report_input;

		let transaction;
		try {
			const report = await this.repository.findOne({
				where: {
					id: report_id,
					is_active: 1
				}
			});

			if (!report) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			let file = await this.file_repository.findOne({
				where: { id: file_id, is_active: 1 }
			});

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			const strain_index_tool = new StrainIndexCalculator(report_input);
			const report_results = strain_index_tool.calculateResults();

			const payload = {
				...report_input,
				...report_results,
				report_user_id: user_id
			};

			transaction = await this.repository.db.sequelize.transaction();

			file.sector_id = report_input.sector_id;

			const workstation = await this.repository.db.Workstation.findOne({
				where: {
					id: report_input.workstation_id
				},
				attributes: ['name']
			});

			file.workstation = workstation?.name;
			file.workstation_id = report_input.workstation_id;

			await file.save({ transaction });
			await report.update(payload, { transaction });

			await transaction.commit();

			this.#sendDataToSocket(file, report);

			const result = report.dataValues;

			logger.info('[StrainIndexReport] service - update finish');
			return result;
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async updateComment({ report_input, user_id }) {
		logger.info('[StrainIndexReport] service - updateComment init');
		const { report_id } = report_input;

		let transaction;
		try {
			const report = await this.repository.findOne({
				where: {
					id: report_id,
					is_active: 1
				}
			});

			if (!report) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			report.comment = report_input.comment;
			report.report_user_id = user_id;

			await report.save({ transaction });
			await transaction.commit();

			const result = report.dataValues;

			logger.info('[StrainIndexReport] service - updateComment finish');
			return result;
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async generatePDF({ file_id, locale }) {
		logger.info('[StrainIndexReport] service - generatePDF init');
		let report_data;
		const language = locale.substring(0, 2);
		i18n.changeLanguage(language);

		try {
			const report = await this.repository.db.StrainIndexReport.findOne({
				where: { file_id, is_active: 1 },
				include: [
					{
						association: 'report_user'
					},
					{
						association: 'file',
						include: [
							{
								association: 'sector',
								attributes: ['name'],
								include: [
									{
										association: 'company',
										attributes: ['name'],
										include: [
											{
												association: 'Organization',
												attributes: ['name']
											}
										]
									}
								]
							},
							{
								association: 'workstations',
								attributes: ['name'],
								include: [
									{
										association: 'line',
										attributes: ['name']
									}
								]
							}
						]
					}
				]
			}).then((data) => (report_data = data.get({ plain: true })));

			if (!report) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			const formattedReport = this.formatDataForPDF(report_data, locale);
			const labels = this.getLabelsForPDF(formattedReport);

			const payload = {
				title: ['Report Results', 'Revised Strain Index'],
				...formattedReport,
				labels,
				language
			};

			const puppeteer = new Puppeteer();
			const html = await puppeteer.render('strain_index', payload);

			const browser = await puppeteer.init();
			const buffer = puppeteer.mounting(browser, html);

			logger.info('[StrainIndexReport] service - generatePDF finish');
			return buffer;
		} catch (error) {
			throw error;
		}
	}

	formatDataForPDF(report, locale) {
		const previousMomentLocale = moment.locale();
		// Format report for displaying data (using locale settings for decimal separator and moment object as string)
		moment.locale(locale);

		let formattedReport = {
			...report,
			file: {
				...report.file,
				sector: {
					...report.file.sector,
					company: {
						...report.file.sector.company,
						organization: {
							...report.file.sector.company.Organization
						}
					}
				},
				workstation: {
					...report.file.workstations,
					line: {
						...report.file.workstations.line
					}
				}
			}
		};

		const efforts_per_minute = this.#effortsCalculation(formattedReport);

		formattedReport = { ...formattedReport, ...efforts_per_minute };

		for (const key in formattedReport) {
			formattedReport[key] = formatDecimalSeparator(formattedReport[key], locale);
		}

		formattedReport.file = {
			...formattedReport.file,
			createdAt: moment(formattedReport.file.createdAt).format('L')
		};
		formattedReport.collection_date = moment(formattedReport.collection_date).format('L');

		moment.locale(previousMomentLocale);
		return formattedReport;
	}

	getLabelsForPDF({
		input_left_wrist_posture,
		input_right_wrist_posture,
		input_right_efforts_per_minute,
		input_left_efforts_per_minute
	}) {
		const labels = {
			FLEXION: 'Flexion',
			EXTENSION: 'Extension'
		};

		return {
			input_left_wrist_posture: labels[input_left_wrist_posture],
			input_right_wrist_posture: labels[input_right_wrist_posture],
			input_left_efforts_per_minute: input_left_efforts_per_minute,
			input_right_efforts_per_minute: input_right_efforts_per_minute
		};
	}

	async getGreatestScoreRsi(params) {
		logger.info('[StrainIndexReport] service - getGreatestScoreRsi init', { params });
		const { organization_id, company_id, sector_id, line_id, workstation_id, start_date, end_date, companies_ids } =
			params;

		let user_id = '';

		if (StorageContext.getStore()?.environment === 'sandbox') {
			user_id = StorageContext.getStore().user_id;
		}

		const [result, error] = await this.repository.getGreatestScoreRsi({
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date,
			companies_ids
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[StrainIndexReport] service - getGreatestScoreRsi finish');
		return result;
	}

	#calculateEffortPerMinute(exertions, observationTime) {
		return exertions / (observationTime / ONE_MINUTE_IN_SECONDS);
	}

	#calculatePercentageOfEffort(value) {
		return Math.round(PERCENTAGE_VALUE * value) / PERCENTAGE_VALUE;
	}

	#effortsCalculation(parameters) {
		const {
			input_left_exertions,
			input_right_exertions,
			input_left_observation_time,
			input_right_observation_time
		} = parameters;

		const input_left_efforts_per_minute = this.#calculateEffortPerMinute(
			input_left_exertions,
			input_left_observation_time
		);

		const input_right_efforts_per_minute = this.#calculateEffortPerMinute(
			input_right_exertions,
			input_right_observation_time
		);

		const left_effort = this.#calculatePercentageOfEffort(input_left_efforts_per_minute);
		const right_effort = this.#calculatePercentageOfEffort(input_right_efforts_per_minute);

		return { input_left_efforts_per_minute: left_effort, input_right_efforts_per_minute: right_effort };
	}

	#sendDataToSocket(file, report) {
		const io = Socket.getInstance().getIO();
		const roomId = getWorkspace({
			organization_id: file.organization_id,
			company_id: file.company_id
		});
		io.of('/report-status')
			.to(roomId)
			.emit(STRAIN_INDEX_REPORT, {
				status: true,
				data: {
					id: report.id,
					file_id: report.file_id,
					updated_at: report.updatedAt,
					score_left_rsi: report.score_left_rsi,
					score_right_rsi: report.score_right_rsi
				}
			});
	}
}
