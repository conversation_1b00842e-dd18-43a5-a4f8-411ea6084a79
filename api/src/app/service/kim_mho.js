import moment from 'moment';
import {
	i18n,
	Socket,
	logger,
	AppError,
	SOCKET_EVENTS,
	RESPONSE_ERROR_STATUS,
	RESPONSE_ERROR_ENTITIES
} from '../helpers/index.js';
import { getWorkspace } from '../util/index.js';
import { Puppeteer } from '../util/puppeteer/index.js';
import { KimManualHandlingCalculator } from '../entities/index.js';
import { StorageContext } from '../utils/storage_context.js';

const { KIM_MHO_REPORT } = SOCKET_EVENTS;
const { REPORT, FILE } = RESPONSE_ERROR_ENTITIES;
const { DATABASE_FAILED_PERFORM_QUERY } = RESPONSE_ERROR_STATUS;

export class KimManualHandlingReportService {
	constructor(repository) {
		this.repository = repository;
	}

	async show(fileId) {
		logger.info('[<PERSON><PERSON><PERSON>alHandlingReport] service - show init');
		try {
			const report = await this.repository.db.KimManualHandlingReport.findOne({
				where: {
					file_id: fileId,
					is_active: true
				},
				raw: true
			});

			if (!report) {
				logger.info('[KimManualHandlingReport] service - show finish');
				return { message: 'This file has not been analyzed yet.', status: 'failed' };
			}

			logger.info('[KimManualHandlingReport] service - show finish');
			return report;
		} catch (error) {
			throw error;
		}
	}

	async create({ report_input, user_id }) {
		logger.info('[KimManualHandlingReport] service - create init');
		const { file_id } = report_input;

		let transaction;

		try {
			let file = await this.repository.db.File.findOne({
				where: { id: file_id, is_active: 1 }
			});

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			const kim_mho = new KimManualHandlingCalculator(report_input);
			const report_results = kim_mho.calculateResults();

			const payload = {
				...report_input,
				...report_results,
				report_user_id: user_id
			};

			transaction = await this.repository.db.sequelize.transaction();

			const created = await this.repository.db.KimManualHandlingReport.create(payload, {
				transaction
			});

			if (!created) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			const result = created.get({ plain: true });

			file.sector_id = report_input.sector_id;

			const workstation = await this.repository.db.Workstation.findOne({
				where: {
					id: report_input.workstation_id
				},
				attributes: ['name']
			});

			file.workstation = workstation?.name;
			file.workstation_id = report_input.workstation_id;

			await file.save({ transaction });

			const io = Socket.getInstance().getIO();

			const roomId = getWorkspace({
				organization_id: file.organization_id,
				company_id: file.company_id
			});

			await transaction.commit();

			io.of('/report-status')
				.to(roomId)
				.emit(KIM_MHO_REPORT, {
					status: true,
					data: {
						id: created.id,
						file_id: created.file_id,
						risk_score: created.risk_score,
						risk_load: created.risk_load,
						updated_at: created.updated_at
					}
				});

			logger.info('[KimManualHandlingReport] service - create finish');
			return result;
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}

	async update({ report_input, user_id }) {
		logger.info('[KimManualHandlingReport] service - update init');
		const { report_id, file_id } = report_input;

		let transaction;
		try {
			const report = await this.repository.db.KimManualHandlingReport.findOne({
				where: {
					id: report_id,
					is_active: 1
				}
			});

			if (!report) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			let file = await this.repository.db.File.findOne({
				where: { id: file_id, is_active: 1 }
			});

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			const kim_mho_tool = new KimManualHandlingCalculator(report_input);
			const report_results = kim_mho_tool.calculateResults();

			const payload = {
				...report_input,
				...report_results,
				report_user_id: user_id
			};

			transaction = await this.repository.db.sequelize.transaction();

			file.sector_id = report_input.sector_id;

			const workstation = await this.repository.db.Workstation.findOne({
				where: {
					id: report_input.workstation_id
				},
				attributes: ['name']
			});

			file.workstation = workstation?.name;
			file.workstation_id = report_input.workstation_id;

			await file.save({ transaction });
			await report.update(payload, { transaction });

			await transaction.commit();

			const result = report.dataValues;

			logger.info('[KimManualHandlingReport] service - update finish');
			return result;
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}

	async updateComment({ report_input, user_id }) {
		logger.info('[KimManualHandlingReport] service - updateComment init');
		const { report_id } = report_input;

		let transaction;
		try {
			let report = await this.repository.db.KimManualHandlingReport.findOne({
				where: {
					id: report_id,
					is_active: 1
				}
			});

			if (!report) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			report.comment = report_input.comment;
			report.report_user_id = user_id;

			await report.save({ transaction });
			await transaction.commit();

			const result = report.dataValues;

			logger.info('[KimManualHandlingReport] service - updateComment finish');
			return result;
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}

	async generatePDF({ file_id, locale }) {
		logger.info('[KimManualHandlingReport] service - generatePDF init');
		let report_data;
		const language = locale.substring(0, 2);
		i18n.changeLanguage(language);

		try {
			const report = await this.repository.db.KimManualHandlingReport.findOne({
				where: { file_id, is_active: 1 },
				include: [
					{
						model: this.repository.db.User,
						as: 'report_user'
					},
					{
						model: this.repository.db.File,
						as: 'file',
						include: [
							{
								model: this.repository.db.Sector,
								as: 'sector',
								attributes: ['name'],
								include: [
									{
										model: this.repository.db.Company,
										as: 'company',
										attributes: ['name'],
										include: [
											{
												model: this.repository.db.Organization,
												as: 'Organization',
												attributes: ['name']
											}
										]
									}
								]
							},
							{
								model: this.repository.db.Workstation,
								as: 'workstations',
								attributes: ['name'],
								include: [
									{
										model: this.repository.db.Line,
										as: 'line',
										attributes: ['name']
									}
								]
							}
						]
					}
				]
			}).then((data) => (report_data = data.get({ plain: true })));

			if (!report) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			const formattedReport = this.formatDataForPDF(report_data, locale);
			const labels = this.getLabelsForPDF(formattedReport);

			const payload = {
				title: ['KIM', 'Manual Handling Operations'],
				...formattedReport,
				labels,
				language
			};

			const puppeteer = new Puppeteer();
			const html = await puppeteer.render('kim_mho', payload);

			const browser = await puppeteer.init();
			const buffer = puppeteer.mounting(browser, html);

			logger.info('[KimManualHandlingReport] service - generatePDF finish');
			return buffer;
		} catch (error) {
			throw error;
		}
	}

	formatDataForPDF(report, locale) {
		const previousMomentLocale = moment.locale();
		moment.locale(locale);

		const formattedReport = {
			...report,
			file: {
				...report.file,
				sector: {
					...report.file.sector,
					company: {
						...report.file.sector.company,
						organization: {
							...report.file.sector.company.Organization
						}
					}
				},
				workstation: {
					...report.file.workstations,
					line: {
						...report.file.workstations.line
					}
				}
			}
		};

		formattedReport.file = {
			...formattedReport.file,
			createdAt: moment(formattedReport.file.createdAt).format('L')
		};
		formattedReport.collection_date = moment(formattedReport.collection_date).format('L');

		moment.locale(previousMomentLocale);
		return formattedReport;
	}

	getLabelsForPDF({
		risk_load,
		left_force_intensity,
		right_force_intensity,
		force_transfer,
		arm_posture,
		temporal_distribution,
		work_conditions,
		body_posture
	}) {
		const labels = {
			RISK: {
				LOW: {
					title: 'Low',
					type: 'success'
				},
				SLIGHTLY_INCREASED: {
					title: 'Slightly increased',
					type: 'warning'
				},
				SUBSTANTIALLY_INCREASED: {
					title: 'Substantially increased',
					type: 'warning'
				},
				HIGH: {
					title: 'High',
					type: 'danger'
				}
			},
			DIAGNOSTICS: {
				LOW: {
					PHYSICAL_OVERLOAD: 'Physical overload is unlikely.',
					HEALTH_CONSEQUENCES: 'No health risk is to be expected.',
					MEASURES: 'None.'
				},
				SLIGHTLY_INCREASED: {
					PHYSICAL_OVERLOAD: 'Physical overload is possible for less resilient persons.',
					HEALTH_CONSEQUENCES:
						'Fatigue, low-grade adaptation problems which can be compensated for during leisure time.',
					MEASURES:
						'For less resilient persons, workplace redesign and other prevention measures may be helpful.'
				},
				SUBSTANTIALLY_INCREASED: {
					PHYSICAL_OVERLOAD: 'Physical overload is possible for normally resilient persons.',
					HEALTH_CONSEQUENCES:
						'Disorders (pain), possibly including dysfunctions, reversible in most cases, without morphological manifestation.',
					MEASURES: 'Workplace redesign and other prevention measures should be considered.'
				},
				HIGH: {
					PHYSICAL_OVERLOAD: 'Physical overload is likely.',
					HEALTH_CONSEQUENCES:
						'More pronounced disorders and/or dysfunctions, structural damage with pathological significance.',
					MEASURES:
						'Workplace redesign measures are necessary. Other prevention measures should be considered.'
				}
			},
			FORCE_INTENSITIES: {
				VERY_LOW: 'Very low',
				MODERATE: 'Moderate',
				HIGH: 'High',
				VERY_HIGH: 'Very high',
				PEAK: 'Peak forces',
				POWERFUL_HITTING: 'Powerful hitting'
			},
			FORCE_TRANSFERS: {
				OPTIMUM: 'Optimum force application',
				RESTRICTED: 'Restricted force application',
				HINDERED: 'Force application considerably hindered'
			},
			ARM_POSTURES: {
				GOOD: {
					title: 'Good',
					description:
						'Position or movements of joints in the middle (relaxed) range, only rare deviations, no continuous static arm posture, hand-arm rest possible as required.',
					indicator: 'success'
				},
				RESTRICTED: {
					title: 'Restricted',
					description:
						'Occasional positions or movements of the joints at the limit of the movement ranges, occasional long continuous static arm posture.',
					indicator: 'warning'
				},
				UNFAVOURABLE: {
					title: 'Unfavourable',
					description:
						'Frequent positions or movements of the joints at the limit of the movement ranges, frequent long continuous static arm posture.',
					indicator: 'warning'
				},
				POOR: {
					title: 'Poor',
					description:
						'Constant positions or movements of the joints at the limit of the movement ranges, constant long continuous static arm posture.',
					indicator: 'danger'
				}
			},
			TEMPORAL_DISTRIBUTIONS: {
				GOOD: {
					title: 'Good',
					description:
						'Frequent variation of the physical workload situation due to other activities (including other types of physical workload) / without a tight sequence of higher physical workloads within one type of physical workload during a single working day.',
					indicator: 'success'
				},
				RESTRICTED: {
					title: 'Restricted',
					description:
						'Rare variation of the physical workload situation due to other activities (including other types of physical workload) / occasional tight sequence of higher physical workloads within one type of physical workload during a single working day.',
					indicator: 'warning'
				},
				UNFAVOURABLE: {
					title: 'Unfavourable',
					description:
						'No/hardly any variation of the physical workload situation due to other activities (including other types of physical workload) / frequent tight sequence of higher physical workloads within one type of physical workload during a single working day with concurrent high load peaks.',
					indicator: 'danger'
				}
			},
			WORK_CONDITIONS: {
				GOOD: {
					title: 'Good',
					description:
						'There are no unfavourable working conditions, i.e. reliable recognition of detail / no dazzle / good climatic conditions.',
					indicator: 'success'
				},
				RESTRICTED: {
					title: 'Restricted',
					description:
						'Occasionally impaired detail recognition due to dazzle or excessively small details difficult conditions such as draught, cold, moisture and/or disturbed concentration due to noise.',
					indicator: 'warning'
				},
				UNFAVOURABLE: {
					title: 'Unfavourable',
					description:
						'Frequently impaired detail recognition due to dazzle or excessively small details frequently difficult conditions such as draught, cold, moisture and/or disturbed concentration due to noise.',
					indicator: 'danger'
				}
			},
			BODY_POSTURES: {
				ALTERNATED_SITTING_STANDING: {
					description: [
						'Alternation between sitting and standing, alternation between standing and walking, dynamic sitting possible',
						'Trunk inclined forward only very slightly',
						'No twisting and/or lateral inclination of the trunk identifiable',
						'Head posture: variable, head not inclined backward and/or severely inclined forward or constantly moving',
						'No gripping above shoulder height / no gripping at a distance from the body'
					],
					success: true,
					warning: false,
					danger: false
				},
				OCCASIONAL_WALKING: {
					description: [
						'Predominantly sitting or standing with occasional walking',
						'Trunk with slight inclination of the body towards the work area',
						'Occasional twisting and/or lateral inclination of the trunk identifiable',
						'Occasional deviations from good “neutral” head posture/movement',
						'Occasional gripping above shoulder height / occasional gripping at a distance from the body'
					],
					success: true,
					warning: false,
					danger: false
				},
				NO_WALKING: {
					description: [
						'Exclusively standing or sitting without walking',
						'Trunk clearly inclined forward and/or frequent twisting and/or lateral inclination of the trunk identifiable',
						'Frequent deviations from good “neutral” head posture/movement',
						'Head posture hunched forward for detail recognition / restricted freedom of movement',
						'Frequent gripping above shoulder height / frequent gripping at a distance from the body'
					],
					success: false,
					warning: true,
					danger: false
				},
				SEVERELY_INCLINED: {
					description: [
						'Trunk severely inclined forward / frequent or long-lasting bending',
						'Work being carried out in a kneeling, squatting, lying position',
						'Body posture strictly fixed / visual check of action through magnifying glasses or microscopes',
						'Constant deviations from good “neutral” head posture/movement',
						'Constant gripping above shoulder height / constant gripping at a distance from the body'
					],
					success: false,
					warning: false,
					danger: true
				}
			}
		};

		return {
			risk_load: labels.RISK[risk_load],
			diagnostics: labels.DIAGNOSTICS[risk_load],
			left_force_intensity: labels.FORCE_INTENSITIES[left_force_intensity],
			right_force_intensity: labels.FORCE_INTENSITIES[right_force_intensity],
			force_transfer: labels.FORCE_TRANSFERS[force_transfer],
			arm_posture: labels.ARM_POSTURES[arm_posture],
			temporal_distribution: labels.TEMPORAL_DISTRIBUTIONS[temporal_distribution],
			work_conditions: labels.WORK_CONDITIONS[work_conditions],
			body_posture: labels.BODY_POSTURES[body_posture]
		};
	}

	async countAllByRisk(params) {
		logger.info('[KimManualHandlingReport] service - countAllByRisk init', { params });
		const {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_with_user_access,
			start_date,
			end_date
		} = params;

		let user_id = '';

		if (StorageContext.getStore()?.environment === 'sandbox') {
			user_id = StorageContext.getStore().user_id;
		}

		const [data, error] = await this.repository.countAllByRisk({
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			companies_with_user_access,
			start_date,
			end_date,
			user_id
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		const result = Object.entries(data).map(([risk_name, total]) => ({ risk_name, total }));

		logger.info('[KimManualHandlingReport] service - countAllByRisk finish');
		return result;
	}
}
