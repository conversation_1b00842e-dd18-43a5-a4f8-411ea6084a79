import moment from 'moment';
import { Puppeteer } from '../util/puppeteer/index.js';
import { NioshCalculator } from '../entities/index.js';
import { formatDecimalSeparator, getWorkspace } from '../util/index.js';
import {
	i18n,
	Socket,
	logger,
	AppError,
	SOCKET_EVENTS,
	RESPONSE_ERROR_STATUS,
	RESPONSE_ERROR_ENTITIES
} from '../helpers/index.js';
import { StorageContext } from '../utils/storage_context.js';

const { DATABASE_FAILED_PERFORM_QUERY } = RESPONSE_ERROR_STATUS;
const { REPORT, FILE } = RESPONSE_ERROR_ENTITIES;
const { NIOSH_REPORT } = SOCKET_EVENTS;

export class NioshReportService {
	constructor(repository) {
		this.repository = repository;
	}

	async show(fileId) {
		logger.info('[NioshReport] service - show init');
		try {
			const niosh = await this.repository.db.NioshReport.findOne({
				where: {
					file_id: fileId,
					is_active: true
				},
				raw: true
			});

			if (!niosh) {
				logger.info('[NioshReport] service - show finish');
				return { message: 'This file has not been analyzed yet.', status: 'failed' };
			}

			const critical_factors = this.getCriticalFactors(niosh);

			logger.info('[NioshReport] service - show finish');
			return { ...niosh, critical_factors };
		} catch (error) {
			logger.error('[NioshReport] service - show finish', error);
			throw error;
		}
	}

	async create({ niosh_input, user_id }) {
		logger.info('[NioshReport] service - create init');
		const { file_id } = niosh_input;

		let transaction;

		try {
			let file = await this.repository.db.File.findOne({
				where: { id: file_id, is_active: 1 }
			});

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			const niosh_tool = new NioshCalculator(niosh_input);
			const niosh_results = niosh_tool.calculateResults();
			niosh_results.critical_factors = this.getCriticalFactors(niosh_results);

			const payload = {
				...niosh_input,
				...niosh_results,
				report_user_id: user_id
			};

			transaction = await this.repository.db.sequelize.transaction();

			const created = await this.repository.db.NioshReport.create(payload, {
				transaction
			});

			if (!created) {
				throw new AppError(REPORT.FAIL_CREATE);
			}

			const data = created.get({ plain: true });

			file.sector_id = niosh_input.sector_id;

			const workstation = await this.repository.db.Workstation.findOne({
				where: {
					id: niosh_input.workstation_id
				},
				attributes: ['name']
			});

			file.workstation = workstation?.name;
			file.workstation_id = niosh_input.workstation_id;

			await file.save({ transaction });

			const { critical_factors } = niosh_results;
			const result = { ...data, critical_factors };

			const io = Socket.getInstance().getIO();

			const roomId = getWorkspace({
				organization_id: file.organization_id,
				company_id: file.company_id
			});

			await transaction.commit();

			io.of('/report-status')
				.to(roomId)
				.emit(NIOSH_REPORT, {
					status: true,
					data: {
						id: created.id,
						file_id: created.file_id,
						lifting_index: created.lifting_index,
						updated_at: created.updated_at
					}
				});

			logger.info('[NioshReport] service - create finish');
			return result;
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}

	async update({ niosh_input, user_id }) {
		logger.info('[NioshReport] service - update init');
		const { report_id, file_id } = niosh_input;

		let transaction;
		try {
			const niosh_tool = new NioshCalculator(niosh_input);

			const niosh_results = niosh_tool.calculateResults();
			niosh_results.critical_factors = this.getCriticalFactors(niosh_results);

			const niosh_payload = {
				...niosh_input,
				...niosh_results,
				report_user_id: user_id
			};

			let niosh = await this.repository.db.NioshReport.findOne({
				where: {
					id: report_id,
					is_active: 1
				}
			});

			if (!niosh) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			let file = await this.repository.db.File.findOne({
				where: { id: file_id, is_active: 1 }
			});

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			file.sector_id = niosh_input.sector_id;

			const workstation = await this.repository.db.Workstation.findOne({
				where: {
					id: niosh_input.workstation_id
				},
				attributes: ['name']
			});

			file.workstation = workstation?.name;
			file.workstation_id = niosh_input.workstation_id;

			await file.save({ transaction });
			await niosh.update(niosh_payload, { transaction });

			await transaction.commit();

			const { critical_factors } = niosh_results;
			const result = { ...niosh.dataValues, critical_factors };

			logger.info('[NioshReport] service - update finish');
			return result;
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}

	async updateComment({ niosh_input, user_id }) {
		logger.info('[NioshReport] service - updateComment init');
		const { report_id } = niosh_input;

		let transaction;
		try {
			let niosh = await this.repository.db.NioshReport.findOne({
				where: {
					id: report_id,
					is_active: 1
				}
			});

			if (!niosh) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			niosh.comment = niosh_input.comment;
			niosh.report_user_id = user_id;

			await niosh.save({ transaction });
			await transaction.commit();

			const critical_factors = this.getCriticalFactors(niosh.dataValues);

			const result = {
				...niosh.dataValues,
				critical_factors
			};

			logger.info('[NioshReport] service - updateComment finish');
			return result;
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}

	getCriticalFactors({
		h_factor,
		dc_factor,
		vc_factor,
		a_factor,
		frequency_factor,
		coupling_factor,
		one_handed_factor
	}) {
		const factors = [
			{
				title: 'Distance H',
				label: 'h_factor',
				value: h_factor,
				suggestion:
					'Bring the load closer to the worker by removing any horizontal barriers or reducing the size of the object. Lifts near the floor should be avoided.'
			},
			{
				title: 'Distance Vc',
				label: 'dc_factor',
				value: dc_factor,
				suggestion:
					'Raise/lower the origin/destination of the lift. Avoid lifting near the floor or above the shoulders.'
			},
			{
				title: 'Distance Dc',
				label: 'vc_factor',
				value: vc_factor,
				suggestion: 'Reduce the vertical distance between the origin and the destination of the lift.'
			},
			{
				title: 'Angle A',
				label: 'a_factor',
				value: a_factor,
				suggestion:
					'Move the origin and destination closer together to reduce the angle of twist, or move them further apart to force the worker to turn the feet and step, rather than twist the body.'
			},
			{
				title: 'Frequency/Duration',
				label: 'frequency_factor',
				value: frequency_factor,
				suggestion:
					'Reduce the lifting frequency rate, reduce the lifting duration, or provide longer recovery periods (i.e., light work period).'
			},
			{
				title: 'Coupling',
				label: 'coupling_factor',
				value: coupling_factor,
				suggestion:
					'Improve the hand-to-object coupling by providing optimal containers with handles or handhold cutouts, or improve the handholds for irregular objects.'
			},
			{
				title: 'One handed',
				label: 'one_handed_factor',
				value: one_handed_factor,
				suggestion: 'Consider using both hands when lifting the weight.'
			}
		];

		factors.sort((a, b) => (b.value - a.value < 0 ? 1 : b.value - a.value > 0 ? -1 : 0));

		const critical_factors = factors.slice(0, 3).filter((factor) => parseFloat(factor.value) !== 1);

		critical_factors.map((factor) => {
			factor.value = Math.round(factor.value * 100) / 100;
			return factor;
		});

		return critical_factors;
	}

	async generatePDF({ file_id, locale }) {
		logger.info('[NioshReport] service - generatePDF init');
		let niosh_data;
		const language = locale.substring(0, 2);
		i18n.changeLanguage(language);

		try {
			const niosh = await this.repository.db.NioshReport.findOne({
				where: { file_id, is_active: 1 },
				include: [
					{
						model: this.repository.db.User,
						as: 'report_user'
					},
					{
						model: this.repository.db.File,
						as: 'file',
						include: [
							{
								model: this.repository.db.Sector,
								as: 'sector',
								attributes: ['name'],
								include: [
									{
										model: this.repository.db.Company,
										as: 'company',
										attributes: ['name'],
										include: [
											{
												model: this.repository.db.Organization,
												as: 'Organization',
												attributes: ['name']
											}
										]
									}
								]
							},
							{
								model: this.repository.db.Workstation,
								as: 'workstations',
								attributes: ['name'],
								include: [
									{
										model: this.repository.db.Line,
										as: 'line',
										attributes: ['name']
									}
								]
							}
						]
					}
				]
			}).then((data) => (niosh_data = data.get({ plain: true })));

			if (!niosh) {
				throw new AppError(REPORT.NOT_FOUND);
			}

			niosh_data.critical_factors = this.getCriticalFactors(niosh_data);
			const formattedReport = this.formatDataForPDF(niosh_data, locale);

			const labels = this.getLabelsForPDF(formattedReport);
			const conclusion = this.getConclusionTexts(formattedReport.risk);

			const payload = {
				title: ['Manual Lifting Report'],
				...formattedReport,
				labels,
				conclusion,
				language
			};

			const puppeteer = new Puppeteer();
			const html = await puppeteer.render('niosh', payload);

			const browser = await puppeteer.init();
			const buffer = puppeteer.mounting(browser, html);

			logger.info('[NioshReport] service - generatePDF finish');
			return buffer;
		} catch (error) {
			logger.error('[NioshReport] service - generatePDF finish', error);
			throw error;
		}
	}

	formatDataForPDF(report, locale) {
		const previousMomentLocale = moment.locale();
		// Format report for displaying data (using locale settings for decimal separator and moment object as string)
		moment.locale(locale);

		const formattedReport = {
			...report,
			file: {
				...report.file,
				sector: {
					...report.file.sector,
					company: {
						...report.file.sector.company,
						organization: {
							...report.file.sector.company.Organization
						}
					}
				},
				workstation: {
					...report.file.workstations,
					line: {
						...report.file.workstations.line
					}
				}
			}
		};

		const skippedKeys = ['frequency'];

		for (const key in formattedReport) {
			if (skippedKeys.includes(key)) continue;

			if (key !== 'critical_factors') {
				formattedReport[key] = formatDecimalSeparator(formattedReport[key], locale);
			} else {
				for (const index in formattedReport.critical_factors) {
					formattedReport.critical_factors[index].value = formatDecimalSeparator(
						formattedReport.critical_factors[index].value,
						locale
					);
				}
			}
		}

		formattedReport.file = {
			...formattedReport.file,
			createdAt: moment(formattedReport.file.createdAt).format('L')
		};
		formattedReport.collection_date = moment(formattedReport.collection_date).format('L');

		moment.locale(previousMomentLocale);
		return formattedReport;
	}

	getLabelsForPDF({ report_type, risk, coupling, gender, age, workers, hands }) {
		const labels = {
			report_type: {
				NIOSH: 'NIOSH',
				ISO_11228: 'ISO 11228-1'
			},
			risk: {
				VERY_LOW: 'Very low risk',
				LOW: 'Low risk',
				MODERATE: 'Moderate risk',
				HIGH: 'High risk',
				VERY_HIGH: 'Very high risk'
			},
			coupling: {
				POOR: 'Poor',
				FAIR: 'Fair',
				GOOD: 'Good'
			},
			gender: {
				MALE: 'Man',
				FEMALE: 'Woman'
			},
			age: {
				LESS_THAN_20: '< 20 years old',
				BETWEEN_20_AND_45: '20 to 45 years old',
				MORE_THAN_45: '> 45 years old'
			},
			workers: {
				1: '1 worker',
				2: '2 workers',
				3: '3 workers'
			},
			hands: {
				1: 'one hand',
				2: 'both hands'
			}
		};

		return {
			report_type: labels.report_type[report_type],
			risk: labels.risk[risk],
			coupling: labels.coupling[coupling],
			gender: labels.gender[gender],
			age: labels.age[age],
			workers: labels.workers[workers],
			hands: labels.hands[hands]
		};
	}

	getConclusionTexts(risk) {
		const conclusion_texts = {
			VERY_HIGH: {
				text: [
					`The weight to be lifted [`,
					` kg ] is greater than the recommended weight limit (RWL) [`,
					` kg ]. The lifting index (LI) [`,
					`] is more than 3.`
				],
				brief: ' There is a very high risk. These values indicate that this lift would be hazardous for a majority of healthy industrial workers.'
			},
			HIGH: {
				text: [
					`The weight to be lifted [`,
					` kg ] is greater than the recommended weight limit (RWL) [`,
					` kg ]. The lifting index (LI) [`,
					`] is more than 2 and less than 3.`
				],
				brief: ' There is a high risk. These values indicate that this lift would be hazardous for a majority of healthy industrial workers.'
			},
			MODERATE: {
				text: [
					`The weight to be lifted [`,
					` kg ] is greater than the recommended weight limit (RWL) [`,
					` kg ]. The lifting index (LI) [`,
					`] is more than 1,5 and less than 2.`
				],
				brief: ' There is a moderate risk. These values indicate that this job is somewhat stressful.'
			},
			LOW: {
				text: [
					`The weight to be lifted [`,
					` kg ] is greater than the recommended weight limit (RWL) [`,
					` kg ]. The lifting index (LI) [`,
					`] is more than 1 and less than 1,5.`
				],
				brief: ' There is a low risk. These values indicate that this job is adequate for the majority of industrial workers, but some may have trouble.'
			},
			VERY_LOW: {
				text: [
					`The weight to be lifted [`,
					` kg ] is less than the recommended weight limit (RWL) [`,
					` kg ]. The lifting index (LI) [`,
					`] is less than 1.`
				],
				brief: ' There is a very low risk. These values indicate that this job is adequate for the majority of industrial workers.'
			}
		};

		return conclusion_texts[risk];
	}

	async countAllByRisk(params) {
		logger.info('[Niosh] service - countAllByRisk init', { params });
		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date
		} = params;

		let user_id = null;

		if (StorageContext.getStore()?.environment === 'sandbox') {
			user_id = StorageContext.getStore().user_id;
		}

		const [data, error] = await this.repository.countAllByRisk({
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date,
			user_id
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		const result = Object.entries(data).map(([risk_name, total]) => ({ risk_name, total }));

		logger.info('[Niosh] service - countAllByRisk finish');
		return result;
	}
}
