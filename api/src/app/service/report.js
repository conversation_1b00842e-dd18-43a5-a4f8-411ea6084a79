import { Storage } from '../modules/storage.js';
import ScoreTable from '../utils/scoreTable.js';
import axios from 'axios';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ValidationError } from '../utils/errors.js';
import _ from 'lodash';
import AWS from 'aws-sdk';
import config from 'config';
import { enumParts } from '../controllers/report.js';
import moment from 'moment';

import BriefBest from '../modules/ErgnomicTools/BriefBest.js';
// import mockBriefBest from './mocks/report/test2.json';
// import BriefBest1 from '../modules/ergonomics/briefbest';

// import mockErgonomic from './mocks/report/ergonomic_movement.json';

import APIError from '../utils/errors/ApiError.js';
import { HttpStatusCode } from '../utils/enum.js';
import Report from '../modules/report.js';

// ----------------------------------
import { Puppeteer } from '../util/puppeteer/index.js';
import { i18n } from '../helpers/i18n.js';
import { AppError } from '../util/errors.js';
import { ERROR_RESPONSE_ENTITIES_ENUM, ERROR_RESPONSE_EXTERNAL_SERVICE } from '../util/enum.js';
import { Storage as StorageS3 } from '../util/storage.js';
import { logger } from '../helpers/logger.js';

const { FILE, USER } = ERROR_RESPONSE_ENTITIES_ENUM;
const { STORAGE } = ERROR_RESPONSE_EXTERNAL_SERVICE;

export class ReportService {
	constructor(repository) {
		this.repository = repository;
	}

	normalizeDataScoreParts = (data) => {
		const body = {};
		Object.keys(data).map((part) => {
			const scorePart = [];
			for (const score in data[part].total) {
				scorePart.push(data[part].total[score]);
			}
			body[part] = scorePart;
		});
		return body;
	};

	async riskDegreeWorkstation(parameters) {
		// const { file_id, organization_id, company_id } = parameters;
		const { legs, force, repetition } = parameters;

		// const file = await this.repository.db.File.findOne({
		// 	where: {
		// 		id: file_id,
		// 		organization_id: organization_id,
		// 		company_id: company_id,
		// 		isActive: true
		// 	}
		// });

		// if (!file) {
		// 	throw new AppError(FILE.NOT_FOUND);
		// }
		const file = {
			id: '12781cd0-8da3-11ec-a0be-795dcf96d25a',
			generated_name: '2caecff2-c7b2-4d1c-afc2-65b275528723-colocando caixa na esteira.mp4'
		};
		const organization_id = '9e2fbb70-785e-11ec-a136-91b1ea157d8f';
		const company_id = 'a915cd40-785e-11ec-a136-91b1ea157d8f';

		const s3 = new StorageS3('sa-east-1');

		const Bucket = StorageS3.bucketName(organization_id, company_id);
		const Key = StorageS3.changeExtension(file.generated_name, 'json');

		const [stream, err] = await s3.getObject({ Bucket, Key });

		if (err) {
			throw new AppError(STORAGE.FAIL_DOWNLOAD_DATA);
		}

		const payload = JSON.parse(stream.Body.toString('utf-8'));

		let result = {};

		let aux = {
			neck: [],
			trunk: [],
			left_lower_arm: [],
			right_lower_arm: [],
			left_upper_arm: [],
			right_upper_arm: []
		};

		for (let key in payload) {
			payload[key].map((item) => {
				result[key] = this.calculateParts(key, item);
			});
		}

		for (let key in result) {
			result[key].score.map((item) => {
				const [keyObject] = Object.keys(item);
				aux[key].push(item[keyObject].score_part);
			});
		}

		let resulFinal = {};

		for (let key in result) {
			resulFinal[key] = result[key].score.map((item, index) => {
				let upperRight = 1,
					lowerRight = 1,
					upperLeft = 1,
					lowerLeft = 1,
					neck = 1,
					trunk = 1;

				if (key === 'left_lower_arm') {
					lowerLeft = aux[key][index] |= 1;
				}

				if (key === 'left_upper_arm') {
					upperLeft = aux[key][index] |= 1;
				}

				if (key === 'right_lower_arm') {
					lowerRight = aux[key][index] |= 1;
				}

				if (key === 'right_upper_arm') {
					upperRight = aux[key][index] |= 1;
				}

				if (key === 'neck') {
					neck = aux[key][index] |= 1;
				}

				if (key === 'trunk') {
					trunk = aux[key][index] |= 1;
				}

				let scoreLeft = this.tableScore.Rula.tableA[`${upperLeft}`][lowerLeft - 1];
				let scoreRight = this.tableScore.Rula.tableA[`${upperRight}`][lowerRight - 1];
				let scoreB =
					Number(legs) === 1
						? this.tableScore.Rula.tableB1[`${neck}`][trunk - 1]
						: this.tableScore.Rula.tableB2[`${neck}`][trunk - 1];

				let scoreArmLeft =
					scoreLeft + Number(force) + (Number(repetition) >= 4 ? 1 : 0) >= 8
						? 8
						: scoreLeft + Number(force) + (Number(repetition) >= 4 ? 1 : 0);
				let scoreArmRight =
					scoreRight + Number(force) + (Number(repetition) >= 4 ? 1 : 0) >= 8
						? 8
						: scoreRight + Number(force) + (Number(repetition) >= 4 ? 1 : 0);

				let conditionScoreNTL =
					scoreB + Number(force) + (Number(repetition) >= 4 ? 1 : 0) >= 7
						? 7
						: scoreB + Number(force) + (Number(repetition) >= 4 ? 1 : 0);

				let scoreCLeft = this.tableScore.Rula.tableC[`${scoreArmLeft}`][conditionScoreNTL - 1];
				let scoreCRight = this.tableScore.Rula.tableC[`${scoreArmRight}`][conditionScoreNTL - 1];

				return {
					...item[`${index + 1}`],
					score_a_left: scoreLeft,
					score_a_right: scoreRight,
					score_arm_left: scoreArmLeft,
					score_arm_right: scoreArmRight,
					score_b: scoreB,
					score_c_left: scoreCLeft,
					score_c_right: scoreCRight,
					score_ntl: conditionScoreNTL,
					score_c: (scoreCLeft + scoreCRight) / 2
				};
			});
		}

		let riskCount = { total: 0, count: 0 };

		resulFinal.trunk.map((item) => {
			riskCount.total += item.score_c;
			riskCount.count++;
		});

		const rulaScore = Math.round(riskCount.total / riskCount.count);

		// await upload.update({ rulaScore });

		return { risk: rulaScore };
	}

	async reportGenerator(params) {
		const { report, parameters, language } = params;

		const data = this.normalizeDataScoreParts(parameters.data);

		const payload = {
			...parameters,
			data: data,
			title: 'Ergonomic Exposure Report'
		};

		i18n.changeLanguage(language);
		const puppeteer = new Puppeteer();
		const html = await puppeteer.render(report, payload);

		const browser = await puppeteer.init();
		const page = await browser.newPage();

		await page.setContent(html, {
			waitUntil: ['load', 'networkidle0']
		});
		await page.emulateMediaType('screen');

		const configPDF = {
			format: 'A4',
			margin: {
				top: '1cm',
				left: '1cm',
				right: '1cm',
				bottom: '1cm'
			}
		};

		const buffer = await page.pdf(configPDF);
		await browser.close();

		return buffer;
	}

	async angleByTimeDocumentPDF(params) {
		logger.info('[Report] service - angleByTimeDocumentPDF init');
		let file_data;
		const { user_id, file_id, parameters, locale } = params;
		const language = locale.substring(0, 2);

		try {
			i18n.changeLanguage(language);
			const file = await this.repository.db.File.findOne({
				where: { id: file_id, is_active: 1 },
				include: [
					{
						model: this.repository.db.Sector,
						as: 'sector',
						attributes: ['name'],
						include: [
							{
								model: this.repository.db.Company,
								as: 'company',
								attributes: ['name'],
								include: [
									{
										model: this.repository.db.Organization,
										as: 'organization',
										attributes: ['name']
									}
								]
							}
						]
					}
				]
			}).then((data) => (file_data = data.get({ plain: true })));

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			const user = await this.repository.db.User.findOne({
				where: { id: user_id, is_active: 1 }
			});

			if (!user) {
				throw new AppError(USER.NOT_FOUND);
			}

			const payload = {
				title: ['Report Results', 'Angle by Time'],
				...parameters,
				file: {
					...file_data,
					duration: moment.unix(file_data.duration).utc().format('mm:ss')
				},
				report_user: { name: user.name },
				language
			};

			const puppeteer = new Puppeteer();
			const html = await puppeteer.render('angle_time', payload);

			const browser = await puppeteer.init();
			const buffer = puppeteer.mounting(browser, html);

			logger.info('[Report] service - angleByTimeDocumentPDF finish');
			return buffer;
		} catch (error) {
			throw error;
		}
	}
}

// Deprecated !!!
export default class ReportServiceOld {
	constructor({ repository, file_repository, report_repository, company_repository }) {
		this.repository = repository;
		this.briefBest = new BriefBest();
		this.fileRepository = file_repository;
		this.reportRepository = report_repository;
		this.companyRepository = company_repository;
		this.lambda = new AWS.Lambda({ region: 'us-east-1' });
		this.tableScore = ScoreTable;
		this.bodyParts = ['neck', 'trunk', 'left_lower_arm', 'right_lower_arm', 'left_upper_arm', 'right_upper_arm'];
	}

	formatDate = (parameter) => {
		const date = new Date(parameter);
		const year = date.getFullYear();
		const day = date.getDate().toString().padStart(2, '0');
		const mounth = (date.getMonth() + 1).toString().padStart(2, '0');
		return `${day}/${mounth}/${year}`;
	};

	calcAnglesMov(payload) {
		let result = {};

		for (let key in payload) {
			if (typeof payload[key] !== 'object') {
				throw new ErrorHandler(400, 'Invalid parameters');
			}

			payload[key].map((item) => {
				result[key] = enumParts[key].calculate(item.angles);
			});
		}

		return result;
	}

	convertSecondsToMinutes(seconds) {
		if (!seconds) return '-';
		return `${moment.unix(seconds).utc().format('mm:ss')}`;
	}

	async ergonomicMovementReport(parameters) {
		const { upload_id, risk, repetition, company_id, information, conclusion, organization_id } = parameters;

		const isUpload = await this.reportRepository.checkExistsUpload({
			organization_id,
			company_id,
			upload_id
		});

		if (!isUpload) {
			throw new ErrorHandler(404, 'File not found');
		}

		const bodyBucket = {
			Key: isUpload.generated_name,
			organization_id,
			company_id
		};

		const storage = new Storage('sa-east-1');

		const payload = await storage.downloadFileData(bodyBucket);

		// const payload = mockErgonomic;

		if (!payload) {
			throw new ErrorHandler(404, 'Archive not found');
		}

		const parts = this.calcAnglesMov(payload);

		const company = await this.companyRepository.db.Company.findOne({
			where: {
				id: company_id,
				isActive: true
			}
		});

		const dateFormated = this.formatDate(information.collectionDate);

		const body = {
			template: {
				shortid: 'SJlR3Q3l8P'
			},
			data: {
				tool: {
					description: 'Relatório de resultados utilizando RULA'
				},
				parameters: {
					repetition
				},
				company: {
					name: company.name,
					date: dateFormated,
					sector: information.sector,
					workstation: information.workstation
				},
				archive: {
					duration: this.convertSecondsToMinutes(isUpload.duration),
					original_name: isUpload.original_name
				},
				risk,
				conclusion,
				parts: [parts]
			}
		};

		const url = config.get('App.report.endpoint');

		const user = config.get('App.report.user');
		const password = config.get('App.report.password');

		const token = `${user}:${password}`;
		const encodedToken = new Buffer.from(token).toString('base64');

		try {
			const options = {
				headers: {
					'Content-Type': 'application/json',
					Authorization: 'Basic ' + encodedToken
				},
				responseType: 'arraybuffer',
				timeout: 40000
			};

			const response = await axios.post(url, body, options);

			if (!response.data) {
				throw new ErrorHandler(500, 'Could not generate the file');
			}

			const buffer = Buffer.from(response.data);

			return buffer;
		} catch (error) {
			throw error;
		}
	}

	async setParameters(parameters) {
		const { upload_id, sector_id, company_id, workstation_id, organization_id } = parameters;

		const config = {
			where: {
				company_id,
				id: upload_id,
				organization_id,
				isActive: true
			}
		};

		const file = await this.fileRepository.findOneFile(config);

		if (!file) {
			throw new ErrorHandler(404, 'File not found');
		}

		const checkSector = await this.companyRepository.checkSectorExists({
			organization_id,
			company_id,
			sector_id
		});

		if (!checkSector) {
			throw new ErrorHandler(500, 'Invalid sector');
		}

		const { transactionInit, transactionCommit, transactionRollback } = this.fileRepository;

		const transaction = await transactionInit();

		try {
			file.sector_id = sector_id;

			const workstation = await this.repository.db.Workstation.findOne({
				where: {
					id: workstation_id
				},
				attributes: ['name']
			});

			file.workstation = workstation?.name;
			file.workstation_id = workstation_id;

			await file.update(body, { transaction });

			await transactionCommit(transaction);

			return { id: file.id };
		} catch (err) {
			await transactionRollback(transaction);
			throw err;
		}
	}

	async scoreRulaMovementFileDate(parameters) {
		const { organization_id, company_id, upload_id } = parameters;

		const config = {
			where: {
				company_id,
				id: upload_id,
				organization_id,
				isActive: true
			}
		};

		const file = await this.fileRepository.findOneFile(config);

		if (!file) {
			throw new ErrorHandler(404, 'File not found');
		}

		const storage = new Storage('sa-east-1');

		const payload = {
			Key: file.generated_name,
			organization_id,
			company_id
		};

		const data = await storage.downloadFileData(payload);

		if (!data) {
			throw new ErrorHandler(404, 'Data not found');
		}

		let result = {};

		for (let key in data) {
			if (typeof data[key] !== 'object') {
				throw new ValidationError('Invalid parameters', 400);
			}
			data[key].map((item) => (result[key] = enumParts[key].calculate(item.angles)));
		}

		return [result];
	}

	async extractedByCompany(params) {
		const { organization_id, company_id } = params;
		const result = await this.reportRepository.countReportExtractedByCompany({
			organization_id,
			company_id
		});
		if (result === (undefined || null)) {
			throw new ErrorHandler(500, 'Internal error');
		}
		return result;
	}

	// async scoreRulaAngleTimeReport(parameters) {

	//   const {
	//     file_id
	//     , data
	//     , company
	//     , conclusion
	//     , information
	//     , organization
	//   } = parameters;

	// const isUpload = await this.reportRepository.checkExistsUpload({
	//   organization_id: organization.id,
	//   company_id: company.id,
	//   upload_id: file_id
	// });

	// if (!isUpload) {
	//   throw new ErrorHandler(404, 'File not found');
	// }

	// const duration = this.convertSecondsToMinutes(isUpload.duration);

	//   const options = {
	//     template: { shortid: 'VnUlRd9' },
	//     data: {
	//       tool: {
	//         name: 'rula',
	//         description: 'Análise de movimento por ângulo'
	//       },
	//       company: {
	//         name: company.name,
	//         sector: information.sector.name,
	//         date: information.date,
	//         workstation: information.workstation
	//       },
	//       archive: {
	//         original_name: isUpload.original_name,
	//         duration
	//       },
	//       conclusion,
	//       data
	//     }
	//   };

	//   const report = ReportFactory.createInstance('js-reports');

	//   const buffer = await report.generateDocument(options);

	//   if (!buffer) {
	//     throw new ErrorHandler(500, 'Error generating the document');
	//   }

	//   const file = Buffer.from(buffer);

	//   return file;
	// }

	async scoreRulaAngleTimeReport(params) {
		const { file_id, data, company, conclusion, information, organization } = params;

		try {
			const isUpload = await this.reportRepository.checkExistsUpload({
				organization_id: organization.id,
				company_id: company.id,
				upload_id: file_id
			});

			if (!isUpload) {
				throw new ErrorHandler(404, 'File not found');
			}

			const url = config.get('App.report.endpoint');

			const user = config.get('App.report.user');
			const password = config.get('App.report.password');

			const token = `${user}:${password}`;
			const encodedToken = new Buffer.from(token).toString('base64');

			const duration = this.convertSecondsToMinutes(isUpload.duration);

			const body = {
				template: {
					shortid: 'VnUlRd9'
				},
				data: {
					tool: {
						name: 'rula',
						description: 'Análise de movimento por ângulo'
					},
					company: {
						name: company.name,
						sector: information.sector.name,
						date: information.date,
						workstation: information.workstation
					},
					archive: {
						original_name: isUpload.original_name,
						duration
					},
					conclusion,
					data
				}
			};

			const options = {
				headers: {
					'Content-Type': 'application/json',
					Authorization: 'Basic ' + encodedToken
				},
				responseType: 'arraybuffer',
				timeout: 40000
			};

			const response = await axios.post(url, body, options);

			if (!response.data) {
				throw new ErrorHandler(500, 'Could not generate the file');
			}

			let buffer = Buffer.from(response.data);

			return buffer;
		} catch (err) {
			throw err;
		}
	}

	ruleForBandsBodyParts(item) {
		const bands = {
			neck: [-15, 25, 10, 25],
			trunk: [5, 15, 40, 55],
			left_lower_arm: [60, 40, 80],
			right_lower_arm: [60, 40, 80],
			// left_upper_arm: [20, 25, 45, 90],
			// right_upper_arm: [20, 25, 45, 90],
			left_upper_arm: [-40, 20, 40, 25, 45, 90],
			right_upper_arm: [-40, 20, 40, 25, 45, 90],
			default: [20, 25, 45, 25]
		};

		return bands[item] || bands.default;
	}

	ruleMaxBodyParts(item, key) {
		// let normalizeAngle = item === 'neck' ? (key | 0) : (key | 0) < 0 ? 0 : key | 0;

		// const verifyMaxAngle = (rule, angle) => angle > rule ? rule : angle;

		// const bodyParts = {
		//   neck: verifyMaxAngle(45, normalizeAngle)
		//   , trunk: verifyMaxAngle(115, normalizeAngle)
		//   , left_lower_arm: verifyMaxAngle(180, normalizeAngle)
		//   , left_upper_arm: verifyMaxAngle(180, normalizeAngle)
		//   , right_lower_arm: verifyMaxAngle(180, normalizeAngle)
		//   , right_upper_arm: verifyMaxAngle(180, normalizeAngle)
		// };

		// return bodyParts[item] || normalizeAngle;

		// Backup
		// const verifyMaxAngle = (rule, angle) => angle > rule ? rule : angle;

		const verifyMaxAngle = (rule, angle) => {
			if (key === '' || key === 0) {
				return null;
			}
			return angle > rule ? rule : angle;
		};

		const bodyParts = {
			neck: verifyMaxAngle(45, key),
			trunk: verifyMaxAngle(115, key),
			left_lower_arm: verifyMaxAngle(180, key),
			left_upper_arm: verifyMaxAngle(180, key),
			right_lower_arm: verifyMaxAngle(180, key),
			right_upper_arm: verifyMaxAngle(180, key)
		};

		return bodyParts[item];
	}

	async scoreRuleBytime(parameters) {
		const { organization_id, company_id, upload_id } = parameters;

		const upload = await this.reportRepository.checkExistsUpload({
			organization_id,
			company_id,
			upload_id
		});

		if (!upload) {
			throw new ErrorHandler(404, 'Archive not found');
		}

		const storage = new Storage('sa-east-1');

		const config = {
			Key: upload.generated_name,
			organization_id,
			company_id
		};

		const dataFile = await storage.downloadFileData(config);

		// function headerSelect(item) {
		//   const headers = {
		//     left_lower_arm: ['x', 'moderate', 'low', 'moderate', 'Angle'],
		//     right_lower_arm: ['x', 'moderate', 'low', 'moderate', 'Angle'],
		//     neck: [
		//       'x',
		//       'critical',
		//       'low',
		//       'moderate',
		//       'high',
		//       'Angle'
		//     ],
		//     left_upper_arm: [
		//       "x",
		//       "moderate",
		//       "low",
		//       "moderate",
		//       "high",
		//       "critical",
		//       "critical",
		//       "Angle"
		//     ],
		//     right_upper_arm: [
		//       "x",
		//       "moderate",
		//       "low",
		//       "moderate",
		//       "high",
		//       "critical",
		//       "critical",
		//       "Angle"
		//     ],
		//     default: ['x',
		//       'low',
		//       'moderate',
		//       'high',
		//       'critical',
		//       'Angle'
		//     ]
		//   };

		//   return headers[item] || headers.default;
		// }

		function headerSelect(item) {
			const headers = {
				left_lower_arm: ['x', 'Moderado', 'Baixo', 'Moderado', 'Ângulo'],
				right_lower_arm: ['x', 'Moderado', 'Baixo', 'Moderado', 'Ângulo'],
				neck: ['x', 'Crítico', 'Baixo', 'Moderado', 'Alto', 'Ângulo'],
				left_upper_arm: ['x', 'Moderado', 'Baixo', 'Moderado', 'Alto', 'Crítico', 'Crítico', 'Ângulo'],
				right_upper_arm: ['x', 'Moderado', 'Baixo', 'Moderado', 'Alto', 'Críticol', 'Crítico', 'Ângulo'],
				default: ['x', 'Baixo', 'Moderado', 'Alto', 'Crítico', 'Ângulo']
			};

			return headers[item] || headers.default;
		}

		let response = {};

		Object.keys(dataFile).map((item) => {
			let seconds = 0;
			const [object] = dataFile[item];

			_.set(response, item, [headerSelect(item)]);

			object.angles.map((key) => {
				if (seconds < object.angles.length) {
					const isAngle = this.ruleMaxBodyParts(item, key);
					response[item].push([seconds, ...this.ruleForBandsBodyParts(item), isAngle]);
					seconds++;
				}
			});
		});

		return response;
	}

	async scoreRula(params, query) {
		const { organization_id, company_id, upload_id } = params;
		const { force, legs, repetition } = query;

		const upload = await this.reportRepository.checkExistsUpload({
			organization_id,
			company_id,
			upload_id
		});

		if (!upload) {
			throw new ErrorHandler(500, 'File not found');
		}

		const storage = new Storage('sa-east-1');

		const config = {
			Key: upload.generated_name,
			company_id,
			organization_id
		};

		const payload = await storage.downloadFileData(config);

		if (!payload) {
			throw new ErrorHandler(404, 'Data not found');
		}

		let result = {};

		let aux = {
			neck: [],
			trunk: [],
			left_lower_arm: [],
			right_lower_arm: [],
			left_upper_arm: [],
			right_upper_arm: []
		};

		for (let key in payload) {
			if (typeof payload[key] !== 'object') {
				throw new ValidationError('Invalid parameters', 400);
			}

			payload[key].map((item) => {
				result[key] = this.calculateParts(key, item);
			});
		}

		for (let key in result) {
			result[key].score.map((item) => {
				const [keyObject] = Object.keys(item);
				aux[key].push(item[keyObject].score_part);
			});
		}

		let resulFinal = {};

		for (let key in result) {
			resulFinal[key] = result[key].score.map((item, index) => {
				let upperRight = 1,
					lowerRight = 1,
					upperLeft = 1,
					lowerLeft = 1,
					neck = 1,
					trunk = 1;

				if (key === 'left_lower_arm') {
					lowerLeft = aux[key][index] |= 1;
				}

				if (key === 'left_upper_arm') {
					upperLeft = aux[key][index] |= 1;
				}

				if (key === 'right_lower_arm') {
					lowerRight = aux[key][index] |= 1;
				}

				if (key === 'right_upper_arm') {
					upperRight = aux[key][index] |= 1;
				}

				if (key === 'neck') {
					neck = aux[key][index] |= 1;
				}

				if (key === 'trunk') {
					trunk = aux[key][index] |= 1;
				}

				let scoreLeft = this.tableScore.Rula.tableA[`${upperLeft}`][lowerLeft - 1];
				let scoreRight = this.tableScore.Rula.tableA[`${upperRight}`][lowerRight - 1];
				let scoreB =
					Number(legs) === 1
						? this.tableScore.Rula.tableB1[`${neck}`][trunk - 1]
						: this.tableScore.Rula.tableB2[`${neck}`][trunk - 1];

				let scoreArmLeft =
					scoreLeft + Number(force) + (Number(repetition) >= 4 ? 1 : 0) >= 8
						? 8
						: scoreLeft + Number(force) + (Number(repetition) >= 4 ? 1 : 0);
				let scoreArmRight =
					scoreRight + Number(force) + (Number(repetition) >= 4 ? 1 : 0) >= 8
						? 8
						: scoreRight + Number(force) + (Number(repetition) >= 4 ? 1 : 0);

				let conditionScoreNTL =
					scoreB + Number(force) + (Number(repetition) >= 4 ? 1 : 0) >= 7
						? 7
						: scoreB + Number(force) + (Number(repetition) >= 4 ? 1 : 0);

				let scoreCLeft = this.tableScore.Rula.tableC[`${scoreArmLeft}`][conditionScoreNTL - 1];
				let scoreCRight = this.tableScore.Rula.tableC[`${scoreArmRight}`][conditionScoreNTL - 1];

				return {
					...item[`${index + 1}`],
					score_a_left: scoreLeft,
					score_a_right: scoreRight,
					score_arm_left: scoreArmLeft,
					score_arm_right: scoreArmRight,
					score_b: scoreB,
					score_c_left: scoreCLeft,
					score_c_right: scoreCRight,
					score_ntl: conditionScoreNTL,
					score_c: (scoreCLeft + scoreCRight) / 2
				};
			});
		}

		let riskCount = { total: 0, count: 0 };

		resulFinal.trunk.map((item) => {
			riskCount.total += item.score_c;
			riskCount.count++;
		});

		const rulaScore = Math.round(riskCount.total / riskCount.count);

		await upload.update({ rulaScore });

		return { risk: rulaScore };
	}

	calculateParts(key, { angles }) {
		const parameters = { key, angles };
		const callFunctionBody = {
			neck: this.neckCalculate(parameters),
			trunk: this.trunkCalculate(parameters),
			left_lower_arm: this.leftLowerArmCalculate(parameters),
			right_lower_arm: this.rightLowerArmCalculate(parameters),
			left_upper_arm: this.leftUpperArmCalculate(parameters),
			right_upper_arm: this.rightUpperArmCalculate(parameters)
		};
		return callFunctionBody[key] || null;
	}

	factoryObject({ key, angles }, validation) {
		let score = angles.map((item, index) => {
			let scorePart = validation(item);
			return {
				[index + 1]: {
					angle: item,
					score_part: scorePart,
					score_a_right: 0,
					score_a_left: 0,
					score_b: 0,
					score_c: 0
				}
			};
		});
		return score;
	}

	neckCalculate(angles) {
		const validation = (x) => {
			if (!!x < 0) {
				return x < 0 && 4;
			}
			return (x >= 0 && x <= 10 && 1) || (x > 10 && x <= 20 && 2) || (x > 20 && 3);
		};

		let score = this.factoryObject(angles, validation);

		return { score };
	}

	trunkCalculate(angles) {
		// const validation = (x) =>
		//   (x === 0 && 1) ||
		//   (x > 0 && x <= 20 && 2) ||
		//   (x >= 21 && x <= 60 && 3) ||
		//   (x >= 60 && 4);

		const validation = (x) =>
			(x >= 0 && x >= 5 && 1) || (x > 5 && x <= 20 && 2) || (x >= 21 && x < 60 && 3) || (x >= 60 && 4);

		let score = this.factoryObject(angles, validation);

		return { score };
	}

	leftLowerArmCalculate(angles) {
		// const validation = (x) =>
		//   (x > 60 && x < 100 && 1) || (x >= 0 && x <= 60 && 2) || (x > 100 && 2);

		const validation = (x) => (x > 60 && x < 100 && 1) || (x >= 0 && x <= 60 && 2) || (x >= 100 && 2);

		let score = this.factoryObject(angles, validation);

		return { score };
	}

	rightLowerArmCalculate(angles) {
		// const validation = (x) =>
		//   (x > 60 && x < 100 && 1) || (x >= 0 && x <= 60 && 2) || (x > 100 && 2);

		const validation = (x) => (x > 60 && x < 100 && 1) || (x >= 0 && x <= 60 && 2) || (x >= 100 && 2);

		let score = this.factoryObject(angles, validation);
		return { score };
	}

	leftUpperArmCalculate(angles) {
		const validation = (x) => {
			if (!!x < 0) {
				return (x > -20 && x < 20 && 1) || (x < -21 && 2);
			}
			return (x >= 21 && x <= 45 && 2) || (x > 45 && x <= 90 && 3) || (x > 90 && 4);
		};
		let score = this.factoryObject(angles, validation);
		return { score };
	}

	rightUpperArmCalculate(angles) {
		const validation = (x) => {
			if (!!x < 0) {
				return (x > -20 && x < 20 && 1) || (x < -21 && 2);
			}
			return (x >= 21 && x <= 45 && 2) || (x > 45 && x <= 90 && 3) || (x > 90 && 4);
		};
		let score = this.factoryObject(angles, validation);
		return { score };
	}

	async briefBestReport(parameters) {
		const { organization_id, company_id, upload_id, config } = parameters;

		const file = await this.reportRepository.checkExistsUpload({
			organization_id,
			company_id,
			upload_id
		});

		if (!file) {
			throw new APIError('NOT FOUND', HttpStatusCode.NOT_FOUND, true, 'File not found');
		}

		// if (file.tool !== 'BRIEF_BEST') {
		//   throw new APIError('BAD REQUEST', HttpStatusCode.BAD_REQUEST, true, 'Failed to submit to this tool');
		// }

		const configBucket = {
			Key: file.generated_name,
			organization_id,
			company_id
		};

		const storage = new Storage('sa-east-1');

		const payload = await storage.downloadFileData(configBucket);

		// const payload = mockBriefBest;

		// const config1 = {
		//   force: {
		//     hands: {
		//       left: 0,
		//       right: 0
		//     },
		//     trunk: 0,
		//     legs: 0,
		//     neck: 0,
		//     left_elbow: 0,
		//     right_elbow: 0,
		//     left_shoulder: 0,
		//     right_shoulder: 0
		//   },
		//   hands_duration: {
		//     left: 0,
		//     right: 0
		//   },
		//   hands_frequency: {
		//     left: 0,
		//     right: 0
		//   },
		//   hands_position: {
		//     left: 0,
		//     right: 0
		//   },
		//   externals_factors: {
		//     impact: 1,
		//     gloves: 0,
		//     vibration: 1,
		//     temperature: 1,
		//     compression: 1
		//   },
		//   factor_exposition: {
		//     more_forty: 0,
		//     twenty_forty: 0,
		//     four_nineteen: 0,
		//     less_four: 1
		//   }
		// };

		const data = await this.briefBest.frenquencyAndDuration(payload);

		const sumBrief = await this.briefBest.sumBrief({
			config,
			data,
			payload
		});

		const totalFactorConversion = await this.briefBest.sumFactorConversion(sumBrief);

		const scoreFinal = await this.briefBest.sumFinalScoreBriefBest({
			data: totalFactorConversion,
			config
		});

		return scoreFinal;

		// const briefbest = new BriefBest1();

		// if (Object.keys(payload).length === 0 || payload === null) {
		//   throw new APIError('BAD REQUEST', HttpStatusCode.BAD_REQUEST, true, 'It was not possible to process the request body of the object');
		// }

		// const data = await briefbest.frequencyAndDuration(payload);

		// if (!data || data.length === 0) {
		//   throw new APIError('BAD REQUEST', HttpStatusCode.BAD_REQUEST, true, 'Could not calculate frequency and duration');
		// }

		// const config_body = { config, data, payload };

		// const sumBrief = await briefbest.calculateBrief(config_body);

		// const scoreFinal = await briefbest.sumFinalScoreBriefBest({
		//   data: sumBrief, config
		// });

		// return scoreFinal;
	}

	async briefBestAngles(parameters) {
		const { organization_id, company_id, upload_id } = parameters;

		const file = await this.reportRepository.checkExistsUpload({
			organization_id,
			company_id,
			upload_id
		});

		if (!file) {
			throw new APIError('NOT FOUND', HttpStatusCode.NOT_FOUND, true, 'File not found');
		}

		const configBucket = {
			Key: file.generated_name,
			organization_id,
			company_id
		};

		const storage = new Storage('sa-east-1');

		const payload = await storage.downloadFileData(configBucket);

		// const payload = mockBriefBest;

		let result = {};

		const body_members = {
			superior: ['extension', 'none'],
			inferior: ['crouched', 'downed', 'none'],
			shoulder: ['arm_raised', 'behind', 'raised', 'none'],
			torso: ['flexion', 'turned', 'extension', 'sideways', 'none']
		};

		const { inferior, superior, torso, shoulder } = body_members;

		const movements = {
			trunk: torso,
			neck: torso,
			legs: inferior,
			left_elbow: superior,
			right_elbow: superior,
			left_shoulder: shoulder,
			right_shoulder: shoulder
		};

		Object.keys(payload).forEach((key) => {
			movements[key].forEach((element) => _.set(result, `${key}.${element}`, 0));
			for (let prop in payload[key]) {
				const [object] = payload[key][prop];
				result[key][object.type] += 1;
			}
		});

		return result;
	}

	async extractReportBriefBest(parameters) {
		const { organization_id, company_id, upload_id } = parameters;

		const file = await this.reportRepository.checkExistsUpload({
			organization_id,
			company_id,
			upload_id
		});

		if (!file) {
			throw new APIError('NOT FOUND', HttpStatusCode.NOT_FOUND, true, 'File not found');
		}

		const report = new Report();

		const config = {
			template: '_aYmVxK',
			data: {
				tool: {
					name: 'rula',
					description: 'Análise de movimento por ângulo'
				},
				company: {
					name: 'Renault do Brasil S.A',
					sector: 'Leo',
					date: '01/11/2020',
					workstation: 'Primer'
				},
				archive: {
					original_name: '123.mp3',
					duration: '12:21'
				},
				conclusion: {
					text: '9237498',
					author: 'Leonardo Grandi'
				},
				data: {
					trunk: [
						['x', 'low', 'moderate', 'high', 'critical', 'Angle'],
						[0, 5, 15, 40, 55, 0],
						[1, 5, 15, 40, 55, 1],
						[2, 5, 15, 40, 55, 11],
						[3, 5, 15, 40, 55, 0],
						[4, 5, 15, 40, 55, 0],
						[5, 5, 15, 40, 55, 0],
						[6, 5, 15, 40, 55, 3],
						[7, 5, 15, 40, 55, 0],
						[8, 5, 15, 40, 55, 0],
						[9, 5, 15, 40, 55, 46],
						[10, 5, 15, 40, 55, 27],
						[11, 5, 15, 40, 55, 0],
						[12, 5, 15, 40, 55, 0],
						[13, 5, 15, 40, 55, 0],
						[14, 5, 15, 40, 55, 1],
						[15, 5, 15, 40, 55, 1],
						[16, 5, 15, 40, 55, 1],
						[17, 5, 15, 40, 55, 2],
						[18, 5, 15, 40, 55, 1],
						[19, 5, 15, 40, 55, 2],
						[20, 5, 15, 40, 55, 1],
						[21, 5, 15, 40, 55, 1],
						[22, 5, 15, 40, 55, 0],
						[23, 5, 15, 40, 55, 21],
						[24, 5, 15, 40, 55, 5],
						[25, 5, 15, 40, 55, 5],
						[26, 5, 15, 40, 55, 1],
						[27, 5, 15, 40, 55, 6],
						[28, 5, 15, 40, 55, 3],
						[29, 5, 15, 40, 55, 0],
						[30, 5, 15, 40, 55, 2],
						[31, 5, 15, 40, 55, 0],
						[32, 5, 15, 40, 55, 1],
						[33, 5, 15, 40, 55, 10]
					],
					neck: [
						['x', 'critical', 'low', 'moderate', 'high', 'Angle'],
						[0, -15, 25, 10, 25, 0],
						[1, -15, 25, 10, 25, 0],
						[2, -15, 25, 10, 25, -15],
						[3, -15, 25, 10, 25, 42],
						[4, -15, 25, 10, 25, 44],
						[5, -15, 25, 10, 25, -15],
						[6, -15, 25, 10, 25, 39],
						[7, -15, 25, 10, 25, -15],
						[8, -15, 25, 10, 25, 45],
						[9, -15, 25, 10, 25, 45],
						[10, -15, 25, 10, 25, 45],
						[11, -15, 25, 10, 25, 42],
						[12, -15, 25, 10, 25, 42],
						[13, -15, 25, 10, 25, -15],
						[14, -15, 25, 10, 25, 45],
						[15, -15, 25, 10, 25, 45],
						[16, -15, 25, 10, 25, 45],
						[17, -15, 25, 10, 25, 45],
						[18, -15, 25, 10, 25, 45],
						[19, -15, 25, 10, 25, 45],
						[20, -15, 25, 10, 25, 45],
						[21, -15, 25, 10, 25, 40],
						[22, -15, 25, 10, 25, 40],
						[23, -15, 25, 10, 25, 40],
						[24, -15, 25, 10, 25, 45],
						[25, -15, 25, 10, 25, 45],
						[26, -15, 25, 10, 25, 42],
						[27, -15, 25, 10, 25, 36],
						[28, -15, 25, 10, 25, 33],
						[29, -15, 25, 10, 25, 28],
						[30, -15, 25, 10, 25, 43],
						[31, -15, 25, 10, 25, 45],
						[32, -15, 25, 10, 25, 45],
						[33, -15, 25, 10, 25, 45]
					],
					left_lower_arm: [
						['x', 'moderate', 'low', 'moderate', 'Angle'],
						[0, 60, 40, 80, 0],
						[1, 60, 40, 80, 92],
						[2, 60, 40, 80, 119],
						[3, 60, 40, 80, 160],
						[4, 60, 40, 80, 161],
						[5, 60, 40, 80, 144],
						[6, 60, 40, 80, 34],
						[7, 60, 40, 80, 22],
						[8, 60, 40, 80, 28],
						[9, 60, 40, 80, 37],
						[10, 60, 40, 80, 72],
						[11, 60, 40, 80, 131],
						[12, 60, 40, 80, 101],
						[13, 60, 40, 80, 125],
						[14, 60, 40, 80, 157],
						[15, 60, 40, 80, 146],
						[16, 60, 40, 80, 143],
						[17, 60, 40, 80, 148],
						[18, 60, 40, 80, 141],
						[19, 60, 40, 80, 137],
						[20, 60, 40, 80, 132],
						[21, 60, 40, 80, 116],
						[22, 60, 40, 80, 126],
						[23, 60, 40, 80, 125],
						[24, 60, 40, 80, 133],
						[25, 60, 40, 80, 138],
						[26, 60, 40, 80, 117],
						[27, 60, 40, 80, 43],
						[28, 60, 40, 80, 23],
						[29, 60, 40, 80, 22],
						[30, 60, 40, 80, 37],
						[31, 60, 40, 80, 33],
						[32, 60, 40, 80, 48],
						[33, 60, 40, 80, 56]
					],
					right_lower_arm: [
						['x', 'moderate', 'low', 'moderate', 'Angle'],
						[0, 60, 40, 80, 0],
						[1, 60, 40, 80, 7],
						[2, 60, 40, 80, 41],
						[3, 60, 40, 80, 41],
						[4, 60, 40, 80, 41],
						[5, 60, 40, 80, 48],
						[6, 60, 40, 80, 45],
						[7, 60, 40, 80, 8],
						[8, 60, 40, 80, 9],
						[9, 60, 40, 80, 10],
						[10, 60, 40, 80, 14],
						[11, 60, 40, 80, 16],
						[12, 60, 40, 80, 39],
						[13, 60, 40, 80, 47],
						[14, 60, 40, 80, 42],
						[15, 60, 40, 80, 43],
						[16, 60, 40, 80, 47],
						[17, 60, 40, 80, 52],
						[18, 60, 40, 80, 44],
						[19, 60, 40, 80, 44],
						[20, 60, 40, 80, 43],
						[21, 60, 40, 80, 43],
						[22, 60, 40, 80, 43],
						[23, 60, 40, 80, 41],
						[24, 60, 40, 80, 43],
						[25, 60, 40, 80, 45],
						[26, 60, 40, 80, 36],
						[27, 60, 40, 80, 18],
						[28, 60, 40, 80, 11],
						[29, 60, 40, 80, 11],
						[30, 60, 40, 80, 56],
						[31, 60, 40, 80, 77],
						[32, 60, 40, 80, 49],
						[33, 60, 40, 80, 8]
					],
					left_upper_arm: [
						['x', 'moderate', 'low', 'moderate', 'high', 'critical', 'critical', 'Angle'],
						[0, -40, 20, 40, 25, 45, 90, 20],
						[1, -40, 20, 40, 25, 45, 90, 25],
						[2, -40, 20, 40, 25, 45, 90, -20],
						[3, -40, 20, 40, 25, 45, 90, -10],
						[4, -40, 20, 40, 25, 45, 90, 20],
						[5, -40, 20, 40, 25, 45, 90, -15],
						[6, -40, 20, 40, 25, 45, 90, 34],
						[7, -40, 20, 40, 25, 45, 90, 50],
						[8, -40, 20, 40, 25, 45, 90, 67],
						[9, -40, 20, 40, 25, 45, 90, 43],
						[10, -40, 20, 40, 25, 45, 90, 13]
					],
					right_upper_arm: [
						['x', 'moderate', 'low', 'moderate', 'high', 'critical', 'critical', 'Angle'],
						[0, -40, 20, 40, 25, 45, 90, 25],
						[1, -40, 20, 40, 25, 45, 90, 15],
						[2, -40, 20, 40, 25, 45, 90, -20],
						[3, -40, 20, 40, 25, 45, 90, -18],
						[4, -40, 20, 40, 25, 45, 90, 39],
						[5, -40, 20, 40, 25, 45, 90, -15],
						[6, -40, 20, 40, 25, 45, 90, 34],
						[7, -40, 20, 40, 25, 45, 90, 50],
						[8, -40, 20, 40, 25, 45, 90, 67],
						[9, -40, 20, 40, 25, 45, 90, 43],
						[10, -40, 20, 40, 25, 45, 90, 13]
					]
				}
			}
		};

		const document = await report.generatedArchive(config);

		if (!document.data) {
			throw new APIError('INTERNAL SERVER', HttpStatusCode.INTERNAL_SERVER, true, 'Error generating file');
		}

		let buffer = Buffer.from(document.data);

		return buffer;
	}

	removeExtension(filename) {
		return filename.substring(0, filename.lastIndexOf('.')) || filename;
	}

	async riskDegreeWorkstation(parameters) {
		const { file_id, organization_id, company_id } = parameters;
		const { legs, force, repetition } = parameters;

		const file = await this.repository.db.File.findOne({
			where: {
				id: file_id,
				organization_id: organization_id,
				company_id: company_id,
				isActive: true
			}
		});

		if (!file) {
			throw new AppError(FILE.NOT_FOUND);
		}

		const s3 = new StorageS3();

		let keyWithoutExtension = this.removeExtension(file.generated_name);

		if (!keyWithoutExtension.includes('/')) {
			let first = organization_id.split('-', 5)[0];
			let second = company_id.split('-', 5)[0];
			keyWithoutExtension = `${first}-${second}/${keyWithoutExtension}`;
		}

		let bucket = config.get('App.bucket');

		const storageConfig = {
			Bucket: bucket,
			Key: `${keyWithoutExtension}.json`
		};

		const [stream, err] = await s3.getObject(storageConfig);

		if (err) {
			throw new AppError(STORAGE.FAIL_DOWNLOAD_DATA);
		}

		const payload = JSON.parse(stream.Body.toString('utf-8'));

		let result = {};

		let aux = {
			neck: [],
			trunk: [],
			left_lower_arm: [],
			right_lower_arm: [],
			left_upper_arm: [],
			right_upper_arm: []
		};

		let onlyParts = ['neck', 'trunk', 'left_lower_arm', 'right_lower_arm', 'left_upper_arm', 'right_upper_arm'];

		for (let key in payload) {
			if (onlyParts.includes(key)) {
				payload[key].forEach((item) => (result[key] = this.calculateParts(key, item)));
			}
		}

		for (let key in result) {
			result[key].score.map((item) => {
				const [keyObject] = Object.keys(item);
				aux[key].push(item[keyObject].score_part);
			});
		}

		let resulFinal = {};

		for (let key in result) {
			resulFinal[key] = result[key].score.map((item, index) => {
				let upperRight = 1,
					lowerRight = 1,
					upperLeft = 1,
					lowerLeft = 1,
					neck = 1,
					trunk = 1;

				if (key === 'left_lower_arm') {
					lowerLeft = aux[key][index] |= 1;
				}

				if (key === 'left_upper_arm') {
					upperLeft = aux[key][index] |= 1;
				}

				if (key === 'right_lower_arm') {
					lowerRight = aux[key][index] |= 1;
				}

				if (key === 'right_upper_arm') {
					upperRight = aux[key][index] |= 1;
				}

				if (key === 'neck') {
					neck = aux[key][index] |= 1;
				}

				if (key === 'trunk') {
					trunk = aux[key][index] |= 1;
				}

				let scoreLeft = this.tableScore.Rula.tableA[`${upperLeft}`][lowerLeft - 1];
				let scoreRight = this.tableScore.Rula.tableA[`${upperRight}`][lowerRight - 1];
				let scoreB =
					Number(legs) === 1
						? this.tableScore.Rula.tableB1[`${neck}`][trunk - 1]
						: this.tableScore.Rula.tableB2[`${neck}`][trunk - 1];

				let scoreArmLeft =
					scoreLeft + Number(force) + (Number(repetition) >= 4 ? 1 : 0) >= 8
						? 8
						: scoreLeft + Number(force) + (Number(repetition) >= 4 ? 1 : 0);
				let scoreArmRight =
					scoreRight + Number(force) + (Number(repetition) >= 4 ? 1 : 0) >= 8
						? 8
						: scoreRight + Number(force) + (Number(repetition) >= 4 ? 1 : 0);

				let conditionScoreNTL =
					scoreB + Number(force) + (Number(repetition) >= 4 ? 1 : 0) >= 7
						? 7
						: scoreB + Number(force) + (Number(repetition) >= 4 ? 1 : 0);

				let scoreCLeft = this.tableScore.Rula.tableC[`${scoreArmLeft}`][conditionScoreNTL - 1];
				let scoreCRight = this.tableScore.Rula.tableC[`${scoreArmRight}`][conditionScoreNTL - 1];

				return {
					...item[`${index + 1}`],
					score_a_left: scoreLeft,
					score_a_right: scoreRight,
					score_arm_left: scoreArmLeft,
					score_arm_right: scoreArmRight,
					score_b: scoreB,
					score_c_left: scoreCLeft,
					score_c_right: scoreCRight,
					score_ntl: conditionScoreNTL,
					score_c: (scoreCLeft + scoreCRight) / 2
				};
			});
		}

		let riskCount = { total: 0, count: 0 };

		resulFinal.trunk.map((item) => {
			riskCount.total += item.score_c;
			riskCount.count++;
		});

		const rulaScore = Math.round(riskCount.total / riskCount.count);

		return { score: rulaScore };
	}

	async rulaRiskDegreeBodyParts(parameters) {
		const { organization_id, company_id, file_id } = parameters;

		const file = await this.repository.db.File.findOne({
			where: {
				id: file_id,
				organization_id: organization_id,
				company_id: company_id,
				isActive: true
			}
		});

		if (!file) {
			throw new AppError(FILE.NOT_FOUND);
		}

		// const storage = new Storage('sa-east-1');

		// const payload = {
		// 	Key: file.generated_name,
		// 	organization_id,
		// 	company_id,
		// };

		// const data = await storage.downloadFileData(payload);

		// if (!data) {
		// 	throw new ErrorHandler(404, 'Data not found');
		// }

		// const file = {
		// 	id: '12781cd0-8da3-11ec-a0be-795dcf96d25a',
		// 	generated_name: '2caecff2-c7b2-4d1c-afc2-65b275528723-colocando caixa na esteira.mp4'
		// };
		// const organization_id = '9e2fbb70-785e-11ec-a136-91b1ea157d8f';
		// const company_id = 'a915cd40-785e-11ec-a136-91b1ea157d8f';

		const s3 = new StorageS3();

		// const Bucket = StorageS3.bucketName(organization_id, company_id)
		// const Key = StorageS3.changeExtension(file.generated_name, 'json')

		let keyWithoutExtension = this.removeExtension(file.generated_name);

		if (!keyWithoutExtension.includes('/')) {
			let first = organization_id.split('-', 5)[0];
			let second = company_id.split('-', 5)[0];
			keyWithoutExtension = `${first}-${second}/${keyWithoutExtension}`;
		}

		let bucket = config.get('App.bucket');

		const storageConfig = {
			Bucket: bucket,
			Key: `${keyWithoutExtension}.json`
		};

		const [stream, err] = await s3.getObject(storageConfig);

		if (err) {
			throw new AppError(STORAGE.FAIL_DOWNLOAD_DATA);
		}

		const payload = JSON.parse(stream.Body.toString('utf-8'));

		let result = {};

		let onlyParts = ['neck', 'trunk', 'left_lower_arm', 'right_lower_arm', 'left_upper_arm', 'right_upper_arm'];

		for (let key in payload) {
			if (onlyParts.includes(key)) {
				payload[key].map((item) => (result[key] = enumParts[key].calculate(item.angles)));
			}
		}

		return result;
	}
}
