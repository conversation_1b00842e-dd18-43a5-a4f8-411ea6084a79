import { AbstractReportSandboxService } from './abstract_report_sandbox.js';
import { logger, AppError } from '../../helpers/index.js';
import { ERROR_RESPONSE_ENTITIES_ENUM } from '../../util/enum.js';
import {
	CustomReportSandboxService,
	SeraReportSandboxService,
	PeaReportSandboxService,
	AngleTimeReportSandboxService,
	BackCompressiveReportSandboxService,
	KimMhoReportSandboxService,
	KimPpReportSandboxService,
	LibertyMutualReportSandboxService,
	NioshReportSandboxService,
	RebaReportSandboxService,
	RecoveryReportSandboxService,
	StrainIndexReportSandboxService
} from './index.js';
import { ActionPlanSql } from '../../mappers/Sandbox/ActionPlanSql.js';
import { Op } from 'sequelize';

export class ActionPlanSandboxService extends AbstractReportSandboxService {
	ewa_services = {
		angle_time: {
			service: AngleTimeReportSandboxService,
			query: 'angle_time_reports'
		},
		back_compressive_force_estimation: {
			service: BackCompressiveReportSandboxService,
			query: 'back_compressive_force_estimation_reports'
		},
		kim_mho: {
			service: KimMhoReportSandboxService,
			query: 'kim_manual_handling_reports'
		},
		kim_pp: {
			service: KimPpReportSandboxService,
			query: 'kim_push_pull_reports'
		},
		liberty_mutual: {
			service: LibertyMutualReportSandboxService,
			query: 'liberty_mutual_reports'
		},
		niosh: {
			service: NioshReportSandboxService,
			query: 'niosh_reports'
		},
		reba: {
			service: RebaReportSandboxService,
			query: 'reba_reports'
		},
		rula: {
			service: RecoveryReportSandboxService,
			query: 'recovery_reports'
		},
		strain_index: {
			service: StrainIndexReportSandboxService,
			query: 'strain_index_reports'
		}
	};

	constructor({ kinebot_repository, sandbox_repository }) {
		super({ kinebot_repository, sandbox_repository });
		this.repository_params = {
			kinebot_repository,
			sandbox_repository
		};
	}

	async sendReport(id) {
		logger.info(`[ActionPlanSandboxService] Sending report ${id}`);

		const transactions = {
			kinebot: await this.kinebot_repository.db.sequelize.transaction(),
			sandbox: await this.sandbox_repository.db.sequelize.transaction()
		};

		try {
			logger.info(`[ActionPlanSandboxService] Processing action plan ${id}`);
			await this.send(id, transactions, true);

			await transactions.kinebot.commit();
			await transactions.sandbox.commit();

			logger.info(`[ActionPlanSandboxService] Report ${id} sent successfully`);
		} catch (error) {
			logger.error(`[ActionPlanSandboxService] Error sending report ${id}: ${error} `);
			await transactions.kinebot.rollback();
			await transactions.sandbox.rollback();
			throw error;
		}
	}

	async send(id, transactions, use_hierarchy = false) {
		const action_plan = await this.sandbox_repository.db.ActionPlanV2.findByPk(id, {
			transaction: transactions.sandbox
		});

		if (!action_plan) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.ACTION_PLAN.NOT_FOUND);
		}

		const action_plan_origin = await this.sandbox_repository.db.ActionPlanOrigin.findByPk(
			action_plan.action_plan_origin_id,
			{
				transaction: transactions.sandbox
			}
		);

		const action_plan_attachments = await this.sandbox_repository.db.ActionPlanAttachment.findAll({
			where: {
				action_plan_id: id
			},
			transaction: transactions.sandbox
		});

		const action_plan_tasks = await this.sandbox_repository.db.ActionPlanTask.findAll({
			where: {
				action_plan_id: id
			},
			transaction: transactions.sandbox
		});

		const action_plan_task_attachments = await this.sandbox_repository.db.ActionPlanTaskAttachment.findAll({
			where: {
				action_plan_task_id: {
					[Op.in]: action_plan_tasks.map((task) => task.id)
				}
			},
			transaction: transactions.sandbox
		});

		const action_plan_related_reports = await this.sandbox_repository.db.ActionPlanRelatedReport.findAll({
			where: {
				action_plan_id: id
			},
			include: [
				{
					model: this.sandbox_repository.db.ErgonomicTool,
					as: 'ergonomic_tool',
					required: true
				}
			],
			transaction: transactions.sandbox
		});

		const action_plan_histories = await this.sandbox_repository.db.ActionPlanHistoryV2.findAll({
			where: {
				action_plan_id: id
			},
			transaction: transactions.sandbox
		});

		const action_plan_comments = await this.sandbox_repository.db.ActionPlanComment.findAll({
			where: {
				action_plan_id: id
			},
			transaction: transactions.sandbox
		});

		if (action_plan_origin.origin_name && !action_plan_origin.table_name.includes('production:') && use_hierarchy) {
			const { service, query, is_direct_query } = this.#setHierarchy(
				action_plan_origin.origin_name,
				action_plan_origin.table_name
			);

			const replacements = is_direct_query ? { id: action_plan_origin.column_id } : { id: action_plan.id };

			const [report] = await this.sandbox_repository.db.sequelize.query(query, {
				transaction: transactions.sandbox,
				replacements
			});

			if (report.length > 0) await service.send(report[0].id, transactions);
		}

		action_plan_origin.table_name = action_plan_origin.table_name.replace('production:', '');
		const processed_action_plan_origin = await this.create(action_plan_origin, 'ActionPlanOrigin', transactions);
		action_plan.action_plan_origin_id = processed_action_plan_origin.id;

		if (action_plan.activity_id) {
			const activity = await this.sandbox_repository.db.Activity.findByPk(action_plan.activity_id, {
				transaction: transactions.sandbox
			});

			const processed_workstation = await this.getOrCreateSimpleDependency(
				activity.workstation_id,
				'Workstation',
				transactions
			);

			activity.workstation_id = processed_workstation.id;

			const activity_data = { ...(activity.dataValues || activity) };
			delete activity_data.id;
			delete activity_data.created_at;
			delete activity_data.updated_at;

			const [processed_activity] = await this.kinebot_repository.db.Activity.findOrCreate({
				where: {
					name: activity.name,
					workstation_id: processed_workstation.id
				},
				defaults: {
					...activity_data
				},
				transaction: transactions.sandbox
			});

			action_plan.activity_id = processed_activity.id;
		}

		const processed_workstation = await this.getOrCreateSimpleDependency(
			action_plan.workstation_id,
			'Workstation',
			transactions
		);
		action_plan.workstation_id = processed_workstation.id;

		const processed_file = await this.getOrCreateFile(action_plan.file_id, transactions);
		action_plan.file_id = processed_file.id;

		const processed_action_plan = await this.create(action_plan, 'ActionPlanV2', transactions);

		for (const action_plan_attachment of action_plan_attachments) {
			action_plan_attachment.action_plan_id = processed_action_plan.id;
			await this.create(action_plan_attachment, 'ActionPlanAttachment', transactions);
		}

		for (const action_plan_task of action_plan_tasks) {
			action_plan_task.action_plan_id = processed_action_plan.id;

			const processed_action_plan_task = await this.create(action_plan_task, 'ActionPlanTask', transactions);

			for (const action_plan_task_attachment of action_plan_task_attachments) {
				action_plan_task_attachment.action_plan_task_id = processed_action_plan_task.id;
				await this.create(action_plan_task_attachment, 'ActionPlanTaskAttachment', transactions);
			}
		}

		for (const action_plan_related_report of action_plan_related_reports) {
			action_plan_related_report.action_plan_id = processed_action_plan.id;
			const { service: ewa_service, query } = this.#setEwaHierarchy(
				action_plan_related_report.ergonomic_tool?.name
			);
			const [report] = await this.sandbox_repository.db.sequelize.query(query, {
				transaction: transactions.sandbox,
				replacements: {
					id: action_plan_related_report.column_id
				}
			});

			if (report.length > 0) {
				const processed_report = await ewa_service.send(report[0].id, transactions);
				action_plan_related_report.report_id = processed_report.id;
			}

			await this.create(action_plan_related_report, 'ActionPlanRelatedReport', transactions);
		}

		for (const action_plan_history of action_plan_histories) {
			action_plan_history.action_plan_id = processed_action_plan.id;
			await this.create(action_plan_history, 'ActionPlanHistoryV2', transactions);
		}

		for (const action_plan_comment of action_plan_comments) {
			action_plan_comment.action_plan_id = processed_action_plan.id;
			await this.create(action_plan_comment, 'ActionPlanComment', transactions);
		}

		await this.sandbox_repository.db.ActionPlanComment.destroy({
			where: {
				action_plan_id: action_plan.id
			},
			force: true,
			transaction: transactions.sandbox
		});

		await this.sandbox_repository.db.ActionPlanHistoryV2.destroy({
			where: {
				action_plan_id: action_plan.id
			},
			force: true,
			transaction: transactions.sandbox
		});

		await this.sandbox_repository.db.ActionPlanRelatedReport.destroy({
			where: {
				action_plan_id: action_plan.id
			},
			force: true,
			transaction: transactions.sandbox
		});

		await this.sandbox_repository.db.ActionPlanTaskAttachment.destroy({
			where: {
				action_plan_task_id: {
					[Op.in]: action_plan_tasks.map((task) => task.id)
				}
			},
			force: true,
			transaction: transactions.sandbox
		});

		await this.sandbox_repository.db.ActionPlanTask.destroy({
			where: {
				action_plan_id: action_plan.id
			},
			force: true,
			transaction: transactions.sandbox
		});

		await this.sandbox_repository.db.ActionPlanAttachment.destroy({
			where: {
				action_plan_id: action_plan.id
			},
			force: true,
			transaction: transactions.sandbox
		});

		await this.sandbox_repository.db.ActionPlanV2.destroy({
			where: {
				id: action_plan.id
			},
			force: true,
			transaction: transactions.sandbox
		});

		await this.sandbox_repository.db.ActionPlanOrigin.destroy({
			where: {
				id: action_plan_origin.id
			},
			force: true,
			transaction: transactions.sandbox
		});
	}

	#setHierarchy(origin_name, table_name) {
		const mapper = new ActionPlanSql();
		const use_direct_query = table_name === 'custom_report_results';

		switch (origin_name) {
			case 'SERA':
				return {
					service: new SeraReportSandboxService(this.repository_params),
					query: mapper.getSeraSummaryId(),
					is_direct_query: false
				};
			case 'JDS-D86':
				return {
					service: new CustomReportSandboxService(this.repository_params),
					query: use_direct_query
						? mapper.getDirectCustomReportWithReviewId()
						: mapper.getCustomReportWithReviewId(),
					is_direct_query: use_direct_query
				};
			default:
				return {
					service: new PeaReportSandboxService(this.repository_params),
					query: use_direct_query ? mapper.getDirectCustomReportId() : mapper.getCustomReportId(),
					is_direct_query: use_direct_query
				};
		}
	}

	#setEwaHierarchy(ewa_name) {
		const service = this.ewa_services[ewa_name];

		if (!service) {
			throw new Error(`EWA service ${ewa_name} not found`);
		}

		return {
			service: service.service(this.repository_params),
			query: mapper.getEwaReportId(service.query)
		};
	}
}
