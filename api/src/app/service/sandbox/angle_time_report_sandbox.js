import { AbstractReportSandboxService } from './abstract_report_sandbox.js';
import { logger, AppError } from '../../helpers/index.js';
import { ERROR_RESPONSE_ENTITIES_ENUM } from '../../util/enum.js';

export class AngleTimeReportSandboxService extends AbstractReportSandboxService {
	constructor({ kinebot_repository, sandbox_repository }) {
		super({ kinebot_repository, sandbox_repository });
	}

	async sendReport(id) {
		logger.info(`Sending Angle Time Reports ${id}`);

		const transactions = {
			kinebot: await this.kinebot_repository.db.sequelize.transaction(),
			sandbox: await this.sandbox_repository.db.sequelize.transaction()
		};

		try {
			await this.send(id, transactions);

			await transactions.kinebot.commit();
			await transactions.sandbox.commit();

			logger.info(`Angle Time Reports ${id} sent successfully`);
		} catch (error) {
			logger.error(`Error sending Angle Time Reports ${id}: ${error}`);
			await transactions.kinebot.rollback();
			await transactions.sandbox.rollback();
			throw error;
		}
	}

	async send(id, transactions) {
		const angle_time_report = await this.sandbox_repository.db.AngleTimeReport.findByPk(id, {
			transaction: transactions.sandbox
		});

		if (!angle_time_report) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.ANGLE_TIME_REPORT.NOT_FOUND);
		}

		const processed_file = await this.getOrCreateFile(angle_time_report.file_id, transactions);

		const exists_angle_time_report = await this.kinebot_repository.db.AngleTimeReport.findOne({
			transaction: transactions.kinebot,
			where: {
				file_id: angle_time_report.file_id
			}
		});

		if (exists_angle_time_report) {
			logger.warn(`[AngleTimeReportSandbox] Angle Time Report ${id} already exists in production`);
			return;
		}

		angle_time_report.file_id = processed_file.id;

		const processed_angle_time_report = await this.create(angle_time_report, 'AngleTimeReport', transactions);

		// Update if action plan related report exists
		const action_plan_related_report_to_update = await this.sandbox_repository.db.ActionPlanRelatedReport.findOne({
			transaction: transactions.sandbox,
			include: [
				{
					as: 'ergonomic_tool',
					where: { name: 'angle_time' },
					required: true
				}
			],
			where: {
				report_id: angle_time_report.id
			}
		});

		if (action_plan_related_report_to_update) {
			action_plan_related_report_to_update.report_id = processed_angle_time_report.id;
			await action_plan_related_report_to_update.save({ transaction: transactions.sandbox });
		}

		await this.sandbox_repository.db.AngleTimeReport.destroy({
			where: { id },
			transaction: transactions.sandbox,
			force: true
		});
	}
}
