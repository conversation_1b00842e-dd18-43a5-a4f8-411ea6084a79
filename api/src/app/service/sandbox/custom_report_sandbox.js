import { Op } from 'sequelize';
import { logger, AppError } from '../../helpers/index.js';
import { ERROR_RESPONSE_ENTITIES_ENUM } from '../../util/enum.js';
import { CustomReport } from '../../entities/Sandbox/CustomReport.js';
import { AbstractReportSandboxService } from './abstract_report_sandbox.js';
import {
	AngleTimeReportSandboxService,
	BackCompressiveReportSandboxService,
	KimMhoReportSandboxService,
	KimPpReportSandboxService,
	LibertyMutualReportSandboxService,
	NioshReportSandboxService,
	RebaReportSandboxService,
	RecoveryReportSandboxService,
	StrainIndexReportSandboxService,
	ActionPlanSandboxService
} from './index.js';

export class CustomReportSandboxService extends AbstractReportSandboxService {
	ewa_services = {
		angle_time: {
			service: AngleTimeReportSandboxService
		},
		back_compressive_force_estimation: {
			service: BackCompressiveReportSandboxService
		},
		kim_mho: {
			service: KimMhoReportSandboxService
		},
		kim_push_pull: {
			service: KimPpReportSandboxService
		},
		liberty_mutual: {
			service: LibertyMutualReportSandboxService
		},
		niosh: {
			service: NioshReportSandboxService
		},
		reba: {
			service: RebaReportSandboxService
		},
		rula: {
			service: RecoveryReportSandboxService
		},
		strain_index: {
			service: StrainIndexReportSandboxService
		},
		action_plan: {
			service: ActionPlanSandboxService
		}
	};
	constructor({ kinebot_repository, sandbox_repository }) {
		super({ kinebot_repository, sandbox_repository });
	}

	async sendReport(id) {
		logger.info(`[CustomReportSandboxService] Sending report ${id}`);
		const transactions = {
			kinebot: await this.kinebot_repository.db.sequelize.transaction(),
			sandbox: await this.sandbox_repository.db.sequelize.transaction()
		};

		try {
			await this.send(id, transactions);

			await transactions.kinebot.commit();
			await transactions.sandbox.commit();
			logger.info(`[CustomReportSandboxService] Report ${id} sent`);
		} catch (error) {
			logger.error(`[CustomReportSandboxService] Error sending report ${id}: ${error}`);
			await transactions.kinebot.rollback();
			await transactions.sandbox.rollback();
			throw error;
		}
	}

	async send(id, transactions) {
		logger.info(`[CustomReportSandboxService] Sending custom report result ${id}`);

		const custom_report_entity = new CustomReport();

		const custom_report_reviews = await this.sandbox_repository.db.CustomReportReview.findAll({
			where: {
				original_custom_report_result_id: id
			},
			transaction: transactions.sandbox
		});

		if (!custom_report_reviews) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.CUSTOM_REPORT.NOT_FOUND);
		}

		const custom_report_results_ids = custom_report_reviews.map((review) => review.custom_report_result_id);

		const custom_report_results = await this.sandbox_repository.db.CustomReportResult.findAll({
			where: {
				id: {
					[Op.in]: custom_report_results_ids
				}
			},
			transaction: transactions.sandbox
		});

		const custom_report_step_key_results = await this.sandbox_repository.db.CustomReportStepKeyResult.findAll({
			where: {
				custom_report_result_id: {
					[Op.in]: custom_report_results_ids
				}
			},
			transaction: transactions.sandbox
		});

		const custom_report_sub_step_key_results =
			await this.sandbox_repository.db.CustomReportSubStepKeyResult.findAll({
				where: {
					custom_report_result_id: {
						[Op.in]: custom_report_results_ids
					}
				},
				transaction: transactions.sandbox
			});

		const custom_report_result_actions_logs = await this.sandbox_repository.db.CustomReportResultActionLog.findAll({
			where: {
				custom_report_result_id: {
					[Op.in]: custom_report_results_ids
				}
			},
			transaction: transactions.sandbox
		});

		const custom_report_step_key_additional_item_results =
			await this.sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.findAll({
				where: {
					custom_report_result_id: {
						[Op.in]: custom_report_results_ids
					}
				},
				transaction: transactions.sandbox
			});

		custom_report_entity.setCustomReportReviews(custom_report_reviews);
		custom_report_entity.setCustomReportResults(custom_report_results);
		custom_report_entity.setCustomReportStepKeyResults(custom_report_step_key_results);
		custom_report_entity.setCustomReportSubStepKeyResults(custom_report_sub_step_key_results);
		custom_report_entity.setCustomReportResultActionsLogs(custom_report_result_actions_logs);
		custom_report_entity.setCustomReportStepKeyAdditionalItemResults(
			custom_report_step_key_additional_item_results
		);

		let custom_report_reviews_to_process = custom_report_reviews;
		let custom_report_result_actions_logs_to_process = custom_report_result_actions_logs;
		let custom_report_step_key_additional_item_results_to_process = custom_report_step_key_additional_item_results;
		let custom_report_step_key_results_to_process = custom_report_step_key_results;
		let custom_report_sub_step_key_results_to_process = custom_report_sub_step_key_results;

		for (const custom_report_result of custom_report_results) {
			custom_report_entity.file_ids.push(custom_report_result.file_id);
			const processed_file = await this.getOrCreateFile(custom_report_result.file_id, transactions);
			const processed_evaluator = await this.getOrCreateSimpleDependency(
				custom_report_result.evaluator_id,
				'Evaluator',
				transactions
			);
			custom_report_entity.processed_file_ids.push(processed_file.id);

			custom_report_result.file_id = processed_file.id;
			custom_report_result.evaluator_id = processed_evaluator.id;
			const processed_custom_report_result = await this.create(
				custom_report_result,
				'CustomReportResult',
				transactions
			);

			// Update if exists action plan
			const action_plan_to_update = await this.sandbox_repository.db.ActionPlanOrigin.findOne({
				where: {
					column_id: custom_report_result.id,
					table_name: 'custom_report_results'
				}
			});

			if (action_plan_to_update) {
				action_plan_to_update.column_id = processed_custom_report_result.id;
				action_plan_to_update.table_name = 'production:custom_report_results';
				await action_plan_to_update.save({ transaction: transactions.sandbox });
			}

			for (const custom_report_review of custom_report_reviews_to_process) {
				custom_report_review.original_custom_report_result_id = processed_custom_report_result.id;
				if (custom_report_review.custom_report_result_id !== custom_report_result.id) continue;

				custom_report_review.custom_report_result_id = processed_custom_report_result.id;
			}

			for (const custom_report_result_action_log of custom_report_result_actions_logs_to_process) {
				custom_report_result_action_log.custom_report_result_id = processed_custom_report_result.id;
			}

			for (const custom_report_step_key_additional_item_result of custom_report_step_key_additional_item_results_to_process) {
				custom_report_step_key_additional_item_result.custom_report_result_id =
					processed_custom_report_result.id;
			}

			for (const custom_report_step_key_result of custom_report_step_key_results_to_process) {
				custom_report_step_key_result.custom_report_result_id = processed_custom_report_result.id;
			}

			for (const custom_report_sub_step_key_result of custom_report_sub_step_key_results_to_process) {
				custom_report_sub_step_key_result.custom_report_result_id = processed_custom_report_result.id;
			}
		}

		for (const custom_report_review of custom_report_reviews_to_process) {
			await this.create(custom_report_review, 'CustomReportReview', transactions);
		}

		for (const custom_report_result_action_log of custom_report_result_actions_logs_to_process) {
			await this.create(custom_report_result_action_log, 'CustomReportResultActionLog', transactions);
		}

		for (const custom_report_step_key_additional_item_result of custom_report_step_key_additional_item_results_to_process) {
			if (custom_report_step_key_additional_item_result.custom_report_step_key_additional_item_id) {
				const processed_custom_report_step_key_additional_item = await this.getOrCreateSimpleDependency(
					custom_report_step_key_additional_item_result.custom_report_step_key_additional_item_id,
					'CustomReportStepKeysAdditionalItem',
					transactions,
					{
						where_fields: ['custom_report_step_key_id', 'additional_item_id'],
						exclude_fields: [],
						dependencies: {}
					}
				);
				custom_report_step_key_additional_item_result.custom_report_step_key_additional_item_id =
					processed_custom_report_step_key_additional_item.id;
			}
			await this.create(
				custom_report_step_key_additional_item_result,
				'CustomReportStepKeyAdditionalItemResult',
				transactions
			);
		}

		for (const custom_report_step_key_result of custom_report_step_key_results_to_process) {
			await this.create(custom_report_step_key_result, 'CustomReportStepKeyResult', transactions);
		}

		for (const custom_report_sub_step_key_result of custom_report_sub_step_key_results_to_process) {
			await this.create(custom_report_sub_step_key_result, 'CustomReportSubStepKeyResult', transactions);
		}

		// Destroy

		const custom_report_results_sandbox = custom_report_entity.getCustomReportResults();
		const custom_report_step_key_results_sandbox = custom_report_entity.getCustomReportStepKeyResults();
		const custom_report_step_key_additional_item_results_sandbox =
			custom_report_entity.getCustomReportStepKeyAdditionalItemResults();
		const custom_report_result_actions_logs_sandbox = custom_report_entity.getCustomReportResultActionsLogs();
		const custom_report_reviews_sandbox = custom_report_entity.getCustomReportReviews();

		const custom_report_results_to_destroy = [];

		for (const custom_report_result of custom_report_results_sandbox) {
			custom_report_results_to_destroy.push(custom_report_result.id);
		}

		const custom_report_step_key_result_to_destroy = [];

		for (const custom_report_step_key_result of custom_report_step_key_results_sandbox) {
			custom_report_step_key_result_to_destroy.push(custom_report_step_key_result.id);
		}

		const custom_report_step_key_additional_item_result_to_destroy = [];

		for (const custom_report_step_key_additional_item_result of custom_report_step_key_additional_item_results_sandbox) {
			custom_report_step_key_additional_item_result_to_destroy.push(
				custom_report_step_key_additional_item_result.id
			);
		}

		const custom_report_result_action_log_to_destroy = [];

		for (const custom_report_result_action_log of custom_report_result_actions_logs_sandbox) {
			custom_report_result_action_log_to_destroy.push(custom_report_result_action_log.id);
		}

		const custom_report_review_to_destroy = [];

		custom_report_entity.file_ids.forEach(async (file_id, index) => {
			const processed_file_id = custom_report_entity.processed_file_ids[index];
			await this.#createAETReports(
				file_id,
				processed_file_id,
				custom_report_entity.getFileAETHierarchy(),
				transactions
			);
		});

		for (const custom_report_review of custom_report_reviews_sandbox) {
			custom_report_review_to_destroy.push(custom_report_review.id);
		}

		await this.sandbox_repository.db.CustomReportSubStepKeyResult.destroy({
			where: {
				custom_report_result_id: {
					[Op.in]: custom_report_results_to_destroy
				}
			},
			transaction: transactions.sandbox,
			force: true
		});

		await this.sandbox_repository.db.CustomReportStepKeyResult.destroy({
			where: {
				id: {
					[Op.in]: custom_report_step_key_result_to_destroy
				}
			},
			transaction: transactions.sandbox,
			force: true
		});

		await this.sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.destroy({
			where: {
				id: {
					[Op.in]: custom_report_step_key_additional_item_result_to_destroy
				}
			},
			transaction: transactions.sandbox,
			force: true
		});

		await this.sandbox_repository.db.CustomReportResultActionLog.destroy({
			where: {
				id: {
					[Op.in]: custom_report_result_action_log_to_destroy
				}
			},
			transaction: transactions.sandbox,
			force: true
		});

		await this.sandbox_repository.db.CustomReportReview.destroy({
			where: {
				id: {
					[Op.in]: custom_report_review_to_destroy
				}
			},
			transaction: transactions.sandbox,
			force: true
		});

		await this.sandbox_repository.db.CustomReportResult.destroy({
			where: {
				id: {
					[Op.in]: custom_report_results_to_destroy
				}
			},
			transaction: transactions.sandbox,
			force: true
		});
	}

	async #createAETReports(file_id, processed_file_id, include_tools, transactions) {
		const file = await this.sandbox_repository.db.File.findByPk(file_id, {
			include: include_tools,
			transaction: transactions.sandbox
		});

		const processed_file = await this.kinebot_repository.db.File.findByPk(processed_file_id, {
			include: include_tools,
			transaction: transactions.kinebot
		});

		if (!file || !processed_file) return;

		for (const include_config of include_tools) {
			const { association } = include_config;

			const tool_data = file[association];
			const processed_tool_data = processed_file[association];

			if (!tool_data || processed_tool_data) continue;

			const ewa_service_key = ewa_service_mapping[association] || association;
			const ewa_service_config = this.ewa_services[ewa_service_key];

			if (!ewa_service_config?.service) {
				logger.warn(
					`[CustomReportSandbox] Serviço EWA não encontrado para: ${association} (${ewa_service_key})`
				);
				continue;
			}

			const service = new ewa_service_config.service({
				kinebot_repository: this.kinebot_repository,
				sandbox_repository: this.sandbox_repository
			});

			if (Array.isArray(tool_data)) {
				for (const tool_record of tool_data) {
					await service.send(tool_record.id, transactions);
				}

				continue;
			}

			await service.send(tool_data.id, transactions);
		}
	}
}
