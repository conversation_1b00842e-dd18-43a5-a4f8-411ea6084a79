import { logger } from '../../helpers/index.js';

export class AbstractReportSandboxService {
	constructor({ kinebot_repository, sandbox_repository }) {
		this.kinebot_repository = kinebot_repository;
		this.sandbox_repository = sandbox_repository;
	}

	#omitFields(obj, fieldsToOmit) {
		const result = {};
		for (const [key, value] of Object.entries(obj)) {
			if (!fieldsToOmit.includes(key)) {
				result[key] = value;
			}
		}
		return result;
	}

	async getOrCreateSimpleDependency(
		id,
		model_name,
		transactions,
		options = { where_fields: ['name'], exclude_fields: [], dependencies: {} }
	) {
		const data = await this.sandbox_repository.db[model_name].findByPk(id);

		const cleanData = data.dataValues || data;

		const where = {};
		options.where_fields.forEach((field) => {
			where[field] = cleanData[field];
		});

		const exclude_fields = [...options.exclude_fields, 'id', 'created_at', 'updated_at'];

		const data_to_copy = this.#omitFields(cleanData, exclude_fields);

		if (options.dependencies) {
			for (const [field, value] of Object.entries(options.dependencies)) {
				data_to_copy[field] = value;
			}
		}

		const [record] = await this.kinebot_repository.db[model_name].findOrCreate({
			where,
			defaults: data_to_copy,
			transaction: transactions.kinebot
		});
		return record;
	}

	async create(sandbox_data, model_name, transactions) {
		const data = sandbox_data.dataValues || sandbox_data;

		const clean_data = this.#omitFields(data, [
			'id',
			'created_at',
			'updated_at',
			'FileId',
			'file',
			'createdAt',
			'updatedAt'
		]);

		const record = await this.kinebot_repository.db[model_name].create(clean_data, {
			transaction: transactions.kinebot
		});
		return record;
	}

	async getOrCreateFile(file_id, transactions) {
		const file = await this.sandbox_repository.db.File.findByPk(file_id, {
			include: [
				{
					association: 'workstations'
				}
			]
		});

		if (!file) {
			throw new Error('File not found in sandbox');
		}

		const existing_file = await this.kinebot_repository.db.File.findOne({
			include: [{ association: 'sector' }, { association: 'workstations' }],
			where: {
				original_name: file.original_name,
				generated_name: file.generated_name
			}
		});

		if (existing_file) {
			return existing_file;
		}

		const sector = await this.getOrCreateSimpleDependency(file.sector_id, 'Sector', transactions, {
			where_fields: ['name'],
			exclude_fields: [],
			dependencies: {}
		});
		const line = await this.getOrCreateSimpleDependency(file.workstations.line_id, 'Line', transactions, {
			where_fields: ['name'],
			exclude_fields: ['sector_id'],
			dependencies: { sector_id: file.sector_id }
		});

		const workstation = await this.getOrCreateSimpleDependency(file.workstation_id, 'Workstation', transactions, {
			where_fields: ['name'],
			exclude_fields: ['line_id'],
			dependencies: { line_id: line.id }
		});

		const originalFileData = file.dataValues || file;
		const file_data = this.#omitFields(originalFileData, ['id', 'created_at', 'updated_at', 'workstations']);

		const defaults = {
			...file_data,
			sector_id: sector.id,
			workstation_id: workstation.id,
			size: file_data.size,
			url: file_data.url
		};

		const file_result = await this.kinebot_repository.db.File.find({
			where: {
				original_name: file.original_name,
				generated_name: file.generated_name
			},
			transaction: transactions.kinebot
		});

		if (!file_result) {
			const created_file = await this.kinebot_repository.db.File.create(defaults, {
				transaction: transactions.kinebot
			});
			return created_file;
		}

		file_result.set(defaults);

		await file_result.save({ transaction: transactions.kinebot });

		return file_result;
	}
}
