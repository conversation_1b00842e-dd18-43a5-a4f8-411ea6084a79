import { AbstractReportSandboxService } from './abstract_report_sandbox.js';
import { logger, AppError } from '../../helpers/index.js';
import { ERROR_RESPONSE_ENTITIES_ENUM } from '../../util/enum.js';

export class StrainIndexReportSandboxService extends AbstractReportSandboxService {
	constructor({ kinebot_repository, sandbox_repository }) {
		super({ kinebot_repository, sandbox_repository });
	}

	async sendReport(id) {
		logger.info(`Sending Strain Index Report ${id}`);

		const transactions = {
			kinebot: await this.kinebot_repository.db.sequelize.transaction(),
			sandbox: await this.sandbox_repository.db.sequelize.transaction()
		};

		try {
			await this.send(id, transactions);

			await transactions.kinebot.commit();
			await transactions.sandbox.commit();
		} catch (error) {
			logger.error(`Error sending Strain Index Report ${id}: ${error}`);
			await transactions.kinebot.rollback();
			await transactions.sandbox.rollback();
			throw error;
		}
	}

	async send(id, transactions) {
		const strain_index_report = await this.sandbox_repository.db.StrainIndexReport.findByPk(id, {
			transaction: transactions.sandbox
		});

		if (!strain_index_report) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.STRAIN_INDEX_REPORT.NOT_FOUND);
		}

		const processed_file = await this.getOrCreateFile(strain_index_report.file_id, transactions);

		strain_index_report.file_id = processed_file.id;

		const processed_strain_index_report = await this.create(strain_index_report, 'StrainIndexReport', transactions);

		// Update if action plan related report exists
		const action_plan_related_report_to_update = await this.sandbox_repository.db.ActionPlanRelatedReport.findOne({
			transaction: transactions.sandbox,
			include: [
				{
					as: 'ergonomic_tool',
					where: { name: 'strain_index' },
					required: true
				}
			],
			where: {
				report_id: strain_index_report.id
			}
		});

		if (action_plan_related_report_to_update) {
			action_plan_related_report_to_update.report_id = processed_strain_index_report.id;
			await action_plan_related_report_to_update.save({ transaction: transactions.sandbox });
		}

		await this.sandbox_repository.db.StrainIndexReport.destroy({
			where: { id },
			transaction: transactions.sandbox,
			force: true
		});
	}
}
