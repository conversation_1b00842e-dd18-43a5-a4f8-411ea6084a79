import { AbstractReportSandboxService } from './abstract_report_sandbox.js';
import { logger, AppError } from '../../helpers/index.js';
import { ERROR_RESPONSE_ENTITIES_ENUM } from '../../util/enum.js';

export class KimMhoReportSandboxService extends AbstractReportSandboxService {
	constructor({ kinebot_repository, sandbox_repository }) {
		super({ kinebot_repository, sandbox_repository });
	}

	async sendReport(id) {
		logger.info(`Sending Kim Mho Report ${id}`);

		const transactions = {
			kinebot: await this.kinebot_repository.db.sequelize.transaction(),
			sandbox: await this.sandbox_repository.db.sequelize.transaction()
		};

		try {
			await this.send(id, transactions);

			await transactions.kinebot.commit();
			await transactions.sandbox.commit();

			logger.info(`Kim Mho Report ${id} sent successfully`);
		} catch (error) {
			logger.error(`Error sending <PERSON> Report ${id}: ${error}`);
			await transactions.kinebot.rollback();
			await transactions.sandbox.rollback();
			throw error;
		}
	}

	async send(id, transactions) {
		const kim_mho_report = await this.sandbox_repository.db.KimManualHandlingReport.findByPk(id, {
			transaction: transactions.sandbox
		});

		if (!kim_mho_report) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.KIM_MHO_REPORT.NOT_FOUND);
		}

		const processed_file = await this.getOrCreateFile(kim_mho_report.file_id, transactions);

		const exists_kim_mho_report = await this.kinebot_repository.db.KimManualHandlingReport.findOne({
			transaction: transactions.kinebot,
			where: {
				file_id: kim_mho_report.file_id
			}
		});

		if (exists_kim_mho_report) {
			logger.warn(`[KimMhoReportSandbox] Kim Mho Report ${id} already exists in production`);

			return;
		}

		kim_mho_report.file_id = processed_file.id;

		const processed_kim_mho_report = await this.create(kim_mho_report, 'KimManualHandlingReport', transactions);

		// Update if action plan related report exists
		const action_plan_related_report_to_update = await this.sandbox_repository.db.ActionPlanRelatedReport.findOne({
			transaction: transactions.sandbox,
			include: [
				{
					as: 'ergonomic_tool',
					where: { name: 'kim_mho' },
					required: true
				}
			],
			where: {
				report_id: kim_mho_report.id
			}
		});

		if (action_plan_related_report_to_update) {
			action_plan_related_report_to_update.report_id = processed_kim_mho_report.id;
			await action_plan_related_report_to_update.save({ transaction: transactions.sandbox });
		}

		await this.sandbox_repository.db.KimManualHandlingReport.destroy({
			where: { id },
			transaction: transactions.sandbox,
			force: true
		});
	}
}
