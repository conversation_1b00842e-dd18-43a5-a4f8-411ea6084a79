import { AbstractReportSandboxService } from './abstract_report_sandbox.js';
import { logger, AppError } from '../../helpers/index.js';
import { ERROR_RESPONSE_ENTITIES_ENUM } from '../../util/enum.js';

export class KimPpReportSandboxService extends AbstractReportSandboxService {
	constructor({ kinebot_repository, sandbox_repository }) {
		super({ kinebot_repository, sandbox_repository });
	}

	async sendReport(id) {
		logger.info(`Sending Kim Pp Report ${id}`);

		const transactions = {
			kinebot: await this.kinebot_repository.db.sequelize.transaction(),
			sandbox: await this.sandbox_repository.db.sequelize.transaction()
		};

		try {
			await this.send(id, transactions);

			await transactions.kinebot.commit();
			await transactions.sandbox.commit();

			logger.info(`Kim Pp Report ${id} sent successfully`);
		} catch (error) {
			logger.error(`Error sending Kim Pp Report ${id}: ${error}`);
			await transactions.kinebot.rollback();
			await transactions.sandbox.rollback();
			throw error;
		}
	}

	async send(id, transactions) {
		const kim_pp_report = await this.sandbox_repository.db.KimPushPullReport.findByPk(id, {
			transaction: transactions.sandbox
		});

		if (!kim_pp_report) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.KIM_PP_REPORT.NOT_FOUND);
		}

		const processed_file = await this.getOrCreateFile(kim_pp_report.file_id, transactions);

		const exists_kim_pp_report = await this.kinebot_repository.db.KimPushPullReport.findOne({
			transaction: transactions.kinebot,
			where: {
				file_id: kim_pp_report.file_id
			}
		});

		if (exists_kim_pp_report) {
			logger.warn(`[KimPpReportSandbox] Kim Pp Report ${id} already exists in production`);
			return;
		}

		const processed_sector = await this.getOrCreateSimpleDependency(
			kim_pp_report.sector_id,
			'Sector',
			transactions
		);

		kim_pp_report.file_id = processed_file.id;
		kim_pp_report.sector_id = processed_sector.id;

		const processed_kim_pp_report = await this.create(kim_pp_report, 'KimPushPullReport', transactions);

		// Update if action plan related report exists
		const action_plan_related_report_to_update = await this.sandbox_repository.db.ActionPlanRelatedReport.findOne({
			transaction: transactions.sandbox,
			include: [
				{
					model: this.sandbox_repository.db.ErgonomicTool,
					as: 'ergonomic_tool',
					where: { name: 'kim_pp' },
					required: true
				}
			],
			where: {
				report_id: kim_pp_report.id
			}
		});

		if (action_plan_related_report_to_update) {
			action_plan_related_report_to_update.report_id = processed_kim_pp_report.id;
			await action_plan_related_report_to_update.save({ transaction: transactions.sandbox });
		}

		await this.sandbox_repository.db.KimPushPullReport.destroy({
			where: { id },
			transaction: transactions.sandbox,
			force: true
		});
	}
}
