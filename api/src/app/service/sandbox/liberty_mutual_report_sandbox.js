import { AbstractReportSandboxService } from './abstract_report_sandbox.js';
import { logger, AppError } from '../../helpers/index.js';
import { ERROR_RESPONSE_ENTITIES_ENUM } from '../../util/enum.js';

export class LibertyMutualReportSandboxService extends AbstractReportSandboxService {
	constructor({ kinebot_repository, sandbox_repository }) {
		super({ kinebot_repository, sandbox_repository });
	}

	async sendReport(id) {
		logger.info(`[LibertyMutualReportSandboxService] Sending report ${id}`);

		const transactions = {
			kinebot: await this.kinebot_repository.db.sequelize.transaction(),
			sandbox: await this.sandbox_repository.db.sequelize.transaction()
		};

		try {
			await this.send(id, transactions);

			await transactions.kinebot.commit();
			await transactions.sandbox.commit();

			logger.info(`[LibertyMutualReportSandboxService] Report ${id} sent`);
		} catch (error) {
			logger.error(`[LibertyMutualReportSandboxService] Error sending report ${id}: ${error}`);
			await transactions.kinebot.rollback();
			await transactions.sandbox.rollback();
			throw error;
		}
	}

	async send(id, transactions) {
		const liberty_mutual_report = await this.sandbox_repository.db.LibertyMutualReport.findByPk(id, {
			include: [{ association: 'report_inputs' }],
			transaction: transactions.sandbox
		});

		if (!liberty_mutual_report) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.LIBERTY_MUTUAL_REPORT.NOT_FOUND);
		}

		const liberty_mutual_report_inputs = liberty_mutual_report.report_inputs;

		const processed_file = await this.getOrCreateFile(liberty_mutual_report.file_id, transactions);

		const exists_liberty_mutual_report = await this.kinebot_repository.db.LibertyMutualReport.findOne({
			transaction: transactions.kinebot,
			where: {
				file_id: liberty_mutual_report.file_id
			}
		});

		if (exists_liberty_mutual_report) {
			logger.warn(`[LibertyMutualReportSandbox] Liberty Mutual Report ${id} already exists in production`);
			return;
		}

		liberty_mutual_report.file_id = processed_file.id;

		const processed_liberty_mutual_report = await this.create(
			liberty_mutual_report,
			'LibertyMutualReport',
			transactions
		);

		for (const liberty_mutual_report_input of liberty_mutual_report_inputs) {
			liberty_mutual_report_input.liberty_mutual_report_id = processed_liberty_mutual_report.id;
			await this.create(liberty_mutual_report_input, 'LibertyMutualReportInput', transactions);
		}

		// Update if action plan related report exists
		const action_plan_related_report_to_update = await this.sandbox_repository.db.ActionPlanRelatedReport.findOne({
			transaction: transactions.sandbox,
			include: [
				{
					as: 'ergonomic_tool',
					where: { name: 'liberty_mutual' },
					required: true
				}
			],
			where: {
				report_id: liberty_mutual_report.id
			}
		});

		if (action_plan_related_report_to_update) {
			action_plan_related_report_to_update.report_id = processed_liberty_mutual_report.id;
			await action_plan_related_report_to_update.save({ transaction: transactions.sandbox });
		}

		await this.sandbox_repository.db.LibertyMutualReportInput.destroy({
			where: {
				liberty_mutual_report_id: liberty_mutual_report.id
			},
			transaction: transactions.sandbox,
			force: true
		});

		await this.sandbox_repository.db.LibertyMutualReport.destroy({
			where: {
				id: liberty_mutual_report.id
			},
			transaction: transactions.sandbox,
			force: true
		});
	}
}
