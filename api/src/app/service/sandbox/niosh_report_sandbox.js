import { AbstractReportSandboxService } from './abstract_report_sandbox.js';
import { logger, AppError } from '../../helpers/index.js';
import { ERROR_RESPONSE_ENTITIES_ENUM } from '../../util/enum.js';

export class NioshReportSandboxService extends AbstractReportSandboxService {
	constructor({ kinebot_repository, sandbox_repository }) {
		super({ kinebot_repository, sandbox_repository });
	}

	async sendReport(id) {
		logger.info(`Sending NIOSH report ${id}`);

		const transactions = {
			kinebot: await this.kinebot_repository.db.sequelize.transaction(),
			sandbox: await this.sandbox_repository.db.sequelize.transaction()
		};

		try {
			await this.send(id, transactions);

			await transactions.kinebot.commit();
			await transactions.sandbox.commit();

			logger.info(`NIOSH report ${id} sent successfully`);
		} catch (error) {
			logger.error(`Error sending NIOSH report ${id}: ${error}`);
			await transactions.kinebot.rollback();
			await transactions.sandbox.rollback();
			throw error;
		}
	}

	async send(id, transactions) {
		const niosh_report = await this.sandbox_repository.db.NioshReport.findByPk(id, {
			transaction: transactions.sandbox
		});

		if (!niosh_report) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.NIOSH_REPORT.NOT_FOUND);
		}

		const processed_file = await this.getOrCreateFile(niosh_report.file_id, transactions);

		const exists_niosh_report = await this.kinebot_repository.db.NioshReport.findOne({
			transaction: transactions.kinebot,
			where: {
				file_id: niosh_report.file_id
			}
		});

		if (exists_niosh_report) {
			logger.warn(`[NioshReportSandbox] Niosh Report ${id} already exists in production`);
			return;
		}

		niosh_report.file_id = processed_file.id;

		const processed_niosh_report = await this.create(niosh_report, 'NioshReport', transactions);

		// Update if action plan related report exists
		const action_plan_related_report_to_update = await this.sandbox_repository.db.ActionPlanRelatedReport.findOne({
			transaction: transactions.sandbox,
			include: [
				{
					as: 'ergonomic_tool',
					where: { name: 'niosh' },
					required: true
				}
			],
			where: {
				report_id: niosh_report.id
			}
		});

		if (action_plan_related_report_to_update) {
			action_plan_related_report_to_update.report_id = processed_niosh_report.id;
			await action_plan_related_report_to_update.save({ transaction: transactions.sandbox });
		}

		await this.sandbox_repository.db.NioshReport.destroy({
			where: { id },
			transaction: transactions.sandbox,
			force: true
		});
	}
}
