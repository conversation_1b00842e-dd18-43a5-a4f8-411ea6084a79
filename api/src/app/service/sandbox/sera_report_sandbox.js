import { AbstractReportSandboxService } from './abstract_report_sandbox.js';
import { logger, AppError } from '../../helpers/index.js';
import { ERROR_RESPONSE_ENTITIES_ENUM } from '../../util/enum.js';
import { Op } from 'sequelize';

export class SeraReportSandboxService extends AbstractReportSandboxService {
	constructor({ kinebot_repository, sandbox_repository }) {
		super({ kinebot_repository, sandbox_repository });
	}

	async sendReport(id) {
		logger.info(`Sending Sera Report ${id}`);

		const transactions = {
			kinebot: await this.kinebot_repository.db.sequelize.transaction(),
			sandbox: await this.sandbox_repository.db.sequelize.transaction()
		};

		try {
			await this.send(id, transactions);

			await transactions.kinebot.commit();
			await transactions.sandbox.commit();
		} catch (error) {
			transactions.kinebot.rollback();
			transactions.sandbox.rollback();
			logger.error(`Error sending Sera Report ${id}`, { message: error.message, stack: error.stack });
		}
	}

	async send(id, transactions) {
		logger.info(`[SeraReportSandboxService] Sending Sera Report ${id}`);

		const sera_summary = await this.sandbox_repository.db.SeraSummary.findByPk(id, {
			transaction: transactions.sandbox
		});

		if (!sera_summary) {
			logger.error(`Sera Summary ${id} not found`);
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.SERA_SUMMARY.NOT_FOUND);
		}

		const sera_summary_files = await this.sandbox_repository.db.SeraSummaryFiles.findAll({
			where: {
				sera_summary_id: id
			},
			transaction: transactions.sandbox
		});

		const sera_summary_reviews = await this.sandbox_repository.db.SeraSummaryReview.findAll({
			where: {
				sera_summary_id: id
			},
			transaction: transactions.sandbox
		});

		const sera_reports = await this.sandbox_repository.db.SeraReport.findAll({
			where: {
				sera_summary_review_id: {
					[Op.in]: sera_summary_reviews.map((review) => review.id)
				}
			},
			transaction: transactions.sandbox
		});

		const sera_review_tasks_results = await this.sandbox_repository.db.SeraReviewTasksResult.findAll({
			where: {
				sera_summary_review_id: {
					[Op.in]: sera_summary_reviews.map((review) => review.id)
				}
			},
			transaction: transactions.sandbox
		});

		const sera_report_updateds = await this.sandbox_repository.db.SeraReportUpdated.findAll({
			where: {
				sera_report_id: {
					[Op.in]: sera_reports.map((report) => report.id)
				}
			},
			transaction: transactions.sandbox
		});

		const sera_review_selectors = await this.sandbox_repository.db.SeraReviewSelector.findAll({
			where: {
				sera_summary_review_id: {
					[Op.in]: sera_summary_reviews.map((review) => review.id)
				}
			},
			transaction: transactions.sandbox
		});

		if (sera_summary.evaluator_id) {
			const processed_evaluator = await this.getOrCreateSimpleDependency(
				sera_summary.evaluator_id,
				'Evaluator',
				transactions
			);
			sera_summary.evaluator_id = processed_evaluator.id;
		}

		const processed_cycle = await this.getOrCreateSimpleDependency(sera_summary.cycle_id, 'Cycle', transactions);
		sera_summary.cycle_id = processed_cycle.id;

		const processed_sera_summary = await this.create(sera_summary, 'SeraSummary', transactions);

		for (const sera_summary_file of sera_summary_files) {
			const processed_sera_summary_file = await this.getOrCreateFile(sera_summary_file.file_id, transactions);
			sera_summary_file.sera_summary_id = processed_sera_summary.id;
			sera_summary_file.file_id = processed_sera_summary_file.id;

			await this.create(sera_summary_file, 'SeraSummaryFiles', transactions);
		}

		for (const sera_summary_review of sera_summary_reviews) {
			sera_summary_review.sera_summary_id = processed_sera_summary.id;
			const processed_sera_summary_review = await this.create(
				sera_summary_review,
				'SeraSummaryReview',
				transactions
			);

			// Update if exists action plan
			const action_plan_to_update = await this.sandbox_repository.db.ActionPlanOrigin.findOne({
				where: {
					column_id: sera_summary_review.id,
					table_name: 'sera_summary_reviews'
				}
			});

			if (action_plan_to_update) {
				action_plan_to_update.column_id = processed_sera_summary_review.id;
				action_plan_to_update.table_name = 'production:sera_summary_reviews';
				await action_plan_to_update.save({ transaction: transactions.sandbox });
			}

			for (const sera_report of sera_reports) {
				if (sera_report.sera_summary_review_id === sera_summary_review.id)
					sera_report.sera_summary_review_id = processed_sera_summary_review.id;
			}

			for (const sera_review_task_result of sera_review_tasks_results) {
				if (sera_review_task_result.sera_summary_review_id === sera_summary_review.id)
					sera_review_task_result.sera_summary_review_id = processed_sera_summary_review.id;
			}

			for (const sera_review_selector of sera_review_selectors) {
				if (sera_review_selector.sera_summary_review_id === sera_summary_review.id) {
					sera_review_selector.sera_summary_review_id = processed_sera_summary_review.id;
					sera_review_selector.sera_summary_id = processed_sera_summary.id;
				}
			}
		}

		for (const sera_report of sera_reports) {
			if (sera_report.evaluator_id) {
				const processed_evaluator = await this.getOrCreateSimpleDependency(
					sera_report.evaluator_id,
					'Evaluator',
					transactions
				);
				sera_report.evaluator_id = processed_evaluator.id;
			}

			const processed_task = await this.getOrCreateSimpleDependency(sera_report.task_id, 'Task', transactions);
			sera_report.task_id = processed_task.id;

			const processed_sera_report = await this.create(sera_report, 'SeraReport', transactions);
			for (const sera_report_updated of sera_report_updateds) {
				if (sera_report_updated.sera_report_id === sera_report.id)
					sera_report_updated.sera_report_id = processed_sera_report.id;
				if (sera_report_updated.sera_report_updated === sera_report.id)
					sera_report_updated.sera_report_updated = processed_sera_report.id;
			}
		}

		for (const sera_review_task_result of sera_review_tasks_results) {
			const processed_task = await this.getOrCreateSimpleDependency(
				sera_review_task_result.task_id,
				'Task',
				transactions
			);
			sera_review_task_result.task_id = processed_task.id;

			await this.create(sera_review_task_result, 'SeraReviewTasksResult', transactions);
		}

		for (const sera_report_updated of sera_report_updateds) {
			await this.create(sera_report_updated, 'SeraReportUpdated', transactions);
		}

		for (const sera_review_selector of sera_review_selectors) {
			await this.create(sera_review_selector, 'SeraReviewSelector', transactions);
		}

		await this.sandbox_repository.db.SeraReportUpdated.destroy({
			where: {
				sera_report_id: {
					[Op.in]: sera_reports.map((report) => report.id)
				}
			}
		});

		await this.sandbox_repository.db.SeraReviewTasksResult.destroy({
			where: {
				sera_summary_review_id: {
					[Op.in]: sera_summary_reviews.map((review) => review.id)
				}
			}
		});

		await this.sandbox_repository.db.SeraReport.destroy({
			where: {
				sera_summary_review_id: {
					[Op.in]: sera_summary_reviews.map((review) => review.id)
				}
			}
		});

		await this.sandbox_repository.db.SeraSummaryReview.destroy({
			where: {
				sera_summary_id: sera_summary.id
			},
			transaction: transactions.sandbox,
			force: true
		});

		await this.sandbox_repository.db.SeraSummaryFiles.destroy({
			where: {
				sera_summary_id: sera_summary.id
			},
			transaction: transactions.sandbox,
			force: true
		});

		await this.sandbox_repository.db.SeraSummary.destroy({
			where: {
				id: sera_summary.id
			},
			transaction: transactions.sandbox,
			force: true
		});
	}
}
