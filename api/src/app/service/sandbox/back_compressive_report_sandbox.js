import { AbstractReportSandboxService } from './abstract_report_sandbox.js';
import { logger, AppError } from '../../helpers/index.js';
import { ERROR_RESPONSE_ENTITIES_ENUM } from '../../util/enum.js';

export class BackCompressiveReportSandboxService extends AbstractReportSandboxService {
	constructor({ kinebot_repository, sandbox_repository }) {
		super({ kinebot_repository, sandbox_repository });
	}

	async sendReport(id) {
		logger.info(`Sending Back Compressive Report ${id}`);

		const transactions = {
			kinebot: await this.kinebot_repository.db.sequelize.transaction(),
			sandbox: await this.sandbox_repository.db.sequelize.transaction()
		};

		try {
			await this.send(id, transactions);

			await transactions.kinebot.commit();
			await transactions.sandbox.commit();

			logger.info(`Back Compressive Report ${id} sent successfully`);
		} catch (error) {
			logger.error(`Error sending Back Compressive Report ${id}: ${error}`);
			await transactions.kinebot.rollback();
			await transactions.sandbox.rollback();
			throw error;
		}
	}

	async send(id, transactions) {
		const back_compressive_report = await this.sandbox_repository.db.BackCompressiveForceEstimationReport.findByPk(
			id,
			{
				transaction: transactions.sandbox
			}
		);

		if (!back_compressive_report) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.BACK_COMPRESSIVE_REPORT.NOT_FOUND);
		}

		const processed_file = await this.getOrCreateFile(back_compressive_report.file_id, transactions);

		const exists_back_compressive_report =
			await this.kinebot_repository.db.BackCompressiveForceEstimationReport.findOne({
				transaction: transactions.kinebot,
				where: {
					file_id: back_compressive_report.file_id
				}
			});

		if (exists_back_compressive_report) {
			logger.warn(`[BackCompressiveReportSandbox] Back Compressive Report ${id} already exists in production`);
			return;
		}

		back_compressive_report.file_id = processed_file.id;

		const processed_back_compressive_report = await this.create(
			back_compressive_report,
			'BackCompressiveForceEstimationReport',
			transactions
		);

		// Update if action plan related report exists
		const action_plan_related_report_to_update = await this.sandbox_repository.db.ActionPlanRelatedReport.findOne({
			transaction: transactions.sandbox,
			include: [
				{
					model: this.sandbox_repository.db.ErgonomicTool,
					as: 'ergonomic_tool',
					where: { name: 'back_compressive_force_estimation' },
					required: true
				}
			],
			where: {
				report_id: back_compressive_report.id
			}
		});

		if (action_plan_related_report_to_update) {
			action_plan_related_report_to_update.report_id = processed_back_compressive_report.id;
			await action_plan_related_report_to_update.save({ transaction: transactions.sandbox });
		}

		await this.sandbox_repository.db.BackCompressiveForceEstimationReport.destroy({
			where: { id },
			transaction: transactions.sandbox,
			force: true
		});
	}
}
