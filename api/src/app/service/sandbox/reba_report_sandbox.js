import { AbstractReportSandboxService } from './abstract_report_sandbox.js';
import { logger, AppError } from '../../helpers/index.js';
import { ERROR_RESPONSE_ENTITIES_ENUM } from '../../util/enum.js';

export class RebaReportSandboxService extends AbstractReportSandboxService {
	constructor({ kinebot_repository, sandbox_repository }) {
		super({ kinebot_repository, sandbox_repository });
	}

	async sendReport(id) {
		logger.info(`Sending Reba Report ${id}`);

		const transactions = {
			kinebot: await this.kinebot_repository.db.sequelize.transaction(),
			sandbox: await this.sandbox_repository.db.sequelize.transaction()
		};

		try {
			await this.send(id, transactions);

			await transactions.kinebot.commit();
			await transactions.sandbox.commit();

			logger.info(`Reba Report ${id} sent successfully`);
		} catch (error) {
			logger.error(`Error sending Reba Report ${id}: ${error}`);
			await transactions.kinebot.rollback();
			await transactions.sandbox.rollback();
			throw error;
		}
	}

	async send(id, transactions) {
		const reba_report = await this.sandbox_repository.db.RebaReport.findByPk(id, {
			transaction: transactions.sandbox
		});

		if (!reba_report) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.REBA_REPORT.NOT_FOUND);
		}

		const processed_file = await this.getOrCreateFile(reba_report.file_id, transactions);

		const exists_reba_report = await this.kinebot_repository.db.RebaReport.findOne({
			transaction: transactions.kinebot,
			where: {
				file_id: reba_report.file_id
			}
		});

		if (exists_reba_report) {
			logger.warn(`[RebaReportSandbox] Reba Report ${id} already exists in production`);
			return;
		}

		reba_report.file_id = processed_file.id;

		const processed_reba_report = await this.create(reba_report, 'RebaReport', transactions);

		// Update if action plan related report exists
		const action_plan_related_report_to_update = await this.sandbox_repository.db.ActionPlanRelatedReport.findOne({
			transaction: transactions.sandbox,
			include: [
				{
					as: 'ergonomic_tool',
					where: { name: 'reba' },
					required: true
				}
			],
			where: {
				report_id: reba_report.id
			}
		});

		if (action_plan_related_report_to_update) {
			action_plan_related_report_to_update.report_id = processed_reba_report.id;
			await action_plan_related_report_to_update.save({ transaction: transactions.sandbox });
		}

		await this.sandbox_repository.db.RebaReport.destroy({
			where: { id },
			transaction: transactions.sandbox,
			force: true
		});
	}
}
