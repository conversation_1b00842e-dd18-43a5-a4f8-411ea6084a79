import { BeraReport } from '../../entities/index.js';
import { AbstractReportSandboxService } from './abstract_report_sandbox.js';
import { Op } from 'sequelize';
import { logger, AppError } from '../../helpers/index.js';
import { ERROR_RESPONSE_ENTITIES_ENUM } from '../../util/enum.js';

export class BeraReportSandboxService extends AbstractReportSandboxService {
	constructor({ kinebot_repository, sandbox_repository }) {
		super({ kinebot_repository, sandbox_repository });
	}

	async sendReport(id) {
		logger.info(`[BeraReportSandboxService] Sending report ${id}`);
		const transactions = {
			kinebot: await this.kinebot_repository.db.sequelize.transaction(),
			sandbox: await this.sandbox_repository.db.sequelize.transaction()
		};

		try {
			const bera_report_entity = new BeraReport();

			const bera_job_summary = await this.sandbox_repository.db.BeraJobSummary.findByPk(id, {
				include: [{ association: 'bera_job_summary_files' }, { association: 'bera_report' }]
			});

			if (!bera_job_summary) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.BERA_JOB_SUMMARY.NOT_FOUND);
			}

			bera_report_entity.setBeraJobSummary(bera_job_summary);
			bera_report_entity.setBeraReports(bera_job_summary.bera_report);
			bera_report_entity.setBeraJobSummaryFiles(bera_job_summary.bera_job_summary_files);

			const cycle = await this.getOrCreateSimpleDependency(bera_job_summary.cycle_id, 'Cycle', transactions);

			bera_job_summary.cycle_id = cycle.id;

			const production_bera_job_summary = await this.create(bera_job_summary, 'BeraJobSummary', transactions);

			for (const report of bera_job_summary.bera_report) {
				const processed_file = await this.getOrCreateFile(report.file_id, transactions);
				const processed_task = await this.getOrCreateSimpleDependency(report.task_id, 'Task', transactions);
				await processed_file.setTask([processed_task], { transaction: transactions.kinebot });
				const processed_evaluator = await this.getOrCreateSimpleDependency(
					report.evaluator_id,
					'Evaluator',
					transactions
				);

				report.file_id = processed_file.id;
				report.bera_job_summary_id = production_bera_job_summary.id;
				report.task_id = processed_task.id;
				report.evaluator_id = processed_evaluator.id;
				await cycle.addTask([processed_task], { transaction: transactions.kinebot });

				await this.create(report, 'BeraReport', transactions);
			}

			for (const file of bera_job_summary.bera_job_summary_files) {
				const processed_file = await this.getOrCreateFile(file.file_id, transactions);
				file.file_id = processed_file.id;
				file.bera_job_summary_id = production_bera_job_summary.id;

				await this.create(file, 'BeraJobSummaryFiles', transactions);
			}

			const bera_report_sandbox = bera_report_entity.getBeraReports();

			let bera_reports_to_destroy = [];

			for (const report of bera_report_sandbox) {
				bera_reports_to_destroy.push(report.id);
			}

			await this.sandbox_repository.db.BeraReport.destroy({
				where: {
					id: {
						[Op.in]: bera_reports_to_destroy
					}
				},
				transaction: transactions.sandbox,
				force: true
			});

			const bera_job_summary_files_sandbox = bera_report_entity.getBeraJobSummaryFiles();

			let bera_job_summary_to_destroy = [];

			for (const file of bera_job_summary_files_sandbox) {
				bera_job_summary_to_destroy.push(file.id);
			}

			await this.sandbox_repository.db.BeraJobSummaryFiles.destroy({
				where: {
					id: {
						[Op.in]: bera_job_summary_to_destroy
					}
				},
				transaction: transactions.sandbox,
				force: true
			});

			const bera_job_summary_sandbox = bera_report_entity.getBeraJobSummary();

			await this.sandbox_repository.db.BeraJobSummary.destroy({
				where: {
					id: bera_job_summary_sandbox.id
				},
				transaction: transactions.sandbox,
				force: true
			});

			await transactions.kinebot.commit();
			await transactions.sandbox.commit();
			logger.info(`[BeraReportSandboxService] Report ${id} sent successfully`);
		} catch (error) {
			await transactions.kinebot.rollback();
			await transactions.sandbox.rollback();
			throw error;
		}
	}
}
