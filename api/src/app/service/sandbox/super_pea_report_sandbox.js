import { AbstractReportSandboxService } from './abstract_report_sandbox.js';
import { SuperPeaReport } from '../../entities/Sandbox/SuperPeaReport.js';
import { Op } from 'sequelize';
import { logger, AppError } from '../../helpers/index.js';
import { ERROR_RESPONSE_ENTITIES_ENUM } from '../../util/enum.js';

export class SuperPeaReportSandboxService extends AbstractReportSandboxService {
	constructor({ kinebot_repository, sandbox_repository }) {
		super({ kinebot_repository, sandbox_repository });
	}

	async sendReport(id) {
		logger.info(`[SuperPeaReportSandboxService] Sending report ${id}`);

		const transactions = {
			kinebot: await this.kinebot_repository.db.sequelize.transaction(),
			sandbox: await this.sandbox_repository.db.sequelize.transaction()
		};

		try {
			const super_pea_report_entity = new SuperPeaReport();

			const super_pea_report = await this.sandbox_repository.db.SuperPeaReport.findByPk(id, {
				transaction: transactions.sandbox
			});

			if (!super_pea_report) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.SUPER_PEA_REPORT.NOT_FOUND);
			}

			const filtered_pea_ids = super_pea_report.pea_ids
				.filter((id) => !id.includes('production:'))
				.map((id) => id.trim());

			const custom_report_results = await this.sandbox_repository.db.CustomReportResult.findAll({
				where: {
					id: {
						[Op.in]: filtered_pea_ids
					}
				},
				transaction: transactions.sandbox
			});

			const custom_report_step_key_results = await this.sandbox_repository.db.CustomReportStepKeyResult.findAll({
				where: {
					custom_report_result_id: {
						[Op.in]: filtered_pea_ids
					}
				},
				transaction: transactions.sandbox
			});

			const custom_report_result_actions_logs =
				await this.sandbox_repository.db.CustomReportResultActionLog.findAll({
					where: {
						custom_report_result_id: {
							[Op.in]: filtered_pea_ids
						}
					},
					transaction: transactions.sandbox
				});

			const custom_report_step_key_additional_item_results =
				await this.sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.findAll({
					where: {
						custom_report_result_id: {
							[Op.in]: filtered_pea_ids
						}
					},
					transaction: transactions.sandbox
				});

			super_pea_report_entity.setSuperPeaReport(super_pea_report);
			super_pea_report_entity.setCustomReportResults(custom_report_results);
			super_pea_report_entity.setCustomReportStepKeyResults(custom_report_step_key_results);
			super_pea_report_entity.setCustomReportResultActionsLogs(custom_report_result_actions_logs);
			super_pea_report_entity.setCustomReportStepKeyAdditionalItemResults(
				custom_report_step_key_additional_item_results
			);

			let custom_report_result_actions_logs_to_process = custom_report_result_actions_logs;
			let custom_report_step_key_additional_item_results_to_process =
				custom_report_step_key_additional_item_results;
			let custom_report_step_key_results_to_process = custom_report_step_key_results;

			const production_pea_ids = super_pea_report.pea_ids
				.filter((pea_id) => pea_id.includes('production:'))
				.map((pea_id) => pea_id.replace('production:', '').trim());

			const pea_ids = [...production_pea_ids];

			for (const custom_report_result of custom_report_results) {
				const processed_file = await this.getOrCreateFile(custom_report_result.file_id, transactions);
				const processed_evaluator = await this.getOrCreateSimpleDependency(
					custom_report_result.evaluator_id,
					'Evaluator',
					transactions
				);

				custom_report_result.file_id = processed_file.id;
				custom_report_result.evaluator_id = processed_evaluator.id;
				const processed_custom_report_result = await this.create(
					custom_report_result,
					'CustomReportResult',
					transactions
				);
				custom_report_result.id = processed_custom_report_result.id;
				pea_ids.push(processed_custom_report_result.id);

				for (const custom_report_result_action_log of custom_report_result_actions_logs_to_process) {
					custom_report_result_action_log.custom_report_result_id = processed_custom_report_result.id;
				}

				for (const custom_report_step_key_additional_item_result of custom_report_step_key_additional_item_results_to_process) {
					custom_report_step_key_additional_item_result.custom_report_result_id =
						processed_custom_report_result.id;
				}

				for (const custom_report_step_key_result of custom_report_step_key_results_to_process) {
					custom_report_step_key_result.custom_report_result_id = processed_custom_report_result.id;
				}
			}

			super_pea_report.pea_ids = pea_ids;
			super_pea_report.production_pea_ids = production_pea_ids.length > 0 ? production_pea_ids : null;

			const processed_super_pea_report = await this.create(super_pea_report, 'SuperPeaReport', transactions);

			const pea_to_super_pea_payload = pea_ids.map((id) => ({
				pea_id: id,
				super_pea_id: processed_super_pea_report.id
			}));

			await this.kinebot_repository.db.PEAToSuperPEA.bulkCreate(pea_to_super_pea_payload, {
				transaction: transactions.kinebot
			});

			for (const custom_report_result_action_log of custom_report_result_actions_logs_to_process) {
				await this.create(custom_report_result_action_log, 'CustomReportResultActionLog', transactions);
			}

			for (const custom_report_step_key_additional_item_result of custom_report_step_key_additional_item_results_to_process) {
				if (custom_report_step_key_additional_item_result.custom_report_step_key_additional_item_id) {
					const processed_custom_report_step_key_additional_item = await this.getOrCreateSimpleDependency(
						custom_report_step_key_additional_item_result.custom_report_step_key_additional_item_id,
						'CustomReportStepKeysAdditionalItem',
						transactions,
						{
							where_fields: ['custom_report_step_key_id', 'additional_item_id'],
							exclude_fields: [],
							dependencies: {}
						}
					);
					custom_report_step_key_additional_item_result.custom_report_step_key_additional_item_id =
						processed_custom_report_step_key_additional_item.id;
				}
				await this.create(
					custom_report_step_key_additional_item_result,
					'CustomReportStepKeyAdditionalItemResult',
					transactions
				);
			}

			for (const custom_report_step_key_result of custom_report_step_key_results_to_process) {
				await this.create(custom_report_step_key_result, 'CustomReportStepKeyResult', transactions);
			}

			// Destroy
			const super_pea_report_sandbox = super_pea_report_entity.getSuperPeaReport();
			const custom_report_results_sandbox = super_pea_report_entity.getCustomReportResults();
			const custom_report_step_key_results_sandbox = super_pea_report_entity.getCustomReportStepKeyResults();
			const custom_report_step_key_additional_item_results_sandbox =
				super_pea_report_entity.getCustomReportStepKeyAdditionalItemResults();
			const custom_report_result_actions_logs_sandbox =
				super_pea_report_entity.getCustomReportResultActionsLogs();

			const custom_report_results_to_destroy = [];

			for (const custom_report_result of custom_report_results_sandbox) {
				custom_report_results_to_destroy.push(custom_report_result.id);
			}

			const custom_report_step_key_result_to_destroy = [];

			for (const custom_report_step_key_result of custom_report_step_key_results_sandbox) {
				custom_report_step_key_result_to_destroy.push(custom_report_step_key_result.id);
			}

			const custom_report_step_key_additional_item_result_to_destroy = [];

			for (const custom_report_step_key_additional_item_result of custom_report_step_key_additional_item_results_sandbox) {
				custom_report_step_key_additional_item_result_to_destroy.push(
					custom_report_step_key_additional_item_result.id
				);
			}

			const custom_report_result_action_log_to_destroy = [];

			for (const custom_report_result_action_log of custom_report_result_actions_logs_sandbox) {
				custom_report_result_action_log_to_destroy.push(custom_report_result_action_log.id);
			}

			await this.sandbox_repository.db.CustomReportStepKeyResult.destroy({
				where: {
					id: {
						[Op.in]: custom_report_step_key_result_to_destroy
					}
				},
				transaction: transactions.sandbox,
				force: true
			});

			await this.sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.destroy({
				where: {
					id: {
						[Op.in]: custom_report_step_key_additional_item_result_to_destroy
					}
				},
				transaction: transactions.sandbox,
				force: true
			});

			await this.sandbox_repository.db.CustomReportResultActionLog.destroy({
				where: {
					id: {
						[Op.in]: custom_report_result_action_log_to_destroy
					}
				},
				transaction: transactions.sandbox,
				force: true
			});

			await this.sandbox_repository.db.CustomReportResult.destroy({
				where: {
					id: {
						[Op.in]: custom_report_results_to_destroy
					}
				},
				transaction: transactions.sandbox,
				force: true
			});

			await this.sandbox_repository.db.PEAToSuperPEA.destroy({
				where: {
					super_pea_id: super_pea_report_sandbox.id
				},
				transaction: transactions.sandbox,
				force: true
			});

			await this.sandbox_repository.db.SuperPeaReport.destroy({
				where: {
					id: super_pea_report_sandbox.id
				},
				transaction: transactions.sandbox,
				force: true
			});

			await transactions.kinebot.commit();
			await transactions.sandbox.commit();
			logger.info(`[SuperPeaReportSandboxService] Report ${id} sent`);
		} catch (error) {
			logger.error(`[SuperPeaReportSandboxService] Error sending report ${id}: ${error}`);
			await transactions.kinebot.rollback();
			await transactions.sandbox.rollback();
			throw error;
		}
	}
}
