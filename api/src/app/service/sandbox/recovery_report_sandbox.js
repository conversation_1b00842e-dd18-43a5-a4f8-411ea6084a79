import { AbstractReportSandboxService } from './abstract_report_sandbox.js';
import { logger, AppError } from '../../helpers/index.js';
import { ERROR_RESPONSE_ENTITIES_ENUM } from '../../util/enum.js';

export class RecoveryReportSandboxService extends AbstractReportSandboxService {
	constructor({ kinebot_repository, sandbox_repository }) {
		super({ kinebot_repository, sandbox_repository });
	}

	async sendReport(id) {
		logger.info(`Sending Recovery Report ${id}`);

		const transactions = {
			kinebot: await this.kinebot_repository.db.sequelize.transaction(),
			sandbox: await this.sandbox_repository.db.sequelize.transaction()
		};

		try {
			await this.send(id, transactions);

			await transactions.kinebot.commit();
			await transactions.sandbox.commit();
		} catch (error) {
			logger.error(`Error sending Recovery Report ${id}: ${error}`);
			await transactions.kinebot.rollback();
			await transactions.sandbox.rollback();
			throw error;
		}
	}

	async send(id, transactions) {
		const recovery_report = await this.sandbox_repository.db.RecoveryReport.findByPk(id, {
			transaction: transactions.sandbox
		});

		if (!recovery_report) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.RECOVERY_REPORT.NOT_FOUND);
		}

		const processed_file = await this.getOrCreateFile(recovery_report.file_id, transactions);

		const exists_recovery_report = await this.kinebot_repository.db.RecoveryReport.findOne({
			transaction: transactions.kinebot,
			where: {
				file_id: recovery_report.file_id
			}
		});

		if (exists_recovery_report) {
			logger.warn(`[RecoveryReportSandbox] Recovery Report ${id} already exists in production`);
			return;
		}

		const processed_sector = await this.getOrCreateSimpleDependency(
			recovery_report.sector_id,
			'Sector',
			transactions
		);

		recovery_report.file_id = processed_file.id;
		recovery_report.sector_id = processed_sector.id;

		const processed_recovery_report = await this.create(recovery_report, 'RecoveryReport', transactions);

		// Update if action plan related report exists
		const action_plan_related_report_to_update = await this.sandbox_repository.db.ActionPlanRelatedReport.findOne({
			transaction: transactions.sandbox,
			include: [
				{
					as: 'ergonomic_tool',
					where: { name: 'rula' },
					required: true
				}
			],
			where: {
				report_id: recovery_report.id
			}
		});

		if (action_plan_related_report_to_update) {
			action_plan_related_report_to_update.report_id = processed_recovery_report.id;
			await action_plan_related_report_to_update.save({ transaction: transactions.sandbox });
		}

		await this.sandbox_repository.db.RecoveryReport.destroy({
			where: { id },
			transaction: transactions.sandbox,
			force: true
		});
	}
}
