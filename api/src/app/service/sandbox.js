import { SandboxServiceStrategy } from '../strategy/sandbox_service.js';
import { logger, AppError } from '../helpers/index.js';
import { StorageContext } from '../utils/storage_context.js';
import { Op } from 'sequelize';
import { ERROR_RESPONSE_ENTITIES_ENUM } from '../util/enum.js';

export class SandboxService {
	constructor({ kinebot_repository, sandbox_repository }) {
		this.kinebot_repository = kinebot_repository;
		this.sandbox_repository = sandbox_repository;
		this.strategy = new SandboxServiceStrategy({
			kinebot_repository: this.kinebot_repository,
			sandbox_repository: this.sandbox_repository
		});
	}

	async sendReport(params) {
		logger.info(`[SandboxService] Sending report`, { params });
		const { report_type, id } = params;
		this.strategy.setStrategy(report_type);
		await this.strategy.sendReport(id);
		logger.info(`[SandboxService] Report sent`);
	}

	async logout() {
		const user_id = StorageContext.getStore()?.user_id;

		if (!user_id) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.USER.NOT_FOUND);
		}

		const transaction = await this.sandbox_repository.db.sequelize.transaction();
		logger.info(`[SandboxService] Logging out sandbox for user ${user_id}`);
		try {
			await this.#defaultClean(transaction, 'KimManualHandlingReport', user_id, 'KimMho');
			await this.#defaultClean(transaction, 'KimPushPullReport', user_id, 'KimPp');
			await this.#defaultClean(transaction, 'RecoveryReport', user_id, 'Recovery', 'user_id');
			await this.#defaultClean(transaction, 'StrainIndexReport', user_id, 'StrainIndex');
			await this.#defaultClean(transaction, 'NioshReport', user_id, 'Niosh');
			await this.#defaultClean(transaction, 'RebaReport', user_id, 'Reba');
			await this.#defaultClean(transaction, 'BackCompressiveForceEstimationReport', user_id, 'BackCompressive');
			await this.#defaultClean(transaction, 'AngleTimeReport', user_id, 'AngleTime');
			await this.#cleanBera(transaction, user_id);
			await this.#cleanCustomReports(transaction, user_id);
			await this.#cleanLibertyMutual(transaction, user_id);
			await this.#cleanSuperPea(transaction, user_id);
			await this.#cleanSera(transaction, user_id);
			await this.#cleanActionPlan(transaction, user_id);
			await this.#cleanFile(transaction, user_id);
			await transaction.commit();
			logger.info(`[SandboxService] Logout completed for user ${user_id}`);
		} catch (error) {
			await transaction.rollback();
			logger.error(`[SandboxService] Failed to logout`, error);
			throw error;
		}
	}

	async #cleanBera(transaction, user_id) {
		const bera_job_summaries = await this.sandbox_repository.db.BeraJobSummary.findAll({
			where: { user_id },
			attributes: ['id'],
			transaction
		});

		if (bera_job_summaries.length === 0) {
			logger.info(`[BeraClean] No BeraJobSummary found for user ${user_id}`);
			return;
		}

		const bera_ids = bera_job_summaries.map((item) => item.id);

		await this.sandbox_repository.db.BeraReport.destroy({
			where: {
				bera_job_summary_id: {
					[Op.in]: bera_ids
				}
			},
			force: true,
			transaction
		});

		await this.sandbox_repository.db.BeraJobSummaryFiles.destroy({
			where: {
				bera_job_summary_id: {
					[Op.in]: bera_ids
				}
			},
			force: true,
			transaction
		});

		await this.sandbox_repository.db.BeraJobSummary.destroy({
			where: {
				id: {
					[Op.in]: bera_ids
				}
			},
			force: true,
			transaction
		});
	}

	async #defaultClean(transaction, model, user_id, prefix = 'Default', field = 'report_user_id') {
		await this.sandbox_repository.db[model].destroy({
			where: {
				[field]: user_id
			},
			force: true,
			transaction
		});

		logger.info(`[${prefix}Clean] Clean completed for user ${user_id}`);
	}

	async #cleanCustomReports(transaction, user_id) {
		const custom_report_results = await this.sandbox_repository.db.CustomReportResult.findAll({
			where: {
				created_by_user_id: user_id
			},
			attributes: ['id'],
			paranoid: false,
			transaction
		});

		if (custom_report_results.length === 0) {
			logger.info(`[CustomReportClean] No CustomReportResult found for user ${user_id}`);
			return;
		}

		const custom_reports_ids = custom_report_results.map((item) => item.id);

		await this.sandbox_repository.db.CustomReportStepKeyResult.destroy({
			where: {
				custom_report_result_id: {
					[Op.in]: custom_reports_ids
				}
			},
			force: true,
			transaction
		});

		await this.sandbox_repository.db.CustomReportResultActionLog.destroy({
			where: {
				custom_report_result_id: {
					[Op.in]: custom_reports_ids
				}
			},
			force: true,
			transaction
		});

		await this.sandbox_repository.db.CustomReportReview.destroy({
			where: {
				custom_report_result_id: {
					[Op.in]: custom_reports_ids
				}
			},
			force: true,
			transaction
		});

		await this.sandbox_repository.db.CustomReportResult.destroy({
			where: {
				id: {
					[Op.in]: custom_reports_ids
				}
			},
			transaction,
			force: true
		});

		logger.info(`[CustomReportClean] Clean completed for user ${user_id}`);
	}

	async #cleanLibertyMutual(transaction, user_id) {
		const liberty_mutual_reports = await this.sandbox_repository.db.LibertyMutualReport.findAll({
			where: {
				report_user_id: user_id
			},
			attributes: ['id'],
			transaction
		});

		if (liberty_mutual_reports.length === 0) {
			logger.info(`[LibertyMutualClean] No LibertyMutualReport found for user ${user_id}`);
			return;
		}

		await this.sandbox_repository.db.LibertyMutualReportInput.destroy({
			where: {
				liberty_mutual_report_id: {
					[Op.in]: liberty_mutual_reports.map((item) => item.id)
				}
			},
			force: true,
			transaction
		});

		await this.sandbox_repository.db.LibertyMutualReport.destroy({
			where: {
				id: {
					[Op.in]: liberty_mutual_reports.map((item) => item.id)
				}
			},
			force: true,
			transaction
		});

		logger.info(`[LibertyMutualClean] Clean completed for user ${user_id}`);
	}

	async #cleanSuperPea(transaction, user_id) {
		const super_pea_reports = await this.sandbox_repository.db.SuperPeaReport.findAll({
			where: {
				user_id
			},
			attributes: ['id'],
			transaction
		});

		if (super_pea_reports.length === 0) {
			logger.info(`[SuperPeaClean] No SuperPeaReport found for user ${user_id}`);
			return;
		}

		const super_pea_ids = super_pea_reports.map((item) => item.id);

		await this.sandbox_repository.db.PEAToSuperPEA.destroy({
			where: {
				super_pea_id: {
					[Op.in]: super_pea_ids
				}
			},
			force: true,
			transaction
		});

		await this.sandbox_repository.db.SuperPeaReport.destroy({
			where: {
				id: {
					[Op.in]: super_pea_ids
				}
			},
			force: true,
			transaction
		});

		logger.info(`[SuperPeaClean] Clean completed for user ${user_id}`);
	}

	async #cleanSera(transaction, user_id) {
		const sera_summaries = await this.sandbox_repository.db.SeraSummary.findAll({
			where: {
				user_id
			},
			attributes: ['id'],
			transaction
		});

		if (sera_summaries.length === 0) {
			logger.info(`[SeraClean] No SeraSummary found for user ${user_id}`);
			return;
		}

		const sera_summary_ids = sera_summaries.map((item) => item.id);

		const sera_summary_reviews = await this.sandbox_repository.db.SeraSummaryReview.findAll({
			where: {
				sera_summary_id: {
					[Op.in]: sera_summaries.map((item) => item.id)
				}
			},
			transaction
		});

		const sera_reports = await this.sandbox_repository.db.SeraReport.findAll({
			where: {
				sera_summary_review_id: {
					[Op.in]: sera_summary_reviews.map((item) => item.id)
				}
			},
			transaction
		});

		if (sera_reports.length > 0) {
			const sera_report_ids = sera_reports.map((item) => item.id);
			await this.sandbox_repository.db.SeraReportUpdated.destroy({
				where: {
					sera_report_id: {
						[Op.in]: sera_report_ids
					}
				},
				force: true,
				transaction
			});

			await this.sandbox_repository.db.SeraReport.destroy({
				where: {
					id: {
						[Op.in]: sera_report_ids
					}
				},
				force: true,
				transaction
			});
		}

		if (sera_summary_reviews.length > 0) {
			const sera_summary_review_ids = sera_summary_reviews.map((item) => item.id);

			await this.sandbox_repository.db.SeraReviewTasksResult.destroy({
				where: {
					sera_summary_review_id: {
						[Op.in]: sera_summary_review_ids
					}
				},
				force: true,
				transaction
			});

			await this.sandbox_repository.db.SeraReviewSelector.destroy({
				where: {
					sera_summary_review_id: {
						[Op.in]: sera_summary_review_ids
					}
				},
				force: true,
				transaction
			});

			await this.sandbox_repository.db.SeraSummaryReview.destroy({
				where: {
					id: {
						[Op.in]: sera_summary_review_ids
					}
				},
				force: true,
				transaction
			});
		}

		await this.sandbox_repository.db.SeraSummaryFiles.destroy({
			where: {
				sera_summary_id: {
					[Op.in]: sera_summary_ids
				}
			},
			force: true,
			transaction
		});

		await this.sandbox_repository.db.SeraSummary.destroy({
			where: {
				id: {
					[Op.in]: sera_summary_ids
				}
			},
			force: true,
			transaction
		});

		logger.info(`[SeraClean] Clean completed for user ${user_id}`);
	}

	async #cleanActionPlan(transaction, user_id) {
		const action_plans = await this.sandbox_repository.db.ActionPlanV2.findAll({
			where: {
				user_id
			},
			attributes: ['id', 'action_plan_origin_id'],
			transaction
		});

		if (action_plans.length === 0) {
			logger.info(`[ActionPlanClean] No ActionPlan found for user ${user_id}`);
			return;
		}

		const action_plan_ids = action_plans.map((item) => item.id);
		const action_plan_origin_ids = action_plans.map((item) => item.action_plan_origin_id);

		await this.sandbox_repository.db.ActionPlanComment.destroy({
			where: {
				action_plan_id: {
					[Op.in]: action_plan_ids
				}
			},
			force: true,
			transaction
		});

		await this.sandbox_repository.db.ActionPlanHistoryV2.destroy({
			where: {
				action_plan_id: {
					[Op.in]: action_plan_ids
				}
			},
			force: true,
			transaction
		});

		await this.sandbox_repository.db.ActionPlanRelatedReport.destroy({
			where: {
				action_plan_id: {
					[Op.in]: action_plan_ids
				}
			},
			force: true,
			transaction
		});

		await this.sandbox_repository.db.ActionPlanAttachment.destroy({
			where: {
				action_plan_id: {
					[Op.in]: action_plan_ids
				}
			},
			force: true,
			transaction
		});

		await this.sandbox_repository.db.ActionPlan.destroy({
			where: {
				id: {
					[Op.in]: action_plan_ids
				}
			},
			force: true,
			transaction
		});

		await this.sandbox_repository.db.ActionPlanOrigin.destroy({
			where: {
				id: {
					[Op.in]: action_plan_origin_ids
				}
			},
			force: true,
			transaction
		});

		logger.info(`[ActionPlanClean] Clean completed for user ${user_id}`);
	}

	async #cleanFile(transaction, user_id) {
		const files = await this.sandbox_repository.db.File.findAll({
			where: {
				user_id
			},
			attributes: ['id'],
			transaction
		});

		if (files.length === 0) {
			logger.info(`[FileClean] No File found for user ${user_id}`);
			return;
		}

		const file_ids = files.map((item) => item.id);

		await this.sandbox_repository.db.TasksFiles.destroy({
			where: {
				file_id: {
					[Op.in]: file_ids
				}
			},
			force: true,
			transaction
		});

		await this.sandbox_repository.db.FileRiskResult.destroy({
			where: {
				file_id: {
					[Op.in]: file_ids
				}
			},
			force: true,
			transaction
		});

		await this.sandbox_repository.db.File.destroy({
			where: {
				id: {
					[Op.in]: file_ids
				}
			},
			force: true,
			transaction
		});

		logger.info(`[FileClean] Clean completed for user ${user_id}`);
	}
}
