import moment from 'moment';
import { StorageContext } from '../utils/storage_context.js';
import { ACTION_PLAN_EVENTS, ERROR_RESPONSE_ENUM } from '../util/enum.js';
import { logger, AppError, RESPONSE_ERROR_ENTITIES, RESPONSE_ERROR_STATUS } from '../helpers/index.js';

const { DATABASE_FAILED_PERFORM_QUERY } = RESPONSE_ERROR_STATUS;
const { ACTION_PLAN, CUSTOM_REPORT_STEP_KEY, PRELIMINARY_ANALYSIS_STEP, CUSTOM_REPORT_SUB_STEP_KEY } =
	RESPONSE_ERROR_ENTITIES;

export class ActionPlanService {
	constructor({
		repository,
		action_plan_task_repository,
		custom_report_step_key_repository,
		preliminary_analysis_step_repository,
		custom_report_sub_step_key_repository
	}) {
		this.repository = repository;
		this.action_plan_task_repository = action_plan_task_repository;
		this.custom_report_step_key_repository = custom_report_step_key_repository;
		this.preliminary_analysis_step_repository = preliminary_analysis_step_repository;
		this.custom_report_sub_step_key_repository = custom_report_sub_step_key_repository;
	}

	async show(params) {
		logger.info('[ActionPlan] service - show init');
		const {
			id,
			organization_id,
			title,
			responsible_user_id,
			deadline_status,
			deadline_date,
			score,
			board,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			file_id,
			is_active,
			lexo_rank,
			report_type
		} = params;

		let { offset, limit } = params;

		let attributes = {
			organizationId: organization_id,
			isActive: is_active,
			lexoRank: lexo_rank
		};

		if (report_type) {
			attributes.report_type = report_type;
		}

		if (title) {
			attributes.title = title;
		}

		if (id) {
			attributes.id = id;
		}

		if (responsible_user_id) {
			attributes.responsibleUserId = responsible_user_id;
		}

		if (deadline_status) {
			attributes.deadlineStatus = deadline_status;
		}

		if (deadline_date) {
			attributes.deadlineDate = deadline_date;
		}

		if (score) {
			attributes.score = score;
		}

		if (board) {
			attributes.board = board;
		}

		if (company_id) {
			attributes.companyId = company_id;
		}

		if (company_id && sector_id) {
			attributes.sectorId = sector_id;
		}

		if (company_id && sector_id && line_id) {
			attributes.lineId = line_id;
		}

		if (company_id && sector_id && line_id && workstation_id) {
			attributes.workstationId = workstation_id;
		}

		if (file_id) {
			attributes.fileId = file_id;
		}

		const { dataValues } = await this.repository.countActionPlans(attributes);
		const { total_action_plans } = dataValues;

		let page = offset;

		const page_count = Math.ceil(total_action_plans / limit);

		if (page > page_count) {
			page = page_count;
		}

		offset = page * limit;

		attributes = { ...attributes, offset, limit };

		const action_plans = await this.repository.actionPlansFilter(attributes);

		const id_conditions = action_plans.map((plan) => ({
			action_plan_id: plan.id,
			is_active: true
		}));

		const Op = this.repository.db.Sequelize.Op;

		const tasks = await this.repository.db.ActionPlansTask.findAll({
			where: {
				[Op.or]: id_conditions
			}
		});

		const action_cards = action_plans.map((plan, index) => {
			plan.dataValues.tasks = tasks.filter((task) => task.dataValues.action_plan_id === plan.id);

			return {
				...plan.dataValues,
				sequence: page * limit + index
			};
		});

		logger.info('[ActionPlan] service - show finish');
		return { total_plans: total_action_plans, action_cards };
	}

	async create(params, passed_transaction) {
		logger.info('[ActionPlan] service - create init');
		const { organization_id, action_plan, user } = params;

		let transaction = passed_transaction || undefined;

		try {
			const last_lexo_rank = await this.repository.getLastLexoRank({
				organizationId: organization_id
			});

			const lexo_rank = last_lexo_rank ? last_lexo_rank + '1' : '1';

			const action_plan_payload = { ...action_plan, lexo_rank, user_id: user.id };

			if (!transaction) transaction = await this.repository.db.sequelize.transaction();

			const newActionPlan = await this.repository.db.ActionPlan.create(action_plan_payload, { transaction });

			const history_event = await this.createActionPlanHistory(
				ACTION_PLAN_EVENTS.ACTION_PLAN.CREATED,
				newActionPlan,
				newActionPlan.id,
				user,
				{ transaction }
			);

			if (!history_event) {
				throw new AppError(ACTION_PLAN.HISTORY_FAIL_CREATE);
			}

			if (!passed_transaction) await transaction.commit();

			logger.info('[ActionPlan] service - create finish');
			return {
				status: 'success',
				message: 'Action Plan created successfully',
				data: newActionPlan
			};
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}

	async createFromChecklist(params) {
		logger.info('[ActionPlan] service - createFromChecklist init');
		let transaction;

		try {
			const {
				tasks,
				file_id,
				step_id,
				user_id,
				action_plan,
				organization_id,
				custom_report_step_key_id,
				custom_report_sub_step_key_id
			} = params;

			if (!step_id && !custom_report_step_key_id && !custom_report_sub_step_key_id) {
				throw new AppError(ERROR_RESPONSE_ENUM.INTERNAL_SERVER_ERROR);
			}

			if (step_id) {
				await this.#getPreliminaryAnalysisStepById(step_id);
			}

			if (custom_report_step_key_id) {
				await this.#getCustomReportStepKeyById(custom_report_step_key_id);
			}

			if (custom_report_sub_step_key_id) {
				await this.#getCustomReportSubStepKeyById(custom_report_sub_step_key_id);
			}

			let existing_action_plan;

			if (step_id) {
				existing_action_plan = await this.getActionPlanByStepId({ step_id });
			}

			if (custom_report_step_key_id) {
				existing_action_plan = await this.#getActionPlanByCustomReportStepKeyId({
					file_id,
					custom_report_step_key_id
				});
			}

			if (custom_report_sub_step_key_id) {
				existing_action_plan = await this.#getActionPlanByCustomReportSubStepKeyId({
					file_id,
					custom_report_sub_step_key_id
				});
			}

			if (existing_action_plan) {
				throw new AppError(ACTION_PLAN.ALREADY_CREATED);
			}

			const last_lexo_rank = await this.repository.getLastLexoRank({
				organizationId: organization_id
			});

			const lexo_rank = last_lexo_rank ? last_lexo_rank + '1' : '1';

			const user = {
				id: user_id
			};

			const action_plan_payload = {
				...action_plan,
				custom_report_sub_step_key_id,
				custom_report_step_key_id,
				lexo_rank,
				user_id,
				file_id,
				step_id
			};

			transaction = await this.repository.db.sequelize.transaction();

			const new_action_plan = await this.repository.create(action_plan_payload, { transaction });

			const history_event = await this.createActionPlanHistory(
				ACTION_PLAN_EVENTS.ACTION_PLAN.CREATED,
				new_action_plan,
				new_action_plan.id,
				user,
				{ transaction }
			);

			if (!history_event) {
				throw new AppError(ACTION_PLAN.HISTORY_FAIL_CREATE);
			}

			const result = {
				newActionPlan: {
					id: new_action_plan.id,
					title: new_action_plan.title,
					step_id: new_action_plan.step_id,
					createdAt: new_action_plan.createdAt,
					custom_report_step_key_id: new_action_plan.custom_report_step_key_id,
					custom_report_sub_step_key_id: new_action_plan.custom_report_sub_step_key_id
				}
			};

			if (tasks?.length > 0) {
				const mappedTasks = tasks.map((task) => {
					return { ...task, action_plan_id: new_action_plan.id };
				});

				const new_tasks = await this.action_plan_task_repository.bulkCreate(mappedTasks, {
					transaction
				});

				result.newTask = new_tasks;
			}

			await transaction.commit();
			logger.info('[ActionPlan] service - createFromChecklist finish');
			return result;
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async createFromSera(params) {
		logger.info('[ActionPlan] service - createFromSera init');
		let transaction;

		try {
			const { organization_id, file_id, sera_summary_review_id, action_plan, user_id, tasks } = params;

			const last_lexo_rank = await this.repository.getLastLexoRank({
				organizationId: organization_id
			});

			const lexo_rank = last_lexo_rank ? last_lexo_rank + '1' : '1';

			const user = {
				id: user_id
			};

			const action_plan_payload = {
				...action_plan,
				lexo_rank,
				user_id,
				file_id,
				sera_summary_review_id
			};

			transaction = await this.repository.db.sequelize.transaction();

			const new_action_plan = await this.repository.create(action_plan_payload, { transaction });

			const history_event = await this.createActionPlanHistory(
				ACTION_PLAN_EVENTS.ACTION_PLAN.CREATED,
				new_action_plan,
				new_action_plan.id,
				user,
				{ transaction }
			);

			if (!history_event) {
				throw new AppError(ACTION_PLAN.HISTORY_FAIL_CREATE);
			}

			const result = {
				new_action_plan: {
					id: new_action_plan.id,
					sera_summary_review_id: new_action_plan.sera_summary_review_id,
					title: new_action_plan.title,
					createdAt: new_action_plan.createdAt
				}
			};

			if (tasks?.length > 0) {
				const mapped_tasks = tasks.map((task) => {
					return { ...task, action_plan_id: new_action_plan.id };
				});

				const new_tasks = await this.repository.db.ActionPlansTask.bulkCreate(mapped_tasks, {
					transaction
				});

				result.new_task = new_tasks;
			}

			await transaction.commit();
			logger.info('[ActionPlan] service - createFromSera finish');
			return result;
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async getActionPlanByStepId(params) {
		logger.info('[ActionPlan] service - getActionPlanByStepId init');
		const { step_id } = params;
		let action_plans = await this.repository.db.ActionPlan.findOne({
			where: {
				step_id,
				is_active: true
			},
			attributes: ['id', 'title', 'description', 'deadline', 'responsible_user_id', 'created_at']
		});
		logger.info('[ActionPlan] service - getActionPlanByStepId init');
		return action_plans;
	}

	async getCustomReportActionPlan({ file_id, custom_report_step_key_id, custom_report_sub_step_key_id }) {
		logger.info('[ActionPlan] service - getCustomReportActionPlan init');
		const where_options = {
			where: {
				file_id,
				is_active: true
			}
		};

		if (custom_report_step_key_id) {
			where_options.where = {
				...where_options.where,
				custom_report_step_key_id
			};
		}

		if (custom_report_sub_step_key_id) {
			where_options.where = {
				...where_options.where,
				custom_report_sub_step_key_id
			};
		}

		const action_plan = await this.repository.findOne({
			where: {
				...where_options.where
			},
			include: [
				{
					association: 'action_plan_task',
					where: {
						is_active: true
					},
					required: false
				}
			],
			attributes: ['id', 'title', 'description', 'deadline', 'responsible_user_id', 'created_at']
		});
		if (!action_plan) {
			throw new AppError(ACTION_PLAN.NOT_FOUND);
		}
		logger.info('[ActionPlan] service - getCustomReportActionPlan init');
		return action_plan;
	}

	async getActionPlansBySeraSummaryReviewId(params) {
		logger.info('[ActionPlan] service - getActionPlansBySeraSummaryReviewId init');
		const { sera_summary_review_id, file_id } = params;
		const action_plans = await this.repository.findAll({
			where: {
				sera_summary_review_id,
				file_id,
				is_active: true
			},
			attributes: ['id', 'title', 'description', 'deadline', 'responsible_user_id', 'created_at']
		});
		logger.info('[ActionPlan] service - getActionPlansBySeraSummaryReviewId init');
		return action_plans;
	}

	async updateFromChecklist(params) {
		logger.info('[ActionPlan] service - updateFromChecklist init');
		const { action_plan_id, action_plan, tasks, user } = params;

		let transaction;

		try {
			const existing_action_plan = await this.repository.findOne({
				where: {
					id: action_plan_id,
					is_active: true
				}
			});

			if (!existing_action_plan) {
				throw new AppError(ACTION_PLAN.NOT_FOUND);
			}

			let changed_key, event;

			for (const key in action_plan) {
				if (action_plan[key] instanceof Date) {
					if (existing_action_plan[key].getTime() !== action_plan[key].getTime()) changed_key = key;
				} else {
					if (existing_action_plan[key] !== action_plan[key]) changed_key = key;
				}
			}

			existing_action_plan[changed_key] = action_plan[changed_key];

			switch (changed_key) {
				case 'deadline':
					event = ACTION_PLAN_EVENTS.ACTION_PLAN.CHANGED_DEADLINE;
					break;
				case 'responsible_user_id':
					event = ACTION_PLAN_EVENTS.ACTION_PLAN.CHANGED_RESPONSIBLE;
					break;
				default:
					break;
			}

			transaction = await this.repository.db.sequelize.transaction();
			await existing_action_plan.save({ transaction });

			if (event) {
				const history_event = await this.createActionPlanHistory(
					event,
					existing_action_plan,
					existing_action_plan.id,
					user,
					{
						transaction
					}
				);

				if (!history_event) {
					throw new AppError(ACTION_PLAN.HISTORY_FAIL_CREATE);
				}
			}

			let result = {
				hasActionPlan: existing_action_plan
			};

			let existingTasks = await this.action_plan_task_repository.findAll({
				where: {
					action_plan_id,
					is_active: true
				},
				attributes: ['id']
			});

			if (existingTasks.length > 0 && (!tasks || tasks?.length === 0)) {
				let tasksToDelete = await this.action_plan_task_repository.findAll({
					where: {
						action_plan_id,
						is_active: true
					}
				});

				const tasksToDeleteCopy = [...tasksToDelete];

				tasksToDelete = tasksToDeleteCopy.map((task) => {
					return {
						id: task.id,
						action_plan_id,
						is_active: false
					};
				});
				await this.action_plan_task_repository.bulkCreate(tasksToDelete, {
					updateOnDuplicate: ['is_active'],
					transaction
				});
			} else if (tasks?.length > 0) {
				let updatingTasksId = tasks.map((task) => task.id);

				const tasks_to_delete = existingTasks
					.map((existingTask) => {
						if (!updatingTasksId.includes(existingTask.id)) {
							return {
								id: existingTask.id,
								action_plan_id,
								is_active: false
							};
						}
					})
					.filter((mappedTask) => mappedTask);

				if (tasks_to_delete.length > 0) {
					await this.action_plan_task_repository.bulkCreate(tasks_to_delete, {
						updateOnDuplicate: ['is_active'],
						transaction
					});
				}

				const mapped_tasks = tasks.map((task) => {
					return {
						...task,
						action_plan_id,
						updated_at: new Date()
					};
				});

				const new_tasks = await this.action_plan_task_repository.bulkCreate(mapped_tasks, {
					updateOnDuplicate: ['description', 'type', 'updated_at'],
					transaction
				});

				result.newTask = new_tasks;
			}

			await transaction.commit();

			logger.info('[ActionPlan] service - updateFromChecklist finish');
			return result;
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async updateFromSera(params) {
		logger.info('[ActionPlan] service - updateFromSera init');
		const { action_plan_id, action_plan, tasks, user } = params;

		let transaction;

		try {
			const has_action_plan = await this.repository.db.ActionPlan.findOne({
				where: {
					id: action_plan_id,
					is_active: true
				}
			});

			if (!has_action_plan) {
				throw new AppError(ACTION_PLAN.NOT_FOUND);
			}

			let changed_key, event;

			for (const key in action_plan) {
				if (action_plan[key] instanceof Date) {
					if (has_action_plan[key].getTime() !== action_plan[key].getTime()) changed_key = key;
				} else {
					if (has_action_plan[key] !== action_plan[key]) changed_key = key;
				}
			}

			has_action_plan[changed_key] = action_plan[changed_key];

			switch (changed_key) {
				case 'deadline':
					event = ACTION_PLAN_EVENTS.ACTION_PLAN.CHANGED_DEADLINE;
					break;
				case 'responsible_user_id':
					event = ACTION_PLAN_EVENTS.ACTION_PLAN.CHANGED_RESPONSIBLE;
					break;
				default:
					break;
			}

			transaction = await this.repository.db.sequelize.transaction();

			await has_action_plan.save({ transaction });

			if (event) {
				const history_event = await this.createActionPlanHistory(
					event,
					has_action_plan,
					has_action_plan.id,
					user,
					{
						transaction
					}
				);

				if (!history_event) {
					throw new AppError(ACTION_PLAN.HISTORY_FAIL_CREATE);
				}
			}

			let result = {
				has_action_plan
			};

			let existing_tasks = await this.repository.db.ActionPlansTask.findAll({
				where: {
					action_plan_id,
					is_active: true
				},
				attributes: ['id']
			});

			if (existing_tasks.length > 0 && (!tasks || tasks?.length === 0)) {
				let tasks_to_delete = await this.repository.db.ActionPlansTask.findAll({
					where: {
						action_plan_id,
						is_active: true
					}
				});

				const tasks_to_delete_copy = [...tasks_to_delete];

				tasks_to_delete = tasks_to_delete_copy.map((task) => {
					return {
						id: task.id,
						action_plan_id,
						is_active: false
					};
				});
				await this.repository.db.ActionPlansTask.bulkCreate(tasks_to_delete, {
					updateOnDuplicate: ['is_active'],
					transaction
				});
			} else if (tasks?.length > 0) {
				let updating_tasks_id = tasks.map((task) => task.id);

				const tasks_to_delete = existing_tasks
					.map((existingTask) => {
						if (!updating_tasks_id.includes(existingTask.id)) {
							return {
								id: existingTask.id,
								action_plan_id,
								is_active: false
							};
						}
					})
					.filter((mappedTask) => mappedTask);

				if (tasks_to_delete.length > 0) {
					await this.repository.db.ActionPlansTask.bulkCreate(tasks_to_delete, {
						updateOnDuplicate: ['is_active'],
						transaction
					});
				}

				const mapped_tasks = tasks.map((task) => {
					return {
						...task,
						action_plan_id,
						updated_at: new Date()
					};
				});

				const new_tasks = await this.repository.db.ActionPlansTask.bulkCreate(mapped_tasks, {
					updateOnDuplicate: ['description', 'type', 'updated_at'],
					transaction
				});

				result.new_task = new_tasks;
			}

			await transaction.commit();

			logger.info('[ActionPlan] service - updateFromSera finish');
			return result;
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async update(params) {
		logger.info('[ActionPlan] service - update init');
		const { action_plan, user } = params;

		let transaction;

		try {
			const has_action_plan = await this.repository.db.ActionPlan.findOne({
				where: {
					id: action_plan.id,
					is_active: true
				}
			});

			if (!has_action_plan) {
				throw new AppError(ACTION_PLAN.NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			let changed_key, event;

			for (const key in action_plan) {
				if (key == 'lexo_rank') continue;
				if (action_plan[key] instanceof Date) {
					if (has_action_plan[key].getTime() !== action_plan[key].getTime()) changed_key = key;
				} else {
					if (has_action_plan[key] !== action_plan[key]) changed_key = key;
				}
			}

			has_action_plan[changed_key] = action_plan[changed_key];
			has_action_plan.lexo_rank = action_plan.lexo_rank;

			switch (changed_key) {
				case 'title':
					event = ACTION_PLAN_EVENTS.ACTION_PLAN.RENAMED;
					break;
				case 'board':
					event = ACTION_PLAN_EVENTS.ACTION_PLAN.CHANGED_STATUS;
					break;
				case 'deadline':
					event = ACTION_PLAN_EVENTS.ACTION_PLAN.CHANGED_DEADLINE;
					break;
				case 'responsible_user_id':
					event = ACTION_PLAN_EVENTS.ACTION_PLAN.CHANGED_RESPONSIBLE;
					break;
				default:
					break;
			}

			if (event) {
				const history_event = await this.createActionPlanHistory(
					event,
					has_action_plan,
					has_action_plan.id,
					user,
					{ transaction }
				);

				if (!history_event) {
					throw new AppError(ACTION_PLAN.HISTORY_FAIL_CREATE);
				}
			}

			await has_action_plan.save({ transaction });
			await transaction.commit();

			logger.info('[ActionPlan] service - update finish');
			return has_action_plan;
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}

	async delete(params) {
		logger.info('[ActionPlan] service - delete init');
		const { action_plan_id } = params;

		let transaction;
		try {
			const action_plan = await this.repository.db.ActionPlan.findOne({
				where: {
					id: action_plan_id,
					is_active: true
				}
			});

			if (!action_plan) {
				throw new AppError(ACTION_PLAN.NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			action_plan.is_active = false;
			await action_plan.save({ transaction });

			await transaction.commit();

			logger.info('[ActionPlan] service - delete finish');
			return {
				status: 'success',
				message: 'Action plan deleted successfully',
				data: { action_plan_id }
			};
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}

	async getTaskByActionPlan(params) {
		logger.info('[ActionPlan] service - getTaskByActionPlan init');
		const { action_plan_id } = params;

		try {
			let tasks = await this.repository.db.ActionPlansTask.findAll({
				where: { action_plan_id, is_active: true },
				order: [['created_at', 'ASC']]
			});

			if (!tasks) {
				throw new AppError(ACTION_PLAN.TASK_NOT_FOUND);
			}

			logger.info('[ActionPlan] service - getTaskByActionPlan init');
			return tasks;
		} catch (error) {
			throw error;
		}
	}

	async createTask(params) {
		logger.info('[ActionPlan] service - createTask init');
		const { task, user } = params;

		let transaction;

		try {
			transaction = await this.repository.db.sequelize.transaction();

			const new_task = await this.repository.db.ActionPlansTask.create(task, {
				transaction
			});

			const history_event = await this.createActionPlanHistory(
				ACTION_PLAN_EVENTS.TASK.CREATED,
				new_task,
				new_task.action_plan_id,
				user,
				{ transaction }
			);

			if (!history_event) {
				throw new AppError(ACTION_PLAN.HISTORY_FAIL_CREATE);
			}

			await transaction.commit();

			logger.info('[ActionPlan] service - createTask finish');
			return {
				status: 'success',
				message: 'Task created successfully',
				new_task
			};
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}

	async updateTask(params) {
		logger.info('[ActionPlan] service - updateTask init');
		const { task, user } = params;

		let transaction;

		try {
			const has_task = await this.repository.db.ActionPlansTask.findOne({
				where: {
					id: task.id,
					is_active: true
				}
			});

			if (!has_task) {
				throw new AppError(ACTION_PLAN.TASK_NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			const { description, type, is_completed } = task;

			let event;

			if (has_task.description !== description || has_task.type !== type) {
				if (has_task.type !== type && has_task.description !== description) {
					event = ACTION_PLAN_EVENTS.TASK.BOTH;
				} else if (has_task.type !== type) {
					event = ACTION_PLAN_EVENTS.TASK.TYPED;
				} else if (has_task.description !== description) {
					event = ACTION_PLAN_EVENTS.TASK.RENAMED;
				}

				has_task.type = type;
				has_task.description = description;
			} else if (has_task.is_completed !== is_completed) {
				event = is_completed ? ACTION_PLAN_EVENTS.TASK.CHECKED : ACTION_PLAN_EVENTS.TASK.UNCHECKED;
				has_task.is_completed = is_completed;
			}

			if (event) {
				const new_history_event = await this.createActionPlanHistory(
					event,
					has_task,
					has_task.action_plan_id,
					user,
					{ transaction }
				);

				if (!new_history_event) {
					throw new AppError(ACTION_PLAN.HISTORY_FAIL_CREATE);
				}
			}

			await has_task.save({ transaction });
			await transaction.commit();

			logger.info('[ActionPlan] service - updateTask finish');
			return {
				status: 'success',
				message: 'Successfully updated task data',
				data: { id: has_task.id }
			};
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}

	async deleteTask(params) {
		logger.info('[ActionPlan] service - deleteTask init');
		const { task_id, user } = params;

		let transaction;
		try {
			const task = await this.repository.db.ActionPlansTask.findOne({
				where: {
					id: task_id,
					is_active: true
				}
			});

			if (!task) {
				throw new AppError(ACTION_PLAN.TASK_NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			task.is_active = false;

			const history_event = await this.createActionPlanHistory(
				ACTION_PLAN_EVENTS.TASK.DELETED,
				task,
				task.action_plan_id,
				user,
				{ transaction }
			);

			if (!history_event) {
				throw new AppError(ACTION_PLAN.HISTORY_FAIL_CREATE);
			}

			await task.save({ transaction });
			await transaction.commit();

			logger.info('[ActionPlan] service - deleteTask finish');
			return {
				status: 'success',
				message: 'Task deleted successfully',
				data: { task_id }
			};
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}

	async getHistoryByActionPlan(params) {
		logger.info('[ActionPlan] service - getHistoryByActionPlan init');
		const { action_plan_id } = params;

		try {
			let history = await this.repository.db.ActionPlanHistory.findAll({
				where: { action_plan_id, is_active: true },
				order: [['event_date', 'DESC']],
				include: [
					{
						model: this.repository.db.User,
						as: 'user',
						attributes: ['name']
					},
					{
						model: this.repository.db.User,
						as: 'responsible_user',
						attributes: ['name']
					}
				]
			});

			if (!history) {
				throw new AppError(ACTION_PLAN.HISTORY_NOT_FOUND);
			}

			logger.info('[ActionPlan] service - getHistoryByActionPlan init');
			return history;
		} catch (error) {
			throw error;
		}
	}

	async createActionPlanHistory(event, event_item, action_plan_id, user, { transaction }) {
		logger.info('[ActionPlan] service - createActionPlanHistory init');
		const event_payload = {
			action_plan_id,
			event: event,
			event_item_id: event_item.id,
			event_value: event_item.title,
			event_date: new Date(),
			user_id: user.id,
			responsible_user_id: null
		};

		if (Object.values(ACTION_PLAN_EVENTS.TASK).includes(event)) event_payload.event_value = event_item.description;

		if (event === ACTION_PLAN_EVENTS.TASK.TYPED) event_payload.event_value = event_item.type;

		if (event === ACTION_PLAN_EVENTS.TASK.BOTH)
			event_payload.event_value = `${event_item.description} - ${event_item.type} `;

		if (event === ACTION_PLAN_EVENTS.ACTION_PLAN.CHANGED_STATUS) event_payload.event_value = event_item.board;

		if (event === ACTION_PLAN_EVENTS.ACTION_PLAN.CHANGED_DEADLINE)
			event_payload.event_value = moment(event_item.deadline).format('DD/MM/YY');

		if (event === ACTION_PLAN_EVENTS.ACTION_PLAN.CHANGED_RESPONSIBLE)
			event_payload.responsible_user_id = event_item.responsible_user_id;

		const new_event = await this.repository.db.ActionPlanHistory.create(event_payload, {
			transaction
		});

		logger.info('[ActionPlan] service - createActionPlanHistory finish');
		return new_event;
	}

	async #getPreliminaryAnalysisStepById(step_id) {
		const preliminary_analysis_step = await this.preliminary_analysis_step_repository.findByPk(step_id, {
			where: {
				is_active: true
			}
		});

		if (!preliminary_analysis_step) {
			throw new AppError(PRELIMINARY_ANALYSIS_STEP.NOT_FOUND);
		}
		return preliminary_analysis_step;
	}

	async #getCustomReportStepKeyById(custom_report_step_key_id) {
		const custom_report_step_key = await this.custom_report_step_key_repository.findByPk(custom_report_step_key_id);

		if (!custom_report_step_key) {
			throw new AppError(CUSTOM_REPORT_STEP_KEY.NOT_FOUND);
		}
		return custom_report_step_key;
	}

	async #getCustomReportSubStepKeyById(custom_report_sub_step_key_id) {
		const custom_report_sub_step_key =
			await this.custom_report_sub_step_key_repository.findByPk(custom_report_sub_step_key_id);

		if (!custom_report_sub_step_key) {
			throw new AppError(CUSTOM_REPORT_SUB_STEP_KEY.NOT_FOUND);
		}
		return custom_report_sub_step_key;
	}

	async #getActionPlanByCustomReportStepKeyId({ file_id, custom_report_step_key_id }) {
		return this.repository.findOne({
			where: {
				file_id,
				is_active: true,
				custom_report_step_key_id
			}
		});
	}

	async #getActionPlanByCustomReportSubStepKeyId({ file_id, custom_report_sub_step_key_id }) {
		return this.repository.findOne({
			where: {
				file_id,
				is_active: true,
				custom_report_sub_step_key_id
			}
		});
	}

	async countAllFromReportsDelayed(params) {
		logger.info('[ActionPlan] service - countAllFromReportsDelayed init', { params });
		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date
		} = params;

		let user_id = '';

		if (StorageContext.getStore()?.environment === 'sandbox') {
			user_id = StorageContext.getStore().user_id;
		}

		const payload = {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date,
			user_id
		};

		const promises = [
			this.repository.countAllFromReports(payload),
			this.repository.countAllFromReports({ ...payload, delayed: true })
		];

		const [[total, error_total], [delayed, error_delayed]] = await Promise.all(promises);

		if (error_total) {
			logger.error(`${error_total.message}, stack trace - ${error_total.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (error_delayed) {
			logger.error(`${error_delayed.message}, stack trace - ${error_delayed.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[ActionPlan] service - countAllFromReportsDelayed finish');
		return { total, delayed };
	}

	async countAllFromReportsByStatus(params) {
		logger.info('[ActionPlan] service - countAllFromReportsByStatus init', { params });
		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date
		} = params;

		let user_id = '';

		if (StorageContext.getStore()?.environment === 'sandbox') {
			user_id = StorageContext.getStore().user_id;
		}

		const payload = {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date,
			user_id
		};

		const promises = [
			this.repository.countAllFromReportsByStatus(payload),
			this.repository.countAllFromReportsByStatus({ ...payload, delayed: true })
		];

		const [[total, error_total], [delayed, error_delayed]] = await Promise.all(promises);

		if (error_total) {
			logger.error(`${error_total.message}, stack trace - ${error_total.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (error_delayed) {
			logger.error(`${error_delayed.message}, stack trace - ${error_delayed.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[ActionPlan] service - countAllFromReportsByStatus finish');
		return { total, delayed };
	}
}
