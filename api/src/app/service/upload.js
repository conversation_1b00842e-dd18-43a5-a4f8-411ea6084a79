import _ from 'lodash';
import config from 'config';
import { v4 as uuid } from 'uuid';
import { Queue } from '../helpers/queue.js';
import { logger } from '../helpers/logger.js';
import { Storage } from '../helpers/storage.js';
import { AppError } from '../helpers/errors.js';
import { removeExtension, normalizeAngles } from '../helpers/util.js';
import { RESPONSE_ERROR_ENTITIES, ERROR_RESPONSE_EXTERNAL_SERVICE, ENUM_STATUS_FILE } from '../helpers/constants.js';

const { FILE } = RESPONSE_ERROR_ENTITIES;
const { STORAGE, QUEUE } = ERROR_RESPONSE_EXTERNAL_SERVICE;
const { CORRUPTED_FILE, EXTRACTED_DATA, NOT_PROCESSED, PROCESSING, PROCESSED } = ENUM_STATUS_FILE;
import { StorageContext } from '../utils/storage_context.js';

const BUCKET = config.get('App.bucket');
export class UploadService {
	constructor(repository) {
		this.repository = repository;
		this.queue = new Queue();
		this.storage = new Storage();
	}

	removeExtension(filename) {
		return filename.substring(0, filename.lastIndexOf('.')) || filename;
	}

	async createUrlSigned(parameters) {
		logger.info('[Upload] service - createUrlSigned init');
		const { organization_id, company_id, content_type, file_name, user_id, blur, custom_report } = parameters;
		let transaction;
		try {
			const folder = Storage.getFolder({
				organizationId: organization_id,
				companyId: company_id
			});

			const randomId = uuid();
			const nomenclature = randomId.split('-', 5)[0];
			const normalize_filename = file_name.replace(/[\u0250-\ue007]/g, '');
			const generated_name = `${folder}/${nomenclature}-${normalize_filename}`;

			const { url } = await this.storage.createSignatureUpload({
				fileName: generated_name,
				contentType: content_type,
				bucket: BUCKET
			});

			transaction = await this.repository.db.sequelize.transaction();

			const file = await this.repository.db.File.create(
				{
					size: 0,
					url: '',
					tool: 'RULA',
					blurFace: blur,
					isActive: false,
					organization_id,
					generated_name,
					company_id,
					status: ENUM_STATUS_FILE.IN_QUEUE,
					original_name: normalize_filename,
					user_id
				},
				{ transaction }
			);

			if (!file) {
				throw new AppError(STORAGE.FAIL_CREATE_SIGNATURE);
			}

			if (custom_report) {
				const custom_report_data = await this.repository.db.CustomReport.findByPk(custom_report);
				custom_report_data.addFile(file);
			}

			await transaction.commit();

			logger.info('[Upload] service - createUrlSigned finish');
			return {
				id: file.id,
				url
			};
		} catch (error) {
			transaction && (await transaction.rollback());
			throw error;
		}
	}

	async createUrlDownload(parameters) {
		logger.info('[Upload] service - createUrlDownload init');
		const { organization_id, company_id, file_id } = parameters;
		try {
			const fileInformations = {
				organization_id,
				isActive: true,
				id: file_id,
				company_id
			};

			const file = await this.repository.db.File.findOne({
				where: fileInformations
			});

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			let keyWithoutExtension = this.removeExtension(file.generated_name);

			if (!keyWithoutExtension.includes('/')) {
				let first = organization_id.split('-', 5)[0];
				let second = company_id.split('-', 5)[0];
				// let nomenclature = config.get('App.bucketNomenclature');
				keyWithoutExtension = `${first}-${second}/${keyWithoutExtension}`;
			}

			let bucket = config.get('App.bucket');
			let keyObject = `${keyWithoutExtension}_proc_end.mp4`;

			const url = await this.storage.createSignatureDownload({
				fileName: `kinebot-${file.original_name}`,
				Bucket: bucket,
				Key: keyObject
			});

			if (!url) {
				throw new AppError(FILE.FAILED_CREATE_DOWNLOAD_URL);
			}

			logger.info('[Upload] service - createUrlDownload finish');
			return url;
		} catch (error) {
			throw error;
		}
	}

	async show({ file_id }) {
		logger.info('[Upload] service - show init');
		try {
			const file = await this.repository.db.File.findOne({
				where: {
					id: file_id,
					is_active: true
				},
				include: [
					{
						association: 'workstations',
						attributes: ['id', 'name'],
						include: [
							{
								association: 'line',
								attributes: ['id', 'name'],
								include: [
									{
										association: 'sector',
										attributes: ['id', 'name'],
										where: {
											is_active: true
										}
									}
								]
							}
						]
					}
				]
			});

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			logger.info('[Upload] service - show init');
			return file;
		} catch (error) {
			throw error;
		}
	}

	async getFilesBySector({ sector_id, workstation_id }) {
		logger.info('[Upload] service - getFilesBySector init');
		const options = {
			where: {
				sector_id,
				is_active: true
			}
		};
		if (workstation_id) {
			_.set(options, 'where.workstation_id', workstation_id);
		}

		const files = await this.repository.db.File.findAll(options);

		if (!files) {
			throw new AppError(FILE.NOT_FOUND);
		}

		logger.info('[Upload] service - getFilesBySector init');
		return files;
	}

	async getAngles(payload) {
		logger.info('[Upload] service - getAngles init');
		const { organization_id, company_id, file_id, parts, normalize } = payload;
		try {
			const file = await this.repository.db.File.findOne({
				where: {
					id: file_id,
					organization_id: organization_id,
					company_id: company_id,
					isActive: true
				}
			});

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			let key_without_extension = removeExtension(file.generated_name);

			if (!key_without_extension.includes('/')) {
				let first = organization_id.split('-', 5)[0];
				let second = company_id.split('-', 5)[0];
				key_without_extension = `${first}-${second}/${key_without_extension}`;
			}

			const Bucket = config.get('App.bucket');
			const Key = `${key_without_extension}.json`;
			const storage_config = { Bucket, Key };

			const data = await this.storage.getObject(storage_config);

			if (!data) {
				throw new AppError(STORAGE.FAIL_DOWNLOAD_DATA);
			}

			const selected_parts = new Object();
			const stream = JSON.parse(data.Body.toString('utf-8'));
			const treated_angles = normalizeAngles(stream);

			const select_option = normalize ? treated_angles : stream;

			Object.keys(select_option).forEach((key) => {
				if (parts.includes(key)) selected_parts[key] = select_option[key];
			});

			logger.info('[Upload] service - getAngles finish');
			return selected_parts;
		} catch (error) {
			throw error;
		}
	}

	async addQueue(parameters) {
		logger.info('[Upload] service - addQueue init');
		const { organization_id, company_id, file_id } = parameters;
		try {
			const file = await this.repository.db.File.findOne({
				where: {
					id: file_id,
					organization_id,
					company_id
				}
			});

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			if ([EXTRACTED_DATA, NOT_PROCESSED, PROCESSING].includes(file.status)) {
				throw new AppError(FILE.FILE_ALREADY_QUEUED);
			}

			if (file.status === PROCESSED || file.status === CORRUPTED_FILE) {
				throw new AppError(FILE.FILE_ALREADY_PROCESSED);
			}

			const payload = {
				message: {
					bucketName: BUCKET,
					keyName: file.generated_name,
					environment: StorageContext.getStore()?.environment
				},
				url: config.get('App.queue.file.upload.url')
			};

			const isSuccess = await this.queue.sendMessage(payload);

			if (!isSuccess) {
				throw new AppError(QUEUE.FAIL_COMMUNICATION);
			}

			logger.info('[Upload] service - addQueue finish');
			return {
				status: 'success',
				message: 'Message sent successfully'
			};
		} catch (error) {
			throw error;
		}
	}

	#filterAndMapRows(files) {
		return files.rows.map((file) => {
			const sector = {
				id: file.sector_id,
				name: file.sector_name
			};
			const line = {
				id: file.line_id,
				name: file.line_name
			};
			const workstations = {
				id: file.workstations_id,
				name: file.workstations_name
			};
			const preliminary_analysis = {
				id: file.preliminary_analysis_id,
				worst_score: file.preliminary_analysis_worst_score,
				consolidated: file.preliminary_analysis_consolidated
			};
			return {
				line,
				sector,
				id: file.id,
				workstations,
				size: file.size,
				status: file.status,
				preliminary_analysis,
				duration: file.duration,
				createdAt: file.created_at,
				updatedAt: file.updated_at,
				original_name: file.original_name
			};
		});
	}

	#convertArrayToSQLFormat(files_with_applied_filter_ids) {
		const stringfied = JSON.stringify(files_with_applied_filter_ids);
		return stringfied.replace('[', '(').replace(']', ')');
	}

	async deleteFile(parameters) {
		logger.info('[FileService] service - deleteFile init');
		const { file_id, organization_id, company_id } = parameters;
		let transaction;
		try {
			let query = {
				where: {
					id: file_id,
					organization_id: organization_id,
					company_id: company_id,
					isActive: true
				}
			};

			const file = await this.repository.findOne(query);

			if (!file) {
				throw new AppError(FILE.NOT_FOUND);
			}

			file.isActive = false;
			transaction = await this.repository.db.sequelize.transaction();

			await file.save({ transaction });
			await transaction.commit();

			logger.info('[FileService] service - deleteFile finish');

			return { file_id: file.id };
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async index(params) {
		logger.info('[Upload] service - index init', { params });
		const { limit, offset } = params;

		const files_with_applied_filter = await this.repository.findAllWithHierarchy(params);
		const files_with_applied_filter_ids = files_with_applied_filter.map((file) => file.id);
		let user_id = null;

		if (files_with_applied_filter_ids.length === 0) {
			return { count: 0, rows: [] };
		}

		const files_with_applied_filter_ids_formatted = this.#convertArrayToSQLFormat(files_with_applied_filter_ids);
		if (StorageContext.getStore()?.environment === 'sandbox') user_id = StorageContext.getStore()?.user_id;

		const files = await this.repository.findAndCountAllRawQuery({
			limit,
			offset: limit * offset,
			file_ids: files_with_applied_filter_ids_formatted,
			user_id
		});

		const filtered_files = this.#filterAndMapRows(files);

		logger.info('[Upload] service - index finish');
		return {
			count: files.count,
			rows: filtered_files
		};
	}
}
