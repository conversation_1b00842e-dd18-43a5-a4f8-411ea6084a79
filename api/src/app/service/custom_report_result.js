import config from 'config';
import sequelize from 'sequelize';

import {
	logger,
	AppError,
	FormatTime,
	HelpersUtil,
	RESPONSE_ERROR_STATUS,
	RESPONSE_ERROR_ENTITIES,
	CUSTOM_REPORT_DEFAULT_STEPS_ENUM
} from '../helpers/index.js';
import {
	<PERSON><PERSON><PERSON><PERSON>,
	ACTIONS_ENUM,
	PDFSectionsMapper,
	ReportPDFGenerator,
	EWAD86DataToPDFMapper,
	RebaDataToEWAPDFMapper,
	NioshDataToEWAPDFMapper,
	CustomReportResultMapper,
	WorkingPopulationConverter,
	CustomReportChecklistMapper,
	CustomReportResultCalculator,
	StrainIndexDataToEWAPDFMapper,
	KimPushPullDataToEWAPDFMapper,
	CustomReportStepKeyResultMapper,
	CustomReportStepKeyResultJoiner,
	LibertyMutualDataToEWAPDFMapper,
	ActionPlanToCustomReportResultMapper,
	BackCompressiveForceEstimationToPD<PERSON><PERSON>per,
	KimManualHandlingOperationsDataToEWAPDFMapper,
	ADDITIONAL_ITEMS_NAMES_TO_TITLES_AND_VALUES_MAPPER
} from '../entities/index.js';
import { CUSTOM_REPORT_NAMES } from '../util/constants-custom-report.js';
import { DASHBOARD_INITIAL_MONTH_FISCAL_YEAR, DASHBOARD_JANUARY_INDEX_MONTH } from '../utils/constants.js';
import { StorageContext } from '../utils/storage_context.js';

const {
	USER,
	FILE,
	COMPANY,
	EVALUATOR,
	WORKSTATION,
	ORGANIZATION,
	CUSTOM_REPORT,
	CUSTOM_REPORT_STEP,
	CUSTOM_REPORT_REVIEW,
	CUSTOM_REPORT_RESULT,
	WORK_CONDITION_RESULT,
	CHARACTERISTIC_RESULT,
	CUSTOM_REPORT_STEP_KEY_RESULT
} = RESPONSE_ERROR_ENTITIES;
const bucket = config.get('App.bucket');

const { DATABASE_FAILED_PERFORM_QUERY } = RESPONSE_ERROR_STATUS;

const { Op } = sequelize;

export class CustomReportResultService {
	#file_joiner;
	#format_time;
	#helpers_util;
	#pdf_generator;
	#pdf_sections_mapper;
	#custom_report_result_mapper;
	#working_population_converter;
	#custom_report_checklist_mapper;
	#custom_report_result_calculator;
	#custom_report_step_key_result_joiner;
	#custom_report_step_key_result_mapper;

	constructor({
		repository,
		user_repository,
		file_repository,
		company_repository,
		evaluator_repository,
		action_plan_repository,
		workstation_repository,
		organization_repository,
		custom_report_repository,
		custom_report_step_repository,
		custom_report_review_repository,
		business_information_repository,
		characteristic_result_repository,
		work_condition_result_repository,
		line_repository,
		sector_repository,
		custom_report_step_key_result_repository,
		custom_report_result_actions_log_repository,
		custom_report_sub_step_key_result_repository,
		custom_report_step_key_additional_item_result_repository,
		upload_repository
	}) {
		this.repository = repository;
		this.#file_joiner = new FileJoiner();
		this.#format_time = new FormatTime();
		this.#helpers_util = new HelpersUtil();
		this.file_repository = file_repository;
		this.upload_repository = upload_repository;
		this.user_repository = user_repository;
		this.company_repository = company_repository;
		this.#pdf_generator = new ReportPDFGenerator();
		this.evaluator_repository = evaluator_repository;
		this.#pdf_sections_mapper = new PDFSectionsMapper();
		this.action_plan_repository = action_plan_repository;
		this.workstation_repository = workstation_repository;
		this.organization_repository = organization_repository;
		this.custom_report_repository = custom_report_repository;
		this.custom_report_step_repository = custom_report_step_repository;
		this.#custom_report_result_mapper = new CustomReportResultMapper();
		this.#working_population_converter = new WorkingPopulationConverter();
		this.business_information_repository = business_information_repository;
		this.custom_report_review_repository = custom_report_review_repository;
		this.characteristic_result_repository = characteristic_result_repository;
		this.line_repository = line_repository;
		this.sector_repository = sector_repository;
		this.work_condition_result_repository = work_condition_result_repository;
		this.#custom_report_result_calculator = new CustomReportResultCalculator();
		this.#custom_report_step_key_result_joiner = new CustomReportStepKeyResultJoiner();
		this.#custom_report_step_key_result_mapper = new CustomReportStepKeyResultMapper();
		this.custom_report_step_key_result_repository = custom_report_step_key_result_repository;
		this.custom_report_result_actions_log_repository = custom_report_result_actions_log_repository;
		this.custom_report_sub_step_key_result_repository = custom_report_sub_step_key_result_repository;
		this.custom_report_step_key_additional_item_result_repository =
			custom_report_step_key_additional_item_result_repository;
		this.#custom_report_checklist_mapper = new CustomReportChecklistMapper();
	}

	async create(params) {
		logger.info('[CustomReportResult] service - create init');
		let transaction;
		try {
			const { user_id, file_id, step_id, sector_id, evaluator_id, workstation_id, custom_report_id } = params;

			await this.#checkAlreadyCreatedReportResult({ file_id, custom_report_id });

			await this.#getUserById(user_id);
			await this.#getWorkstationById(workstation_id);
			await this.#getEvaluatorById(evaluator_id);
			const file = await this.#getFileById(file_id);
			const current_step_id = await this.#setCurrentStepId(step_id, custom_report_id);
			const custom_report = await this.#getCustomReportById(custom_report_id);

			file.workstation_id = workstation_id;
			file.sector_id = sector_id;

			transaction = await this.repository.db.sequelize.transaction();
			const created_custom_report_result = await this.#createCustomReportResult(
				{
					...params,
					current_step_id
				},
				transaction
			);

			if (created_custom_report_result && custom_report.name === CUSTOM_REPORT_NAMES.JDS_D86) {
				await this.#createCustomReportReview(
					{
						version: 0,
						name: 'General History',
						custom_report_result_id: null,
						original_custom_report_result_id: created_custom_report_result.id
					},
					transaction
				);
				await this.#createCustomReportReview(
					{
						version: 1,
						name: 'Initial',
						custom_report_result_id: created_custom_report_result.id,
						original_custom_report_result_id: created_custom_report_result.id
					},
					transaction
				);
				await this.custom_report_result_actions_log_repository.create(
					{
						user_id,
						action: ACTIONS_ENUM.REVIEW,
						custom_report_result_id: created_custom_report_result.id
					},
					{
						transaction
					}
				);
			}

			await file.save({ transaction });
			await transaction.commit();

			logger.info('[CustomReportResult] service - create finish');
			return {
				...created_custom_report_result.get({ plain: true }),
				sector_id: file.sector_id,
				company_id: file.company_id,
				organization_id: file.organization_id
			};
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async update(params) {
		logger.info('[CustomReportResult] service - update init');
		let transaction;
		try {
			const {
				user_id,
				file_id,
				step_id,
				sector_id,
				evaluator_id,
				workstation_id,
				custom_report_id,
				custom_report_result_id
			} = params;
			await this.#getCustomReportResultById(custom_report_result_id);
			await this.#getUserById(user_id);
			await this.#getWorkstationById(workstation_id);
			await this.#getEvaluatorById(evaluator_id);
			const file = await this.#getFileById(file_id);
			const current_step_id = await this.#setCurrentStepId(step_id, custom_report_id);

			file.workstation_id = workstation_id;
			file.sector_id = sector_id;

			transaction = await this.repository.db.sequelize.transaction();
			await this.#updateCustomReportResult(
				{
					...params,
					current_step_id
				},
				transaction
			);
			await file.save({ transaction });
			await transaction.commit();

			const updated_custom_report = await this.#getCustomReportResultById(custom_report_result_id);
			logger.info('[CustomReportResult] service - update finish');
			return {
				...updated_custom_report.get({ plain: true }),
				sector_id: file.sector_id,
				company_id: file.company_id,
				organization_id: file.organization_id
			};
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async listAll(payload) {
		logger.info('[CustomReportResult] service - listAll init');
		const { custom_report_name, user_company_ids, filters } = payload;

		const custom_report = await this.#getCustomReportByName(custom_report_name);

		const sector_ids = await this.#getSectorsByCompany(filters, user_company_ids);
		const line_ids = await this.#getLinesBySector(filters, sector_ids);
		const workstation_ids = await this.#getWorkstationsByLine(filters, line_ids);
		const filtered_file_ids = await this.#getFileIdsByWorkstations(filters, workstation_ids);
		const params = await this.#setListAllFilters(filters, filtered_file_ids, custom_report.id);

		const response = await this.repository.findAndCountAll(params);

		logger.info('[CustomReportResult] service - listAll finish');
		return {
			...response,
			custom_report_id: custom_report.id
		};
	}

	async listFiles(parameters) {
		logger.info('[CustomReportResult] listFiles - init');
		const file_ids = await this.#getFilesId(parameters.custom_report_id);
		const paginatedResult = await this.upload_repository.findAndCountAllWithHierarchy({
			...parameters,
			exclude_ids: file_ids
		});

		logger.info('[CustomReportResult] listFiles - finish');
		return paginatedResult;
	}

	async hierarchyWorstScores(params) {
		logger.info('[CustomReportResult] service - hierarchyWorstScores init');
		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date,
			custom_report_id,
			limit
		} = params;

		const [data, error] = await this.repository.hierarchyWorstScores({
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date,
			custom_report_id,
			limit
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[CustomReportResult] service - hierarchyWorstScores finish');
		return data;
	}

	async createWorkCondition(params) {
		logger.info('[CustomReportResult] service - createWorkCondition init');
		let transaction;
		try {
			const { step_id, workstation_id, custom_report_result_id, custom_report_id } = params;
			await this.#getWorkstationById(workstation_id);
			await this.#checkAlreadyCreatedWorkCondition(workstation_id);
			await this.#getCustomReportResultById(custom_report_result_id);
			const current_step_id = await this.#setCurrentStepId(step_id, custom_report_id);
			transaction = await this.repository.db.sequelize.transaction();
			const created_work_condition_result = await this.#createWorkConditionResult(params, transaction);
			await this.#updateCurrentStepId(
				{
					custom_report_result_id,
					current_step_id
				},
				transaction
			);
			await transaction.commit();
			logger.info('[CustomReportResult] service - createWorkCondition finish');
			return created_work_condition_result;
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async updateWorkCondition(params) {
		logger.info('[CustomReportResult] service - updateWorkCondition init');
		let transaction;
		try {
			const { step_id, work_condition_id, workstation_id, custom_report_result_id, custom_report_id } = params;
			await this.#getWorkstationById(workstation_id);
			await this.#getWorkConditionById(work_condition_id);
			await this.#getCustomReportResultById(custom_report_result_id);
			const current_step_id = await this.#setCurrentStepId(step_id, custom_report_id);
			transaction = await this.repository.db.sequelize.transaction();
			await this.#updateWorkConditionResult(params, transaction);
			await this.#updateCurrentStepId(
				{
					custom_report_result_id,
					current_step_id
				},
				transaction
			);
			await transaction.commit();
			const updated_custom_report = await this.#getWorkConditionById(work_condition_id);
			logger.info('[CustomReportResult] service - updateWorkCondition finish');
			return updated_custom_report;
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async createCharacteristic(params) {
		logger.info('[CustomReportResult] service - createCharacteristic init');
		let transaction;
		try {
			const { custom_report_result_id, step_id, workstation_id, custom_report_id } = params;
			await this.#getWorkstationById(workstation_id);
			await this.#checkAlreadyCreatedCharacteristic(workstation_id);
			await this.#getCustomReportResultById(custom_report_result_id);
			const current_step_id = await this.#setCurrentStepId(step_id, custom_report_id);
			transaction = await this.repository.db.sequelize.transaction();
			const created_custom_report = await this.#createCustomReportResultCharacteristic(params, transaction);
			await this.#updateCurrentStepId(
				{
					custom_report_result_id,
					current_step_id
				},
				transaction
			);
			await transaction.commit();
			logger.info('[CustomReportResult] service - createCharacteristic finish');
			return created_custom_report;
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async updateCharacteristic(params) {
		logger.info('[CustomReportResult] service - updateCharacteristic init');
		let transaction;
		try {
			const { step_id, custom_report_id, characteristic_id, workstation_id, custom_report_result_id } = params;
			await this.#getWorkstationById(workstation_id);
			await this.#getCharacteristicById(characteristic_id);
			await this.#getCustomReportResultById(custom_report_result_id);
			const current_step_id = await this.#setCurrentStepId(step_id, custom_report_id);
			transaction = await this.repository.db.sequelize.transaction();
			await this.#updateCustomReportResultCharacteristic(params, transaction);
			await this.#updateCurrentStepId(
				{
					custom_report_result_id,
					current_step_id
				},
				transaction
			);
			await transaction.commit();
			const updated_custom_report = await this.#getCharacteristicById(characteristic_id);
			logger.info('[CustomReportResult] service - updateCharacteristic finish');
			return updated_custom_report;
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async updateComment({ comment, custom_report_result_id }) {
		logger.info('[CustomReportResult] service - updateComment init');
		let transaction;

		try {
			await this.#getCustomReportResultById(custom_report_result_id);

			transaction = await this.repository.db.sequelize.transaction();
			await this.repository.update(
				{
					comment
				},
				{
					where: {
						id: custom_report_result_id
					},
					transaction
				}
			);
			await transaction.commit();

			logger.info('[CustomReportResult] service - updateComment finish');
			return {
				message: 'Comment updated successfully'
			};
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async updateResultScore(custom_report_result_id) {
		logger.info('[CustomReportResult] service - updateResultScore init');
		let transaction;
		try {
			const custom_report_result = await this.#getCustomReportResult(custom_report_result_id);
			const report_result = custom_report_result.get({ plain: true });
			const results = [...report_result.step_key_results, ...report_result.sub_step_key_results];
			const { result, sum_score, worst_score, average_score } =
				this.#custom_report_result_calculator.calculateScores(results);
			transaction = await this.repository.db.sequelize.transaction();
			await this.repository.update(
				{
					result,
					sum_score,
					worst_score,
					average_score
				},
				{
					where: {
						id: custom_report_result_id
					},
					transaction
				}
			);
			await transaction.commit();
			const updated_custom_report = await this.#getCustomReportResultById(custom_report_result_id);
			logger.info('[CustomReportResult] service - updateResultScore finish');
			return updated_custom_report;
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async generatePDF({
		locale,
		user_id,
		file_id,
		report_id,
		company_id,
		organization_id,
		selected_pdf_sections,
		custom_report_result_id,
		original_custom_report_result_id
	}) {
		logger.info('[CustomReportResult] service - generatePDF init');
		let transaction;
		try {
			let result_id = custom_report_result_id;

			if (original_custom_report_result_id) {
				const last_review = await this.custom_report_review_repository.findLastReviewByOriginalResultId(
					original_custom_report_result_id
				);
				result_id = last_review.custom_report_result_id;
			}

			const { custom_report_result, report } = await this.#validateConsolidateAndGeneratePDFMethodsIds({
				file_id,
				user_id,
				report_id,
				company_id,
				organization_id,
				custom_report_result_id: result_id
			});
			const custom_report = await this.#getCustomReportById(custom_report_result.custom_report_id);

			const result = this.#custom_report_result_mapper.setResultInitialValue({
				report_id,
				file_id,
				custom_report_result_id: result_id
			});
			const tools = this.#pdf_sections_mapper.setIncludeTools(selected_pdf_sections);
			const file = await this.#getFileToolsAndHierarchyById(file_id, tools);
			await this.#setResult({
				file,
				result,
				locale,
				report_id,
				custom_report_result,
				selected_pdf_sections
			});
			const prefix = this.#helpers_util.getPrefix(organization_id, company_id);
			const remove_extension = this.#helpers_util.removeExtension(file.generated_name);
			const file_key = this.#pdf_generator.formatFileKey(remove_extension, prefix);
			const target_file = `${report.description}-${remove_extension}.pdf`;

			if (custom_report_result.consolidated && custom_report_result.location) {
				const url = await this.#pdf_generator.generateDownloadURL({
					Bucket: bucket,
					Key: custom_report_result.location,
					fileName: target_file
				});
				transaction = await this.repository.db.sequelize.transaction();
				await this.custom_report_result_actions_log_repository.create(
					{
						user_id,
						action: ACTIONS_ENUM.DOWNLOAD_PDF,
						custom_report_result_id: result_id
					},
					{
						transaction
					}
				);
				await transaction.commit();
				logger.info('[PreliminaryAnalysis] service - getResults finish');
				return { url };
			}

			const actions_logs = await this.#getLastLogForSpecficAction(ACTIONS_ENUM.CONSOLIDATE, result_id);
			const business_info = await this.#getBusinessInformation(company_id, organization_id);
			const config = this.#custom_report_result_mapper.setPDFConfigData({
				locale,
				prefix,
				result,
				file_key,
				actions_logs,
				business_info,
				custom_report
			});
			const key = await this.#pdf_generator.generatedPDF(config);
			const url = await this.#pdf_generator.generateDownloadURL({
				Key: key,
				Bucket: bucket,
				fileName: target_file
			});
			transaction = await this.repository.db.sequelize.transaction();
			await this.custom_report_result_actions_log_repository.create(
				{
					user_id,
					action: ACTIONS_ENUM.DOWNLOAD_PDF,
					custom_report_result_id: result_id
				},
				{
					transaction
				}
			);
			await transaction.commit();
			logger.info('[CustomReportResult] service - generatePDF finish');
			return {
				url
			};
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async consolidateReport(params) {
		logger.info('[CustomReportResult] service - consolidateReport init', { params });
		const {
			locale,
			user_id,
			file_id,
			report_id,
			company_id,
			organization_id,
			selected_pdf_sections,
			custom_report_result_id,
			original_custom_report_result_id
		} = params;
		let transaction;
		try {
			let result_id = custom_report_result_id;

			if (original_custom_report_result_id) {
				const custom_report_review = await this.#getLastReviewThatHasResult(original_custom_report_result_id);
				result_id = custom_report_review.custom_report_result.id;
			}

			const { custom_report_result } = await this.#validateConsolidateAndGeneratePDFMethodsIds({
				file_id,
				user_id,
				report_id,
				company_id,
				organization_id,
				custom_report_result_id: result_id
			});
			const custom_report = await this.#getCustomReportById(custom_report_result.custom_report_id);
			const result = this.#custom_report_result_mapper.setResultInitialValue({
				report_id,
				file_id,
				custom_report_result_id: result_id
			});
			const tools = this.#pdf_sections_mapper.setIncludeTools(selected_pdf_sections);
			const file = await this.#getFileToolsAndHierarchyById(file_id, tools);
			await this.#setResult({
				file,
				result,
				locale,
				report_id,
				custom_report_result,
				selected_pdf_sections
			});
			const result_step = await this.#getResultStep(custom_report.id);
			if (custom_report_result.consolidated) {
				transaction = await this.repository.db.sequelize.transaction();
				await this.#deconsolidateReport({
					user_id,
					transaction,
					step_id: result_step.id,
					custom_report_result_id: result_id
				});
				await transaction.commit();
				const updated_result = await this.#getCustomReportResultById(result_id);
				logger.info('[CustomReportResult] service - consolidateReport finish');
				return updated_result;
			}

			const actions_logs = await this.#getLastLogForSpecficAction(ACTIONS_ENUM.CONSOLIDATE, result_id);
			const business_info = await this.#getBusinessInformation(company_id, organization_id);
			const prefix = this.#helpers_util.getPrefix(organization_id, company_id);
			const remove_extension = this.#helpers_util.removeExtension(file.generated_name);
			const file_key = this.#pdf_generator.formatFileKey(remove_extension, prefix);
			const config = this.#custom_report_result_mapper.setPDFConfigData({
				locale,
				prefix,
				result,
				file_key,
				actions_logs,
				business_info,
				custom_report
			});

			const key = await this.#pdf_generator.generatedPDF(config);

			transaction = await this.repository.db.sequelize.transaction();

			await this.#consolidate({
				key,
				result,
				user_id,
				result_step,
				transaction,
				custom_report_result_id: result_id
			});

			await transaction.commit();
			const updated_result = await this.#getCustomReportResultById(result_id);

			logger.info('[CustomReportResult] service - consolidateReport success');
			return updated_result;
		} catch (error) {
			logger.error('[CustomReportResult] service - consolidateReport error', { error });
			transaction && (await transaction?.rollback());
			throw error;
		} finally {
			logger.info('[CustomReportResult] service - consolidateReport finish');
		}
	}

	async show(params) {
		logger.info('[CustomReportResult] service - show init', { params });
		const {
			custom_report_result_id,
			file_id,
			report_id,
			report_name,
			selected_pdf_sections,
			original_custom_report_result_id,
			version
		} = params;

		let report_result_id = custom_report_result_id;
		let review_comment = null;

		if (report_name === CUSTOM_REPORT_NAMES.JDS_D86) {
			const custom_report_review = await this.#getCustomReportReview(original_custom_report_result_id, version);
			report_result_id = custom_report_review.custom_report_result_id;
			review_comment = custom_report_review.comment;
		}

		const { custom_report_result } = await this.#validateShowMethodIds({
			file_id,
			custom_report_result_id: report_result_id
		});

		if (review_comment) {
			custom_report_result.comment = review_comment;
		}

		const result = this.#custom_report_result_mapper.setResultInitialValue({
			file_id,
			report_id,
			custom_report_result_id: report_result_id
		});
		const tools = this.#pdf_sections_mapper.setIncludeTools(selected_pdf_sections);
		const file = await this.#getFileToolsAndHierarchyById(file_id, tools);

		const result_params = {
			file,
			result,
			report_id,
			custom_report_result,
			selected_pdf_sections
		};

		await this.#setResult(result_params);

		logger.info('[CustomReportResult] service - show finish');
		return result;
	}

	async delete({ custom_report_result_id }) {
		logger.info('[CustomReportResult] service - delete init');
		let transaction;

		try {
			await this.#getCustomReportResultById(custom_report_result_id);

			const custom_report_reviews = await this.custom_report_review_repository.findAll({
				attributes: ['id'],
				where: {
					original_custom_report_result_id: custom_report_result_id
				},
				include: [
					{
						association: 'custom_report_result',
						attributes: ['id']
					}
				]
			});

			transaction = await this.repository.db.sequelize.transaction();

			await this.#deleteCustomReportResults(custom_report_result_id, transaction);

			if (this.#customReportResultHasReviews(custom_report_reviews)) {
				const custom_report_review_ids = custom_report_reviews
					.map((custom_report_review) => custom_report_review.id)
					.filter((id) => id);
				const custom_report_result_ids = custom_report_reviews
					.map((custom_report_review) => custom_report_review.custom_report_result?.id ?? '')
					.filter((id) => id);

				await this.#deleteCustomReportReviews(custom_report_review_ids, transaction);

				await this.#deleteCustomReportResults(custom_report_result_ids, transaction);
			}

			await transaction.commit();

			const deleted_custom_report_result = await this.repository.findByPk(custom_report_result_id, {
				paranoid: false
			});
			logger.info('[CustomReportResult] service - delete finish');
			return deleted_custom_report_result;
		} catch (error) {
			logger.error('[CustomReportResult] service - delete error', { error });
			await transaction?.rollback();
			throw error;
		}
	}

	async findLastStepKeyResult({ original_custom_report_result_id, step_key_id }) {
		logger.info('[CustomReportResult] service - findLastStepKeyResult init');

		const last_review = await this.#getLastReviewThatHasResult(original_custom_report_result_id);

		if (!last_review) {
			throw new AppError(CUSTOM_REPORT_REVIEW.NOT_FOUND);
		}

		const custom_report_result_id = last_review.custom_report_result_id;
		const last_result = await this.#getCustomReportResultById(custom_report_result_id);

		const { custom_report_step_key_result, custom_report_sub_step_keys_results } =
			await this.#getStepKeyAndSubStepKeysResultsByStepKeyId(custom_report_result_id, step_key_id);

		const additional_item_results = await this.#getAdditionalItemsResultByStepKeyId({
			step_key_id,
			custom_report_result_id
		});

		let step_key_results = {};
		let sub_step_keys_results = [];

		if (!this.#stepKeyHasSubStepKey(custom_report_sub_step_keys_results)) {
			const action_plan_response = await this.#getActionPlanByFileIdAndStepKey(last_result, step_key_id);

			const additional_items = additional_item_results.map((additional_item_result) => {
				const { id, result } = additional_item_result;

				const formatted_additional_items = this.#formatAdditionalItems(additional_item_result);

				return {
					id,
					result,
					values: formatted_additional_items
				};
			});

			let action_plan = {};

			if (action_plan_response) {
				action_plan = {
					id: action_plan_response.id,
					title: action_plan_response.title,
					deadline: action_plan_response.deadline,
					description: action_plan_response.description,
					responsible_name: action_plan_response.responsible_user.name,
					tasks: action_plan_response.action_plan_task
				};
			}

			step_key_results = {
				...custom_report_step_key_result,
				additional_items,
				action_plan
			};
		}

		if (this.#stepKeyHasSubStepKey(custom_report_sub_step_keys_results)) {
			const previous_sub_step_key_result = this.#setSubStepKeyPreviousResult(custom_report_sub_step_keys_results);
			sub_step_keys_results = previous_sub_step_key_result;
		}

		const response = {
			custom_report_review: {
				id: last_review.id,
				name: last_review.name,
				created_at: last_review.created_at
			},
			step_key_results,
			sub_step_keys_results
		};

		logger.info('[CustomReportResult] service - findLastStepKeyResult finish');
		return response;
	}

	async getCountChecklist(params) {
		logger.info('[CustomReportResult] service - getCountChecklist init', { params });
		const {
			custom_report_id,
			created_at_start,
			organization_id,
			created_at_end,
			workstation_id,
			companies_ids,
			granularity,
			fiscal_year,
			company_id,
			sector_id,
			line_id
		} = params;

		const custom_report = await this.#getCustomReportById(custom_report_id);
		const has_reviews = this.#customReportHasReviews(custom_report.name);
		const year_start_month = fiscal_year ? DASHBOARD_INITIAL_MONTH_FISCAL_YEAR : DASHBOARD_JANUARY_INDEX_MONTH;
		let user_id = '';

		if (StorageContext.getStore()?.environment === 'sandbox') {
			user_id = StorageContext.getStore().user_id;
		}

		const [custom_report_results, error] = await this.repository.countCreatedReportResultsByPeriod({
			period_group: granularity,
			year_start_month,
			custom_report_id,
			created_at_start,
			organization_id,
			created_at_end,
			workstation_id,
			companies_ids,
			has_reviews,
			company_id,
			sector_id,
			line_id,
			user_id
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		const filled_results = this.#custom_report_checklist_mapper.fillChecklistResult({
			start_date: created_at_start,
			end_date: created_at_end,
			custom_report_results,
			period: granularity,
			year_start_month
		});

		const formatted_results = this.#custom_report_checklist_mapper.formatResult({
			custom_report_results: filled_results,
			name: custom_report.name,
			period: granularity,
			fiscal_year
		});

		logger.info('[CustomReportResult] service - getCountChecklist finish');
		return formatted_results;
	}

	#customReportHasReviews(custom_report_name) {
		return custom_report_name === CUSTOM_REPORT_NAMES.JDS_D86;
	}

	async #getBusinessInformation(company_id, organization_id) {
		let business_informations = await this.business_information_repository.findOne({
			where: {
				company_id,
				organization_id,
				is_active: true
			},
			include: [
				{
					association: 'organization',
					attributes: ['id', 'name', 'url_logo']
				}
			],
			attributes: ['company_name', 'fantasy_name', 'cnpj', 'address', 'city', 'state', 'zipcode', 'district']
		});
		if (!business_informations) {
			business_informations = {
				organization: {
					name: 'Kinebot',
					url_logo: null
				},
				company_name: 'Kinebot',
				fantasy_name: 'Kinebot - Tecnologia da Informacao LTDA',
				cnpj: '41.350.359/0001-76',
				address: 'Marginal Comendador Franco, 1341',
				city: 'Curitiba',
				state: 'Paraná',
				zipcode: '80215-090',
				district: 'Jardim Botânico'
			};
		}
		return business_informations;
	}

	async #getLastReviewThatHasResult(original_custom_report_result_id) {
		const last_review = await this.custom_report_review_repository.findOne({
			attributes: ['id', 'version', 'name', 'created_at', 'custom_report_result_id'],
			where: {
				original_custom_report_result_id
			},
			include: [
				{
					association: 'custom_report_result',
					attributes: ['id', 'result']
				}
			],
			limit: 1,
			offset: 1,
			order: [['version', 'DESC']]
		});

		return last_review;
	}

	async #validateConsolidateAndGeneratePDFMethodsIds({
		file_id,
		user_id,
		report_id,
		company_id,
		organization_id,
		custom_report_result_id
	}) {
		const user = await this.#getUserById(user_id);
		const file = await this.#getFileById(file_id);
		const company = await this.#getCompanyById(company_id);
		const report = await this.#getCustomReportById(report_id);
		const organization = await this.#getOrganizationById(organization_id);
		const custom_report_result = await this.#getCustomReportResultById(custom_report_result_id, {
			include: [
				{
					association: 'current_step',
					attributes: ['id', 'name', 'description', 'sequence']
				},
				{
					association: 'evaluator',
					attributes: ['id', 'name']
				},
				{
					association: 'created_by_user',
					attributes: ['id', 'name']
				}
			]
		});

		return {
			user,
			file,
			company,
			report,
			organization,
			custom_report_result
		};
	}

	async #validateShowMethodIds({ file_id, custom_report_result_id }) {
		const file = await this.#getFileById(file_id);
		const custom_report_result = await this.#getCustomReportResultById(custom_report_result_id, {
			include: [
				{
					association: 'current_step',
					attributes: ['id', 'name', 'description', 'sequence']
				},
				{
					association: 'evaluator',
					attributes: ['id', 'name']
				},
				{
					association: 'created_by_user',
					attributes: ['id', 'name']
				},
				{
					association: 'activity',
					attributes: ['id', 'name']
				}
			]
		});

		return {
			file,
			custom_report_result
		};
	}

	async #getFileToolsAndHierarchyById(file_id, tools) {
		const file = await this.file_repository.findByPk(file_id, {
			include: [this.#file_joiner.includeAllHierarchy(), ...tools]
		});
		if (!file) {
			throw new AppError(FILE.NOT_FOUND);
		}
		return file;
	}

	async #setResult({ file, result, locale, report_id, custom_report_result, selected_pdf_sections = [] }) {
		const workstation_id = file.workstation_id;

		const report_info_steps_step_keys_and_sub_step_keys =
			await this.#getReportInfoStepsStepKeysAndSubStepKeys(report_id);

		const { step_keys_results, sub_step_keys_results } = await this.#getStepKeysAndSubStepKeysResults(
			custom_report_result.id,
			selected_pdf_sections
		);

		const additional_items_results_by_step_key =
			await this.#setAdditionalItemsResultsByStepKey(custom_report_result);

		if (this.#pdf_sections_mapper.shouldShowWorkConditionSection(selected_pdf_sections)) {
			await this.#setResultWorkCondition(workstation_id, result);
		}

		if (this.#pdf_sections_mapper.shouldShowCharacteristicSection(selected_pdf_sections)) {
			await this.#setResultCharacteristic(workstation_id, result);
		}

		if (this.#pdf_sections_mapper.shouldShowActionPlansSection(selected_pdf_sections)) {
			await this.#setResultActionPlans({ step_keys_results, sub_step_keys_results, result, locale });
		}

		const show_not_evaluated = this.#pdf_sections_mapper.shouldShowNotEvaluatedStepKeys(selected_pdf_sections);

		const steps = this.#setStepsResults({
			result,
			step_keys_results,
			show_not_evaluated,
			sub_step_keys_results,
			additional_items_results_by_step_key,
			report_info_steps_step_keys_and_sub_step_keys
		});

		this.#setInformationsResult({
			file,
			steps,
			result,
			custom_report_result,
			report_info_steps_step_keys_and_sub_step_keys
		});

		await this.#setTools(result, file.get({ plain: true }), steps);
	}

	async #setAdditionalItemsResultsByStepKey(custom_report_result) {
		const additional_items_results = await this.#getAdditionalItemsResult(custom_report_result);

		const additional_items_results_by_step_key = additional_items_results.reduce(
			(result_by_step_key, current_additional_item_result) => {
				const { id, result, value_1, value_2, value_3, value_4, option_1, option_2, step_key_additional_item } =
					current_additional_item_result;
				const { custom_report_step_key_id, additional_item } = step_key_additional_item;
				const titles = this.#setTitlesAndUnits(additional_item);
				const previous_results = result_by_step_key[custom_report_step_key_id]?.results ?? [];
				const updated_results = [...previous_results];
				const mapped_values = {
					value_1,
					value_2,
					value_3,
					value_4,
					value_5: option_1?.description,
					value_6: option_2?.description
				};
				const additional_item_titles = ADDITIONAL_ITEMS_NAMES_TO_TITLES_AND_VALUES_MAPPER[additional_item.name];
				const new_result = {
					id,
					result,
					value_1: mapped_values[additional_item_titles['title_1']],
					value_2: mapped_values[additional_item_titles['title_2']],
					value_3: mapped_values[additional_item_titles['title_3']],
					value_4: mapped_values[additional_item_titles['title_4']],
					value_5: mapped_values[additional_item_titles['title_5']],
					value_6: mapped_values[additional_item_titles['title_6']]
				};
				updated_results.push(new_result);
				return {
					...result_by_step_key,
					[custom_report_step_key_id]: {
						titles,
						results: updated_results
					}
				};
			},
			{}
		);
		return additional_items_results_by_step_key;
	}

	#setTitlesAndUnits(additional_item) {
		const { title_1, unit_1, title_2, unit_2, title_3, unit_3, title_4, unit_4, title_5, unit_5, title_6, unit_6 } =
			additional_item;
		const titles = [
			{
				title: title_1,
				unit: unit_1
			},
			{
				title: title_2,
				unit: unit_2
			},
			{
				title: title_3,
				unit: unit_3
			},
			{
				title: title_4,
				unit: unit_4
			},
			{
				title: title_5,
				unit: unit_5
			},
			{
				title: title_6,
				unit: unit_6
			}
		].filter(({ title }) => !!title);

		return titles;
	}

	async #getAdditionalItemsResult(custom_report_result) {
		const additional_items_results_response =
			await this.custom_report_step_key_additional_item_result_repository.findAllByForeignKey({
				where: {
					custom_report_result_id: custom_report_result.id
				},
				attributes: [
					'id',
					'result',
					'value_1',
					'value_2',
					'value_3',
					'value_4',
					'custom_report_result_id',
					'additional_item_option_id_1',
					'additional_item_option_id_2',
					'custom_report_step_key_additional_item_id'
				],
				include: [
					{
						association: 'option_1',
						attributes: ['id', 'description']
					},
					{
						association: 'option_2',
						attributes: ['id', 'description']
					},
					{
						association: 'step_key_additional_item',
						attributes: ['additional_item_id', 'custom_report_step_key_id'],
						include: [
							{
								association: 'additional_item'
							}
						]
					}
				]
			});

		const additional_items_results = additional_items_results_response?.map((additional_items_result) =>
			additional_items_result.get({ plain: true })
		);
		return additional_items_results;
	}

	async #getAdditionalItemsResultByStepKeyId({ step_key_id, custom_report_result_id }) {
		const additional_items_results_response =
			await this.custom_report_step_key_additional_item_result_repository.findAll({
				where: {
					custom_report_result_id: custom_report_result_id
				},
				attributes: ['id', 'result', 'value_1', 'value_2', 'value_3', 'value_4'],
				where: {
					custom_report_result_id
				},
				include: [
					{
						association: 'option_1',
						attributes: ['id', 'description']
					},
					{
						association: 'option_2',
						attributes: ['id', 'description']
					},
					{
						association: 'step_key_additional_item',
						attributes: ['additional_item_id', 'custom_report_step_key_id'],
						where: {
							custom_report_step_key_id: step_key_id
						},
						include: [
							{
								association: 'additional_item'
							}
						]
					}
				]
			});

		const additional_items_results = additional_items_results_response?.map((additional_items_result) =>
			additional_items_result.get({ plain: true })
		);
		return additional_items_results;
	}

	#setStepsResults({
		result,
		step_keys_results,
		show_not_evaluated,
		sub_step_keys_results,
		additional_items_results_by_step_key,
		report_info_steps_step_keys_and_sub_step_keys
	}) {
		const steps = this.#custom_report_step_key_result_mapper.mapStepsResults({
			step_keys_results,
			show_not_evaluated,
			sub_step_keys_results,
			additional_items_results_by_step_key,
			report_info_steps_step_keys_and_sub_step_keys
		});
		result.steps = steps;
		result.total_steps = result.steps.length;
		return steps;
	}

	#setInformationsResult({
		file,
		steps,
		result,
		custom_report_result,
		report_info_steps_step_keys_and_sub_step_keys
	}) {
		const informations = this.#custom_report_result_mapper.mapInformationsResults({
			file,
			steps,
			custom_report_result
		});
		result.informations = informations;
		result.comment = custom_report_result.comment;
		result.consolidated = custom_report_result.consolidated;
		result.current_step = custom_report_result.current_step.sequence;
		result.current_step_name = custom_report_result.current_step.name;
		result.id = report_info_steps_step_keys_and_sub_step_keys.id;
		result.name = report_info_steps_step_keys_and_sub_step_keys.name;
		result.acronym = report_info_steps_step_keys_and_sub_step_keys.acronym;
		result.is_completed = !!custom_report_result.result && custom_report_result.result > 0;
		result.description = report_info_steps_step_keys_and_sub_step_keys.description;
	}

	async #setTools(result, file, steps) {
		result.reba = null;
		result.niosh = null;
		result.kim_pp = null;
		result.kim_mho = null;
		result.angle_time = null;
		result.strain_index = null;
		result.liberty_mutual = null;

		if (file.angle_time) {
			result.angle_time = file.angle_time;
		}
		if (file.reba) {
			const is_ewa_report = result.name === CUSTOM_REPORT_NAMES.EWA;
			const reba = is_ewa_report ? await this.#setEwaRebaData(file) : await this.#setRebaData(file);
			result.reba = reba;
		}
		if (file.niosh) {
			const niosh_mapper = new NioshDataToEWAPDFMapper();
			const { conclusion, risk_description, risk_score } = niosh_mapper.normalizeInputs(file.niosh);
			result.niosh = {
				...file.niosh,
				conclusion,
				risk_score,
				risk_description
			};
		}
		if (file.strain_index) {
			const strain_index_mapper = new StrainIndexDataToEWAPDFMapper();
			const strain_index = strain_index_mapper.normalizeInputs(file.strain_index);
			result.strain_index = strain_index;
		}
		if (file.kim_mho) {
			const kim_mho_data_mapper = new KimManualHandlingOperationsDataToEWAPDFMapper();
			const kim_mho = kim_mho_data_mapper.normalizeInputs(file.kim_mho);
			result.kim_mho = kim_mho;
		}
		if (file.kim_push_pull) {
			const kim_pp_data_mapper = new KimPushPullDataToEWAPDFMapper();
			const kim_push_pull = kim_pp_data_mapper.normalizeInputs(file.kim_push_pull);
			result.kim_pp = kim_push_pull;
		}
		if (file.liberty_mutual) {
			const liberty_mutual_data_mapper = new LibertyMutualDataToEWAPDFMapper();
			const liberty_mutual = file.liberty_mutual.report_inputs.map((report_input) =>
				liberty_mutual_data_mapper.normalizeInputs({
					...report_input,
					system_of_units: file.liberty_mutual.system_of_units
				})
			);
			result.liberty_mutual = liberty_mutual;
		}
		if (file.back_compressive_force_estimation) {
			const back_compressive_force_estimation_data_mapper = new BackCompressiveForceEstimationToPDFMapper();
			result.back_compressive_force_estimation = back_compressive_force_estimation_data_mapper.normalizeInputs(
				file.back_compressive_force_estimation
			);
		}

		result.tools_to_show = this.#custom_report_result_mapper.mapErgonomicToolsToShowOnResult({ result, steps });
	}

	async #setRebaData(file) {
		const ewa_d86_data_mapper = new EWAD86DataToPDFMapper();
		const prefix = this.#helpers_util.getPrefix(file.organization_id, file.company_id);
		const file_name = this.#helpers_util.removeExtension(file.generated_name);
		return {
			...file.reba,
			data: await ewa_d86_data_mapper.normalizeInputs({
				prefix,
				file_name
			})
		};
	}

	async #setEwaRebaData(file) {
		const reba_data_mapper = new RebaDataToEWAPDFMapper();
		const prefix = this.#helpers_util.getPrefix(file.organization_id, file.company_id);
		const file_name = this.#helpers_util.removeExtension(file.generated_name);
		return {
			...file.reba,
			data: await reba_data_mapper.normalizeInputs({
				prefix,
				file_name
			})
		};
	}

	async #getLastLogForSpecficAction(action, custom_report_result_id) {
		return await this.custom_report_result_actions_log_repository.findAll({
			limit: 1,
			order: [['created_at', 'DESC']],
			attributes: ['id', 'created_at'],
			where: {
				action,
				custom_report_result_id
			},
			include: [
				{
					association: 'user',
					attributes: ['id', 'name']
				}
			]
		});
	}

	async #getReportInfoStepsStepKeysAndSubStepKeys(report_id) {
		const custom_report = await this.custom_report_repository.findByPk(report_id, {
			attributes: ['id', 'name', 'description', 'acronym'],
			include: [
				{
					association: 'step',
					attributes: ['id', 'name', 'description', 'sequence'],
					include: [
						{
							association: 'step_key',
							attributes: ['id', 'name', 'description', 'sequence', 'custom_report_step_id'],
							include: [
								{
									association: 'sub_step_keys',
									attributes: ['id', 'name', 'description', 'sequence', 'custom_report_step_key_id']
								},
								{
									association: 'ergonomic_tool',
									attributes: ['id', 'name', 'description', 'title', 'subtitle']
								}
							]
						}
					]
				}
			],
			order: [
				['step', 'sequence', 'ASC'],
				['step', 'step_key', 'sequence', 'ASC']
			]
		});
		if (!custom_report) {
			throw new AppError(CUSTOM_REPORT.NOT_FOUND);
		}
		return custom_report;
	}

	async #getStepKeysAndSubStepKeysResults(custom_report_result_id, selected_pdf_sections) {
		const step_key_and_sub_step_key_result_include =
			this.#custom_report_step_key_result_joiner.setStepKeyAndSubStepKeyResultInclude();
		const ewa_step_key_and_sub_step_key_result_where_condition =
			this.#custom_report_step_key_result_joiner.setStepKeyAndSubStepKeyResultWhereCondition(
				custom_report_result_id,
				selected_pdf_sections
			);
		const step_keys_results_promise = this.custom_report_step_key_result_repository.findAll({
			...ewa_step_key_and_sub_step_key_result_where_condition,
			attributes: ['id', 'score', 'result', 'custom_report_result_id', 'custom_report_step_key_id'],
			include: [...step_key_and_sub_step_key_result_include]
		});
		const sub_step_keys_results_promise = this.custom_report_sub_step_key_result_repository.findAll({
			...ewa_step_key_and_sub_step_key_result_where_condition,
			attributes: ['id', 'score', 'result', 'custom_report_result_id', 'custom_report_sub_step_key_id'],
			include: [...step_key_and_sub_step_key_result_include]
		});
		const [step_keys_results, sub_step_keys_results] = await Promise.allSettled([
			step_keys_results_promise,
			sub_step_keys_results_promise
		]);
		if (step_keys_results.status === 'rejected' || sub_step_keys_results.status === 'rejected') {
			throw new AppError(CUSTOM_REPORT_STEP_KEY_RESULT.NOT_FOUND);
		}
		return {
			step_keys_results: step_keys_results.value,
			sub_step_keys_results: sub_step_keys_results.value
		};
	}

	async #setResultWorkCondition(workstation_id, result) {
		const work_conditions = await this.#getWorkConditionForResultByWorkstationId(workstation_id);

		if (!work_conditions) {
			result.work_conditions = undefined;
			return;
		}

		const { hours, minutes } = this.#format_time.convertSecondsToHoursAndMinutes(work_conditions.work_schedule);
		const mapped_work_conditions = {
			work_hours: hours,
			work_minutes: minutes,
			id: work_conditions.id,
			place_description: work_conditions.place_description,
			expected_task_description: work_conditions.expected_task_description,
			performed_task_description: work_conditions.performed_task_description
		};
		result.work_conditions = mapped_work_conditions;
	}

	async #getWorkConditionForResultByWorkstationId(workstation_id) {
		const work_condition = await this.work_condition_result_repository.findOne({
			where: {
				workstation_id
			},
			attributes: [
				'id',
				'work_schedule',
				'place_description',
				'expected_task_description',
				'performed_task_description'
			]
		});

		return work_condition;
	}

	async #setResultCharacteristic(workstation_id, result) {
		const characteristic = await this.#getCharacteristicForResultByWorkstationId(workstation_id);

		if (!characteristic) {
			result.characteristics = undefined;
			return;
		}

		const [working_population_men, working_population_women, working_population_others] =
			this.#working_population_converter.setWorkingPopulationAbsoluteValues(characteristic);
		const mapped_characteristic = {
			id: characteristic.id,
			working_population_men,
			working_population_women,
			working_population_others,
			worker_self_evaluation: characteristic.worker_self_evaluation,
			total_working_population: characteristic.total_working_population,
			worker_self_evaluation_id: characteristic.worker_self_evaluation.id,
			particularities_description: characteristic?.particularities_description,
			working_population_men_percentage: characteristic.working_population_men,
			working_population_women_percentage: characteristic.working_population_women,
			working_population_others_percentage: characteristic.working_population_others,
			worker_verbalization_description: characteristic?.worker_verbalization_description
		};
		result.characteristics = mapped_characteristic;
	}

	async #getCharacteristicForResultByWorkstationId(workstation_id) {
		const custom_report_result_characteristic = await this.characteristic_result_repository.findOne({
			where: {
				workstation_id
			},
			attributes: [
				'id',
				'working_population_men',
				'working_population_women',
				'total_working_population',
				'working_population_others',
				'particularities_description',
				'worker_verbalization_description'
			],
			include: [
				{
					association: 'worker_self_evaluation',
					attributes: ['id', 'name', 'description']
				}
			]
		});

		return custom_report_result_characteristic;
	}

	async #setResultActionPlans({ step_keys_results, sub_step_keys_results, result, locale }) {
		const step_key_ids = step_keys_results
			.filter(({ score }) => score && score > 0)
			.map(({ custom_report_step_key_id }) => custom_report_step_key_id);
		const sub_step_key_ids = sub_step_keys_results
			.filter(({ score }) => score && score > 0)
			.map(({ custom_report_sub_step_key_id }) => custom_report_sub_step_key_id);
		const step_keys_and_sub_step_keys_action_plans_and_tasks = await this.#getActionsPlansForStepKeysAndSubStepKeys(
			{
				result,
				step_key_ids,
				sub_step_key_ids
			}
		);
		const action_plan_mapper = new ActionPlanToCustomReportResultMapper(
			step_keys_and_sub_step_keys_action_plans_and_tasks,
			locale
		);
		result.action_plans = action_plan_mapper.mapActionPlans();
	}

	async #getActionsPlansForStepKeysAndSubStepKeys({ result, step_key_ids, sub_step_key_ids }) {
		const [action_plans, error] = await this.action_plan_repository.findAll(
			{
				file_id: result.file_id
			},
			{
				include: [
					{
						association: 'action_plan_tasks',
						required: false,
						attribute: ['id', 'title', 'is_completed', 'due_date'],
						include: {
							association: 'responsible_user',
							required: false,
							attribute: ['name']
						}
					},
					{
						association: 'responsible_user',
						attributes: ['name'],
						where: {
							is_active: true
						}
					},
					{
						association: 'action_plan_origin',
						required: true,
						where: {
							[Op.or]: [
								{
									table_name: 'custom_report_step_keys',
									column_id: step_key_ids
								},
								{
									table_name: 'custom_report_sub_step_keys',
									column_id: sub_step_key_ids
								}
							]
						}
					}
				]
			}
		);

		if (error) {
			throw new AppError(CUSTOM_REPORT_RESULT.NOT_FOUND);
		}

		const enriched_action_plans = await this.#enrichActionPlansWithCorrectScores(action_plans, result.file_id);

		return enriched_action_plans;
	}

	async #enrichActionPlansWithCorrectScores(action_plans, file_id) {
		const plain_action_plans = action_plans.map((action_plan) => action_plan.get({ plain: true }));

		const sub_step_key_ids = [];
		const step_key_ids = [];

		plain_action_plans.forEach((action_plan) => {
			const origin = action_plan.action_plan_origin;
			if (origin) {
				if (origin.table_name === 'custom_report_sub_step_keys') {
					sub_step_key_ids.push(origin.column_id);
				} else if (origin.table_name === 'custom_report_step_keys') {
					step_key_ids.push(origin.column_id);
				}
			}
		});

		const score_maps = await this.#fetchScoresInBatch(sub_step_key_ids, step_key_ids, file_id);

		return plain_action_plans.map((action_plan) => {
			const origin = action_plan.action_plan_origin;
			if (origin) {
				const score_key = `${origin.table_name}_${origin.column_id}`;
				const correct_score = score_maps[score_key];
				if (correct_score !== undefined) {
					action_plan.score = correct_score;
				}
			}
			return action_plan;
		});
	}

	async #fetchScoresInBatch(sub_step_key_ids, step_key_ids, file_id) {
		const score_maps = {};

		try {
			if (sub_step_key_ids.length > 0) {
				const sub_step_results = await this.custom_report_sub_step_key_result_repository.findAll({
					where: {
						custom_report_sub_step_key_id: sub_step_key_ids
					},
					include: [
						{
							association: 'custom_report_result',
							where: {
								file_id: file_id
							},
							required: true
						}
					]
				});

				sub_step_results.forEach((result) => {
					const key = `custom_report_sub_step_keys_${result.custom_report_sub_step_key_id}`;
					score_maps[key] = result.score || 0;
				});
			}

			if (step_key_ids.length > 0) {
				const step_results = await this.custom_report_step_key_result_repository.findAll({
					where: {
						custom_report_step_key_id: step_key_ids
					},
					include: [
						{
							association: 'custom_report_result',
							where: {
								file_id: file_id
							},
							required: true
						}
					]
				});

				step_results.forEach((result) => {
					const key = `custom_report_step_keys_${result.custom_report_step_key_id}`;
					score_maps[key] = result.score || 0;
				});
			}
		} catch (error) {
			logger.error(`[CustomReportResult] service - Error fetching scores in batch: ${error.message}`);
		}

		return score_maps;
	}

	async #consolidate({ key, result_step, transaction, user_id, custom_report_result_id }) {
		await this.repository.update(
			{
				location: key,
				consolidated: true,
				current_step_id: result_step.id
			},
			{
				where: {
					id: custom_report_result_id
				},
				transaction
			}
		);
		await this.custom_report_result_actions_log_repository.create(
			{
				user_id,
				action: ACTIONS_ENUM.CONSOLIDATE,
				custom_report_result_id
			},
			{
				transaction
			}
		);
	}

	async #deconsolidateReport({ step_id, transaction, user_id, custom_report_result_id }) {
		await this.repository.update(
			{
				location: null,
				consolidated: false,
				current_step_id: step_id
			},
			{
				where: {
					id: custom_report_result_id
				},
				transaction
			}
		);
		await this.custom_report_result_actions_log_repository.create(
			{
				user_id,
				custom_report_result_id,
				action: ACTIONS_ENUM.DECONSOLIDATE
			},
			{
				transaction
			}
		);
	}

	async #getResultStep(custom_report_id) {
		const result_step = await this.custom_report_step_repository.findOne({
			where: {
				name: CUSTOM_REPORT_DEFAULT_STEPS_ENUM.RESULT,
				custom_report_id
			}
		});
		if (!result_step) {
			throw new AppError(CUSTOM_REPORT_STEP.NOT_FOUND);
		}
		return result_step;
	}

	async #checkAlreadyCreatedCharacteristic(workstation_id) {
		const custom_report_result_characteristic = await this.characteristic_result_repository.findOne({
			where: {
				workstation_id
			}
		});
		if (custom_report_result_characteristic) {
			throw new AppError(CHARACTERISTIC_RESULT.ALREADY_CREATED);
		}
		return custom_report_result_characteristic;
	}

	async #getCharacteristicById(characteristic_id) {
		const custom_report_result_characteristic =
			await this.characteristic_result_repository.findByPk(characteristic_id);
		if (!custom_report_result_characteristic) {
			throw new AppError(CHARACTERISTIC_RESULT.NOT_FOUND);
		}
		return custom_report_result_characteristic;
	}

	async #createCustomReportResultCharacteristic(
		{
			workstation_id,
			working_population_men,
			custom_report_result_id,
			working_population_women,
			total_working_population,
			worker_self_evaluation_id,
			working_population_others,
			particularities_description,
			worker_verbalization_description
		},
		transaction
	) {
		const [
			working_population_men_percentage,
			working_population_women_percentage,
			working_population_others_percentage
		] = this.#working_population_converter.setWorkingPopulationsPercentage({
			total_working_population,
			working_population_women,
			working_population_men,
			working_population_others
		});
		return await this.characteristic_result_repository.create(
			{
				workstation_id,
				custom_report_result_id,
				total_working_population,
				worker_self_evaluation_id,
				particularities_description,
				worker_verbalization_description,
				working_population_men: working_population_men_percentage,
				working_population_women: working_population_women_percentage,
				working_population_others: working_population_others_percentage
			},
			{ transaction }
		);
	}

	async #updateCustomReportResultCharacteristic(
		{
			workstation_id,
			characteristic_id,
			working_population_men,
			custom_report_result_id,
			working_population_women,
			total_working_population,
			worker_self_evaluation_id,
			working_population_others,
			particularities_description,
			worker_verbalization_description
		},
		transaction
	) {
		const [
			working_population_men_percentage,
			working_population_women_percentage,
			working_population_others_percentage
		] = this.#working_population_converter.setWorkingPopulationsPercentage({
			working_population_men,
			total_working_population,
			working_population_women,
			working_population_others
		});
		return await this.characteristic_result_repository.update(
			{
				workstation_id,
				custom_report_result_id,
				total_working_population,
				worker_self_evaluation_id,
				particularities_description,
				worker_verbalization_description,
				working_population_men: working_population_men_percentage,
				working_population_women: working_population_women_percentage,
				working_population_others: working_population_others_percentage
			},
			{
				where: {
					id: characteristic_id
				},
				transaction
			}
		);
	}

	async #checkAlreadyCreatedWorkCondition(workstation_id) {
		const work_condition = await this.work_condition_result_repository.findOne({
			where: {
				workstation_id
			}
		});
		if (work_condition) {
			throw new AppError(WORK_CONDITION_RESULT.ALREADY_CREATED);
		}
		return work_condition;
	}

	async #getWorkConditionById(work_condition_id) {
		const work_condition = await this.work_condition_result_repository.findByPk(work_condition_id);
		if (!work_condition) {
			throw new AppError(WORK_CONDITION_RESULT.NOT_FOUND);
		}
		return work_condition;
	}

	async #getCustomReportResultById(custom_report_result_id, options) {
		const custom_report_result = await this.repository.findByPk(custom_report_result_id, options);
		if (!custom_report_result) {
			throw new AppError(CUSTOM_REPORT_RESULT.NOT_FOUND);
		}
		return custom_report_result;
	}

	async #createWorkConditionResult(
		{
			work_hours,
			work_minutes,
			workstation_id,
			place_description,
			expected_task_description,
			performed_task_description
		},
		transaction
	) {
		const seconds = this.#format_time.convertHoursAndMinutesToSeconds({
			hours: work_hours ?? 0,
			minutes: work_minutes ?? 0
		});
		return await this.work_condition_result_repository.create(
			{
				workstation_id,
				place_description,
				work_schedule: seconds,
				expected_task_description,
				performed_task_description
			},
			{
				transaction
			}
		);
	}

	async #updateWorkConditionResult(
		{
			work_hours,
			work_minutes,
			workstation_id,
			work_condition_id,
			place_description,
			expected_task_description,
			performed_task_description
		},
		transaction
	) {
		const seconds = this.#format_time.convertHoursAndMinutesToSeconds({
			hours: work_hours ?? 0,
			minutes: work_minutes ?? 0
		});
		return this.work_condition_result_repository.update(
			{
				workstation_id,
				place_description,
				work_schedule: seconds,
				expected_task_description,
				performed_task_description
			},
			{
				where: {
					id: work_condition_id
				},
				transaction
			}
		);
	}

	async #checkAlreadyCreatedReportResult({ file_id, custom_report_id }) {
		const custom_report_result = await this.repository.findOne({
			where: {
				file_id
			},
			include: [
				{
					association: 'custom_report',
					where: {
						id: custom_report_id
					}
				}
			]
		});

		if (custom_report_result) {
			throw new AppError(CUSTOM_REPORT_RESULT.ALREADY_CREATED);
		}
	}

	async #getCustomReportResult(custom_report_result_id) {
		const custom_report_result = await this.repository.findOne({
			where: {
				id: custom_report_result_id
			},
			include: [
				{
					association: 'step_key_results'
				},
				{
					association: 'sub_step_key_results'
				}
			]
		});
		if (!custom_report_result) {
			throw new AppError(CUSTOM_REPORT_RESULT.NOT_FOUND);
		}
		return custom_report_result;
	}

	async #getUserById(user_id) {
		const user = await this.user_repository.findByPk(user_id);
		if (!user) {
			throw new AppError(USER.NOT_FOUND);
		}
		return user;
	}

	async #getCompanyById(company_id) {
		const company = await this.company_repository.findByPk(company_id);
		if (!company) {
			throw new AppError(COMPANY.NOT_FOUND);
		}
		return company;
	}

	async #getOrganizationById(organization_id) {
		const organization = await this.organization_repository.findByPk(organization_id);
		if (!organization) {
			throw new AppError(ORGANIZATION.NOT_FOUND);
		}
		return organization;
	}

	async #getWorkstationById(workstation_id) {
		const workstation = await this.workstation_repository.findByPk(workstation_id);
		if (!workstation) {
			throw new AppError(WORKSTATION.NOT_FOUND);
		}
		return workstation;
	}

	async #getCustomReportById(custom_report_id) {
		const custom_report = await this.custom_report_repository.findByPk(custom_report_id);
		if (!custom_report) {
			throw new AppError(CUSTOM_REPORT.NOT_FOUND);
		}
		return custom_report;
	}

	async #getEvaluatorById(evaluator_id) {
		const evaluator = await this.evaluator_repository.findByPk(evaluator_id);
		if (!evaluator) {
			throw new AppError(EVALUATOR.NOT_FOUND);
		}
		return evaluator;
	}

	async #getFileById(file_id) {
		const file = await this.file_repository.findByPk(file_id);
		if (!file) {
			throw new AppError(FILE.NOT_FOUND);
		}
		return file;
	}

	async #createCustomReportResult(
		{
			name,
			user_id,
			file_id,
			role_name,
			activity_id,
			evaluator_id,
			collection_date,
			current_step_id,
			interviewee_name,
			custom_report_id,
			interviewer_name
		},
		transaction
	) {
		return await this.repository.create(
			{
				name,
				file_id,
				role_name,
				activity_id,
				evaluator_id,
				current_step_id,
				collection_date,
				custom_report_id,
				interviewee_name,
				interviewer_name,
				created_by_user_id: user_id
			},
			{ transaction }
		);
	}

	async #createCustomReportReview(params, transaction) {
		return await this.custom_report_review_repository.create(params, { transaction });
	}

	async #updateCustomReportResult(
		{
			user_id,
			file_id,
			role_name,
			activity_id,
			evaluator_id,
			collection_date,
			current_step_id,
			interviewee_name,
			interviewer_name,
			custom_report_id,
			custom_report_result_id
		},
		transaction
	) {
		return await this.repository.update(
			{
				file_id,
				role_name,
				activity_id,
				evaluator_id,
				current_step_id,
				collection_date,
				interviewee_name,
				custom_report_id,
				interviewer_name,
				created_by_user_id: user_id
			},
			{
				where: {
					id: custom_report_result_id
				},
				transaction
			}
		);
	}

	async #updateCurrentStepId({ custom_report_result_id, current_step_id }, transaction) {
		return await this.repository.update(
			{
				current_step_id
			},
			{
				where: {
					id: custom_report_result_id
				},
				transaction
			}
		);
	}

	async #setCurrentStepId(step_id, custom_report_id) {
		const last_step = await this.#getStepById(step_id);
		const next_step = last_step.sequence + 1;
		const current_step_setting = await this.#getCurrentStepBySequenceAndCustomReportId(next_step, custom_report_id);
		return current_step_setting.id;
	}

	async #getStepById(step_id) {
		const step = await this.custom_report_step_repository.findByPk(step_id);
		if (!step) {
			throw new AppError(CUSTOM_REPORT_STEP.NOT_FOUND);
		}
		return step;
	}

	async #getCurrentStepBySequenceAndCustomReportId(sequence, custom_report_id) {
		const step = await this.custom_report_step_repository.findOne({
			where: {
				sequence,
				custom_report_id
			}
		});
		if (!step) {
			throw new AppError(CUSTOM_REPORT_STEP.NOT_FOUND);
		}
		return step;
	}

	async #getCustomReportReview(original_custom_report_result_id, version) {
		if (this.#versionWasNotSelected(original_custom_report_result_id, version)) {
			const last_review = await this.#getLastCustomReportReview(original_custom_report_result_id);

			version = last_review.version;
			original_custom_report_result_id = last_review.original_custom_report_result_id;
		}

		const custom_report_review = await this.custom_report_review_repository.findOne({
			attributes: ['custom_report_result_id', 'comment'],
			where: {
				version,
				original_custom_report_result_id
			}
		});

		if (!custom_report_review) {
			throw new AppError(CUSTOM_REPORT_REVIEW.NOT_FOUND);
		}

		return custom_report_review;
	}

	async #getLastCustomReportReview(original_custom_report_result_id) {
		const find_last_review = await this.custom_report_review_repository.findOne({
			attributes: [
				'original_custom_report_result_id',
				[sequelize.fn('MAX', sequelize.col('version')), 'max_version']
			],
			where: {
				original_custom_report_result_id
			},
			group: ['original_custom_report_result_id']
		});
		const last_review = find_last_review.get({ plain: true });

		return {
			version: last_review.max_version,
			original_custom_report_result_id: last_review.original_custom_report_result_id
		};
	}

	async #deleteCustomReportReviews(custom_report_review_ids, transaction) {
		await this.custom_report_review_repository.delete({
			where: {
				id: custom_report_review_ids
			},
			transaction
		});
	}

	async #deleteCustomReportResults(custom_report_result_id, transaction) {
		await this.repository.delete({
			where: {
				id: custom_report_result_id
			},
			transaction
		});
	}

	#versionWasNotSelected(original_custom_report_result_id, version) {
		return original_custom_report_result_id && !version;
	}

	#customReportResultHasReviews(custom_report_review) {
		return custom_report_review.length > 0;
	}

	async #getStepKeyAndSubStepKeysResultsByStepKeyId(custom_report_result_id, step_key_id) {
		const step_key_and_sub_step_key_result_include =
			this.#custom_report_step_key_result_joiner.setStepKeyAndSubStepKeyResultInclude();

		const custom_report_step_key_result_response = await this.custom_report_step_key_result_repository.findOne({
			where: {
				custom_report_result_id,
				custom_report_step_key_id: step_key_id
			},
			attributes: ['id', 'score', 'result'],
			include: [...step_key_and_sub_step_key_result_include]
		});
		const custom_report_sub_step_keys_results_response =
			await this.custom_report_sub_step_key_result_repository.findAll({
				attributes: ['id', 'score', 'result'],
				where: {
					custom_report_result_id
				},
				include: [
					...step_key_and_sub_step_key_result_include,
					{
						association: 'sub_step_key',
						attributes: ['id', 'name', 'description', 'sequence'],
						where: {
							custom_report_step_key_id: step_key_id
						},
						include: [
							{
								association: 'action_plan',
								required: false,
								where: {
									is_active: true
								},
								include: [
									{
										association: 'action_plan_task'
									},
									{
										association: 'responsible_user',
										attributes: ['name']
									}
								]
							}
						],
						order: [['sequence'], 'ASC']
					}
				]
			});

		const custom_report_step_key_result = custom_report_step_key_result_response?.get({ plain: true });
		const custom_report_sub_step_keys_results = custom_report_sub_step_keys_results_response?.map(
			(sub_step_key_result) => sub_step_key_result.get({ plain: true })
		);

		return {
			custom_report_step_key_result,
			custom_report_sub_step_keys_results
		};
	}

	async #getActionPlanByFileIdAndStepKey(last_result, step_key_id) {
		const [result] = await this.action_plan_repository.findOne(
			{
				file_id: last_result.file_id
			},
			{
				include: [
					{
						association: 'action_plan_task'
					},
					{
						association: 'action_plan_origin',
						required: true,
						where: {
							association: 'action_plan_origin',
							column_id: step_key_id
						}
					},
					{
						association: 'responsible_user',
						attributes: ['name']
					}
				]
			}
		);

		return result;
	}

	#setSubStepKeyPreviousResult(custom_report_sub_step_keys_results) {
		return custom_report_sub_step_keys_results.map(
			({
				id,
				score,
				result,
				severity,
				exposure,
				risk_damage,
				sub_step_key,
				vulnerability,
				risk_category,
				risk_description
			}) => {
				let action_plan = {};

				if (sub_step_key.action_plan.length > 0) {
					const action_plan_sub_step_key = sub_step_key.action_plan[0];

					action_plan = {
						id: action_plan_sub_step_key.id,
						title: action_plan_sub_step_key.title,
						deadline: action_plan_sub_step_key.deadline,
						description: action_plan_sub_step_key.description,
						responsible_name: action_plan_sub_step_key.responsible_user.name,
						tasks: action_plan_sub_step_key.action_plan_task
					};
				}

				return {
					id,
					score,
					result,
					severity,
					exposure,
					risk_damage,
					vulnerability,
					risk_category,
					risk_description,
					sub_step_key: {
						id: sub_step_key.id
					},
					action_plan
				};
			},
			[]
		);
	}

	#formatAdditionalItems(additional_item_result) {
		const { value_1, value_2, value_3, value_4, option_1, option_2, step_key_additional_item } =
			additional_item_result;
		const { title_1, unit_1, title_2, unit_2, title_3, unit_3, title_4, unit_4, title_5, unit_5, title_6, unit_6 } =
			step_key_additional_item.additional_item;

		const mapped_values = {
			value_1,
			value_2,
			value_3,
			value_4,
			value_5: option_1?.description,
			value_6: option_2?.description
		};
		const additional_item_titles =
			ADDITIONAL_ITEMS_NAMES_TO_TITLES_AND_VALUES_MAPPER[step_key_additional_item.additional_item.name];

		const additional_items = [
			{
				title: title_1,
				unit: unit_1,
				value: mapped_values[additional_item_titles['title_1']]
			},
			{
				title: title_2,
				unit: unit_2,
				value: mapped_values[additional_item_titles['title_2']]
			},
			{
				title: title_3,
				unit: unit_3,
				value: mapped_values[additional_item_titles['title_3']]
			},
			{
				title: title_4,
				unit: unit_4,
				value: mapped_values[additional_item_titles['title_4']]
			},
			{
				title: title_5,
				unit: unit_5,
				value: mapped_values[additional_item_titles['title_5']]
			},
			{
				title: title_6,
				unit: unit_6,
				value: mapped_values[additional_item_titles['title_6']]
			}
		].filter(({ title }) => !!title);

		return additional_items;
	}

	#stepKeyHasSubStepKey(custom_report_sub_step_keys_results) {
		return custom_report_sub_step_keys_results.length > 0;
	}

	async #getCustomReportReviewById(review_id) {
		const custom_report_review = await this.custom_report_review_repository.findByPk(review_id);

		if (!custom_report_review) {
			throw new AppError(CUSTOM_REPORT_REVIEW.NOT_FOUND);
		}
		return custom_report_review;
	}

	async #updateCustomReportResultOrReviewComment({ review_id, comment, transaction, custom_report_result_id }) {
		if (review_id) {
			return await this.custom_report_review_repository.update(
				{
					comment
				},
				{
					where: {
						id: review_id
					},
					transaction
				}
			);
		}

		return await this.repository.update(
			{
				comment
			},
			{
				where: {
					id: custom_report_result_id
				},
				transaction
			}
		);
	}

	async #getLinesBySector(filters, sector_ids) {
		const lines = await this.line_repository.findAllByForeignKey({
			where: {
				sector_id: filters?.sector_id ?? sector_ids
			},
			attributes: ['id']
		});

		const line_ids = lines.map((workstation) => workstation?.id);
		return line_ids;
	}

	async #getWorkstationsByLine(filters, line_ids) {
		const workstations = await this.workstation_repository.findAllByForeignKey({
			where: {
				line_id: filters?.line_id ?? line_ids
			},
			attributes: ['id']
		});

		const workstations_ids = workstations.map((workstation) => workstation?.id);
		return workstations_ids;
	}

	async #getFileIdsByWorkstations(filters, workstation_ids) {
		const where_companies = {
			attributes: ['id'],
			where: {
				workstation_id: filters?.workstation_id ?? workstation_ids,
				is_active: true
			}
		};

		const files_response = await this.file_repository.findAll(where_companies);

		const user_access_file_ids = files_response?.map((file) => file?.id);

		return user_access_file_ids;
	}

	async #getSectorsByCompany(filters, user_company_ids) {
		const sectors = await this.sector_repository.findAllByForeignKey({
			where: {
				company_id: filters?.company_id ?? user_company_ids,
				is_active: true
			},
			attributes: ['id']
		});

		const sectors_ids = sectors.map((sector) => sector?.id);
		return sectors_ids;
	}

	async #getCustomReportByName(custom_report_name) {
		const custom_report = await this.custom_report_repository.findOne({
			where: {
				name: custom_report_name
			}
		});

		if (!custom_report) {
			throw new AppError(CUSTOM_REPORT.NOT_FOUND);
		}

		return custom_report.get({ plain: true });
	}

	async #setListAllFilters(filters, file_ids, custom_report_id) {
		const { name, created_start_date, created_end_date, evaluator_id, type_rpn, min_rpn, max_rpn, limit, offset } =
			filters;

		const find_custom_report_results = {
			attributes: ['name', 'id', 'worst_score', 'result', 'file_id', 'created_at'],
			where: {
				custom_report_id,
				file_id: file_ids
			},
			include: [
				{
					association: 'evaluator',
					attributes: ['id', 'name']
				},
				{
					association: 'file',
					required: true,
					attributes: ['id', 'original_name', 'workstation_id'],
					include: [
						{
							association: 'workstations',
							required: true,
							attributes: ['id', 'name']
						}
					]
				}
			],
			limit,
			offset: limit * offset
		};

		if (evaluator_id) {
			_.set(find_custom_report_results, 'where', {
				...find_custom_report_results.where,
				evaluator_id
			});
		}

		if (name) {
			_.set(find_custom_report_results, 'where', {
				...find_custom_report_results.where,
				name: {
					[Op.like]: `%${name}%`
				}
			});
		}

		if (created_start_date && created_end_date) {
			_.set(find_custom_report_results, 'where', {
				...find_custom_report_results.where,
				created_at: {
					[Op.between]: [created_start_date, created_end_date]
				}
			});
		}

		if (min_rpn || max_rpn) {
			if (min && max_rpn) {
				_.set(find_custom_report_results, 'where', {
					...find_custom_report_results.where,
					worts_score: {
						[Op.between]: [min_rpn, max_rpn]
					}
				});
			} else {
				_.set(find_custom_report_results, 'where', {
					...find_custom_report_results.where,
					worts_score: {
						[Op[type_rpn]]: min_rpn ?? max_rpn
					}
				});
			}
		}

		return find_custom_report_results;
	}

	async #getFilesId(custom_report_id) {
		const custom_report_results = await this.repository.findAllByForeignKey({
			where: {
				custom_report_id
			},
			attributes: ['file_id']
		});

		return custom_report_results.map((m) => m.file_id);
	}

	async countAllByRisk(params) {
		logger.info('[CustomReportResult] service - countAllByRisk init', { params });
		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date,
			custom_report_id
		} = params;

		const custom_report = await this.#getCustomReportById(custom_report_id);
		const has_reviews = this.#customReportHasReviews(custom_report.name);

		const [total_by_risk, error] = await this.repository.countAllByRisk({
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date,
			custom_report_id,
			has_reviews
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[CustomReportResult] service - countAllByRisk finish');
		return total_by_risk;
	}

	async listEwa(params) {
		logger.info('[CustomReportResult] service - listEwa init', { params });
		let user_id = '';
		if (StorageContext.getStore()?.environment === 'sandbox') {
			user_id = StorageContext.getStore().user_id;
			logger.info('[CustomReportResult] service - listEwa init sandbox', { params });
		}

		const {
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			evaluator_id,
			collection_date_start,
			collection_date_end,
			created_at_start,
			created_at_end,
			file_name,
			offset,
			limit
		} = params;

		const find_all_promise = this.repository.findAllEwa({
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			evaluator_id,
			collection_date_start,
			collection_date_end,
			created_at_start,
			created_at_end,
			file_name,
			offset,
			limit,
			user_id
		});

		const count_all_promise = this.repository.countAllEwa({
			organization_id,
			company_id,
			companies_with_user_access,
			sector_id,
			line_id,
			workstation_id,
			evaluator_id,
			collection_date_start,
			collection_date_end,
			created_at_start,
			created_at_end,
			file_name,
			user_id
		});

		const [[rows, find_error], [count, count_error]] = await Promise.all([find_all_promise, count_all_promise]);

		if (find_error) {
			logger.error(`${find_error.message}, stack trace - ${find_error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		if (count_error) {
			logger.error(`${count_error.message}, stack trace - ${count_error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[CustomReportResult] service - listEwa finish');
		return { rows, count };
	}

	async findResultsByReportType(params) {
		logger.info('[CustomReportResult] service - findResultsByReportType init', { params });
		const { limit, offset, selected_company_id, companies_with_user_access, ...rest } = params;

		if (selected_company_id && !companies_with_user_access.includes(selected_company_id)) {
			throw new AppError(COMPANY.NOT_AUTHORIZED);
		}

		if (!companies_with_user_access.length) {
			throw new AppError(COMPANY.NOT_AUTHORIZED);
		}

		const [data, error] = await this.repository.findResultsByReportType({
			limit,
			offset: (offset - 1) * limit,
			selected_company_id,
			companies_with_user_access,
			...rest
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		const result = {
			...data,
			limit,
			page: offset
		};

		logger.info('[CustomReportResult] service - findResultsByReportType finish');
		return result;
	}
}
