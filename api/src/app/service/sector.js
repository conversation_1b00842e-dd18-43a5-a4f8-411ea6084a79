import { Sequelize } from 'sequelize';

import { ERROR_RESPONSE_ENTITIES_ENUM, ERROR_RESPONSE_ENUM } from '../util/enum.js';
import { MosaicWorstScores } from './mosaic_worst_scores.js';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../utils/errors.js';
import { AppError } from '../helpers/errors.js';
import { logger } from '../helpers/logger.js';

const { Op } = Sequelize;
const { SECTOR } = ERROR_RESPONSE_ENTITIES_ENUM;

export class SectorService {
	constructor({
		repository,
		file_repository,
		line_repository,
		workstation_repository,
		preliminary_analysis_repository
	}) {
		this.repository = repository;
		this.file_repository = file_repository;
		this.line_repository = line_repository;
		this.mosaicWorstScore = new MosaicWorstScores();
		this.workstation_repository = workstation_repository;
		this.preliminary_analysis_repository = preliminary_analysis_repository;
	}

	async index(parameters) {
		logger.info('[Sector] controller - index init');
		const { organization_id, company_id } = parameters;
		const sectors = await this.repository.findByCompanyAndOrganization({
			where: {
				is_active: true
			},
			attributes: ['id', 'name'],
			include: [
				{
					association: 'Company',
					where: {
						id: company_id,
						is_active: true
					},
					attributes: [],
					include: [
						{
							association: 'Organization',
							where: {
								id: organization_id,
								is_active: true
							},
							attributes: []
						}
					]
				}
			]
		});
		logger.info('[Sector] controller - index finish');
		return sectors;
	}

	async findAllByCompanyId({ company_id }) {
		logger.info('[Sector] service - findAllByCompanyId init');
		const sectors = await this.repository.findAllByForeignKey({
			where: {
				is_active: true,
				company_id
			},
			attributes: ['id', 'name', 'company_id']
		});
		logger.info('[Sector] service - findAllByCompanyId finish');
		return sectors;
	}

	async findAllWithWorstScore(company_id) {
		logger.info('[Sector] service - findAllWithWorstScore init');
		const sectors = await this.repository.findAllWithWorstScore(company_id);
		const mapped_sectors_worst_score = this.#mapSectorsWorstScore(sectors);
		logger.info('[Sector] service - findAllWithWorstScore finish');
		return mapped_sectors_worst_score;
	}

	#mapSectorsWorstScore(sectors) {
		const reduced_sector = sectors.reduce((reduced_sectors, currentSector) => {
			const reduced_sector = reduced_sectors[currentSector.id];
			const curent_worst_score = currentSector['line.workstation.file.preliminary_analysis.worst_score'];
			const worst_score = this.mosaicWorstScore.setWorstScore(reduced_sector, curent_worst_score);
			const lines = this.mosaicWorstScore.setChildren(reduced_sector?.child, currentSector, 'line.id');
			const size = lines?.length;

			return {
				...reduced_sectors,
				[currentSector.id]: {
					id: currentSector.id,
					name: currentSector.name,
					child: lines || [],
					size,
					worst_score
				}
			};
		}, {});

		const mapped_sectors = this.mosaicWorstScore.convertDataToArray(reduced_sector);

		return mapped_sectors;
	}

	async criticalSector(parameters) {
		const { organization_id, company_id } = parameters;

		const result = await this.repository.getCritical({ organization_id, company_id });

		if (!result) return { sector: null };

		const { sector, score } = result;

		const scoreParse = parseInt(score);

		if (scoreParse === 0) return { sector: null };

		return { sector, score: scoreParse };
	}

	async countSector(parameters) {
		const { organization_id, company_id } = parameters;
		const result = this.repository.countSector({ organization_id, company_id });
		return result;
	}

	async create(params) {
		logger.info('[Sector] service - create init');
		const { company_id, name } = params;
		const transaction = await this.repository.db.sequelize.transaction();
		try {
			const sector = await this.repository.create(
				{
					name,
					company_id
				},
				{ transaction }
			);

			if (!sector) {
				throw new AppError(ERROR_RESPONSE_ENUM.INTERNAL_SERVER_ERROR);
			}

			await transaction.commit();
			logger.info('[Sector] service - create finish');
			return sector;
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async update({ id, name }) {
		logger.info('[Sector] service - update init');
		let transaction;
		try {
			const sector = await this.repository.findByPk(id, {
				where: {
					is_active: true
				}
			});

			if (!sector) {
				throw new AppError(SECTOR.NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();
			sector.name = name;
			await sector.save({
				transaction
			});
			await transaction.commit();
			logger.info('[Sector] service - update finish');
			return sector;
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}

	async delete({ id }) {
		logger.info('[Sector] service - delete init');
		let transaction;
		try {
			const sector = await this.repository.findByPk(id, {
				where: {
					is_active: true
				}
			});

			if (!sector) {
				throw new AppError(SECTOR.NOT_FOUND);
			}

			const lines = await this.line_repository.findAllByForeignKey({
				where: {
					sector_id: sector?.id
				}
			});

			const line_ids = lines.map((line) => line?.id).filter((line) => line);

			const workstations = await this.workstation_repository.findAllByForeignKey({
				where: {
					line_id: line_ids
				}
			});

			const workstation_ids = workstations
				.map((workstation) => workstation?.id)
				.filter((workstation) => workstation);

			const files = await this.file_repository.findAllByForeignKey({
				where: {
					[Op.or]: [
						{
							is_active: true,
							workstation_id: workstation_ids
						},
						{
							is_active: true,
							sector_id: sector.id
						}
					]
				}
			});

			const file_ids = files.map((file) => file?.id).filter((file) => file);

			const preliminary_analyzes = await this.preliminary_analysis_repository.findAllByForeignKey({
				where: {
					file_id: file_ids
				}
			});

			const preliminary_analysis_ids = preliminary_analyzes
				.map((preliminary_analysis) => preliminary_analysis?.id)
				.filter((preliminary_analysis) => preliminary_analysis);

			transaction = await this.repository.db.sequelize.transaction();
			sector.is_active = false;
			await sector.save({
				transaction
			});
			await this.file_repository.update(
				{
					sector_id: null,
					workstation: null,
					workstation_id: null
				},
				{
					where: {
						id: file_ids
					}
				},
				{
					transaction
				}
			);
			await this.preliminary_analysis_repository.update(
				{
					consolidated: false
				},
				{
					where: {
						id: preliminary_analysis_ids
					}
				},
				{
					transaction
				}
			);
			await this.line_repository.delete(
				{
					where: {
						id: line_ids
					}
				},
				{
					transaction
				}
			);
			await transaction.commit();
			const deleted_company = await this.repository.findByPk(id);
			logger.info('[Sector] service - delete finish');
			return deleted_company;
		} catch (error) {
			await transaction?.rollback();
			throw error;
		}
	}
}

export default class SectorService2 {
	constructor(repository) {
		this.sectorRepository = repository;
	}

	async totalCriticalByCompany(parameters) {
		const { organization_id, company_id } = parameters;
		const result = await this.sectorRepository.counterTotalCriticalSectors({
			organization_id,
			company_id
		});
		if (result === (undefined || null)) {
			throw new ErrorHandler(500, 'Internal error');
		}
		return result;
	}

	async totalByCompany(parameters) {
		const { organization_id, company_id } = parameters;
		const result = await this.sectorRepository.totalByCompany({
			organization_id,
			company_id
		});
		if (result === (undefined || null)) {
			throw new ErrorHandler(500, 'Internal error');
		}
		return result;
	}

	async usageCheck(sectorId, companyId) {
		try {
			const { count } = await this.sectorRepository.usageCheck(sectorId, companyId);
			const response = { result: !!count > 0, count };
			return response;
		} catch (err) {
			throw err;
		}
	}
}
