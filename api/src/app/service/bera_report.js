import config from 'config';
import moment from 'moment';

import { ERROR_RESPONSE_ENTITIES_ENUM } from '../util/enum.js';
import { BeraScoresCalculator, CustomReportChecklistMapper, TASK_TIME_FORMAT } from '../entities/index.js';
import { forceHandsMap, forceTransferMap, physicalOverloadMap, riskLoadMap } from '../util/constants-kim_mho.js';
import {
	Util,
	Lambda,
	logger,
	Storage,
	AppError,
	RESPONSE_ERROR_STATUS,
	RESPONSE_ERROR_ENTITIES,
	CUSTOM_REPORTS_NAMES_ENUM
} from '../helpers/index.js';
import {
	BERA_SIX_TOO,
	DASHBOARD_GENDER_NEUTRAL_TEXT,
	DASHBOARD_JANUARY_INDEX_MONTH,
	DASHBOARD_INITIAL_MONTH_FISCAL_YEAR
} from '../utils/constants.js';
import { StorageContext } from '../utils/storage_context.js';

const bucket = config.get('App.bucket');
const { DATABASE_FAILED_PERFORM_QUERY } = RESPONSE_ERROR_STATUS;
const BERA_TASKS_NAMES_WITH_RPN_ONE = ['cart_push_pull', 'hand_activities'];

export class BeraReportService {
	constructor({
		repository,
		file_repository,
		task_repository,
		bera_job_repository,
		workstation_repository,
		custom_report_repository,
		custom_report_step_repository,
		bera_step_key_result_repository,
		bera_weighted_average_repository
	}) {
		this.lambda = new Lambda();
		this.storage = new Storage();
		this.repository = repository;
		this.file_repository = file_repository;
		this.task_repository = task_repository;
		this.workstation_repository = workstation_repository;
		this.bera_job_repository = bera_job_repository;
		this.custom_report_repository = custom_report_repository;
		this.granularity_mapper = new CustomReportChecklistMapper();
		this.custom_report_step_repository = custom_report_step_repository;
		this.bera_step_key_result_repository = bera_step_key_result_repository;
		this.bera_weighted_average_repository = bera_weighted_average_repository;
	}

	async create({ bera_report_data, bera_step_key_result_data }) {
		logger.info('[BeraReport] service - create init');
		const {
			operator_evaluated,
			work_center,
			comment,
			evaluator_id,
			task_time,
			task_time_format,
			has_known_injury,
			collection_date,
			severity,
			exposure,
			vulnerability,
			rpn,
			task_id,
			file_id,
			workstation_id,
			bera_job_summary_id
		} = bera_report_data;

		let transaction;
		try {
			const existing_bera_report = await this.repository.findOne({
				where: {
					task_id,
					file_id,
					bera_job_summary_id
				}
			});

			if (existing_bera_report) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.BERA_REPORT.ALREADY_EXISTS);
			}

			const formatted_task_time = task_time_format === TASK_TIME_FORMAT.MINUTES ? task_time * 60 : task_time;

			transaction = await this.repository.db.sequelize.transaction();
			const created_bera_report = await this.repository.create(
				{
					operator_evaluated,
					work_center,
					comment,
					evaluator_id,
					task_time: formatted_task_time,
					task_time_format,
					has_known_injury,
					collection_date,
					severity,
					exposure,
					vulnerability,
					rpn,
					task_id,
					file_id,
					bera_job_summary_id
				},
				{ transaction }
			);

			if (!created_bera_report) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.BERA_REPORT.NOT_FOUND);
			}

			const existing_task = await this.task_repository.findByPk(task_id);

			if (!existing_task) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.TASK.NOT_FOUND);
			}

			const file = await this.file_repository.findByPk(file_id);

			if (!file) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.FILE.NOT_FOUND);
			}

			const workstation = await this.workstation_repository.findByPk(workstation_id, {
				include: [
					{
						association: 'line',
						attributes: ['id', 'sector_id'],
						required: true
					}
				]
			});

			if (!workstation) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.WORKSTATION.NOT_FOUND);
			}

			await existing_task.addFile(file);

			await this.file_repository.update(
				{
					workstation_id,
					sector_id: workstation?.line?.sector_id
				},
				{
					where: {
						id: file_id
					},
					transaction
				}
			);

			const bera_report_id = created_bera_report.id;
			const mapped_step_key_results = bera_step_key_result_data.map((result) => ({
				...result,
				bera_report_id
			}));

			const created_bera_step_key_results = await Promise.all(
				mapped_step_key_results.map((result) =>
					this.bera_step_key_result_repository.create(result, {
						transaction
					})
				)
			);

			await transaction.commit();
			logger.info('[BeraReport] service - create finish');
			return { created_bera_report, created_bera_step_key_results };
		} catch (error) {
			await transaction?.rollback();
			logger.error('[BeraReport] service - create error', { error });
			throw error;
		}
	}

	async update({ bera_report_data, bera_step_key_result_data, id }) {
		logger.info('[BeraReport] service - update init');

		let transaction;
		try {
			const existing_bera_report = await this.repository.findByPk(id);

			if (!existing_bera_report) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.BERA_REPORT.NOT_FOUND);
			}

			if (bera_report_data.task_time) {
				bera_report_data.task_time =
					bera_report_data.task_time_format === TASK_TIME_FORMAT.MINUTES
						? bera_report_data.task_time * 60
						: bera_report_data.task_time;
			}

			transaction = await this.repository.db.sequelize.transaction();
			const updated_bera_report = await this.repository.update(
				{
					...bera_report_data,
					consolidated: bera_report_data.consolidated !== false
				},
				{
					where: {
						id
					},
					transaction
				}
			);

			if (!updated_bera_report) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.BERA_REPORT.NOT_FOUND);
			}

			if (bera_report_data.workstation_id) {
				const workstation = await this.workstation_repository.findByPk(bera_report_data.workstation_id, {
					include: [
						{
							association: 'line',
							attributes: ['id', 'sector_id'],
							required: true
						}
					]
				});

				if (!workstation) {
					throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.WORKSTATION.NOT_FOUND);
				}

				await this.file_repository.update(
					{
						workstation_id: bera_report_data.workstation_id,
						sector_id: workstation?.line?.sector_id
					},
					{
						where: {
							id: bera_report_data.file_id
						},
						transaction
					}
				);
			}

			if (bera_step_key_result_data?.length > 0) {
				const mapped_step_key_results = bera_step_key_result_data.map((result) => ({
					...result,
					bera_report_id: id
				}));

				await Promise.all(
					mapped_step_key_results.map((result) =>
						this.bera_step_key_result_repository.update(result, {
							where: {
								custom_report_step_key_id: result.custom_report_step_key_id,
								bera_report_id: id
							},
							transaction
						})
					)
				);
			}

			const bera_report = await this.repository.findByPk(id);

			await transaction.commit();
			logger.info('[BeraReport] service - update finish');
			return bera_report;
		} catch (error) {
			await transaction?.rollback();
			logger.error('[BeraReport] service - update error', { error });
			throw error;
		}
	}

	async downloadPDF(payload) {
		logger.info('[BeraReport] service - downloadPDF init');
		const { organization_id, company_id, file_id, bera_job_summary_id, locale } = payload;

		const {
			task_association,
			niosh_association,
			kim_mho_association,
			evaluator_association,
			strain_index_association,
			workstations_association,
			bera_step_key_result_association
		} = this.#setAssociations(company_id);

		const existing_bera_report = await this.repository.findOne({
			where: {
				file_id,
				bera_job_summary_id
			},
			include: [
				task_association,
				evaluator_association,
				{
					association: 'file',
					where: {
						is_active: true
					},
					required: false,
					attributes: ['id', 'original_name', 'generated_name', 'updated_at'],
					include: [
						niosh_association,
						kim_mho_association,
						strain_index_association,
						workstations_association
					]
				}
			]
		});

		if (!existing_bera_report) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.BERA_REPORT.NOT_FOUND);
		}

		const existing_custom_report = await this.custom_report_repository.findOne({
			where: {
				name: 'bera'
			},
			attributes: ['id']
		});

		if (!existing_custom_report) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.CUSTOM_REPORT.NOT_FOUND);
		}

		const existing_bera_report_steps = await this.custom_report_step_repository.findAllByForeignKey({
			where: {
				custom_report_id: existing_custom_report.id
			},
			include: [
				{
					association: 'step_key',
					required: true,
					include: [bera_step_key_result_association],
					attributes: ['id', 'name', 'description']
				}
			],
			attributes: ['id', 'name', 'description'],
			order: [
				['sequence', 'ASC'],
				['step_key', 'sequence', 'ASC']
			]
		});

		if (!existing_bera_report_steps) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.CUSTOM_REPORT_STEP.NOT_FOUND);
		}

		let bera_report = existing_bera_report.toJSON();
		const file = bera_report.file.generated_name;
		let bera_report_steps = existing_bera_report_steps.map((step) => step.toJSON());

		const mapped_bera_report = this.#mapBeraReportResult(bera_report, bera_report_steps);

		mapped_bera_report.cycle.task_time = this.getTaskTime(mapped_bera_report.cycle, locale);
		const normalized_kim_mho = this.#normalizeKimMhoData(mapped_bera_report);

		mapped_bera_report.tools['kim_mho'] = normalized_kim_mho;

		const prefix = Util.getPrefix(organization_id, company_id);
		const remove_extension = this.#removeExtension(file);
		const file_key = this.#formatFileKey(remove_extension, prefix, 'bera-report');

		const target_file = `bera-report-${remove_extension}.pdf`;

		const language = locale.replace('-', '_');

		const config = {
			functionName: 'pdf_container',
			data: {
				...mapped_bera_report,
				bera_report_id: bera_report.id,
				title: 'Basic Ergonomics Risk Assessment (B.E.R.A.)',
				report_type: 'john-deere',
				type: 'john-deere/bera-report',
				comments: mapped_bera_report.cycle.comment,
				language,
				bucket,
				prefix,
				file_key
			}
		};

		const key = await this.#generatedPDF(config);
		const url = await this.#generateDownloadURL({ fileName: target_file, Bucket: bucket, Key: key });

		logger.info('[BeraReport] service - downloadPDF finish');
		return { url };
	}

	async delete(id) {
		logger.info('[BeraReport] service - delete init');
		let transaction;
		try {
			const existing_bera_report = await this.repository.findByPk(id);

			if (!existing_bera_report) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.BERA_REPORT.NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();
			await this.repository.delete(
				{
					where: {
						id
					}
				},
				transaction
			);

			const existing_bera_job_summary = await this.#getExistingBeraJobSummaryFromDatabase(
				existing_bera_report.bera_job_summary_id
			);

			if (existing_bera_job_summary?.bera_report?.length === 0) {
				await this.bera_job_repository.delete(
					{
						where: {
							id: existing_bera_report.bera_job_summary_id
						}
					},
					transaction
				);
				await transaction.commit();
				logger.info('[BeraReport] service - delete finish');
				return [];
			}

			const {
				bera_weighted_average,
				mapped_weighted_average,
				overall_score,
				step_keys_scores_array,
				total_time,
				weighted_rsi
			} = new BeraScoresCalculator(existing_bera_job_summary).calculate();

			await this.bera_job_repository.update(
				{
					total_time, // seconds
					overall_score,
					weighted_rsi
				},
				{
					where: {
						id: existing_bera_job_summary.id
					},
					transaction
				}
			);

			if (!bera_weighted_average || bera_weighted_average.length === 0) {
				await this.bera_weighted_average_repository.bulkCreate(mapped_weighted_average, {
					transaction,
					updateOnDuplicate: ['average']
				});
			} else {
				await Promise.all(
					step_keys_scores_array.map((step_key, index) =>
						this.bera_weighted_average_repository.update(
							{
								average_type: 'RPN',
								average: step_key[index].score
							},
							{
								where: {
									average_type: 'RPN',
									custom_report_step_key_id: step_key[index].step_key_id,
									bera_job_summary_id: existing_bera_job_summary.id
								},
								transaction
							}
						)
					)
				);
			}

			await transaction.commit();

			const deleted_bera_job_summary = await this.repository.findByPk(id, { paranoid: false });
			logger.info('[BeraReport] service - delete finish');
			return deleted_bera_job_summary;
		} catch (error) {
			logger.error('[BeraReport] service - delete error', { error });
			await transaction?.rollback();
			throw error;
		}
	}

	async show(id) {
		logger.info('[BeraReport] service - show init');
		try {
			const existing_bera_report = await this.repository.findByPk(id);

			if (!existing_bera_report) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.BERA_REPORT.NOT_FOUND);
			}
			logger.info('[BeraReport] service - show finish');
			return existing_bera_report;
		} catch (error) {
			logger.error('[BeraReport] service - show error', { error });
			throw error;
		}
	}

	async findByTaskId(task_id) {
		logger.info('[BeraReport] service - findByTaskId init');
		try {
			const existing_bera_report = await this.repository.findOne({
				where: {
					task_id
				}
			});

			if (!existing_bera_report) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.BERA_REPORT.NOT_FOUND);
			}
			logger.info('[BeraReport] service - findByTaskId finish');
			return existing_bera_report;
		} catch (error) {
			logger.error('[BeraReport] service - findByTaskId error', { error });
			throw error;
		}
	}

	async findByFileId(file_id, bera_job_summary_id) {
		logger.info('[BeraReport] service - findByFileId init');
		try {
			const bera_step_key_result_association = this.#setFindByFileIdAssociations();

			const existing_bera_report = await this.repository.findOne({
				where: {
					file_id,
					bera_job_summary_id
				},
				include: [bera_step_key_result_association]
			});

			if (!existing_bera_report) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.BERA_REPORT.NOT_FOUND);
			}

			const bera_report = existing_bera_report.toJSON();

			const mapped_bera_report = this.#mapBeraReport(bera_report, existing_bera_report);

			logger.info('[BeraReport] service - findByFileId finish');
			return mapped_bera_report;
		} catch (error) {
			logger.error('[BeraReport] service - findByFileId error', { error });
			throw error;
		}
	}

	async findResultByFileId({ file_id, bera_job_summary_id, company_id }) {
		logger.info('[BeraReport] service - findByFileId init');
		try {
			const {
				task_association,
				niosh_association,
				kim_mho_association,
				evaluator_association,
				strain_index_association,
				workstations_association,
				bera_step_key_result_association
			} = this.#setAssociations(company_id);

			const existing_bera_report = await this.repository.findOne({
				where: {
					file_id,
					bera_job_summary_id
				},
				include: [
					task_association,
					evaluator_association,
					{
						association: 'file',
						where: {
							is_active: true
						},
						required: false,
						attributes: ['id', 'original_name', 'generated_name', 'updated_at'],
						include: [
							niosh_association,
							kim_mho_association,
							strain_index_association,
							workstations_association
						]
					}
				]
			});

			if (!existing_bera_report) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.BERA_REPORT.NOT_FOUND);
			}

			const existing_custom_report = await this.custom_report_repository.findOne({
				where: {
					name: 'bera'
				},
				attributes: ['id']
			});

			if (!existing_custom_report) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.CUSTOM_REPORT.NOT_FOUND);
			}

			const existing_bera_report_steps = await this.custom_report_step_repository.findAllByForeignKey({
				where: {
					custom_report_id: existing_custom_report.id
				},
				include: [
					{
						association: 'step_key',
						required: true,
						include: [bera_step_key_result_association],
						attributes: ['id', 'name', 'description']
					}
				],
				attributes: ['id', 'name', 'description'],
				order: [
					['sequence', 'ASC'],
					['step_key', 'sequence', 'ASC']
				]
			});

			if (!existing_bera_report_steps) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.CUSTOM_REPORT_STEP.NOT_FOUND);
			}

			let bera_report = existing_bera_report.toJSON();
			let bera_report_steps = existing_bera_report_steps.map((step) => step.toJSON());

			const mapped_bera_report = this.#mapBeraReportResult(bera_report, bera_report_steps);

			logger.info('[BeraReport] service - findByFileId finish');
			return mapped_bera_report;
		} catch (error) {
			logger.error('[BeraReport] service - findByFileId error', { error });
			throw error;
		}
	}

	async findAllByBeraJobSummaryId(bera_job_summary_id) {
		logger.info('[BeraReport] service - findAllByBeraJobSummaryId init');
		try {
			const existing_bera_report = await this.repository.findAllByForeignKey({
				where: {
					bera_job_summary_id
				}
			});

			if (!existing_bera_report) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.BERA_REPORT.NOT_FOUND);
			}
			logger.info('[BeraReport] service - findAllByBeraJobSummaryId finish');
			return existing_bera_report;
		} catch (error) {
			logger.error('[BeraReport] service - findAllByBeraJobSummaryId error', { error });
			throw error;
		}
	}

	async sumRPN(params) {
		logger.info('[BeraReport] service - sumRPN init', { params });
		const { organization_id, workstation_id, companies_ids, company_id, start_date, sector_id, end_date, line_id } =
			params;

		let user_id = '';

		if (StorageContext.getStore()?.environment === 'sandbox') {
			user_id = StorageContext.getStore().user_id;
		}

		const [result, error] = await this.repository.sumRPN({
			organization_id,
			workstation_id,
			companies_ids,
			company_id,
			start_date,
			sector_id,
			end_date,
			line_id
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[BeraReport] service - sumRPN finish');
		return result;
	}

	async genderNeutralCount(params) {
		logger.info('[BeraReport] service - genderNeutralCount init', { params });
		const {
			is_gender_neutral,
			organization_id,
			workstation_id,
			companies_ids,
			fiscal_year,
			granularity,
			company_id,
			start_date,
			sector_id,
			end_date,
			line_id
		} = params;

		const existing_custom_report = await this.custom_report_repository.findOne({
			attributes: ['id'],
			where: { name: CUSTOM_REPORTS_NAMES_ENUM.bera }
		});

		if (!existing_custom_report) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.CUSTOM_REPORT.NOT_FOUND);
		}

		const bera_steps = await this.custom_report_step_repository.findAllByForeignKey({
			attributes: ['id'],
			where: {
				custom_report_id: existing_custom_report.id,
				name: [BERA_SIX_TOO.TOO_MANY, BERA_SIX_TOO.TOO_MUCH]
			},
			include: [
				{
					required: true,
					association: 'step_key',
					where: {
						name: BERA_TASKS_NAMES_WITH_RPN_ONE
					}
				}
			]
		});

		if (!bera_steps.length) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.CUSTOM_REPORT_STEP_KEY.NOT_FOUND);
		}

		const six_too_tasks_neutral_with_rpn_one = bera_steps.flatMap((step) => step.step_key.map((key) => key.id));
		const year_start_month = fiscal_year ? DASHBOARD_INITIAL_MONTH_FISCAL_YEAR : DASHBOARD_JANUARY_INDEX_MONTH;

		let user_id = '';

		if (StorageContext.getStore()?.environment === 'sandbox') {
			user_id = StorageContext.getStore().user_id;
		}

		const [data, error] = await this.repository.genderNeutralCount({
			six_too_tasks_neutral_with_rpn_one,
			period_group: granularity,
			is_gender_neutral,
			year_start_month,
			organization_id,
			workstation_id,
			companies_ids,
			fiscal_year,
			company_id,
			start_date,
			sector_id,
			end_date,
			line_id,
			user_id
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		const filled_results = this.granularity_mapper.fillChecklistResult({
			custom_report_results: data,
			period: granularity,
			year_start_month,
			start_date,
			end_date
		});

		const result = this.granularity_mapper.formatResult({
			name: is_gender_neutral ? DASHBOARD_GENDER_NEUTRAL_TEXT : `Not ${DASHBOARD_GENDER_NEUTRAL_TEXT}`,
			custom_report_results: filled_results,
			period: granularity,
			year_start_month,
			fiscal_year,
			start_date,
			end_date
		});

		logger.info('[BeraReport] service - genderNeutralCount finish');
		return result;
	}

	async hierarchySumRPN(params) {
		logger.info('[BeraReport] service - hierarchySumRPN init');
		const {
			organization_id,
			workstation_id,
			companies_ids,
			company_id,
			start_date,
			sector_id,
			end_date,
			line_id,
			offset,
			limit
		} = params;

		let user_id = '';

		if (StorageContext.getStore()?.environment === 'sandbox') {
			user_id = StorageContext.getStore().user_id;
		}

		const [result, error] = await this.repository.hierarchySumRPN({
			organization_id,
			workstation_id,
			companies_ids,
			company_id,
			start_date,
			sector_id,
			end_date,
			line_id,
			offset,
			limit,
			user_id
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		logger.info('[BeraReport] service - hierarchySumRPN finish');
		return result;
	}

	async sixTooSumRPN(params) {
		logger.info('[BeraReport] service - sixTooSumRPN init', { params });
		const { organization_id, workstation_id, companies_ids, start_date, company_id, sector_id, end_date, line_id } =
			params;

		const existing_custom_report = await this.custom_report_repository.findOne({
			attributes: ['id'],
			where: {
				name: CUSTOM_REPORTS_NAMES_ENUM.bera
			}
		});

		if (!existing_custom_report) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.CUSTOM_REPORT.NOT_FOUND);
		}

		const six_too_names = Object.values(BERA_SIX_TOO);

		const bera_steps = await this.custom_report_step_repository.findAllByForeignKey({
			attributes: ['id', 'description'],
			where: {
				custom_report_id: existing_custom_report.id,
				name: six_too_names
			}
		});

		if (!bera_steps.length) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.CUSTOM_REPORT_STEP.NOT_FOUND);
		}

		let user_id = '';

		if (StorageContext.getStore()?.environment === 'sandbox') {
			user_id = StorageContext.getStore().user_id;
		}

		const [data, error] = await this.repository.sixTooSumRPN({
			custom_report_step_ids: bera_steps.map((step) => step.id),
			organization_id,
			workstation_id,
			companies_ids,
			company_id,
			start_date,
			sector_id,
			end_date,
			line_id,
			user_id
		});

		if (error) {
			logger.error(`${error.message}, stack trace - ${error.stack}`);
			throw new AppError(DATABASE_FAILED_PERFORM_QUERY);
		}

		const result = data.map(({ custom_report_step_id, sum_rpn }) => {
			const six_too = bera_steps.find((step) => step.id === custom_report_step_id);
			return {
				sum_rpn,
				six_too_name: six_too.description
			};
		});

		logger.info('[BeraReport] service - sixTooSumRPN finish');
		return result;
	}

	#mapBeraReportResult(bera_report, bera_report_steps) {
		const {
			id,
			collection_date,
			comment,
			consolidated,
			evaluator,
			exposure,
			file,
			has_known_injury,
			operator_evaluated,
			severity,
			task,
			rpn,
			task_time,
			task_time_format,
			work_center,
			vulnerability,
			bera_job_summary_id
		} = bera_report;

		const workstation = file?.workstations;
		const organization_name = workstation?.line.sector.company.Organization.name;
		const company_name = workstation?.line.sector.company.name;
		const sector_name = workstation?.line.sector.name;
		const line_name = workstation?.line.name;
		const workstation_name = workstation?.name;
		const cycle_name = bera_report?.task?.cycle[0]?.name;
		const formatted_task_time = task_time_format === TASK_TIME_FORMAT.MINUTES ? task_time / 60 : task_time;
		const formatted_collection_date = collection_date ? moment(collection_date).format('L') : '-';

		const organization = {
			name: organization_name,
			company_name,
			line_name,
			sector_name,
			workstation_name
		};

		const cycle = {
			collection_date: formatted_collection_date,
			cycle_name,
			evaluator: evaluator.name,
			file_name: file?.original_name,
			has_known_injury,
			operator_evaluated,
			rpn: {
				exposure,
				severity,
				vulnerability,
				rpn
			},
			task_name: task?.name,
			work_center,
			task_time: formatted_task_time,
			task_time_format,
			comment,
			consolidated,
			bera_job_summary_id
		};

		const tools = {
			niosh: file?.niosh,
			kim_mho: file.kim_mho,
			strain_index: file?.strain_index
		};

		const bera_steps_result = bera_report_steps.map((step) => {
			return {
				...step,
				step_key: this.#mapStepKeyResult(step, id)
			};
		});

		return {
			bera_report_id: id,
			organization,
			cycle,
			bera_steps_result,
			tools
		};
	}

	#mapStepKeyResult(step, id) {
		return step.step_key.map((step_key) => {
			const bera_step_key_result = step_key.bera_step_key_result.find((result) => result.bera_report_id === id);
			return {
				id: step_key.id,
				description: step_key.description,
				name: step_key.name,
				result: {
					task_rpn: bera_step_key_result?.task_rpn,
					frequency: bera_step_key_result?.frequency,
					stress_level: bera_step_key_result?.stress_level,
					total_task_duration: bera_step_key_result?.total_task_duration,
					job_element: bera_step_key_result?.job_element
				}
			};
		});
	}

	#setAssociations(company_id) {
		const cycle_association = {
			association: 'cycle',
			required: false,
			attributes: ['id', 'name']
		};

		const evaluator_association = {
			association: 'evaluator',
			required: true,
			where: {
				company_id
			},
			attributes: ['id', 'name']
		};

		const task_association = {
			association: 'task',
			required: false,
			attributes: ['id', 'name', 'description'],
			include: [cycle_association]
		};

		const niosh_association = {
			association: 'niosh',
			where: {
				is_active: true
			},
			required: false,
			attributes: ['id', 'mass_m', 'recommended_weight_limit', 'lifting_index', 'risk', 'reference_weight']
		};

		const strain_index_association = {
			association: 'strain_index',
			where: {
				is_active: true
			},
			required: false,
			attributes: [
				'id',
				'left_risk',
				'right_risk',
				'score_left_rsi',
				'score_right_rsi',
				'score_left_borg_scale',
				'score_right_borg_scale',
				'score_left_exertion_duration',
				'score_right_exertion_duration',
				'score_left_efforts_per_minute',
				'score_right_efforts_per_minute',
				'score_left_wrist_posture',
				'score_right_wrist_posture',
				'score_left_daily_duration',
				'score_right_daily_duration'
			]
		};

		const kim_mho_association = {
			association: 'kim_mho',
			where: {
				is_active: true
			},
			required: false,
			attributes: [
				'id',
				'duration',
				'risk_score',
				'risk_load',
				'force_transfer',
				'left_force_intensity',
				'right_force_intensity'
			]
		};

		const organization_association = {
			association: 'Organization',
			required: false,
			where: {
				is_active: true
			},
			attributes: ['id', 'name']
		};

		const company_association = {
			association: 'company',
			required: false,
			where: {
				is_active: true
			},
			include: [organization_association],
			attributes: ['id', 'name']
		};

		const sector_association = {
			association: 'sector',
			required: false,
			where: {
				is_active: true
			},
			include: [company_association],
			attributes: ['id', 'name']
		};

		const line_association = {
			association: 'line',
			required: false,
			include: [sector_association],
			attributes: ['id', 'name']
		};

		const workstations_association = {
			association: 'workstations',
			required: false,
			include: [line_association],
			attributes: ['id', 'name']
		};

		const stress_level_association = {
			association: 'stress_level',
			required: false,
			attributes: ['id', 'name', 'description']
		};

		const frequency_association = {
			association: 'frequency',
			required: false,
			attributes: ['id', 'name', 'description']
		};

		const total_task_duration_association = {
			association: 'total_task_duration',
			required: false,
			attributes: ['id', 'name', 'description']
		};

		const bera_step_key_result_association = {
			association: 'bera_step_key_result',
			required: true,
			include: [stress_level_association, frequency_association, total_task_duration_association],
			attributes: [
				'id',
				'task_rpn',
				'pre_populate',
				'job_element',
				'bera_report_id',
				'video_analysis_stress_level_id',
				'stress_level_id',
				'video_analysis_frequency_id',
				'frequency_id',
				'video_analysis_total_task_duration_id',
				'total_task_duration_id',
				'custom_report_step_key_id'
			]
		};

		return {
			task_association,
			niosh_association,
			kim_mho_association,
			evaluator_association,
			strain_index_association,
			workstations_association,
			bera_step_key_result_association
		};
	}

	#mapBeraReport(bera_report, existing_bera_report) {
		const step_key_results = bera_report.bera_step_key_result.map((result) => {
			return {
				id: result.id,
				step_id: result?.custom_report_step_key?.step?.id,
				step_key_id: result?.custom_report_step_key?.id,
				stress_level_id: result?.stress_level_id,
				stress_level_score: result?.stress_level?.score,
				frequency_id: result?.frequency_id,
				frequency_score: result?.frequency?.score,
				total_task_duration_id: result?.total_task_duration_id,
				total_task_duration_score: result?.total_task_duration?.score,
				job_element: result?.job_element,
				task_rpn: result?.task_rpn
			};
		});

		const { consolidated, task_id, task_time, task_time_format, has_known_injury, collection_date } =
			existing_bera_report;

		const formatted_task_time = task_time_format === TASK_TIME_FORMAT.MINUTES ? task_time / 60 : task_time;

		const mapped_bera_report = {
			id: existing_bera_report.id,
			file_id: existing_bera_report.file_id,
			consolidated,
			task_id,
			task_time: formatted_task_time,
			task_time_format,
			has_known_injury,
			collection_date,
			step_key_results
		};
		return mapped_bera_report;
	}

	#setFindByFileIdAssociations() {
		const custom_report_step_association = {
			association: 'step',
			required: true
		};

		const custom_report_step_key_association = {
			association: 'custom_report_step_key',
			required: false,
			include: [custom_report_step_association]
		};

		const stress_level_association = {
			association: 'stress_level',
			required: false,
			attributes: ['id', 'name', 'description', 'score']
		};

		const frequency_association = {
			association: 'frequency',
			required: false,
			attributes: ['id', 'name', 'description', 'score']
		};

		const total_task_duration_association = {
			association: 'total_task_duration',
			required: false,
			attributes: ['id', 'name', 'description', 'score']
		};

		const bera_step_key_result_association = {
			association: 'bera_step_key_result',
			required: false,
			include: [
				stress_level_association,
				frequency_association,
				total_task_duration_association,
				custom_report_step_key_association
			],
			attributes: [
				'id',
				'task_rpn',
				'pre_populate',
				'job_element',
				'bera_report_id',
				'video_analysis_stress_level_id',
				'stress_level_id',
				'video_analysis_frequency_id',
				'frequency_id',
				'video_analysis_total_task_duration_id',
				'total_task_duration_id'
			]
		};

		return bera_step_key_result_association;
	}

	async #generatedPDF(data) {
		const { key } = await this.lambda.call(data);

		if (!key) {
			throw new AppError(RESPONSE_ERROR_ENTITIES.REPORT.FAIL_CREATE);
		}

		return key;
	}

	async #generateDownloadURL({ fileName, Bucket, Key }) {
		const url = await this.storage.createSignatureDownload({ fileName, Bucket, Key });

		if (!url) {
			throw new AppError(RESPONSE_ERROR_ENTITIES.FILE.FAILED_CREATE_DOWNLOAD_URL);
		}

		return url;
	}

	#removeExtension(fileName) {
		return fileName?.substring(0, fileName.lastIndexOf('.')) || fileName;
	}

	#formatFileKey(file_name, prefix, report) {
		const [, file_legacy] = file_name.split('/');

		// for file lagacy;
		if (!file_name.includes('/')) {
			return `${prefix}/${report}/${file_name}.pdf`;
		}

		return `${prefix}/${report}/${file_legacy}.pdf`;
	}

	#normalizeKimMhoData(mapped_bera_report) {
		return mapped_bera_report?.tools?.kim_mho
			? {
					duration: mapped_bera_report?.tools?.kim_mho?.duration,
					risk_load: physicalOverloadMap[mapped_bera_report?.tools?.kim_mho?.risk_load]?.PHYSICAL_OVERLOAD,
					risk_score: mapped_bera_report?.tools?.kim_mho?.risk_score,
					force_transfer: forceTransferMap[mapped_bera_report?.tools?.kim_mho?.force_transfer],
					left_force_intensity: forceHandsMap[mapped_bera_report?.tools?.kim_mho?.left_force_intensity],
					right_force_intensity: forceHandsMap[mapped_bera_report?.tools?.kim_mho?.right_force_intensity],
					possible_health_consequences:
						physicalOverloadMap[mapped_bera_report?.tools?.kim_mho?.risk_load]?.HEALTH_CONSEQUENCES,
					risk: riskLoadMap[mapped_bera_report?.tools?.kim_mho?.risk_load],
					measurements: physicalOverloadMap[mapped_bera_report?.tools?.kim_mho?.risk_load]?.MEASURES
				}
			: undefined;
	}

	async #getExistingBeraJobSummaryFromDatabase(id) {
		const include_strain_index = {
			association: 'strain_index',
			required: false,
			where: {
				is_active: true
			},
			attributes: ['id', 'score_left_rsi', 'score_right_rsi']
		};

		const include_file = {
			association: 'file',
			required: true,
			where: {
				is_active: true
			},
			attributes: ['id'],
			include: [include_strain_index]
		};

		const include_weighted_averages = {
			association: 'bera_weighted_average',
			attributes: ['id', 'average_type', 'average', 'bera_job_summary_id']
		};

		const include_bera_step_key_result = {
			association: 'bera_step_key_result',
			required: false,
			attributes: ['id', 'task_rpn', 'custom_report_step_key_id']
		};

		const include_bera_report = {
			association: 'bera_report',
			attributes: ['id', 'rpn', 'task_time', 'task_time_format', 'bera_job_summary_id'],
			include: [include_bera_step_key_result, include_file]
		};

		const existing_bera_job_summary = await this.bera_job_repository.findByPk(id, {
			include: [include_bera_report, include_weighted_averages],
			attributes: ['id', 'report_name', 'total_time', 'overall_score', 'updated_at']
		});

		return existing_bera_job_summary;
	}

	getTaskTime(cycle, language) {
		const formatter = new Intl.NumberFormat(language, {
			minimumFractionDigits: 0,
			maximumFractionDigits: 2
		});

		if (cycle?.task_time_format === TASK_TIME_FORMAT.SECONDS) {
			const task_time = cycle?.task_time / 60;
			const time_format = task_time < 2 ? 'minute' : 'minutes';
			return `${formatter.format(task_time)} ${time_format}`;
		}

		const time_format = cycle?.task_time < 2 ? 'minute' : 'minutes';
		return `${formatter.format(cycle?.task_time)} ${time_format}`;
	}
}
