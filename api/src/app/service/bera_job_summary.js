import _ from 'lodash';
import config from 'config';
import { AppError, logger, Util } from '../helpers/index.js';
import { BeraJobSummary, BeraScoresCalculator, BeraJobSummaryFiles } from '../entities/index.js';
import { ERROR_RESPONSE_ENTITIES_ENUM, ERROR_RESPONSE_ENUM } from '../util/enum.js';
import { Op } from 'sequelize';
import { StorageContext } from '../utils/storage_context.js';

const bucket = config.get('App.bucket');

export class BeraJobSummaryService {
	constructor({
		repository,
		bera_weighted_average_repository,
		bera_report_repository,
		bera_job_summary_files_repository
	}) {
		this.repository = repository;
		this.bera_job_summary = new BeraJobSummary();
		this.bera_weighted_average_repository = bera_weighted_average_repository;
		this.bera_report_repository = bera_report_repository;
		this.bera_job_summary_files_repository = bera_job_summary_files_repository;
	}

	async create(payload) {
		logger.info('[BeraJobSummary] service - create init');
		const { total_time, report_name, overall_score, comment, cycle_id, user_id } = payload;
		let transaction;
		try {
			transaction = await this.repository.db.sequelize.transaction();
			const created_bera_job_summary = await this.repository.create(
				{
					total_time,
					report_name,
					overall_score,
					comment,
					cycle_id,
					user_id
				},
				transaction
			);

			if (!created_bera_job_summary) {
				throw new AppError(ERROR_RESPONSE_ENUM.INTERNAL_SERVER_ERROR);
			}

			await transaction.commit();
			logger.info('[BeraJobSummary] service - create finish');
			return created_bera_job_summary;
		} catch (error) {
			logger.error('[BeraJobSummary] service - create error', { error });
			await transaction?.rollback();
			throw error;
		}
	}

	async update(payload) {
		logger.info('[BeraJobSummary] service - update init');
		let transaction;
		try {
			transaction = await this.repository.db.sequelize.transaction();
			const updated_bera_job_summary = await this.repository.update(
				{
					...payload
				},
				{
					where: {
						id: payload.id
					},
					transaction
				}
			);

			if (!updated_bera_job_summary) {
				throw new AppError(ERROR_RESPONSE_ENUM.INTERNAL_SERVER_ERROR);
			}

			await transaction.commit();
			const existing_bera_job_summary = await this.repository.findByPk(payload.id);
			logger.info('[BeraJobSummary] service - update finish');
			return existing_bera_job_summary;
		} catch (error) {
			logger.error('[BeraJobSummary] service - update error', { error });
			await transaction?.rollback();
			throw error;
		}
	}

	async show(params) {
		logger.info('[BeraJobSummary] service - show init', { params });
		const { id, company_id } = params;

		const { include_bera_report, include_cycle, include_bera_job_summary_files } =
			this.bera_job_summary.setBeraJobSummaryInclude(company_id);

		const existing_bera_job_summary = await this.repository.findByPk(id, {
			include: [include_bera_report, include_cycle, include_bera_job_summary_files],
			attributes: ['id', 'report_name', 'total_time', 'overall_score', 'updated_at'],
			order: [['bera_report', 'file', 'original_name', 'ASC']]
		});

		if (!existing_bera_job_summary) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.BERA_JOB_SUMMARY.NOT_FOUND);
		}

		const result = this.bera_job_summary.mapBeraJobSummary(existing_bera_job_summary);

		logger.info('[BeraJobSummary] service - show finish');
		return result;
	}

	async delete(id) {
		logger.info('[BeraJobSummary] service - delete init');
		let transaction;
		try {
			const existing_bera_job_summary = await this.repository.findByPk(id);

			if (!existing_bera_job_summary) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.BERA_JOB_SUMMARY.NOT_FOUND);
			}

			transaction = await this.repository.db.sequelize.transaction();

			await this.bera_report_repository.delete({
				where: {
					bera_job_summary_id: id
				},
				transaction
			});

			await this.bera_job_summary_files_repository.delete({
				where: {
					bera_job_summary_id: id
				},
				transaction
			});

			await this.repository.delete(
				{
					where: {
						id
					}
				},
				transaction
			);
			await transaction.commit();
			const deleted_bera_job_summary = await this.repository.findByPk(id, { paranoid: false });
			logger.info('[BeraJobSummary] service - delete finish');
			return deleted_bera_job_summary;
		} catch (error) {
			logger.error('[BeraJobSummary] service - delete error', { error });
			await transaction?.rollback();
			throw error;
		}
	}

	async fetchResults(params) {
		logger.info('[BeraJobSummary] service - fetchResults init');
		const { id, company_id, language } = params;
		const existing_bera_job_summary_report = await this.repository.findByPk(id);
		if (!existing_bera_job_summary_report) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.BERA_JOB_SUMMARY.NOT_FOUND);
		}

		const { include_bera_report, include_cycle, include_weighted_averages } =
			this.bera_job_summary.setBeraResultInclude(company_id);
		const existing_bera_job_summary = await this.repository.findByPk(id, {
			include: [include_bera_report, include_cycle, include_weighted_averages],
			attributes: [
				'id',
				'report_name',
				'total_time',
				'overall_score',
				'weighted_rsi',
				'updated_at',
				'comment',
				'gns'
			]
		});

		if (!existing_bera_job_summary) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.BERA_JOB_SUMMARY.RESULT_NOT_FOUND);
		}

		const bera_job = existing_bera_job_summary.toJSON();

		const mapped_results = this.bera_job_summary.mapResults(bera_job, language);

		logger.info('[BeraJobSummary] service - fetchResults finish');
		return mapped_results;
	}

	async findAllForBeraSummaryList(payload) {
		logger.info('[BeraJobSummary] service - findAllForBeraSummaryList init');
		const { company_id, limit, offset, filter } = payload;
		const {
			filter_company,
			filter_sector,
			filter_line,
			filter_workstation,
			filter_cycle,
			filter_task,
			filter_report_name,
			filter_created_at,
			filter_updated_at,
			filter_overall_score,
			filter_rpn
		} = this.bera_job_summary.setBeraJobSummaryListFilter(filter);

		const { include_bera_report, include_cycle, include_bera_job_summary_files } =
			this.bera_job_summary.setBeraJobSummaryInclude(company_id, {
				filter_company,
				filter_sector,
				filter_line,
				filter_workstation,
				filter_cycle,
				filter_task,
				filter_rpn
			});
		let find_all = {
			include: [include_bera_report, include_cycle, include_bera_job_summary_files],
			where: {},
			distinct: true,
			attributes: ['id', 'report_name', 'total_time', 'overall_score', 'updated_at'],
			order: [['updated_at', 'DESC']],
			limit,
			offset: limit * offset
		};

		if (filter_report_name) {
			_.set(find_all, 'where', {
				...find_all.where,
				...filter_report_name
			});
		}

		if (filter_created_at) {
			_.set(find_all, 'where', {
				...find_all.where,
				...filter_created_at
			});
		}

		if (filter_updated_at) {
			_.set(find_all, 'where', {
				...find_all.where,
				...filter_updated_at
			});
		}

		if (filter_overall_score) {
			_.set(find_all, 'where', {
				...find_all.where,
				...filter_overall_score
			});
		}

		if (StorageContext.getStore()?.environment === 'sandbox') {
			_.set(find_all, 'where', {
				...find_all.where,
				user_id: StorageContext.getStore().user_id
			});
		}

		const existing_bera_job_summaries = await this.repository.findAndCountAll(find_all);
		const bera_job_summaries_rows = existing_bera_job_summaries?.rows;
		const mapped_bera_job_summaries = this.bera_job_summary.mapBeraJobSummariesList(bera_job_summaries_rows);
		logger.info('[BeraJobSummary] service - findAllForBeraSummaryList finish');
		return {
			count: existing_bera_job_summaries.count,
			rows: mapped_bera_job_summaries
		};
	}

	async index(user_id) {
		logger.info('[BeraJobSummary] service - index init');
		const existing_bera_job_summaries = await this.repository.findAll({
			where: {
				user_id
			}
		});
		logger.info('[BeraJobSummary] service - index finish');
		return existing_bera_job_summaries;
	}

	async downloadPDF(payload) {
		logger.info('[BeraJobSummary] service - downloadPDF init');

		const { organization_id, company_id, bera_job_summary_id, locale } = payload;

		const { include_bera_report, include_cycle, include_weighted_averages } =
			this.bera_job_summary.setBeraResultInclude(company_id);

		const existing_bera_job_summary = await this.repository.findByPk(bera_job_summary_id, {
			include: [include_bera_report, include_cycle, include_weighted_averages],
			attributes: [
				'id',
				'report_name',
				'total_time',
				'overall_score',
				'comment',
				'weighted_rsi',
				'updated_at',
				'gns'
			]
		});

		if (!existing_bera_job_summary) {
			throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.BERA_JOB_SUMMARY.RESULT_NOT_FOUND);
		}

		const bera_job = existing_bera_job_summary.toJSON();

		const mapped_results = this.bera_job_summary.mapResults(bera_job, locale);

		const prefix = Util.getPrefix(organization_id, company_id);
		const file_key = this.bera_job_summary.formatFileKey(bera_job.report_name, prefix, 'bera-job-summary');

		const target_file = `bera-job-summary-${bera_job.report_name}.pdf`;

		const language = locale.replace('-', '_');

		const config = {
			functionName: 'pdf_container',
			data: {
				...mapped_results,
				id: bera_job_summary_id,
				report_type: 'john-deere',
				type: 'john-deere/bera-job-result',
				title: 'Cycle scores - B.E.R.A.',
				sub_column_titles: [
					'Striking parts',
					'Hand activities',
					'Lifting',
					'Push/Pull One Hand',
					'Push/Pull Two Hand',
					'Push/Pull Cart',
					'Vibration',
					'Inadequate Clearance',
					'Contact Stress'
				],
				comment: bera_job.comment,
				language: language,
				bucket: bucket,
				prefix: prefix,
				file_key: file_key
			}
		};

		const key = await this.bera_job_summary.generatedPDF(config);
		const url = await this.bera_job_summary.generateDownloadURL({
			fileName: target_file,
			Bucket: bucket,
			Key: key
		});

		logger.info('[BeraJobSummary] service - downloadPDF finish');
		return { url };
	}

	async calculateOverallScore(id) {
		logger.info('[BeraJobSummary] service - calculateOverallScore init');
		let transaction;
		try {
			const { include_bera_report, include_weighted_averages } =
				this.bera_job_summary.setCalculateOverallScoreInclude();
			const existing_bera_job_summary = await this.repository.findByPk(id, {
				include: [include_bera_report, include_weighted_averages],
				attributes: ['id', 'report_name', 'total_time', 'overall_score', 'updated_at']
			});

			if (!existing_bera_job_summary) {
				throw new AppError(ERROR_RESPONSE_ENTITIES_ENUM.BERA_JOB_SUMMARY.RESULT_NOT_FOUND);
			}

			const {
				total_time,
				overall_score,
				weighted_rsi,
				bera_weighted_average,
				mapped_weighted_average,
				step_keys_scores_array,
				gns
			} = new BeraScoresCalculator(existing_bera_job_summary).calculate();

			transaction = await this.repository.db.sequelize.transaction();
			await this.repository.update(
				{
					total_time, // seconds
					overall_score,
					weighted_rsi,
					gns
				},
				{
					where: {
						id
					},
					transaction
				}
			);

			if (!bera_weighted_average || bera_weighted_average.length === 0) {
				await this.bera_weighted_average_repository.bulkCreate(mapped_weighted_average, {
					transaction,
					updateOnDuplicate: ['average']
				});
			} else {
				await Promise.all(
					step_keys_scores_array.map((step_key, index) =>
						this.bera_weighted_average_repository.update(
							{
								average_type: 'RPN',
								average: step_key[index].score
							},
							{
								where: {
									average_type: 'RPN',
									custom_report_step_key_id: step_key[index].step_key_id,
									bera_job_summary_id: id
								},
								transaction
							}
						)
					)
				);
			}

			await transaction.commit();

			const updated_bera_job_summary = await this.repository.findByPk(id);

			logger.info('[BeraJobSummary] service - calculateOverallScore finish');
			return updated_bera_job_summary;
		} catch (error) {
			logger.error('[BeraJobSummary] service - calculateOverallScore error', { error });
			await transaction?.rollback();
			throw error;
		}
	}

	async bindFilesToBeraJobSummary(params) {
		logger.info('[BeraJobSummary] service - bindFilesToBeraJobSummary init', { params });
		const { bera_job_summary_id, file_ids } = params;

		const all_files = await this.bera_job_summary_files_repository.findAll({
			where: {
				bera_job_summary_id
			},
			paranoid: false
		});

		const beraJobSummaryFiles = new BeraJobSummaryFiles({ bera_job_summary_id, file_ids });
		const { files_to_restore, new_files, files_to_delete } =
			beraJobSummaryFiles.getBeraJobSummaryFilesFilters(all_files);

		if (files_to_restore.length > 0) {
			await this.bera_job_summary_files_repository.restore({
				where: {
					id: { [Op.in]: files_to_restore.map((file) => file.id) }
				}
			});
		}

		if (new_files.length > 0) {
			await this.bera_job_summary_files_repository.bulkCreate(new_files);
		}

		if (files_to_delete.length > 0) {
			logger.info('[BeraJobSummary] service - unbindFileFromBeraJobSummary files', files_to_delete.length);
			await this.bera_job_summary_files_repository.delete({ where: { id: { [Op.in]: files_to_delete } } });
		}

		logger.info('[BeraJobSummary] service - bindFilesToBeraJobSummary finish');
		return await this.bera_job_summary_files_repository.findAll({
			where: { bera_job_summary_id, deleted_at: null }
		});
	}

	async unbindFileFromBeraJobSummary(params) {
		logger.info('[BeraJobSummary] service - unbindFileFromBeraJobSummary init');
		const { bera_job_summary_id, file_id } = params;
		await this.bera_job_summary_files_repository.delete({ where: { bera_job_summary_id, file_id } });
		logger.info('[BeraJobSummary] service - unbindFileFromBeraJobSummary finish');
	}
}
