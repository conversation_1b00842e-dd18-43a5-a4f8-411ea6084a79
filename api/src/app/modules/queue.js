import AWS from 'aws-sdk';
import config from 'config';
import { logger } from '../helpers/logger.js';
import { <PERSON>rror<PERSON>and<PERSON> } from '../utils/errors.js';

import { v4 as uuidv4 } from 'uuid';
import db from '../models/index.js';
import SQSConsumer from 'sqs-consumer';

import SocketService, { formatRoom } from '../modules/realtime.js';
import FileRepository from '../repository/fileRepository.js';

const { Consumer } = SQSConsumer;

const optionsQueue = {
	queueUrlPrefix: config.get('App.queue.baseEnpoint'),
	queueName: config.get('App.queue.subscribe.toProcess.queueName')
};

// const optionsQueue = {
// 	queueUrlPrefix: config.get('App.queue.subscribe.statusFile.queueUrlPrefix'),
// 	// queueName: config.get('App.queue.subscribe.statusFile.queueName')
// 	queueName: config.get('App.queue.fifo.endpoint')
// };

// const optionsQueue = {
// 	queueUrlPrefix: config.get('App.queue.baseEnpoint'),
// 	queueName: config.get('App.queue.subscribe.toProcess.queueName')
// };

// const root = path.resolve(__dirname, "..", "..", "..");

// if (process.env.NODE_ENV !== "production") {
// 	AWS.config.loadFromPath(`${root}/config/aws.json`);
// }

const Types = {
	JSON: 'json',
	VIDEO: 'video'
};

const EnumStatus = {
	EXTRACTED_DATA: 'EXTRACTED_DATA',
	NOT_PROCESSED: 'NOT_PROCESSED',
	PROCESSING: 'PROCESSING',
	PROCESSED: 'PROCESSED'
};

class Queue1 {
	constructor() {
		this.client = new AWS.SQS({
			credentials: {
				accessKeyId: config.get('App.aws.accessKeyId'),
				secretAccessKey: config.get('App.aws.secretAccessKey')
			},
			region: 'us-east-1'
		});
		this.fileRepository = new FileRepository(db);
	}

	extractedAttributes(payload) {
		let response = {};
		Object.keys(payload).map((item) => (response[item] = payload[item].StringValue));
		return response;
	}

	async sendMessage(parameters) {
		const { bucket, Key, organization, company, id_file, blur_face, tool, logo } = parameters;

		try {
			const params = {
				MessageAttributes: {
					idFile: {
						DataType: 'String',
						StringValue: id_file
					},
					companyId: {
						DataType: 'String',
						StringValue: company
					},
					organizationId: {
						DataType: 'String',
						StringValue: organization
					},
					originBucket: {
						DataType: 'String',
						StringValue: bucket
					},
					objectName: {
						DataType: 'String',
						StringValue: Key
					},
					blur_face: {
						DataType: 'String',
						StringValue: blur_face
					},
					tool: {
						DataType: 'String',
						StringValue: tool
					},
					logo: {
						DataType: 'String',
						StringValue: logo || 'kinebot'
					}
				},
				MessageBody: JSON.stringify(''),
				// QueueUrl:
				// 	"https://sqs.us-east-1.amazonaws.com/************/KinebotToProcess.fifo",
				// QueueUrl: 'https://sqs.us-east-1.amazonaws.com/************/QueuePDIHomolog.fifo',
				QueueUrl: optionsQueue.queueUrlPrefix + optionsQueue.queueName,
				MessageGroupId: 'KinebotToProcess',
				MessageDeduplicationId: uuidv4()
			};

			const data = await this.client.sendMessage(params).promise();

			return data;
		} catch (error) {
			console.log(error, 'error');
		}
	}

	toReceiveMessage() {
		const app = Consumer.create({
			queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/KinebotOnSuccess',
			handleMessage: async (message) => {
				const { type, idFile, duration, companyId, objectName, organizationId } = this.extractedAttributes(
					message.MessageAttributes
				);

				const transaction = await this.fileRepository.sequelize.transaction();

				try {
					const file = await db.File.findOne({
						where: {
							id: idFile,
							company_id: companyId,
							organization_id: organizationId
						}
					});

					if (!file) {
						throw new ErrorHandler('File not found');
					}

					if (type === Types.JSON) {
						await file.update(
							{
								status: EnumStatus.EXTRACTED_DATA,
								file_processed: objectName,
								duration: parseInt(duration)
							},
							{ transaction }
						);
					} else {
						await file.update(
							{
								status: EnumStatus.PROCESSED,
								file_processed: objectName,
								duration: parseInt(duration)
							},
							{ transaction }
						);
					}

					const socket = SocketService.getInstance();

					const payload = {
						organization: file.organization_id,
						company: file.company_id
					};

					const room = formatRoom(payload);

					socket.to(room).emit('getUpdateFile', file);

					await transaction.commit();
				} catch (err) {
					await transaction.rollback();
					logger.error(err);
				}
			},
			messageAttributeNames: ['type', 'idFile', 'duration', 'companyId', 'objectName', 'organizationId'],
			sqs: this.client
		});

		app.on('error', (error) => logger.error(error));

		app.on('processing_error', (error) => logger.error(error));

		app.start();
	}
}

export default new Queue1();

class Queue {
	constructor() {
		this.queue = new AWS.SQS({
			credentials: {
				accessKeyId: config.get('App.aws.accessKeyId'),
				secretAccessKey: config.get('App.aws.secretAccessKey')
			},
			region: 'us-east-1'
			// region: config.get("App.queue.region"),
			// endpoint: config.get("App.queue.endpoint"),
		});
	}

	async sendMensage(parameters) {
		const { organization, company } = parameters;

		try {
			const payload = {
				MessageAttributes: {
					idFile: {
						DataType: 'String',
						StringValue: uuidv4()
					}
				},
				MessageBody: JSON.stringify(''),
				QueueUrl: config.get('App.queue.fifo.endpoint'),
				// MessageGroupId: "myMessageGroupId",
				MessageGroupId: 'Group01',
				MessageDeduplicationId: uuidv4()
			};

			const response = await this.queue.sendMessage(payload).promise();

			return response;
		} catch (err) {
			throw err;
		}

		// const params = {
		// MessageAttributes: {
		//   'idFile': {
		//     DataType: 'String',
		//     StringValue: id_file
		//   },
		//     'companyId': {
		//       DataType: 'String',
		//       StringValue: company
		//     },
		//     'organizationId': {
		//       DataType: 'String',
		//       StringValue: organization
		//     },
		//     'originBucket': {
		//       DataType: 'String',
		//       StringValue: bucket
		//     },
		//     'objectName': {
		//       DataType: 'String',
		//       StringValue: Key
		//     }
		//   },
		// MessageBody: JSON.stringify(''),
		// QueueUrl: 'https://sqs.us-east-1.amazonaws.com/************/KinebotToProcess.fifo',
		// MessageGroupId: "myMessageGroupId",
		// MessageDeduplicationId: uuidv4()
		// };

		// const data = await this.client.sendMessage(params).promise();

		// return data;
	}
}

const queue2 = new Queue();

export { queue2 };
