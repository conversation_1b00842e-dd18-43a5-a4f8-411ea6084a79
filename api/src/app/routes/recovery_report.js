import { Router } from 'express';
import database from '../models/index.js';
import { RecoveryReportFactory } from '../factories/recovery_report.js';

const router = Router();
const { schema, middleware, controller } = RecoveryReportFactory.createInstance(database);

router.post('/create', middleware.validateSchema(schema.create), middleware.checkingPermissions, controller.create);
router.put('/update', middleware.validateSchema(schema.update), middleware.checkingPermissions, controller.update);
router.get(
	'/:organization_id/:company_id/:file_id/:type',
	middleware.validateSchema(schema.findOne),
	middleware.checkingPermissions,
	controller.findOne
);

export { router as recoveryReportRouter };
