import { Router } from 'express';

import UserAccessMiddleware from '../middlewares/userAccess.js';
import FileMiddlewareOld from '../middlewares/file.js';
import { FileFactory } from '../factories/file.js';
import database from '../models/index.js';

const router = Router();

// Middlewares
const fileMiddleware = new FileMiddlewareOld();
const userAccess = new UserAccessMiddleware();

const { schema, middleware, controller, oldMiddleware, controllerOld } = FileFactory.createInstance(database);

router.get(
	'/percent_risk/:organization_id/:company_id',
	fileMiddleware.defaultParameters(),
	fileMiddleware.validation,
	userAccess.validationUser,
	controllerOld.percentRisk
);
router.get(
	'/sector/:organization_id/:company_id',
	fileMiddleware.defaultParameters(),
	fileMiddleware.validation,
	userAccess.validationUser,
	controllerOld.videoBySector
);
router.get(
	'/reports-and-tools/:file_id',
	middleware.getReportsAndTools,
	middleware.checkingACL,
	controller.getReportsAndTools
);
router.get(
	'/risk_by_time/:organization_id/:company_id',
	fileMiddleware.defaultParameters(),
	fileMiddleware.validation,
	userAccess.validationUser,
	controllerOld.riskByTime
);
router.get(
	'/:organization_id/:company_id/',
	oldMiddleware.validateSchema(schema.index),
	userAccess.validationUser,
	controller.index
);
router.get(
	'/custom-reports',
	middleware.getCustomReportsFiles,
	middleware.checkingACL,
	controller.getCustomReportsFiles
);

router.delete('/delete', middleware.deleteFiles, middleware.checkingACL, controller.deleteFile);

router.post(
	'/download/angles',
	oldMiddleware.validateSchema(schema.downloadAngles),
	oldMiddleware.checkingPermissions,
	controller.downloadAngles
);

router.get(
	'/video_counter/:organization_id/:company_id',
	oldMiddleware.validateSchema(schema.defaultParameters),
	oldMiddleware.checkingPermissions,
	controller.countFile
);

router.get(
	'/duration_total/:organization_id/:company_id',
	oldMiddleware.validateSchema(schema.defaultParameters),
	oldMiddleware.checkingPermissions,
	controller.countOfMinutesProcessed
);

export default router;
