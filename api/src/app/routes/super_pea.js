import { Router } from 'express';
import database from '../models/index.js';
import { SuperPEAReportFactory } from '../factories/index.js';

const router = Router();
const { controller, middleware } = SuperPEAReportFactory.createInstance(database);

router.post('/', middleware.create, middleware.checkingACL, controller.create);
router.patch('/:super_pea_id', middleware.delete, middleware.checkingACL, controller.delete);
router.get('/:organization_id/:company_id', middleware.list, middleware.checkingACL, controller.list);
router.post('/download-pdf', middleware.downloadPDF, middleware.checkingACL, controller.downloadPDF);

export { router as superPEARouter };
