import acl from 'express-acl';
import { Router } from 'express';

import planRouter from './plan.js';
import fileRouter from './file.js';
import userAccessRouter from './userAccess.js';
import reportRouter from '../routes/report.js';
import sectorRouter from '../routes/sector.js';

// ***** Refactors *****
import { swaggerDocs } from '../swagger/setup.js';
import organizationRouter from './organization.js';
import { loggerRequest } from '../helpers/logger.js';
import { ErrorHandler } from '../middlewares/errors.js';
import { SandboxEnvironmentMiddleware } from '../middlewares/sandbox_environment.js';
import { ACLAuthorize, authMiddleware } from '../helpers/authorize.js';

import { userRouter } from './user.js';
import { authRouter } from './auth.js';
import { taskRouter } from './task.js';
import { lineRouter } from './line.js';
import { beraRouter } from './bera.js';
import { seraRouter } from './sera.js';
import { cycleRouter } from './cycle.js';
import { uploadRouter } from './upload.js';
import { companyRouter } from './company.js';
import { customerRouter } from './customer.js';
import { activityRouter } from './activity.js';
import { servicesRouter } from './services.js';
import { superPEARouter } from './super_pea.js';
import { evaluatorRouter } from './evaluator.js';
import { dashboardRouter } from './dashboard.js';
import { actionPlanRouter } from './action_plan.js';
import { workstationRouter } from './workstation.js';
import { customReportRouter } from './custom_report.js';
import { customizationRouter } from './customization.js';
import { useManagementRouter } from './userManagement.js';
import { ergonomicToolRouter } from './ergonomic_tool.js';
import { systemOfUnitsRouter } from './system_of_units.js';
import { recoveryReportRouter } from './recovery_report.js';
import { twoFARouter } from './two_factor_authentication.js';
import { workerSelfEvaluationRouter } from './worker_self_evaluation.js';
import { sandboxRouter } from './sandbox.js';
import { dataExtractionRouter } from './data-extraction.js';

const router = Router();

swaggerDocs(router);
acl.config(ACLAuthorize);
router.use(loggerRequest);
router.use('/auth', authRouter);
router.use('/services', servicesRouter);
router.use(authMiddleware);
router.use(acl.authorize);
router.use('/2fa', twoFARouter);
router.use('/user', userRouter);
router.use('/plan', planRouter);
router.use('/company', companyRouter);
router.use('/organization', organizationRouter);
router.use('/customer', customerRouter);
router.use('/dashboard', dashboardRouter);
router.use('/user_management', useManagementRouter);
router.use('/user_access', userAccessRouter);
router.use('/customization', customizationRouter);

//Sandbox
router.use(SandboxEnvironmentMiddleware.setEnvironment);
router.use('/file', fileRouter);
router.use('/line', lineRouter);
router.use('/task', taskRouter);
router.use('/sera', seraRouter);
router.use('/bera', beraRouter);
router.use('/cycle', cycleRouter);
router.use('/upload', uploadRouter);
router.use('/sector', sectorRouter);
router.use('/report', reportRouter);
router.use('/activity', activityRouter);
router.use('/super-pea', superPEARouter);
router.use('/evaluator', evaluatorRouter);
router.use('/action_plan', actionPlanRouter);
router.use('/workstation', workstationRouter);
router.use('/custom-report', customReportRouter);
router.use('/ergonomic-tool', ergonomicToolRouter);
router.use('/system-of-units', systemOfUnitsRouter);
router.use('/recovery_report', recoveryReportRouter);
router.use('/worker-self-evaluation', workerSelfEvaluationRouter);
router.use('/sandbox', sandboxRouter);
//End Sandbox
router.use('/data-extraction', dataExtractionRouter);
router.use(ErrorHandler.handler);

export { router as appRoutes };
