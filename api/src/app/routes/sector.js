import { Router } from 'express';

import { SectorFactory } from '../factories/sector.js';
import SectorMiddleware from '../middlewares/sector.js';
import UserAccess from '../middlewares/userAccess.js';
import database from '../models/index.js';

const router = Router();

const { middleware, schema, middleware_DEPRECATED, controller, sectorController } =
	SectorFactory.createInstance(database);

// Middlewares
const sectorMiddleware = new SectorMiddleware();
const userAccessMiddleware = new UserAccess();

router.get(
	'/mosaic/:company_id',
	middleware.findAllWithWorstScore,
	middleware.checkingACL,
	controller.findAllWithWorstScore
);

router.get('/list', middleware.findAllByCompanyId, middleware.checkingACL, controller.findAllByCompanyId);
router.get('/:organization_id/:company_id', middleware.index, middleware.checkingACL, controller.index);

router.post('/create', middleware.create, middleware.checkingACL, controller.create);
router.put('/', middleware.update, middleware.checkingACL, controller.update);
router.delete('/', middleware.delete, middleware.checkingACL, controller.delete);

router.get(
	'/total/:organization_id/:company_id',
	middleware_DEPRECATED.validateSchema(schema.defaultParameters),
	middleware_DEPRECATED.checkingPermissions,
	controller.countSector
);

router.get(
	'/critical_sector/:organization_id/:company_id',
	middleware_DEPRECATED.validateSchema(schema.defaultParameters),
	middleware_DEPRECATED.checkingPermissions,
	controller.criticalSector
);

router.post(
	'/usage_check',
	sectorMiddleware.usageCheck(),
	sectorMiddleware.validation,
	userAccessMiddleware.validationUser,
	sectorController.usageCheck
);

router.get(
	'/total_critical/:organization_id/:company_id',
	sectorMiddleware.defaultParameters(),
	sectorMiddleware.validation,
	userAccessMiddleware.validationUser,
	sectorController.totalCriticalByCompany
);

export default router;
