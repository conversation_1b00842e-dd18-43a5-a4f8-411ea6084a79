import { Router } from 'express';
import database from '../models/index.js';
import { BeraJobSummaryFactory } from '../factories/index.js';

const router = Router();
const { controller, middleware } = BeraJobSummaryFactory.createInstance(database);

router.post(
	'/files/bind',
	middleware.bindFilesToBeraJobSummary,
	middleware.checkingACL,
	controller.bindFilesToBeraJobSummary
);
router.delete(
	'/files',
	middleware.unbindFileFromBeraJobSummary,
	middleware.checkingACL,
	controller.unbindFileFromBeraJobSummary
);
router.post('/', middleware.create, middleware.checkingACL, controller.create);
router.get('/', middleware.index, middleware.checkingACL, controller.index);
router.delete('/:id', middleware.delete, middleware.checkingACL, controller.delete);
router.post(
	'/download-pdf/:bera_job_summary_id',
	middleware.downloadPDF,
	middleware.checkingACL,
	controller.downloadPDF
);
router.post('/:id', middleware.calculateOverallScore, middleware.checkingACL, controller.calculateOverallScore);
router.put('/:id', middleware.update, middleware.checkingACL, controller.update);
router.get('/result/:id', middleware.fetchResults, middleware.checkingACL, controller.fetchResults);
router.get('/list', middleware.findAllForBeraSummaryList, middleware.checkingACL, controller.findAllForBeraSummaryList);
router.get('/:id', middleware.show, middleware.checkingACL, controller.show);

export { router as beraJobSummaryRouter };
