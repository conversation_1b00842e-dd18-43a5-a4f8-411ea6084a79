import { Router } from 'express';
import { ReportFactory } from '../factories/report.js';

// ---------------------------------------------
import ReportMiddleware from '../middlewares/report.js';
import UserAccessMiddleware from '../middlewares/userAccess.js';
import database from '../models/index.js';
import reportControllers from '../controllers/report.js';

const router = Router();
const { controller, newController, middleware: newMiddleware, schema } = ReportFactory.createInstance(database);

//Middlewares
const middleware = new ReportMiddleware();
const userAccess = new UserAccessMiddleware();

router.post('/upload', reportControllers.upload); // Deprecado
router.post('/angles', reportControllers.angles);
router.post('/score_rula/angle_time', controller.scoreRulaAngleTimeReport);

router.post(
	'/ergonomic_movement',
	middleware.ergonomicMovementReport(),
	middleware.validation,
	userAccess.validationUser,
	controller.ergonomicMovementReport
);

// New routes
router.post(
	'/set_parameters',
	middleware.setParameters(),
	middleware.validation,
	userAccess.validationUser,
	controller.setParameters
);

router.get(
	'/score_rula/:organization_id/:company_id/:upload_id',
	middleware.scoreRula(),
	middleware.validation,
	userAccess.validationUser,
	controller.scoreRula
);

router.get(
	'/extracted/:organization_id/:company_id',
	middleware.extractReports(),
	middleware.validation,
	userAccess.validationUser,
	controller.extractedByCompany
);

router.get(
	'/score_rula/movement/file_data/:organization_id/:company_id/:upload_id',
	middleware.scoreRulaMovement(),
	middleware.validation,
	userAccess.validationUser,
	controller.scoreRulaMovementFileDate
);

router.get(
	'/score_rula/angle_time/:organization_id/:company_id/:upload_id',
	middleware.scoreRulaByTime(),
	middleware.validation,
	userAccess.validationUser,
	// reportController.scoreRuleBytime // Alterar nome da função
	controller.scoreRuleBytime
);

router.post(
	'/brief_best/score',
	middleware.briefBestScore(),
	middleware.validation,
	userAccess.validationUser,
	controller.briefBestReport
);

router.post(
	'/brief_best/angles',
	middleware.defaultParameters(),
	middleware.validation,
	userAccess.validationUser,
	controller.briefBestAngles
);

router.post(
	'/brief_best/extract/report',
	middleware.defaultParameters(),
	middleware.validation,
	userAccess.validationUser,
	controller.extractReportBriefBest
);

// Routes in refactoring !!!
router.post(
	'/risk_degree/workstation',
	newMiddleware.validateSchema(schema.riskDegreeWorkstation),
	controller.riskDegreeWorkstation
);

router.post(
	'/angle-by-time/document/pdf',
	newMiddleware.validateSchema(schema.angleByTimeDocumentPDF),
	newMiddleware.verifyLogo,
	newController.angleByTimeDocumentPDF
);

router.post('/rula/risk_degree/body_parts', controller.rulaRiskDegreeBodyParts);

router.post('/document', newMiddleware.verifyLogo, newController.reportGenerator);

export default router;
