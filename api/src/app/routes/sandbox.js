import { Router } from 'express';
import database from '../models/index.js';
import databaseSandbox from '../models/sandbox.js';
import { SandboxFactory } from '../factories/sandbox.js';

const router = Router();
const { schema, middleware, controller } = SandboxFactory.createInstance(database, databaseSandbox);

router.post('/send-report', schema.sendReport, middleware.checkingACL, controller.sendReport);
router.post('/logout', controller.logout);

export { router as sandboxRouter };
