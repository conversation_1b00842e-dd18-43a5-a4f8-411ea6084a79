import { logger } from '../helpers/index.js';

import {
	BeraReportSandboxService,
	CustomReportSandboxService,
	PeaReportSandboxService,
	SuperPeaReportSandboxService,
	NioshReportSandboxService,
	LibertyMutualReportSandboxService,
	BackCompressiveReportSandboxService,
	AngleTimeReportSandboxService,
	KimMhoReportSandboxService,
	RebaReportSandboxService,
	KimPpReportSandboxService,
	RecoveryReportSandboxService,
	StrainIndexReportSandboxService,
	SeraReportSandboxService,
	ActionPlanSandboxService
} from '../service/sandbox/index.js';

export class SandboxServiceStrategy {
	strategy;
	sandbox_strategies = {
		BERA: BeraReportSandboxService,
		CUSTOM: CustomReportSandboxService,
		PEA: PeaReportSandboxService,
		SUPER_PEA: SuperPeaReportSandboxService,
		NIOSH: NioshReportSandboxService,
		LIBERTY_MUTUAL: LibertyMutualReportSandboxService,
		BACK_COMPRESSIVE: BackCompressiveReportSandboxService,
		ANGLE_TIME: AngleTimeReportSandboxService,
		KIM_MHO: KimMhoReportSandboxService,
		KIM_PP: KimPpReportSandboxService,
		REBA: RebaReportSandboxService,
		RECOVERY: RecoveryReportSandboxService,
		STRAIN_INDEX: StrainIndexReportSandboxService,
		SERA: SeraReportSandboxService,
		ACTION_PLAN: ActionPlanSandboxService
	};

	constructor({ kinebot_repository, sandbox_repository }) {
		this.kinebot_repository = kinebot_repository;
		this.sandbox_repository = sandbox_repository;
		this.repository_params = {
			kinebot_repository: this.kinebot_repository,
			sandbox_repository: this.sandbox_repository
		};
	}

	setStrategy(report_type) {
		logger.info('report_type', { report_type });
		const Strategy = this.sandbox_strategies[report_type];
		logger.info('Strategy', { Strategy });
		if (!Strategy) {
			throw new Error(`Strategy ${report_type} not found`);
		}
		this.strategy = new Strategy(this.repository_params);
	}

	async sendReport(id) {
		return this.strategy.sendReport(id);
	}
}
