import { logger } from '../helpers/logger.js';

export class SectorController {
	constructor(service) {
		this.service = service;
	}

	index = async (req, res, next) => {
		logger.info('[Sector] controller - index init');
		const { organization_id, company_id } = req.payload;
		try {
			const parameters = { organization_id, company_id };
			const result = await this.service.index(parameters);
			logger.info('[Sector] controller - index init');
			return res.status(200).send(result);
		} catch (error) {
			next(error);
		}
	};

	findAllByCompanyId = async (req, res, next) => {
		logger.info('[Sector] controller - findAllByCompanyId init');
		const { company_id } = req.payload;
		try {
			const parameters = { company_id };
			const result = await this.service.findAllByCompanyId(parameters);
			logger.info('[Sector] controller - findAllByCompanyId init');
			return res.status(200).send(result);
		} catch (error) {
			next(error);
		}
	};

	findAllWithWorstScore = async (req, res, next) => {
		logger.info('[Sector] controller - findAllWithWorstScore init');
		const { company_id } = req.payload;
		try {
			const result = await this.service.findAllWithWorstScore(company_id);
			logger.info('[Sector] controller - findAllWithWorstScore finish');
			return res.status(200).send(result);
		} catch (error) {
			next(error);
		}
	};

	criticalSector = async (req, res, next) => {
		const {
			params: { organization_id, company_id }
		} = req;
		try {
			const parameters = { organization_id, company_id };
			const result = await this.service.criticalSector(parameters);
			res.status(200).send(result);
		} catch (error) {
			next(error);
		}
	};

	countSector = async (req, res, next) => {
		const {
			params: { organization_id, company_id }
		} = req;
		try {
			const parameters = { organization_id, company_id };
			const result = await this.service.countSector(parameters);
			res.status(200).send(result);
		} catch (error) {
			next(error);
		}
	};

	create = async (req, res, next) => {
		logger.info('[Sector] controller - create init');
		const { name, company_id_creation } = req.payload;
		try {
			const parameters = { name, company_id: company_id_creation };
			const result = await this.service.create(parameters);
			logger.info('[Sector] controller - create finish');
			return res.status(200).send(result);
		} catch (error) {
			next(error);
		}
	};

	update = async (req, res, next) => {
		logger.info('[Sector] controller - update init');
		const { id, name } = req.payload;
		try {
			const parameters = { id, name };
			const result = await this.service.update(parameters);
			logger.info('[Sector] controller - update finish');
			return res.status(200).send(result);
		} catch (error) {
			next(error);
		}
	};

	delete = async (req, res, next) => {
		logger.info('[Sector] controller - delete init');
		const { id } = req.payload;
		try {
			const parameters = { id };
			const result = await this.service.delete(parameters);
			logger.info('[Sector] controller - delete finish');
			return res.status(200).send(result);
		} catch (error) {
			next(error);
		}
	};
}

export default class SectorController2 {
	constructor(service) {
		this.sectorService = service;
	}

	totalCriticalByCompany = async (req, res, next) => {
		const { params } = req;
		try {
			const response = await this.sectorService.totalCriticalByCompany(params);
			return res.status(200).send(response);
		} catch (err) {
			next(err);
		}
	};

	usageCheck = async (req, res, next) => {
		const { company_id, sector_id } = req.body;
		try {
			const response = await this.sectorService.usageCheck(sector_id, company_id);
			return res.status(200).send(response);
		} catch (err) {
			next(err);
		}
	};

	totalByCompany = async (req, res, next) => {
		const { params } = req;
		try {
			const response = await this.sectorService.totalByCompany(params);
			return res.status(200).send(response);
		} catch (err) {
			next(err);
		}
	};
}
