import databaseSandbox from '../models/sandbox.js';
import { StorageContext } from '../utils/storage_context.js';

export class ControllerProxy {
	constructor({ Controller, Service, Repositories }) {
		this.Controller = Controller;
		this.Service = Service;
		this.Repositories = Repositories;
	}

	createInstance(database) {
		const repositories = this._createRepositories(database);
		const service = this._createService(repositories);
		const controller = this._createController(service);

		return new Proxy(controller, {
			get: (target, prop) => {
				if (typeof target[prop] === 'function') {
					return (req, res, next) => {
						const context = StorageContext.getStore();
						const prefix = context?.environment || '';
						const db = prefix === 'sandbox' ? databaseSandbox : database;

						const new_repositories = this._createRepositories(db);
						const new_service = this._createService(new_repositories);
						target.service = new_service;

						return target[prop](req, res, next);
					};
				}
				return target[prop];
			}
		});
	}

	_createRepositories(database) {
		const repositories = {};
		Object.entries(this.Repositories).forEach(([key, RepositoryClass]) => {
			repositories[key] = new RepositoryClass(database);
		});

		return repositories;
	}

	_createService(repositories) {
		if (Object.keys(repositories).length <= 1) {
			const repositoryKey = Object.keys(repositories)[0];
			return new this.Service(repositories[repositoryKey]);
		}

		return new this.Service({ ...repositories });
	}

	_createController(service) {
		return new this.Controller(service);
	}
}
