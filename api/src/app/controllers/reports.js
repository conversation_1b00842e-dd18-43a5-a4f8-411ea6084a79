import AWS from 'aws-sdk';

export default class ReportControllerDeprecated {
	constructor(service) {
		this.service = service;
		this.lambda = new AWS.Lambda({
			region: 'us-east-1'
		});
	}

	rulaRiskDegreeBodyParts = async (req, res, next) => {
		try {
			const result = await this.service.rulaRiskDegreeBodyParts(req.body);
			res.status(200).send(result);
		} catch (error) {
			next(error);
		}
	};

	riskDegreeWorkstation = async (req, res, next) => {
		try {
			const result = await this.service.riskDegreeWorkstation(req.body);
			return res.status(200).send(result);
		} catch (error) {
			next(error);
		}
	};

	extractReportBriefBest = async (req, res, next) => {
		try {
			const result = await this.service.extractReportBriefBest(req.body);
			res.contentType('application/pdf');
			return res.status(200).send(result);
		} catch (error) {
			next(error);
		}
	};

	briefBestAngles = async (req, res, next) => {
		try {
			const response = await this.service.briefBestAngles(req.body);
			return res.status(200).send(response);
		} catch (error) {
			next(error);
		}
	};

	briefBestReport = async (req, res, next) => {
		try {
			const response = await this.service.briefBestReport(req.body);
			return res.status(200).send(response);
		} catch (err) {
			next(err);
		}
	};

	setParameters = async (req, res, next) => {
		try {
			const response = await this.service.setParameters(req.body);
			return res.status(200).send(response);
		} catch (err) {
			next(err);
		}
	};

	scoreRulaMovementFileDate = async (req, res, next) => {
		const { params } = req;
		try {
			const response = await this.service.scoreRulaMovementFileDate(params);
			return res.status(200).send(response);
		} catch (err) {
			next(err);
		}
	};

	extractedByCompany = async (req, res, next) => {
		const { params } = req;
		try {
			const response = await this.service.extractedByCompany(params);
			return res.status(200).send(response);
		} catch (err) {
			next(err);
		}
	};

	scoreRuleBytime = async (req, res, next) => {
		const { params, query } = req;
		try {
			const result = await this.service.scoreRuleBytime(params, query);
			return res.status(200).send(result);
		} catch (err) {
			next(err);
		}
	};

	scoreRula = async (req, res, next) => {
		const { params, query } = req;
		try {
			const result = await this.service.scoreRula(params, query);
			return res.status(200).send(result);
		} catch (err) {
			next(err);
		}
	};

	scoreRulaAngleTimeReport = async (req, res, next) => {
		const { body } = req;
		try {
			const result = await this.service.scoreRulaAngleTimeReport(body);
			res.contentType('application/pdf');
			return res.status(200).send(result);
		} catch (error) {
			next(error);
		}
	};

	ergonomicMovementReport = async (req, res, next) => {
		const { body } = req;
		try {
			const result = await this.service.ergonomicMovementReport(body);
			res.contentType('application/pdf');
			return res.status(200).send(result);
		} catch (err) {
			next(err);
		}
	};
}

export class ReportController {
	constructor(service) {
		this.service = service;
	}

	riskDegreeWorkstation = async (req, res, next) => {
		try {
			const result = await this.service.riskDegreeWorkstation(req.body);
			res.status(200).send(result);
		} catch (error) {
			next(error);
		}
	};

	reportGenerator = async (req, res, next) => {
		try {
			const response = await this.service.reportGenerator(req.body);
			res.setHeader('Content-type', 'application/pdf');
			res.end(Buffer.from(response));
		} catch (error) {
			next(error);
		}
	};

	angleByTimeDocumentPDF = async (req, res, next) => {
		const { id: user_id } = req.user;

		try {
			const response = await this.service.angleByTimeDocumentPDF({
				...req.body,
				user_id
			});
			res.setHeader('Content-type', 'application/pdf');
			res.end(Buffer.from(response));
		} catch (error) {
			next(error);
		}
	};
}
