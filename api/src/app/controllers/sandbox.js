import { logger } from '../helpers/logger.js';

export class SandboxController {
	constructor(service) {
		this.service = service;
	}

	async sendReport(req, res, next) {
		logger.info('[SandboxController] - sendReport init');
		const { id, report_type } = req.payload;
		try {
			await this.service.sendReport({ id, report_type });
			logger.info('[SandboxController] - sendReport success');
			return res.status(200).send();
		} catch (error) {
			logger.error('[SandboxController] - sendReport error', { error });
			next(error);
		} finally {
			logger.info('[SandboxController] - sendReport finish');
		}
	}

	logout = async (_, res, next) => {
		try {
			logger.info('[SandboxController] - logout init');
			await this.service.logout();
			logger.info('[SandboxController] - logout finish');
			return res.status(200).send();
		} catch (error) {
			logger.error('[SandboxController] - logout error', { error });
			next(error);
		} finally {
			logger.info('[SandboxController] - logout finish');
		}
	};
}
