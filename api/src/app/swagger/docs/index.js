import { nioshRouter } from './niosh.js';
import { kimPushPullRouter } from './kim_pp.js';
import { beraReportRouter } from './bera_report.js';
import { seraReportRouter } from './sera_report.js';
import { actionPlanRouter } from './action_plan.js';
import { strainIndexRouter } from './strain_index.js';
import { kimManualHandlingRouter } from './kim_mho.js';
import { customReportRouter } from './custom_report.js';
import { actionPlanV2Router } from './action_plan_v2.js';
import { libertyMutualReportRouter } from './liberty_mutual_report.js';
import { beraJobSummaryFilesRouter } from './bera_job_summary_files.js';
import { sandboxRouter } from './sandbox.js';
import { dataExtractionRouter } from './data-extraction.js';
import { ergonomicToolRouter } from './ergonomic_tool.js';
import { uploadRouter } from './upload.js';
import { fileRouter } from './file.js';

export default {
	...customReportRouter,
	...kimManualHandlingRouter,
	...beraReportRouter,
	...seraReportRouter,
	...actionPlanRouter,
	...actionPlanV2Router,
	...nioshRouter,
	...libertyMutualReportRouter,
	...kimPushPullRouter,
	...strainIndexRouter,
	...beraJobSummaryFilesRouter,
	...sandboxRouter,
	...dataExtractionRouter,
	...ergonomicToolRouter,
	...uploadRouter,
	...fileRouter
};
