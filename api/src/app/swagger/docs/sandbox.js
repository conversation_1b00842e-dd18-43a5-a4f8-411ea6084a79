export const sandboxRouter = {
	'/sandbox/send_report': {
		post: {
			tags: ['Sandbox'],
			summary: 'Send report data from sandbox to production environment',
			requestBody: {
				required: true,
				content: {
					'application/json': {
						schema: {
							type: 'object',
							required: ['organization_id', 'report_type'],
							properties: {
								organization_id: {
									type: 'string',
									format: 'uuid',
									description: 'Organization ID',
									example: '19e2ea58-6f71-409f-9d87-b125062ed2a8'
								},
								workstation_id: {
									type: 'string',
									format: 'uuid',
									description: 'Workstation ID (optional)',
									example: '19e2ea58-6f71-409f-9d87-b125062ed2a8'
								},
								company_id: {
									type: 'string',
									format: 'uuid',
									description: 'Company ID (required when sector_id is present)',
									example: '19e2ea58-6f71-409f-9d87-b125062ed2a8'
								},
								sector_id: {
									type: 'string',
									format: 'uuid',
									description: 'Sector ID',
									example: '19e2ea58-6f71-409f-9d87-b125062ed2a8'
								},
								report_type: {
									type: 'string',
									description: 'Type of report to be sent',
									enum: ['REBA', 'NIOSH', 'KIM_MHO', 'KIM_PP', 'CUSTOM_REPORT'],
									example: 'REBA'
								},
								id: {
									type: 'string',
									format: 'uuid',
									description: 'Report ID',
									example: '19e2ea58-6f71-409f-9d87-b125062ed2a8'
								}
							}
						}
					}
				}
			},
			responses: {
				200: {
					description: 'Report sent successfully',
					content: {
						'application/json': {
							schema: {
								type: 'object',
								properties: {
									status: {
										type: 'string',
										example: 'success'
									},
									message: {
										type: 'string',
										example: 'Report sent successfully'
									},
									data: {
										type: 'object',
										properties: {
											report_id: {
												type: 'string',
												format: 'uuid',
												example: '19e2ea58-6f71-409f-9d87-b125062ed2a8'
											},
											status: {
												type: 'string',
												example: 'processed'
											}
										}
									}
								}
							}
						}
					}
				},
				400: {
					description: 'Invalid request',
					content: {
						'application/json': {
							schema: {
								type: 'object',
								properties: {
									status: {
										type: 'string',
										example: 'fail'
									},
									message: {
										type: 'string',
										example: 'Invalid parameters'
									}
								}
							}
						}
					}
				},
				500: {
					description: 'Internal server error',
					content: {
						'application/json': {
							schema: {
								type: 'object',
								properties: {
									status: {
										type: 'string',
										example: 'error'
									},
									message: {
										type: 'string',
										example: 'Failed to send report'
									}
								}
							}
						}
					}
				}
			}
		}
	},
	'/sandbox/logout': {
		post: {
			tags: ['Sandbox'],
			summary: 'Logout from sandbox environment',
			requestBody: {
				required: false,
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								session_id: {
									type: 'string',
									description: 'Session ID to be invalidated',
									example: 'sess_19e2ea58-6f71-409f-9d87-b125062ed2a8'
								}
							}
						}
					}
				}
			},
			responses: {
				200: {
					description: 'Logout successful',
					content: {
						'application/json': {
							schema: {
								type: 'object',
								properties: {
									status: {
										type: 'string',
										example: 'success'
									},
									message: {
										type: 'string',
										example: 'Logout successful'
									},
									data: {
										type: 'object',
										properties: {
											logged_out: {
												type: 'boolean',
												example: true
											},
											timestamp: {
												type: 'string',
												format: 'date-time',
												example: '2024-01-15T10:30:00Z'
											}
										}
									}
								}
							}
						}
					}
				},
				400: {
					description: 'Invalid request',
					content: {
						'application/json': {
							schema: {
								type: 'object',
								properties: {
									status: {
										type: 'string',
										example: 'fail'
									},
									message: {
										type: 'string',
										example: 'Invalid session'
									}
								}
							}
						}
					}
				},
				500: {
					description: 'Internal server error',
					content: {
						'application/json': {
							schema: {
								type: 'object',
								properties: {
									status: {
										type: 'string',
										example: 'error'
									},
									message: {
										type: 'string',
										example: 'Failed to logout'
									}
								}
							}
						}
					}
				}
			}
		}
	}
};
