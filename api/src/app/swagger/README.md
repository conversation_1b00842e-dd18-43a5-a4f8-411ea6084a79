Swagger Documentation - docs

Este diretório contém os arquivos de documentação da API em formato Swagger. Cada arquivo é separado por responsabilidade, de acordo com os recursos ou módulos da API.

Estrutura

Cada recurso da API tem um arquivo dedicado dentro desta pasta. Esses arquivos seguem a convenção de nomear o arquivo com o nome do recurso que está sendo documentado. Por exemplo:

src/
├── app/
│ ├── swagger/
│ │ ├── docs/
│ │ │ ├── user.js // Documentação do recurso "User"
│ │ │ └── ... // Outros recursos

Cada arquivo documenta as rotas da API relacionadas a um recurso específico, como rosa, user, company, etc.

Como Criar a Documentação de um Novo Recurso

Para adicionar a documentação de um novo recurso, siga estas etapas:

1. Criar um Novo Arquivo: Dentro da pasta docs, crie um novo arquivo JavaScript com o nome do recurso (por exemplo, entity.js).

2. Estrutura do Arquivo: Dentro do arquivo, use a estrutura de documentação do Swagger para definir as rotas e as operações (GET, POST, PUT, DELETE) relacionadas ao recurso.

3. Exemplo de CRUD Completo (GET, POST, PUT, DELETE): Aqui está um exemplo de como documentar um CRUD para um recurso genérico.

Exemplo de CRUD (Arquivo: entity.js)

const entityDoc = {
'/entities': {
get: {
summary: 'Listar todos os recursos',
tags: ['Entity'],
responses: {
200: {
description: 'Lista de recursos',
content: {
'application/json': {
schema: {
type: 'array',
items: {
type: 'object',
properties: {
id: { type: 'string', example: '123' },
name: { type: 'string', example: 'Recurso A' },
description: { type: 'string', example: 'Descrição do recurso' }
}
}
}
}
}
}
}
},
post: {
summary: 'Criar um novo recurso',
tags: ['Entity'],
requestBody: {
required: true,
content: {
'application/json': {
schema: {
type: 'object',
properties: {
name: { type: 'string', example: 'Novo Recurso' },
description: { type: 'string', example: 'Descrição do recurso' }
}
}
}
}
},
responses: {
201: { description: 'Recurso criado com sucesso' }
}
}
},
'/entities/{id}': {
get: {
summary: 'Obter detalhes de um recurso',
tags: ['Entity'],
parameters: [
{
name: 'id',
in: 'path',
required: true,
schema: { type: 'string' },
description: 'ID do recurso'
}
],
responses: {
200: {
description: 'Detalhes do recurso',
content: {
'application/json': {
schema: {
type: 'object',
properties: {
id: { type: 'string', example: '123' },
name: { type: 'string', example: 'Recurso A' },
description: { type: 'string', example: 'Descrição do recurso' }
}
}
}
}
}
}
},
put: {
summary: 'Atualizar um recurso existente',
tags: ['Entity'],
parameters: [
{
name: 'id',
in: 'path',
required: true,
schema: { type: 'string' },
description: 'ID do recurso'
}
],
requestBody: {
required: true,
content: {
'application/json': {
schema: {
type: 'object',
properties: {
name: { type: 'string', example: 'Recurso Atualizado' },
description: { type: 'string', example: 'Descrição atualizada' }
}
}
}
}
},
responses: {
200: { description: 'Recurso atualizado com sucesso' }
}
},
delete: {
summary: 'Deletar um recurso',
tags: ['Entity'],
parameters: [
{
name: 'id',
in: 'path',
required: true,
schema: { type: 'string' },
description: 'ID do recurso'
}
],
responses: {
204: { description: 'Recurso deletado com sucesso' }
}
}
}
};

export default entityDoc;

4. Atualizar o index.js dentro de docs:

Depois de criar o novo arquivo de documentação, adicione-o ao index.js da pasta docs, para que seja consolidado com as outras documentações:

import { exampleRouter } from './example.js';

// Exporta todos os documentos de rota
export default {
...exampleRouter,
// Adicione outros documentos aqui conforme necessário
};

5. Verificar a Rota no Swagger UI:

Depois de adicionar o arquivo de documentação, o Swagger automaticamente incluirá o novo recurso na interface gráfica, disponível na rota /docs (em desenvolvimento).

Contribuições Futuras

- Para adicionar novas rotas ou recursos, siga o padrão de organização por responsabilidade, criando um arquivo separado para cada recurso.
- Ao documentar novas rotas, use os padrões estabelecidos acima para CRUD e operações adicionais.
