export const ENUM_STATUS = {
	SUCCESS: 'success',
	WARNING: 'warning',
	ERROR: 'error'
};

export const ROLE_ENUM = {
	ADMIN: 'ADMIN',
	MASTER: 'MASTER',
	SUPERVISOR: 'SUPERVISOR',
	USER: 'USER'
};

export const ERROR_RESPONSE_ENUM = {
	INVALID_PARAMETERS: {
		code: 400,
		message: 'Check the parameters entered'
	},
	INTERNAL_SERVER_ERROR: {
		code: 500,
		message: 'Internal Server Error'
	},
	UNAUTHORIZED: {
		code: 401,
		message: 'You are unauthorized to access the requested resource'
	},
	INVALID_HEADERS_PARAMETERS: {
		code: 400,
		message: 'Check the header parameters entered'
	},
	INVALID_TOKEN: {
		code: 400,
		message: 'Invalid Token'
	},
	EXPIRED_TOKEN: {
		code: 400,
		message: 'Expired token'
	}
};

export const ERROR_RESPONSE_ENTITIES_ENUM = {
	RANGE_RISK: {
		NOT_FOUND: {
			code: 404,
			message: 'Range risk not found'
		},
		FAIL_DOWNLOAD_DATA: {
			code: 500,
			message: 'Failed to fetch risk tracks'
		},
		CREATION_LIMIT: {
			code: 400,
			message: 'Limit of bands reached'
		}
	},

	TASK: {
		NOT_FOUND: {
			code: 404,
			message: 'Task not found'
		},
		ALREADY_EXISTS: {
			code: 409,
			message: 'Task already exists'
		}
	},
	CYCLE: {
		NOT_FOUND: {
			code: 404,
			message: 'Cycle not found'
		},
		ALREADY_EXISTS: {
			code: 409,
			message: 'Cycle already exists'
		}
	},
	STRESS_LEVEL: {
		NOT_FOUND: {
			code: 404,
			message: 'Stress level not found'
		}
	},
	FREQUENCY: {
		NOT_FOUND: {
			code: 404,
			message: 'Frequency not found'
		}
	},
	TOTAL_TASK_DURATION: {
		NOT_FOUND: {
			code: 404,
			message: 'Total task duration not found'
		}
	},
	EVALUATOR: {
		NOT_FOUND: {
			code: 404,
			message: 'Evaluator not found'
		}
	},
	EXPOSURE: {
		NOT_FOUND: {
			code: 404,
			message: 'Exposure not found'
		}
	},
	VULNERABILITY: {
		NOT_FOUND: {
			code: 404,
			message: 'Vulnerability not found'
		}
	},
	SEVERITY: {
		NOT_FOUND: {
			code: 404,
			message: 'Severity not found'
		}
	},
	RISK_DESCRIPTION: {
		NOT_FOUND: {
			code: 404,
			message: 'Risk description not found'
		}
	},
	RISK_DAMAGE: {
		NOT_FOUND: {
			code: 404,
			message: 'Risk damage not found'
		},
		SEVERITY_ALREADY_SET: {
			code: 409,
			message: 'Severity already set to this risk damage'
		}
	},
	RISK_CATEGORY: {
		NOT_FOUND: {
			code: 404,
			message: 'Risk category not found'
		}
	},
	CUSTOM_REPORT_STEP_KEY: {
		NOT_FOUND: {
			code: 404,
			message: 'Custom report step key not found'
		}
	},
	CUSTOM_REPORT_SUB_STEP_KEY: {
		NOT_FOUND: {
			code: 404,
			message: 'Custom report sub step key not found'
		}
	},
	CUSTOM_REPORT_STEP: {
		NOT_FOUND: {
			code: 404,
			message: 'Custom report step not found'
		}
	},
	CUSTOM_REPORT: {
		NOT_FOUND: {
			code: 404,
			message: 'Custom report not found'
		}
	},
	BERA_STEP_KEY_RESULT: {
		NOT_FOUND: {
			code: 404,
			message: 'Bera step key result not found'
		},
		ALREADY_EXISTS: {
			code: 409,
			message: 'Bera step key result already exists'
		}
	},
	BERA_WEIGHTED_AVERAGE: {
		NOT_FOUND: {
			code: 404,
			message: 'Bera weighted average not found'
		},
		ALREADY_EXISTS: {
			code: 409,
			message: 'Bera weighted average already exists'
		}
	},
	BERA_REPORT: {
		NOT_FOUND: {
			code: 404,
			message: 'Bera report not found'
		},
		ALREADY_EXISTS: {
			code: 409,
			message: 'Bera report already exists'
		}
	},
	BERA_JOB_SUMMARY: {
		NOT_FOUND: {
			code: 404,
			message: 'Bera job summary not found'
		},
		RESULT_NOT_FOUND: {
			code: 404,
			message: 'Bera job summary result not found'
		},
		ALREADY_EXISTS: {
			code: 409,
			message: 'Bera job summary already exists'
		}
	},
	SERA_REPORT: {
		NOT_FOUND: {
			code: 404,
			message: 'Sera report not found'
		},
		ALREADY_EXISTS: {
			code: 409,
			message: 'Sera report already exists'
		}
	},
	SERA_SUMMARY: {
		NOT_FOUND: {
			code: 404,
			message: 'Sera summary not found'
		},
		ALREADY_EXISTS: {
			code: 409,
			message: 'Sera summary already exists'
		}
	},
	SERA_SUMMARY_REVIEW: {
		NOT_FOUND: {
			code: 404,
			message: 'Sera summary review not found'
		},
		ALREADY_EXISTS: {
			code: 409,
			message: 'Sera summary review already exists'
		}
	},
	RECOVERY_REPORT: {
		NOT_FOUND: {
			code: 404,
			message: 'Report data not found'
		}
	},
	FILE: {
		NOT_FOUND: {
			code: 404,
			message: 'File not found'
		},
		READ_FAILURE: {
			code: 500,
			message: 'Failed to read file'
		},
		FAILED_SAVE_FILE: {
			code: 500,
			message: 'Failed to save file'
		}
	},
	MASTER: {
		NOT_FOUND: {
			code: 404,
			message: 'Master user not found'
		}
	},
	POTENTIAL_CUSTOMER: {
		NOT_FOUND: {
			code: 404,
			message: 'Potential customer not found'
		},
		CUSTOMER_REGISTERED: {
			code: 400,
			message: 'Customer already registered'
		}
	},
	VOUCHER: {
		NOT_FOUND: {
			code: 404,
			message: 'Voucher not found'
		},
		INVALID: {
			code: 400,
			message: 'Invalid voucher'
		}
	},
	PLAN: {
		INVALID_RECURRENCE: {
			code: 400,
			message: 'Invalid recurrence'
		},
		NOT_FOUND: {
			code: 404,
			message: 'Plan not found'
		},
		ALREADY_CANCELED: {
			code: 400,
			message: 'Plan already canceled'
		},
		INVALID_SUBSCRIPTION_TYPE: {
			code: 500,
			message: 'Invalid subscription type'
		},
		RECENTLY_HIRED_PLAN: {
			code: 400,
			message: 'It is necessary to spend a day after contracting the service to change the plan'
		},
		SELECTED_PLAN_ALREADY_IN_USE: {
			code: 400,
			message: 'Choose a plan different from your current plan'
		},
		DOWNGRADE_YEARLY_PLAN: {
			code: 400,
			message: 'It is not possible to downgrade the annual plan'
		}
	},
	HOOKS: {
		BAD_REQUEST: {
			code: 400,
			message: 'Failed to decode payload'
		}
	},
	CUSTOMER_INFORMATION: {
		EXPIRATION_PLAN: {
			code: 400,
			message: 'Account subscription expired, renew to continue.'
		},
		EXPIRATION_PLAN_NOT_FOUND: {
			code: 400,
			message: 'Plan expiration date not found'
		},
		DOCUMENT_ALREADY_REGISTERED: {
			code: 400,
			message: 'Document already registered'
		},
		NOT_FOUND: {
			code: 404,
			message: 'Customer information not found'
		},
		CANCELED_PLAN: {
			code: 400,
			message: 'Account subscription cancelled, renew to continue.'
		}
	},
	CUSTOMER_PLAN: {
		FAILED_CREATE: {
			code: 500,
			message: 'Failed to create user plan'
		},
		NOT_FOUND: {
			code: 404,
			message: 'Customer plan not found'
		},
		MAX_REGISTERED_USERS: {
			code: 400,
			message: 'Maximum registered users'
		}
	},
	USER: {
		FAILED_CREATE: {
			code: 500,
			message: 'Failed to create user'
		},
		NOT_FOUND: {
			code: 404,
			message: 'User not found'
		},
		INVALID_EMAIL_OR_PASSWORD: {
			code: 400,
			message: 'Invalid email or password'
		},
		INVALID_CURRENT_PASSWORD: {
			code: 400,
			message: 'Invalid current password'
		},
		USER_ALREADY_REGISTERED: {
			code: 400,
			message: 'User already registered'
		}
	},
	ORGANIZATION: {
		NOT_FOUND: {
			code: 404,
			message: 'Organization not found'
		}
	},
	COMPANY: {
		NOT_FOUND: {
			code: 404,
			message: 'Company not found'
		},
		FAIL_CREATE: {
			code: 500,
			message: 'Failed to create company'
		}
	},
	SECTOR: {
		NOT_FOUND: {
			code: 404,
			message: 'Sector not found'
		}
	},
	LINE: {
		NOT_FOUND: {
			code: 404,
			message: 'Line not found'
		}
	},
	WORKSTATION: {
		NOT_FOUND: {
			code: 404,
			message: 'Workstation not found'
		}
	},
	USER_ACCESS: {
		NOT_FOUND: {
			code: 404,
			message: 'User access not found'
		},
		FAIL_CREATE: {
			code: 500,
			message: 'Failed to create a user permission'
		}
	},
	ANGLE_TIME_REPORT: {
		NOT_FOUND: {
			code: 404,
			message: 'Angle time report not found'
		}
	},
	BACK_COMPRESSIVE_REPORT: {
		NOT_FOUND: {
			code: 404,
			message: 'Back compressive report not found'
		}
	},
	KIM_MHO_REPORT: {
		NOT_FOUND: {
			code: 404,
			message: 'Kim mho report not found'
		}
	},
	KIM_PP_REPORT: {
		NOT_FOUND: {
			code: 404,
			message: 'Kim pp report not found'
		}
	},
	LIBERTY_MUTUAL_REPORT: {
		NOT_FOUND: {
			code: 404,
			message: 'Liberty mutual report not found'
		}
	},
	NIOSH_REPORT: {
		NOT_FOUND: {
			code: 404,
			message: 'Niosh report not found'
		}
	},
	PEA_REPORT: {
		NOT_FOUND: {
			code: 404,
			message: 'Pea report not found'
		}
	},
	REBA_REPORT: {
		NOT_FOUND: {
			code: 404,
			message: 'Reba report not found'
		}
	},
	STRAIN_INDEX_REPORT: {
		NOT_FOUND: {
			code: 404,
			message: 'Strain index report not found'
		}
	},
	SUPER_PEA_REPORT: {
		NOT_FOUND: {
			code: 404,
			message: 'Super pea report not found'
		}
	},
	ACTION_PLAN: {
		NOT_FOUND: {
			code: 404,
			message: 'Action plan not found'
		}
	},
	DATA_EXTRACTION_STEP: {
		NOT_FOUND: {
			code: 404,
			message: 'Data extraction step not found'
		}
	},
	DATA_EXTRACTION: {
		NOT_FOUND: {
			code: 404,
			message: 'Data extraction not found'
		},
		NOT_PROCESSED: {
			code: 400,
			message: 'Data extraction not processed'
		},
		FAILED_CREATE: {
			code: 500,
			message: 'Failed to create data extraction'
		},
		FAILED_UPDATE: {
			code: 500,
			message: 'Failed to update data extraction'
		},
		FAILED_DELETE: {
			code: 500,
			message: 'Failed to delete data extraction'
		},
		INVALID_DATA_EXTRACTION_TYPE: {
			code: 400,
			message: 'Invalid data extraction type'
		},
		FAILED_DOWNLOAD: {
			code: 500,
			message: 'Failed to download data extraction'
		},
		FAILED_DOWNLOAD_FILE: {
			code: 500,
			message: 'Failed to download data extraction file'
		},
		FAILED_DELETE: {
			code: 500,
			message: 'Failed to delete data extraction'
		},
		NOT_PROCESSED: {
			status: 'error',
			code: 400,
			message: 'Data extraction not processed'
		},
		FAILED_SEND_TO_QUEUE: {
			code: 500,
			message: 'Failed to send data extraction to queue'
		}
	},
	DATA_EXTRACTION_STEP: {
		NOT_FOUND: {
			code: 404,
			message: 'Data extraction step not found'
		}
	},
	DATA_EXTRACTION_MODEL: {
		NOT_FOUND: {
			code: 404,
			message: 'Data extraction model not found'
		},
		FAILED_CREATE: {
			code: 500,
			message: 'Failed to create data extraction model'
		},
		FAILED_UPDATE: {
			code: 500,
			message: 'Failed to update data extraction model'
		},
		FAILED_DELETE: {
			code: 500,
			message: 'Failed to delete data extraction model'
		}
	}
};

export const RESPONSE_ENTITIES_ENUM = {
	SECTOR: {
		CREATED: 'Sector created successfully'
	},
	RISK_RANGE: {
		SUCCESSFULLY_DELETED: 'Risk ranges deleted successfully',
		CREATED: 'Risk ranges created successfully'
	},
	CUSTOMER_PLAN: {
		NOT_FOUND: 'Customer plan not found'
	},
	USER: {
		CREATED: 'User created successfully',
		CHANGE_PASSWORD_SUCCESS: 'Password changed successfully'
	},
	PAYMENT: {
		SUCCESS: 'Payment made successfully',
		PROCESSING: 'Payment being processed'
	},
	PLAN: {
		CANCELED: 'Plan successfully canceled',
		RENEWED: 'Successfully renewed plan',
		PAYMENT_IN_PROCESS: 'We received your order, we are processing your payment'
	},
	CUSTOMER_INFORMATION: {
		DATA_NOT_FOUND: 'Customer data not found'
	}
};

export const ERROR_RESPONSE_EXTERNAL_SERVICE = {
	EMAIL: {
		FAIL_SEND: {
			code: 500,
			message: 'Failed to send email'
		}
	},
	STORAGE: {
		FAIL_CREATE_BUCKET: {
			code: 500,
			message: 'Failed to create storage bucket'
		},
		FAIL_UPLOAD_FILE: {
			code: 500,
			message: 'Failed to upload file to storage'
		},
		FAIL_DOWNLOAD_DATA: {
			code: 500,
			message: 'Failed to download data'
		},
		FAIL_DELETE_FILE: {
			code: 500,
			message: 'Failed to delete file'
		},
		FAIL_GET_INFORMATION_STORAGE: {
			code: 500,
			message: 'Fail get information storage'
		},
		FAIL_STORE_DATA: {
			code: 500,
			message: 'Failed to store data'
		}
	},
	QUEUE: {
		FAIL_COMMUNICATION: {
			code: 500,
			message: 'Reply queue communication failure'
		}
	},
	PUPPETEER: {
		REPORT_NOT_FOUND: {
			code: 500,
			message: 'Selected report not found'
		}
	}
};

export const ERRORS_ENUM = {
	RESPONSE_ENTITIES_ENUM,
	ERROR_RESPONSE_ENTITIES_ENUM,
	ERROR_RESPONSE_EXTERNAL_SERVICE
};

export const ACTION_PLAN_EVENTS = {
	TASK: {
		CREATED: 'Task created',
		DELETED: 'Task deleted',
		RENAMED: 'Task renamed',
		TYPED: 'Task type changed',
		BOTH: 'Task and type changed',
		RECONDITIONED: 'Task reconditioned',
		CHECKED: 'Task checked',
		UNCHECKED: 'Task unchecked'
	},
	ACTION_PLAN: {
		CREATED: 'Action plan created',
		RENAMED: 'Action plan renamed',
		CHANGED_STATUS: 'Status changed',
		CHANGED_DEADLINE: 'Deadline changed',
		CHANGED_RESPONSIBLE: 'Responsible user changed'
	}
};

export const SPECIAL_PLAN_ENUM = {
	PREMIUM: 'PREMIUM',
	TELEWORLD: 'TELEWORLD'
};

export const DATA_EXTRACTION_CONTROL_STATUS = {
	PENDING: 'pending',
	IN_PROGRESS: 'in_progress',
	COMPLETED: 'completed',
	FAILED: 'failed'
};

export const AVATAR_DEFAULT = 'https://kinebot-statics-john-deere.s3.amazonaws.com/john-deere-logo.png';
