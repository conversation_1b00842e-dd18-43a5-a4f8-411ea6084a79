import { Consumer } from 'sqs-consumer';
import config from 'config';
import AWS from 'aws-sdk';

import { SOCKET_EVENTS, FILE_STATUS } from '../helpers/constants.js';
import { FileFactory } from '../factories/file.js';
import database from '../../app/models/index.js';
import databaseSandbox from '../../app/models/sandbox.js';
import { getWorkspace } from '../util/index.js';
import { Socket } from '../helpers/socket.js';
import { logger } from '../helpers/logger.js';

const { UPDATE_FILE_STATUS } = SOCKET_EVENTS;

export const optionsQueue = {
	queueUrlPrefix: config.get('App.queue.subscribe.statusFile.queueUrlPrefix'),
	queueName: config.get('App.queue.subscribe.statusFile.queueName')
};

const { repository } = FileFactory.createInstance(database);
const { repository: sandbox_repository } = FileFactory.createInstance(databaseSandbox);

export class Queue {
	constructor() {
		this.sqs = new AWS.SQS({
			credentials: {
				accessKeyId: config.get('App.aws.accessKeyId'),
				secretAccessKey: config.get('App.aws.secretAccessKey')
			},
			region: 'us-east-1'
		});
		this.repository = repository;
	}

	getAttributesBodyMessage(payload) {
		let replacedItems = {};
		Object.keys(payload).map((key) => (replacedItems[key] = payload[key].StringValue));
		return replacedItems;
	}

	async sendMessage(data, options) {
		const { queueUrlPrefix, queueName } = optionsQueue;

		const { idFile, organizationId, companyId, type } = data;

		try {
			const params = {
				MessageAttributes: {
					idFile: {
						DataType: 'String',
						StringValue: idFile
					},
					organizationId: {
						DataType: 'String',
						StringValue: organizationId
					},
					companyId: {
						DataType: 'String',
						StringValue: companyId
					},
					type: {
						DataType: 'String',
						StringValue: type
					}
					// originBucket: {
					// 	DataType: "String",
					// 	StringValue: bucket,
					// },
					// objectName: {
					// 	DataType: "String",
					// 	StringValue: Key,
					// },
					// blur_face: {
					// 	DataType: "String",
					// 	StringValue: blur_face,
					// },
					// tool: {
					// 	DataType: "String",
					// 	StringValue: tool,
					// },
				},
				MessageBody: 'Testing',
				QueueUrl: queueUrlPrefix + queueName
				// MessageGroupId: "myMessageGroupId",
				// MessageDeduplicationId: uuidv4(),
			};

			const data = await this.sqs.sendMessage(params).promise();

			console.log(data, 'data');

			return data;
		} catch (error) {
			console.log(error, 'error');
		}
	}

	subscribe(params) {
		const context = {
			_service: 'queue',
			_layer: 'worker',
			_meta: 'subscribe'
		};

		logger.child({ context, params }).info('Init');

		const { queueUrlPrefix, queueName } = params;

		const consumer = Consumer.create({
			sqs: this.sqs,
			queueUrl: queueUrlPrefix + queueName,
			messageAttributeNames: ['All'],
			handleMessage: async (message) => {
				const {
					idFile: file_id,
					organizationId: organization_id,
					objectName,
					companyId: company_id,
					duration,
					type
				} = this.getAttributesBodyMessage(message.MessageAttributes);

				const resume = {
					file_id,
					organization_id,
					company_id,
					status: type
				};

				logger.child({ context, params: resume }).info('Received message');

				let transaction;
				let sandbox_transaction;

				try {
					const file = await this.repository.db.File.findOne({
						where: {
							id: file_id,
							company_id,
							organization_id
						},
						paranoid: false,
						include: [
							{
								association: 'user',
								include: [
									{
										association: 'customer',
										include: [
											{
												association: 'customer_credits'
											}
										]
									}
								]
							}
						]
					});

					if (!file) {
						logger.child({ parameters: { file_id } }).error('[Queue] subscribe - File not found');
						throw new Error('File not found');
					}

					if (file.status === FILE_STATUS.PROCESSED) {
						logger.child({ parameters: { file_id } }).info('[Queue] subscribe - File already processed');
						return;
					}

					const sandbox_file = await sandbox_repository.db.File.findOne({
						where: {
							id: file_id,
							company_id,
							organization_id,
							original_name: file.original_name,
							generated_name: file.generated_name
						}
					});

					transaction = await this.repository.db.sequelize.transaction();

					const status = type === 'json' ? FILE_STATUS.EXTRACTED_DATA : FILE_STATUS.PROCESSED;

					const parse_duration = file.duration;
					const credit_id = file.user?.customer?.customer_credits?.id;

					const payload = {
						status: status,
						file_processed: objectName
					};

					await file.update(payload, { transaction });

					if (sandbox_file) {
						sandbox_transaction = await sandbox_repository.db.sequelize.transaction();
						await sandbox_file.update(payload, { transaction: sandbox_transaction });
					}

					if (credit_id && type === 'video') {
						const operator = {
							[this.repository.db.Sequelize.Op.gte]: parse_duration
						};
						const opts = {
							by: parse_duration,
							where: {
								id: credit_id,
								minutes: operator
							}
						};
						await this.repository.db.CustomerCredits.decrement('minutes', opts, transaction);
					}

					await transaction.commit();
					sandbox_transaction && (await sandbox_transaction.commit());

					const io = Socket.getInstance().getIO();

					const roomId = getWorkspace({
						organization_id: file.organization_id,
						company_id: file.company_id
					});

					io.of('/file')
						.to(roomId)
						.emit(UPDATE_FILE_STATUS, { id: file.id, status: file.status, workstation: file.workstation });

					logger.child({ context, params: payload }).info('Message processed');
				} catch (error) {
					logger.child({ context, params: resume }).error(error);
					transaction && (await transaction.rollback());
					throw error;
				}
			}
		});

		consumer.start();

		process.on('SIGINT', () => {
			logger.info('SIGINT Received, stopping consumer');
			consumer.stop();
			setTimeout(process.exit, 10000);
		});

		return consumer;
	}
}
