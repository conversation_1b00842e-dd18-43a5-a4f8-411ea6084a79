const config = require('config');

module.exports = {
	host: config.get('App.database.host'),
	port: config.get('App.database.port') || 3306,
	username: config.get('App.database.user'),
	password: config.get('App.database.password'),
	dialect: config.get('App.database.dialect') || 'mysql',
	dialectOptions: { connectTimeout: 5000 },
	operatorsAliases: '0',
	logging: false,
	database: config.get('App.database.name'),
	storage: './__tests__/database.sqlite',
	define: {
		timestamps: true,
		underscored: true,
		underscoredAll: true
	},
	pool: {
		max: 25,
		min: 0,
		acquire: 30000,
		idle: 5000
	}
};
