import { jest } from '@jest/globals';

import { faker } from '@faker-js/faker';

import {
	mockedS3Service,
	mockedExistsObjectParams,
	mockedPutObjectParams,
	mockedGetObjectParams,
	mockedBucketName,
	mockedObjects,
	mockedSignatureUploadParams,
	mockedSignedUrlParams,
	mockedSignatureDownloadParams,
	mockedListObjectsParams,
	mockedRemoveObjectParams,
	mockedOrganizationId,
	mockedCompanyId
} from '../fixtures/storage.js';

import { Storage } from '@src/app/helpers/storage.js';

describe('[Storage] - Helper', () => {
	let storage;

	beforeAll(() => {
		storage = new Storage('us-east-1', mockedS3Service);
	});

	beforeEach(() => {
		jest.clearAllMocks();
	});

	describe('[getFolder]', () => {
		it('should return formatted folder name', () => {
			const result = Storage.getFolder({ organizationId: mockedOrganizationId, companyId: mockedCompanyId });
			const expectedOrganization = mockedOrganizationId.split('-', 5)[0];
			const expectedCompany = mockedCompanyId.split('-', 5)[0];

			expect(result).toBe(`${expectedOrganization}-${expectedCompany}`);
		});
	});

	describe('[existsObject]', () => {
		it('should return throw error', async () => {
			const error = new Error('Service error');
			jest.spyOn(mockedS3Service, 'headObject').mockReturnValue({
				promise: jest.fn().mockRejectedValueOnce(error)
			});

			await expect(storage.existsObject(mockedExistsObjectParams)).rejects.toThrow('Service error');
		});

		it('should return false when object not found', async () => {
			const error = new Error('Not found');
			error.code = 'NotFound';
			jest.spyOn(mockedS3Service, 'headObject').mockReturnValue({
				promise: jest.fn().mockRejectedValueOnce(error)
			});

			const result = await storage.existsObject(mockedExistsObjectParams);

			expect(result).toBe(false);
		});

		it('should return true when object exists', async () => {
			jest.spyOn(mockedS3Service, 'headObject').mockReturnValue({
				promise: jest.fn().mockResolvedValueOnce({})
			});

			const result = await storage.existsObject(mockedExistsObjectParams);

			expect(mockedS3Service.headObject).toHaveBeenCalledWith(mockedExistsObjectParams);
			expect(result).toBe(true);
		});
	});

	describe('[putObject]', () => {
		it('should return throw error', async () => {
			const error = new Error('Put object failed');
			jest.spyOn(mockedS3Service, 'putObject').mockReturnValue({
				promise: jest.fn().mockRejectedValueOnce(error)
			});

			await expect(storage.putObject(mockedPutObjectParams)).rejects.toThrow('Put object failed');
		});

		it('should invoke S3 service with putObject method', async () => {
			const mockResponse = { ETag: faker.datatype.uuid() };
			jest.spyOn(mockedS3Service, 'putObject').mockReturnValue({
				promise: jest.fn().mockResolvedValueOnce(mockResponse)
			});

			const result = await storage.putObject(mockedPutObjectParams);

			expect(mockedS3Service.putObject).toHaveBeenCalledWith(mockedPutObjectParams);
			expect(result).toBe(mockResponse);
		});
	});

	describe('[getObject]', () => {
		it('should return null when get object fails', async () => {
			const error = new Error('Get object failed');
			jest.spyOn(mockedS3Service, 'getObject').mockReturnValue({
				promise: jest.fn().mockRejectedValueOnce(error)
			});

			const result = await storage.getObject(mockedGetObjectParams);

			expect(result).toBe(null);
		});

		it('should invoke S3 service with getObject method', async () => {
			const mockResponse = { Body: Buffer.from('test content') };
			jest.spyOn(mockedS3Service, 'getObject').mockReturnValue({
				promise: jest.fn().mockResolvedValueOnce(mockResponse)
			});

			const result = await storage.getObject(mockedGetObjectParams);

			expect(mockedS3Service.getObject).toHaveBeenCalledWith(mockedGetObjectParams);
			expect(result).toBe(mockResponse);
		});
	});

	describe('[exitsBucket]', () => {
		it('should return throw error', async () => {
			await expect(storage.exitsBucket()).rejects.toThrow('Invalid parameters');
		});

		it('should return false when bucket not found', async () => {
			const error = new Error('Not found');
			error.statusCode = 404;
			jest.spyOn(mockedS3Service, 'headBucket').mockReturnValue({
				promise: jest.fn().mockRejectedValueOnce(error)
			});

			const result = await storage.exitsBucket(mockedBucketName);

			expect(result).toBe(false);
		});

		it('should return true when bucket exists', async () => {
			jest.spyOn(mockedS3Service, 'headBucket').mockReturnValue({
				promise: jest.fn().mockResolvedValueOnce({})
			});

			const result = await storage.exitsBucket(mockedBucketName);

			expect(mockedS3Service.headBucket).toHaveBeenCalledWith({ Bucket: mockedBucketName });
			expect(result).toBe(true);
		});
	});

	describe('[listObjectsBucket]', () => {
		it('should return throw error', async () => {
			await expect(storage.listObjectsBucket()).rejects.toThrow('Invalid parameters');
		});

		it('should throw error when list objects fails', async () => {
			const error = new Error('List failed');
			jest.spyOn(mockedS3Service, 'listObjects').mockReturnValue({
				promise: jest.fn().mockRejectedValueOnce(error)
			});

			await expect(storage.listObjectsBucket(mockedBucketName)).rejects.toThrow('List failed');
		});

		it('should invoke S3 service with listObjects method', async () => {
			const mockContents = mockedObjects;
			jest.spyOn(mockedS3Service, 'listObjects').mockReturnValue({
				promise: jest.fn().mockResolvedValueOnce({ Contents: mockContents })
			});

			const result = await storage.listObjectsBucket(mockedBucketName);

			expect(mockedS3Service.listObjects).toHaveBeenCalledWith({ Bucket: mockedBucketName });
			expect(result).toEqual(mockContents);
		});
	});

	describe('[deleteObjectsBucket]', () => {
		it('should return throw error', async () => {
			await expect(storage.deleteObjectsBucket()).rejects.toThrow('Invalid parameters');
		});

		it('should throw error when delete objects fails', async () => {
			const error = new Error('Delete failed');
			jest.spyOn(mockedS3Service, 'deleteObjects').mockReturnValue({
				promise: jest.fn().mockRejectedValueOnce(error)
			});

			await expect(storage.deleteObjectsBucket(mockedBucketName, mockedObjects)).rejects.toThrow('Delete failed');
		});

		it('should invoke S3 service with deleteObjects method', async () => {
			jest.spyOn(mockedS3Service, 'deleteObjects').mockReturnValue({
				promise: jest.fn().mockResolvedValueOnce({})
			});

			await storage.deleteObjectsBucket(mockedBucketName, mockedObjects);

			expect(mockedS3Service.deleteObjects).toHaveBeenCalledWith({
				Bucket: mockedBucketName,
				Delete: {
					Objects: mockedObjects.map(({ Key }) => ({ Key }))
				}
			});
		});
	});

	describe('[createSignatureUpload]', () => {
		it('should return throw error', async () => {
			const error = new Error('Signature failed');
			jest.spyOn(mockedS3Service, 'getSignedUrlPromise').mockRejectedValueOnce(error);

			await expect(storage.createSignatureUpload(mockedSignatureUploadParams)).rejects.toThrow(
				'Signature failed'
			);
		});

		it('should invoke S3 service with getSignedUrlPromise method', async () => {
			const mockUrl = faker.internet.url();
			jest.spyOn(mockedS3Service, 'getSignedUrlPromise').mockResolvedValueOnce(mockUrl);

			const result = await storage.createSignatureUpload(mockedSignatureUploadParams);

			expect(mockedS3Service.getSignedUrlPromise).toHaveBeenCalledWith('putObject', {
				Key: mockedSignatureUploadParams.fileName,
				Expires: 30 * 60,
				ContentType: mockedSignatureUploadParams.contentType,
				Bucket: mockedSignatureUploadParams.bucket
			});
			expect(result).toEqual({ url: mockUrl });
		});
	});

	describe('[getSignedUrl]', () => {
		it('should return throw error', async () => {
			const error = new Error('Signed URL failed');
			jest.spyOn(mockedS3Service, 'getSignedUrlPromise').mockRejectedValueOnce(error);

			await expect(storage.getSignedUrl(mockedSignedUrlParams)).rejects.toThrow('Signed URL failed');
		});

		it('should invoke S3 service with getSignedUrlPromise method', async () => {
			const mockUrl = faker.internet.url();
			jest.spyOn(mockedS3Service, 'getSignedUrlPromise').mockResolvedValueOnce(mockUrl);

			const result = await storage.getSignedUrl(mockedSignedUrlParams);

			expect(mockedS3Service.getSignedUrlPromise).toHaveBeenCalledWith('getObject', mockedSignedUrlParams);
			expect(result).toBe(mockUrl);
		});
	});

	describe('[createSignatureDownload]', () => {
		it('should return throw error', async () => {
			const error = new Error('Signature download failed');
			jest.spyOn(mockedS3Service, 'getSignedUrl').mockImplementationOnce(() => {
				throw error;
			});

			await expect(storage.createSignatureDownload(mockedSignatureDownloadParams)).rejects.toThrow(
				'Signature download failed'
			);
		});

		it('should invoke S3 service with getSignedUrl method', async () => {
			const mockUrl = faker.internet.url();
			jest.spyOn(mockedS3Service, 'getSignedUrl').mockReturnValueOnce(mockUrl);

			const result = await storage.createSignatureDownload(mockedSignatureDownloadParams);

			expect(mockedS3Service.getSignedUrl).toHaveBeenCalledWith('getObject', {
				Key: mockedSignatureDownloadParams.Key,
				Bucket: mockedSignatureDownloadParams.Bucket,
				Expires: mockedSignatureDownloadParams.Expires,
				ResponseContentDisposition: `attachment; filename ="${mockedSignatureDownloadParams.fileName}"`
			});
			expect(result).toBe(mockUrl);
		});
	});

	describe('[listObjects]', () => {
		it('should return throw error', async () => {
			const error = new Error('List objects failed');
			jest.spyOn(mockedS3Service, 'listObjectsV2').mockReturnValue({
				promise: jest.fn().mockRejectedValueOnce(error)
			});

			await expect(storage.listObjects(mockedListObjectsParams)).rejects.toThrow('List objects failed');
		});

		it('should invoke S3 service with listObjectsV2 method', async () => {
			const mockResponse = { Contents: [{ Key: 'file1.txt' }] };
			jest.spyOn(mockedS3Service, 'listObjectsV2').mockReturnValue({
				promise: jest.fn().mockResolvedValueOnce(mockResponse)
			});

			const result = await storage.listObjects(mockedListObjectsParams);

			expect(mockedS3Service.listObjectsV2).toHaveBeenCalledWith(mockedListObjectsParams);
			expect(result).toBe(mockResponse);
		});
	});

	describe('[removeObject]', () => {
		it('should return throw error', async () => {
			const error = new Error('Remove object failed');
			jest.spyOn(mockedS3Service, 'deleteObject').mockReturnValue({
				promise: jest.fn().mockRejectedValueOnce(error)
			});

			await expect(storage.removeObject(mockedRemoveObjectParams)).rejects.toThrow('Remove object failed');
		});

		it('should invoke S3 service with deleteObject method', async () => {
			const mockResponse = { DeleteMarker: true };
			jest.spyOn(mockedS3Service, 'deleteObject').mockReturnValue({
				promise: jest.fn().mockResolvedValueOnce(mockResponse)
			});

			const result = await storage.removeObject(mockedRemoveObjectParams);

			expect(mockedS3Service.deleteObject).toHaveBeenCalledWith(mockedRemoveObjectParams);
			expect(result).toBe(mockResponse);
		});
	});
});
