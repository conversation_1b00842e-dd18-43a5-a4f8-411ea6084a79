import Sequelize from 'sequelize';
import { jest } from '@jest/globals';
import { ActionPlanSandboxService } from '@src/app/service/sandbox/action_plan_sandbox.js';
import { AppError } from '@src/app/helpers/index.js';
import { ERROR_RESPONSE_ENTITIES_ENUM } from '@src/app/util/enum.js';
import {
	mockedActionPlanV2ForSandbox,
	mockedActionPlanOriginForSandbox,
	mockedActionPlanOriginProductionForSandbox,
	mockedActivityForSandbox,
	mockedActionPlanWorkstationForSandbox,
	mockedActionPlanLineForSandbox,
	mockedActionPlanSectorForSandbox,
	mockedActionPlanFileForSandbox,
	mockedActionPlanEvaluatorForSandbox,
	mockedActionPlanTasksForSandbox,
	mockedActionPlanAttachmentsForSandbox,
	mockedActionPlanTaskAttachmentsForSandbox,
	mockedActionPlanRelatedReportsForSandbox,
	mockedActionPlanHistoriesForSandbox,
	mockedActionPlanCommentsForSandbox,
	mockedRebaReportForActionPlanSandbox,
	mockedCustomReportResultForSandbox,
	mockedCustomReportStepKeyResultsListForSandbox,
	mockedCustomReportResultActionLogsListForSandbox,
	mockedCustomReportStepKeyAdditionalItemResultsListForSandbox,
	mockedCustomReportStepKeysAdditionalItemForSandbox,
	mockedTransaction
} from '../fixtures/sandbox.js';

const { Op } = Sequelize;

const transaction = mockedTransaction;

describe('[ActionPlanSandboxService] - service', () => {
	let kinebot_repository;
	let sandbox_repository;
	let service;
	let transactions;

	beforeEach(() => {
		transactions = {
			kinebot: transaction,
			sandbox: transaction
		};

		kinebot_repository = {
			db: {
				ActionPlanV2: {
					findOrCreate: jest.fn().mockResolvedValue([{ id: 'new-action-plan-id' }]),
					create: jest.fn()
				},
				ActionPlanOrigin: {
					findOrCreate: jest.fn().mockResolvedValue([{ id: 'new-origin-id' }]),
					create: jest.fn()
				},
				ActionPlanTask: {
					findOrCreate: jest.fn().mockResolvedValue([{ id: 'new-task-id' }]),
					create: jest.fn()
				},
				ActionPlanAttachment: {
					findOrCreate: jest.fn().mockResolvedValue([{ id: 'new-attachment-id' }]),
					create: jest.fn()
				},
				ActionPlanRelatedReport: {
					findOrCreate: jest.fn().mockResolvedValue([{ id: 'new-related-report-id' }]),
					create: jest.fn()
				},
				ActionPlanHistoryV2: {
					findOrCreate: jest.fn().mockResolvedValue([{ id: 'new-history-id' }]),
					create: jest.fn()
				},
				ActionPlanComment: {
					findOrCreate: jest.fn().mockResolvedValue([{ id: 'new-comment-id' }]),
					create: jest.fn()
				},
				ActionPlanTaskAttachment: {
					findOrCreate: jest.fn().mockResolvedValue([{ id: 'new-task-attachment-id' }]),
					create: jest.fn()
				},
				Activity: { findOrCreate: jest.fn().mockResolvedValue([{ id: 'new-activity-id' }]), create: jest.fn() },
				Workstation: {
					findOrCreate: jest.fn().mockResolvedValue([{ id: 'new-workstation-id' }]),
					create: jest.fn()
				},
				File: {
					findOrCreate: jest.fn().mockResolvedValue([{ id: 'new-file-id' }]),
					create: jest.fn(),
					findOne: jest.fn()
				},
				Sector: { findOrCreate: jest.fn().mockResolvedValue([{ id: 'new-sector-id' }]), create: jest.fn() },
				Line: { findOrCreate: jest.fn().mockResolvedValue([{ id: 'new-line-id' }]), create: jest.fn() },
				Evaluator: {
					findOrCreate: jest.fn().mockResolvedValue([{ id: 'new-evaluator-id' }]),
					create: jest.fn()
				},
				CustomReportResult: {
					findOrCreate: jest.fn().mockResolvedValue([{ id: 'new-custom-report-result-id' }]),
					create: jest.fn()
				},
				CustomReportStepKeyResult: {
					findOrCreate: jest.fn().mockResolvedValue([{ id: 'new-custom-report-step-key-result-id' }]),
					create: jest.fn()
				},
				CustomReportResultActionLog: {
					findOrCreate: jest.fn().mockResolvedValue([{ id: 'new-custom-report-result-action-log-id' }]),
					create: jest.fn()
				},
				CustomReportStepKeyAdditionalItemResult: {
					findOrCreate: jest
						.fn()
						.mockResolvedValue([{ id: 'new-custom-report-step-key-additional-item-result-id' }]),
					create: jest.fn()
				},
				CustomReportStepKeysAdditionalItem: {
					findOrCreate: jest
						.fn()
						.mockResolvedValue([{ id: 'new-custom-report-step-keys-additional-item-id' }]),
					create: jest.fn()
				},
				RebaReport: {
					findOrCreate: jest.fn().mockResolvedValue([{ id: 'new-reba-report-id' }]),
					create: jest.fn()
				},
				sequelize: {
					transaction: jest.fn().mockResolvedValue(transaction),
					query: jest.fn()
				}
			}
		};

		sandbox_repository = {
			db: {
				ActionPlanV2: {
					findByPk: jest.fn(),
					destroy: jest.fn()
				},
				ActionPlanOrigin: {
					findByPk: jest.fn(),
					destroy: jest.fn(),
					findOne: jest.fn()
				},
				ActionPlanTask: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				ActionPlanAttachment: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				ActionPlanRelatedReport: {
					findAll: jest.fn(),
					destroy: jest.fn(),
					findOne: jest.fn()
				},
				ActionPlanHistoryV2: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				ActionPlanComment: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				ActionPlanTaskAttachment: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				Activity: {
					findByPk: jest.fn()
				},
				CustomReportResult: {
					findByPk: jest.fn(),
					destroy: jest.fn()
				},
				CustomReportStepKeyResult: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				CustomReportResultActionLog: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				CustomReportStepKeyAdditionalItemResult: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				PEAToSuperPEA: {
					findOne: jest.fn()
				},
				SuperPeaReport: {
					findByPk: jest.fn()
				},
				RebaReport: {
					findByPk: jest.fn(),
					destroy: jest.fn()
				},
				sequelize: {
					transaction: jest.fn().mockResolvedValue(transaction),
					query: jest.fn()
				},
				File: {
					findByPk: jest.fn()
				},
				Workstation: {
					findByPk: jest.fn()
				},
				Sector: {
					findByPk: jest.fn()
				},
				Line: {
					findByPk: jest.fn()
				},
				Evaluator: {
					findByPk: jest.fn()
				},
				CustomReportStepKeysAdditionalItem: {
					findByPk: jest.fn()
				},
				ErgonomicTool: {
					findByPk: jest.fn()
				}
			}
		};

		service = new ActionPlanSandboxService({ kinebot_repository, sandbox_repository });
	});

	describe('sendReport', () => {
		it('should send action plan report and destroy sandbox data successfully', async () => {
			const mockActionPlan = { ...mockedActionPlanV2ForSandbox };
			const mockOrigin = { ...mockedActionPlanOriginForSandbox };
			const mockActivity = { ...mockedActivityForSandbox };
			const mockTasks = [...mockedActionPlanTasksForSandbox];
			const mockAttachments = [...mockedActionPlanAttachmentsForSandbox];
			const mockTaskAttachments = [...mockedActionPlanTaskAttachmentsForSandbox];
			const mockRelatedReports = [...mockedActionPlanRelatedReportsForSandbox];
			const mockHistories = [...mockedActionPlanHistoriesForSandbox];
			const mockComments = [...mockedActionPlanCommentsForSandbox];

			sandbox_repository.db.ActionPlanV2.findByPk.mockResolvedValue(mockActionPlan);
			sandbox_repository.db.ActionPlanOrigin.findByPk.mockResolvedValue(mockOrigin);
			sandbox_repository.db.Activity.findByPk.mockResolvedValue(mockActivity);
			sandbox_repository.db.ActionPlanTask.findAll.mockResolvedValue(mockTasks);
			sandbox_repository.db.ActionPlanAttachment.findAll.mockResolvedValue(mockAttachments);
			sandbox_repository.db.ActionPlanTaskAttachment.findAll.mockResolvedValue(mockTaskAttachments);
			sandbox_repository.db.ActionPlanRelatedReport.findAll.mockResolvedValue(mockRelatedReports);
			sandbox_repository.db.ActionPlanHistoryV2.findAll.mockResolvedValue(mockHistories);
			sandbox_repository.db.ActionPlanComment.findAll.mockResolvedValue(mockComments);
			sandbox_repository.db.sequelize.query.mockResolvedValue([{ id: mockedCustomReportResultForSandbox.id }]);

			sandbox_repository.db.CustomReportResult.findByPk.mockResolvedValue(mockedCustomReportResultForSandbox);
			sandbox_repository.db.CustomReportStepKeyResult.findAll.mockResolvedValue([]);
			sandbox_repository.db.CustomReportResultActionLog.findAll.mockResolvedValue([]);
			sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.findAll.mockResolvedValue([]);
			sandbox_repository.db.PEAToSuperPEA.findOne.mockResolvedValue(null);
			sandbox_repository.db.ActionPlanOrigin.findOne.mockResolvedValue(null);

			sandbox_repository.db.RebaReport.findByPk.mockResolvedValue(mockedRebaReportForActionPlanSandbox);
			sandbox_repository.db.ActionPlanRelatedReport.findOne.mockResolvedValue(null);

			sandbox_repository.db.File.findByPk.mockResolvedValue(mockedActionPlanFileForSandbox);
			sandbox_repository.db.Sector.findByPk.mockResolvedValue(mockedActionPlanSectorForSandbox);
			sandbox_repository.db.Line.findByPk.mockResolvedValue(mockedActionPlanLineForSandbox);
			sandbox_repository.db.Workstation.findByPk.mockResolvedValue(mockedActionPlanWorkstationForSandbox);
			sandbox_repository.db.Evaluator.findByPk.mockResolvedValue(mockedActionPlanEvaluatorForSandbox);
			sandbox_repository.db.CustomReportStepKeysAdditionalItem.findByPk.mockResolvedValue(
				mockedCustomReportStepKeysAdditionalItemForSandbox
			);

			service.getOrCreateSimpleDependency = jest.fn().mockResolvedValue({ id: 'new-workstation-id' });
			service.getOrCreateFile = jest.fn().mockResolvedValue({ id: 'new-file-id' });

			service.pea_service = { send: jest.fn().mockResolvedValue({ id: 'new-processed-report-id' }) };
			service.reba_service = { send: jest.fn().mockResolvedValue({ id: 'new-processed-reba-report-id' }) };
			service.create = jest.fn().mockImplementation((entity, model) => {
				if (model === 'ActionPlanV2') return Promise.resolve({ id: 'new-action-plan-id' });
				if (model === 'ActionPlanOrigin') return Promise.resolve({ id: 'new-origin-id' });
				if (model === 'ActionPlanTask') return Promise.resolve({ id: 'new-task-id' });
				return Promise.resolve({ id: 'new-entity-id' });
			});

			kinebot_repository.db.Activity.findOrCreate.mockResolvedValue([{ id: 'new-activity-id' }]);

			sandbox_repository.db.ActionPlanComment.destroy.mockResolvedValue(1);
			sandbox_repository.db.ActionPlanHistoryV2.destroy.mockResolvedValue(1);
			sandbox_repository.db.ActionPlanRelatedReport.destroy.mockResolvedValue(1);
			sandbox_repository.db.ActionPlanTaskAttachment.destroy.mockResolvedValue(1);
			sandbox_repository.db.ActionPlanTask.destroy.mockResolvedValue(1);
			sandbox_repository.db.ActionPlanAttachment.destroy.mockResolvedValue(1);
			sandbox_repository.db.ActionPlanV2.destroy.mockResolvedValue(1);
			sandbox_repository.db.ActionPlanOrigin.destroy.mockResolvedValue(1);

			sandbox_repository.db.CustomReportStepKeyResult.destroy.mockResolvedValue(0);
			sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.destroy.mockResolvedValue(0);
			sandbox_repository.db.CustomReportResultActionLog.destroy.mockResolvedValue(0);
			sandbox_repository.db.CustomReportResult.destroy.mockResolvedValue(1);

			sandbox_repository.db.RebaReport.destroy.mockResolvedValue(1);

			await service.sendReport(mockActionPlan.id);

			expect(sandbox_repository.db.ActionPlanV2.findByPk).toHaveBeenCalledWith(mockActionPlan.id, {
				transaction: transaction
			});

			expect(sandbox_repository.db.ActionPlanOrigin.findByPk).toHaveBeenCalledWith(mockOrigin.id, {
				transaction: transaction
			});

			expect(sandbox_repository.db.ActionPlanTask.findAll).toHaveBeenCalledWith({
				where: { action_plan_id: mockActionPlan.id },
				transaction: transaction
			});

			expect(kinebot_repository.db.Activity.findOrCreate).toHaveBeenCalled();

			expect(service.create).toHaveBeenCalledWith(
				expect.objectContaining({
					workstation_id: 'new-workstation-id',
					file_id: 'new-file-id',
					activity_id: 'new-activity-id'
				}),
				'ActionPlanV2',
				transactions
			);

			expect(sandbox_repository.db.ActionPlanComment.destroy).toHaveBeenCalledWith({
				where: { action_plan_id: mockActionPlan.id },
				force: true,
				transaction: transaction
			});

			expect(sandbox_repository.db.ActionPlanV2.destroy).toHaveBeenCalledWith({
				where: { id: mockActionPlan.id },
				force: true,
				transaction: transaction
			});

			expect(transaction.commit).toHaveBeenCalled();
		});

		it('should throw error when action plan not found', async () => {
			const action_plan_id = 'non-existent-id';

			sandbox_repository.db.ActionPlanV2.findByPk.mockResolvedValue(null);

			await expect(service.sendReport(action_plan_id)).rejects.toThrow(AppError);
			await expect(service.sendReport(action_plan_id)).rejects.toThrow(
				ERROR_RESPONSE_ENTITIES_ENUM.ACTION_PLAN.NOT_FOUND
			);

			expect(transaction.rollback).toHaveBeenCalled();
		});

		it('should handle action plan without activity', async () => {
			const mockActionPlan = {
				...mockedActionPlanV2ForSandbox,
				activity_id: null
			};

			const mockOrigin = { ...mockedActionPlanOriginForSandbox };

			sandbox_repository.db.ActionPlanV2.findByPk.mockResolvedValue(mockActionPlan);
			sandbox_repository.db.ActionPlanOrigin.findByPk.mockResolvedValue(mockOrigin);
			sandbox_repository.db.ActionPlanTask.findAll.mockResolvedValue([]);
			sandbox_repository.db.ActionPlanAttachment.findAll.mockResolvedValue([]);
			sandbox_repository.db.ActionPlanTaskAttachment.findAll.mockResolvedValue([]);
			sandbox_repository.db.ActionPlanRelatedReport.findAll.mockResolvedValue([]);
			sandbox_repository.db.ActionPlanHistoryV2.findAll.mockResolvedValue([]);
			sandbox_repository.db.ActionPlanComment.findAll.mockResolvedValue([]);

			sandbox_repository.db.sequelize.query.mockResolvedValue([{ id: mockedCustomReportResultForSandbox.id }]);

			sandbox_repository.db.CustomReportResult.findByPk.mockResolvedValue(mockedCustomReportResultForSandbox);
			sandbox_repository.db.CustomReportStepKeyResult.findAll.mockResolvedValue([]);
			sandbox_repository.db.CustomReportResultActionLog.findAll.mockResolvedValue([]);
			sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.findAll.mockResolvedValue([]);
			sandbox_repository.db.PEAToSuperPEA.findOne.mockResolvedValue(null);
			sandbox_repository.db.ActionPlanOrigin.findOne.mockResolvedValue(null);

			sandbox_repository.db.File.findByPk.mockResolvedValue(mockedActionPlanFileForSandbox);
			sandbox_repository.db.Sector.findByPk.mockResolvedValue(mockedActionPlanSectorForSandbox);
			sandbox_repository.db.Line.findByPk.mockResolvedValue(mockedActionPlanLineForSandbox);
			sandbox_repository.db.Workstation.findByPk.mockResolvedValue(mockedActionPlanWorkstationForSandbox);
			sandbox_repository.db.Evaluator.findByPk.mockResolvedValue(mockedActionPlanEvaluatorForSandbox);
			sandbox_repository.db.CustomReportStepKeysAdditionalItem.findByPk.mockResolvedValue(
				mockedCustomReportStepKeysAdditionalItemForSandbox
			);

			service.getOrCreateSimpleDependency = jest.fn().mockResolvedValue({ id: 'new-workstation-id' });
			service.getOrCreateFile = jest.fn().mockResolvedValue({ id: 'new-file-id' });

			service.pea_service = { send: jest.fn().mockResolvedValue({ id: 'new-processed-report-id' }) };
			service.reba_service = { send: jest.fn().mockResolvedValue({ id: 'new-processed-reba-report-id' }) };

			service.create = jest.fn().mockResolvedValue({ id: 'new-action-plan-id' });

			sandbox_repository.db.ActionPlanComment.destroy.mockResolvedValue(0);
			sandbox_repository.db.ActionPlanHistoryV2.destroy.mockResolvedValue(0);
			sandbox_repository.db.ActionPlanRelatedReport.destroy.mockResolvedValue(0);
			sandbox_repository.db.ActionPlanTaskAttachment.destroy.mockResolvedValue(0);
			sandbox_repository.db.ActionPlanTask.destroy.mockResolvedValue(0);
			sandbox_repository.db.ActionPlanAttachment.destroy.mockResolvedValue(0);
			sandbox_repository.db.ActionPlanV2.destroy.mockResolvedValue(1);
			sandbox_repository.db.ActionPlanOrigin.destroy.mockResolvedValue(1);

			await service.sendReport(mockActionPlan.id);

			expect(sandbox_repository.db.Activity.findByPk).not.toHaveBeenCalled();
			expect(kinebot_repository.db.Activity.findOrCreate).not.toHaveBeenCalled();

			expect(transaction.commit).toHaveBeenCalled();
		});

		it('should handle production origin tables', async () => {
			const mockActionPlan = { ...mockedActionPlanV2ForSandbox };
			const mockOrigin = { ...mockedActionPlanOriginProductionForSandbox };
			const mockActivity = { ...mockedActivityForSandbox };

			sandbox_repository.db.ActionPlanV2.findByPk.mockResolvedValue(mockActionPlan);
			sandbox_repository.db.ActionPlanOrigin.findByPk.mockResolvedValue(mockOrigin);
			sandbox_repository.db.Activity.findByPk.mockResolvedValue(mockActivity);
			sandbox_repository.db.ActionPlanTask.findAll.mockResolvedValue([]);
			sandbox_repository.db.ActionPlanAttachment.findAll.mockResolvedValue([]);
			sandbox_repository.db.ActionPlanTaskAttachment.findAll.mockResolvedValue([]);
			sandbox_repository.db.ActionPlanRelatedReport.findAll.mockResolvedValue([]);
			sandbox_repository.db.ActionPlanHistoryV2.findAll.mockResolvedValue([]);
			sandbox_repository.db.ActionPlanComment.findAll.mockResolvedValue([]);

			sandbox_repository.db.File.findByPk.mockResolvedValue(mockedActionPlanFileForSandbox);
			sandbox_repository.db.Sector.findByPk.mockResolvedValue(mockedActionPlanSectorForSandbox);
			sandbox_repository.db.Line.findByPk.mockResolvedValue(mockedActionPlanLineForSandbox);
			sandbox_repository.db.Workstation.findByPk.mockResolvedValue(mockedActionPlanWorkstationForSandbox);

			service.getOrCreateSimpleDependency = jest.fn().mockResolvedValue({ id: 'new-workstation-id' });
			service.getOrCreateFile = jest.fn().mockResolvedValue({ id: 'new-file-id' });
			service.create = jest.fn().mockResolvedValue({ id: 'new-action-plan-id' });

			kinebot_repository.db.Activity.findOrCreate.mockResolvedValue([{ id: 'new-activity-id' }]);

			sandbox_repository.db.ActionPlanComment.destroy.mockResolvedValue(0);
			sandbox_repository.db.ActionPlanHistoryV2.destroy.mockResolvedValue(0);
			sandbox_repository.db.ActionPlanRelatedReport.destroy.mockResolvedValue(0);
			sandbox_repository.db.ActionPlanTaskAttachment.destroy.mockResolvedValue(0);
			sandbox_repository.db.ActionPlanTask.destroy.mockResolvedValue(0);
			sandbox_repository.db.ActionPlanAttachment.destroy.mockResolvedValue(0);
			sandbox_repository.db.ActionPlanV2.destroy.mockResolvedValue(1);
			sandbox_repository.db.ActionPlanOrigin.destroy.mockResolvedValue(1);

			await service.sendReport(mockActionPlan.id);

			expect(sandbox_repository.db.sequelize.query).not.toHaveBeenCalled();

			expect(transaction.commit).toHaveBeenCalled();
		});

		it('should rollback on error', async () => {
			const action_plan_id = 'action-plan-id-123';

			sandbox_repository.db.ActionPlanV2.findByPk.mockRejectedValue(new Error('Database error'));

			await expect(service.sendReport(action_plan_id)).rejects.toThrow('Database error');

			expect(transaction.rollback).toHaveBeenCalled();
		});
	});
});
