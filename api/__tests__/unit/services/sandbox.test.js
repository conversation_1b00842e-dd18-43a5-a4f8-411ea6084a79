import { jest } from '@jest/globals';
import { SandboxService } from '@src/app/service/sandbox.js';
import { StorageContext } from '@src/app/utils/storage_context.js';

describe('[SandboxService] - service', () => {
	let kinebot_repository;
	let sandbox_repository;
	let service;
	let mockTransaction;

	beforeEach(() => {
		jest.clearAllMocks();
		jest.spyOn(StorageContext, 'getStore').mockReturnValue({ user_id: 'test-user-id' });

		mockTransaction = {
			commit: jest.fn().mockResolvedValue(undefined),
			rollback: jest.fn().mockResolvedValue(undefined)
		};

		kinebot_repository = {
			db: {}
		};

		sandbox_repository = {
			db: {
				sequelize: {
					transaction: jest.fn().mockResolvedValue(mockTransaction)
				},
				KimManualHandlingReport: { destroy: jest.fn().mockResolvedValue(1) },
				KimPushPullReport: { destroy: jest.fn().mockResolvedValue(1) },
				RecoveryReport: { destroy: jest.fn().mockResolvedValue(1) },
				StrainIndexReport: { destroy: jest.fn().mockResolvedValue(1) },
				NioshReport: { destroy: jest.fn().mockResolvedValue(1) },
				RebaReport: { destroy: jest.fn().mockResolvedValue(1) },
				BackCompressiveForceEstimationReport: { destroy: jest.fn().mockResolvedValue(1) },
				AngleTimeReport: { destroy: jest.fn().mockResolvedValue(1) },
				BeraJobSummary: {
					findAll: jest.fn().mockResolvedValue([{ id: 'bera-1' }, { id: 'bera-2' }]),
					destroy: jest.fn().mockResolvedValue(1)
				},
				BeraReport: { destroy: jest.fn().mockResolvedValue(1) },
				BeraJobSummaryFiles: { destroy: jest.fn().mockResolvedValue(1) },
				CustomReportResult: {
					findAll: jest.fn().mockResolvedValue([{ id: 'custom-1' }, { id: 'custom-2' }]),
					destroy: jest.fn().mockResolvedValue(1)
				},
				CustomReportStepKeyResult: { destroy: jest.fn().mockResolvedValue(1) },
				CustomReportStepKeyAdditionalItemResult: { destroy: jest.fn().mockResolvedValue(1) },
				CustomReportResultActionLog: { destroy: jest.fn().mockResolvedValue(1) },
				CustomReportReview: { destroy: jest.fn().mockResolvedValue(1) },
				LibertyMutualReport: {
					findAll: jest.fn().mockResolvedValue([{ id: 'liberty-1' }]),
					destroy: jest.fn().mockResolvedValue(1)
				},
				LibertyMutualReportInput: { destroy: jest.fn().mockResolvedValue(1) },
				SuperPeaReport: {
					findAll: jest.fn().mockResolvedValue([{ id: 'super-pea-1' }]),
					destroy: jest.fn().mockResolvedValue(1)
				},
				PEAToSuperPEA: { destroy: jest.fn().mockResolvedValue(1) },
				SeraSummary: {
					findAll: jest.fn().mockResolvedValue([{ id: 'sera-1' }]),
					destroy: jest.fn().mockResolvedValue(1)
				},
				SeraSummaryReview: {
					findAll: jest.fn().mockResolvedValue([{ id: 'sera-review-1', sera_report_id: 'sera-report-1' }]),
					destroy: jest.fn().mockResolvedValue(1)
				},
				SeraReport: {
					findAll: jest.fn().mockResolvedValue([]),
					destroy: jest.fn().mockResolvedValue(1)
				},
				SeraReportUpdated: { destroy: jest.fn().mockResolvedValue(1) },
				SeraReviewTasksResult: { destroy: jest.fn().mockResolvedValue(1) },
				SeraSummaryFiles: { destroy: jest.fn().mockResolvedValue(1) },
				SeraReviewSelector: { destroy: jest.fn().mockResolvedValue(1) },
				ActionPlanV2: {
					findAll: jest.fn().mockResolvedValue([{ id: 'action-plan-1', action_plan_origin_id: 'origin-1' }])
				},
				ActionPlan: {
					destroy: jest.fn().mockResolvedValue(1)
				},
				ActionPlanComment: { destroy: jest.fn().mockResolvedValue(1) },
				ActionPlanHistoryV2: { destroy: jest.fn().mockResolvedValue(1) },
				ActionPlanRelatedReport: { destroy: jest.fn().mockResolvedValue(1) },
				ActionPlanAttachment: { destroy: jest.fn().mockResolvedValue(1) },
				ActionPlanOrigin: { destroy: jest.fn().mockResolvedValue(1) },
				File: {
					findAll: jest.fn().mockResolvedValue([{ id: 'file-1' }]),
					destroy: jest.fn().mockResolvedValue(1)
				},
				TasksFiles: { destroy: jest.fn().mockResolvedValue(1) },
				FileRiskResult: { destroy: jest.fn().mockResolvedValue(1) }
			}
		};

		service = new SandboxService({ kinebot_repository, sandbox_repository });
	});

	describe('[sendReport]', () => {
		it('should send BERA report', async () => {
			const params = {
				report_type: 'BERA',
				id: 'test-id'
			};

			const mockStrategy = {
				sendReport: jest.fn().mockResolvedValue(undefined)
			};

			jest.spyOn(service.strategy, 'setStrategy').mockImplementation(() => {
				service.strategy.strategy = mockStrategy;
			});

			await service.sendReport(params);

			expect(service.strategy.setStrategy).toHaveBeenCalledWith('BERA');
			expect(mockStrategy.sendReport).toHaveBeenCalledWith('test-id');
		});

		it('should send another type of report', async () => {
			const params = {
				report_type: 'OTHER_REPORT',
				id: 'another-test-id'
			};

			const mockStrategy = {
				sendReport: jest.fn().mockResolvedValue(undefined)
			};

			jest.spyOn(service.strategy, 'setStrategy').mockImplementation(() => {
				service.strategy.strategy = mockStrategy;
			});

			await service.sendReport(params);

			expect(service.strategy.setStrategy).toHaveBeenCalledWith('OTHER_REPORT');
			expect(mockStrategy.sendReport).toHaveBeenCalledWith('another-test-id');
		});
	});

	describe('[logout]', () => {
		const user_id = 'test-user-id';

		beforeEach(() => {
			jest.spyOn(StorageContext, 'getStore').mockReturnValue({ user_id });
		});

		it('should successfully clean all sandbox data and logout user', async () => {
			await service.logout();

			expect(StorageContext.getStore).toHaveBeenCalled();

			expect(sandbox_repository.db.sequelize.transaction).toHaveBeenCalled();

			expect(sandbox_repository.db.KimManualHandlingReport.destroy).toHaveBeenCalled();
			expect(sandbox_repository.db.KimPushPullReport.destroy).toHaveBeenCalled();

			expect(sandbox_repository.db.BeraJobSummary.findAll).toHaveBeenCalled();
			expect(sandbox_repository.db.BeraReport.destroy).toHaveBeenCalled();

			expect(mockTransaction.commit).toHaveBeenCalled();
		});

		it('should throw error when user_id is not found', async () => {
			jest.spyOn(StorageContext, 'getStore').mockReturnValue(null);

			await expect(service.logout()).rejects.toThrow();

			expect(sandbox_repository.db.sequelize.transaction).not.toHaveBeenCalled();
		});

		it('should throw error when user_id is undefined', async () => {
			jest.spyOn(StorageContext, 'getStore').mockReturnValue({});

			await expect(service.logout()).rejects.toThrow();

			expect(sandbox_repository.db.sequelize.transaction).not.toHaveBeenCalled();
		});

		it('should handle empty data gracefully', async () => {
			sandbox_repository.db.BeraJobSummary.findAll.mockResolvedValue([]);
			sandbox_repository.db.CustomReportResult.findAll.mockResolvedValue([]);
			sandbox_repository.db.LibertyMutualReport.findAll.mockResolvedValue([]);
			sandbox_repository.db.SuperPeaReport.findAll.mockResolvedValue([]);
			sandbox_repository.db.SeraSummary.findAll.mockResolvedValue([]);
			sandbox_repository.db.ActionPlanV2.findAll.mockResolvedValue([]);
			sandbox_repository.db.File.findAll.mockResolvedValue([]);

			await service.logout();

			expect(mockTransaction.commit).toHaveBeenCalled();
		});

		it('should rollback transaction and throw error on failure', async () => {
			const mockError = new Error('Database error during cleanup');

			sandbox_repository.db.KimManualHandlingReport.destroy.mockRejectedValue(mockError);

			await expect(service.logout()).rejects.toThrow('Database error during cleanup');

			expect(sandbox_repository.db.sequelize.transaction).toHaveBeenCalled();

			expect(mockTransaction.rollback).toHaveBeenCalled();

			expect(mockTransaction.commit).not.toHaveBeenCalled();
		});

		it('should handle transaction creation failure', async () => {
			const mockError = new Error('Failed to create transaction');
			sandbox_repository.db.sequelize.transaction.mockRejectedValue(mockError);

			await expect(service.logout()).rejects.toThrow('Failed to create transaction');
		});

		it('should handle cleanup errors in specific models', async () => {
			const mockError = new Error('Bera cleanup failed');
			sandbox_repository.db.BeraJobSummary.findAll.mockRejectedValue(mockError);

			await expect(service.logout()).rejects.toThrow('Bera cleanup failed');

			expect(mockTransaction.rollback).toHaveBeenCalled();
			expect(mockTransaction.commit).not.toHaveBeenCalled();
		});
	});
});
