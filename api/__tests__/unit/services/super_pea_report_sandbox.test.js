import Sequelize from 'sequelize';
import { jest } from '@jest/globals';
import { SuperPeaReportSandboxService } from '@src/app/service/sandbox/super_pea_report_sandbox.js';

const { Op } = Sequelize;

describe('[SuperPeaReportSandboxService] - service', () => {
	let kinebot_repository;
	let sandbox_repository;
	let service;
	let transaction;

	beforeEach(() => {
		transaction = {
			commit: jest.fn(),
			rollback: jest.fn()
		};

		kinebot_repository = {
			db: {
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) },
				File: { findOne: jest.fn(), findOrCreate: jest.fn(), create: jest.fn() },
				Sector: { findOrCreate: jest.fn(), create: jest.fn() },
				Line: { findOrCreate: jest.fn(), create: jest.fn() },
				Workstation: { findOrCreate: jest.fn(), create: jest.fn() },
				Evaluator: { findOrCreate: jest.fn(), create: jest.fn() },
				CustomReportResult: { findOrCreate: jest.fn(), create: jest.fn() },
				CustomReportStepKeyResult: { findOrCreate: jest.fn(), create: jest.fn() },
				CustomReportResultActionLog: { findOrCreate: jest.fn(), create: jest.fn() },
				CustomReportStepKeyAdditionalItemResult: { findOrCreate: jest.fn(), create: jest.fn() },
				CustomReportStepKeysAdditionalItem: { findOrCreate: jest.fn(), create: jest.fn() },
				SuperPeaReport: { findOrCreate: jest.fn(), create: jest.fn() },
				PEAToSuperPEA: { bulkCreate: jest.fn() }
			}
		};

		sandbox_repository = {
			db: {
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) },
				SuperPeaReport: {
					findByPk: jest.fn(),
					destroy: jest.fn()
				},
				CustomReportResult: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				CustomReportStepKeyResult: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				CustomReportResultActionLog: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				CustomReportStepKeyAdditionalItemResult: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				PEAToSuperPEA: {
					destroy: jest.fn()
				},
				File: {
					findByPk: jest.fn()
				},
				Evaluator: {
					findByPk: jest.fn()
				},
				CustomReportStepKeysAdditionalItem: {
					findByPk: jest.fn()
				}
			}
		};

		service = new SuperPeaReportSandboxService({ kinebot_repository, sandbox_repository });
	});

	it('should send report and destroy sandbox data', async () => {
		const super_pea_report_id = 'super-pea-report-uuid';
		const pea_id_1 = 'pea-1-uuid';
		const pea_id_2 = 'pea-2-uuid';
		const file_id_1 = 'file-1-uuid';
		const file_id_2 = 'file-2-uuid';
		const evaluator_id_1 = 'evaluator-1-uuid';
		const evaluator_id_2 = 'evaluator-2-uuid';
		const step_key_result_id = 'step-key-result-uuid';
		const action_log_id = 'action-log-uuid';
		const additional_item_result_id = 'additional-item-result-uuid';
		const additional_item_id = 'additional-item-uuid';

		const super_pea_report = {
			id: super_pea_report_id,
			pea_ids: [pea_id_1, pea_id_2],
			name: 'Super PEA Report Test'
		};

		const custom_report_results = [
			{
				id: pea_id_1,
				file_id: file_id_1,
				evaluator_id: evaluator_id_1,
				collection_date: '2024-11-07T17:14:41.000Z'
			},
			{
				id: pea_id_2,
				file_id: file_id_2,
				evaluator_id: evaluator_id_2,
				collection_date: '2024-11-07T17:14:41.000Z'
			}
		];

		const custom_report_step_key_results = [
			{
				id: step_key_result_id,
				custom_report_result_id: pea_id_1
			}
		];

		const custom_report_result_actions_logs = [
			{
				id: action_log_id,
				custom_report_result_id: pea_id_1
			}
		];

		const custom_report_step_key_additional_item_results = [
			{
				id: additional_item_result_id,
				custom_report_result_id: pea_id_1,
				custom_report_step_key_additional_item_id: additional_item_id
			}
		];

		sandbox_repository.db.SuperPeaReport.findByPk.mockResolvedValue(super_pea_report);
		sandbox_repository.db.CustomReportResult.findAll.mockResolvedValue(custom_report_results);
		sandbox_repository.db.CustomReportStepKeyResult.findAll.mockResolvedValue(custom_report_step_key_results);
		sandbox_repository.db.CustomReportResultActionLog.findAll.mockResolvedValue(custom_report_result_actions_logs);
		sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.findAll.mockResolvedValue(
			custom_report_step_key_additional_item_results
		);

		const mockFile1 = { id: 'new-file-1-id' };
		const mockFile2 = { id: 'new-file-2-id' };
		const mockEvaluator1 = { id: 'new-evaluator-1-id' };
		const mockEvaluator2 = { id: 'new-evaluator-2-id' };
		const mockAdditionalItem = { id: 'new-additional-item-id' };

		service.getOrCreateFile = jest.fn().mockResolvedValueOnce(mockFile1).mockResolvedValueOnce(mockFile2);

		service.getOrCreateSimpleDependency = jest.fn().mockImplementation((id, model) => {
			if (model === 'Evaluator') {
				if (id === evaluator_id_1) return Promise.resolve(mockEvaluator1);
				if (id === evaluator_id_2) return Promise.resolve(mockEvaluator2);
			}
			if (model === 'CustomReportStepKeysAdditionalItem') return Promise.resolve(mockAdditionalItem);
			return Promise.resolve({ id: 'new-id' });
		});

		service.create = jest
			.fn()
			.mockResolvedValueOnce({ id: 'new-pea-1-id' })
			.mockResolvedValueOnce({ id: 'new-pea-2-id' })
			.mockResolvedValueOnce({ id: 'new-super-pea-id' })
			.mockResolvedValue({ id: 'new-created-id' });

		kinebot_repository.db.PEAToSuperPEA.bulkCreate.mockResolvedValue([]);

		sandbox_repository.db.CustomReportStepKeyResult.destroy.mockResolvedValue(1);
		sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.destroy.mockResolvedValue(1);
		sandbox_repository.db.CustomReportResultActionLog.destroy.mockResolvedValue(1);
		sandbox_repository.db.CustomReportResult.destroy.mockResolvedValue(2);
		sandbox_repository.db.PEAToSuperPEA.destroy.mockResolvedValue(2);
		sandbox_repository.db.SuperPeaReport.destroy.mockResolvedValue(1);

		await service.sendReport(super_pea_report_id);

		expect(sandbox_repository.db.SuperPeaReport.findByPk).toHaveBeenCalledWith(super_pea_report_id, {
			transaction: transaction
		});

		expect(sandbox_repository.db.CustomReportResult.findAll).toHaveBeenCalledWith({
			where: { id: { [Op.in]: [pea_id_1, pea_id_2] } },
			transaction: transaction
		});

		expect(sandbox_repository.db.CustomReportStepKeyResult.findAll).toHaveBeenCalledWith({
			where: { custom_report_result_id: { [Op.in]: [pea_id_1, pea_id_2] } },
			transaction: transaction
		});

		expect(sandbox_repository.db.CustomReportResultActionLog.findAll).toHaveBeenCalledWith({
			where: { custom_report_result_id: { [Op.in]: [pea_id_1, pea_id_2] } },
			transaction: transaction
		});

		expect(sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.findAll).toHaveBeenCalledWith({
			where: { custom_report_result_id: { [Op.in]: [pea_id_1, pea_id_2] } },
			transaction: transaction
		});

		expect(service.getOrCreateFile).toHaveBeenCalledWith(file_id_1, { kinebot: transaction, sandbox: transaction });
		expect(service.getOrCreateFile).toHaveBeenCalledWith(file_id_2, { kinebot: transaction, sandbox: transaction });
		expect(service.getOrCreateSimpleDependency).toHaveBeenCalledWith(evaluator_id_1, 'Evaluator', {
			kinebot: transaction,
			sandbox: transaction
		});
		expect(service.getOrCreateSimpleDependency).toHaveBeenCalledWith(evaluator_id_2, 'Evaluator', {
			kinebot: transaction,
			sandbox: transaction
		});
		expect(service.getOrCreateSimpleDependency).toHaveBeenCalledWith(
			additional_item_id,
			'CustomReportStepKeysAdditionalItem',
			{ kinebot: transaction, sandbox: transaction },
			{
				where_fields: ['custom_report_step_key_id', 'additional_item_id'],
				exclude_fields: [],
				dependencies: {}
			}
		);

		expect(service.create).toHaveBeenCalledWith(
			expect.objectContaining({ file_id: mockFile1.id, evaluator_id: mockEvaluator1.id }),
			'CustomReportResult',
			{ kinebot: transaction, sandbox: transaction }
		);

		expect(service.create).toHaveBeenCalledWith(
			expect.objectContaining({ file_id: mockFile2.id, evaluator_id: mockEvaluator2.id }),
			'CustomReportResult',
			{ kinebot: transaction, sandbox: transaction }
		);

		expect(service.create).toHaveBeenCalledWith(
			expect.objectContaining({ pea_ids: ['new-pea-1-id', 'new-pea-2-id'] }),
			'SuperPeaReport',
			{ kinebot: transaction, sandbox: transaction }
		);

		expect(kinebot_repository.db.PEAToSuperPEA.bulkCreate).toHaveBeenCalledWith(
			[
				{ pea_id: 'new-pea-1-id', super_pea_id: 'new-super-pea-id' },
				{ pea_id: 'new-pea-2-id', super_pea_id: 'new-super-pea-id' }
			],
			{ transaction: transaction }
		);

		expect(service.create).toHaveBeenCalledWith(expect.any(Object), 'CustomReportResultActionLog', {
			kinebot: transaction,
			sandbox: transaction
		});

		expect(service.create).toHaveBeenCalledWith(
			expect.objectContaining({ custom_report_step_key_additional_item_id: mockAdditionalItem.id }),
			'CustomReportStepKeyAdditionalItemResult',
			{ kinebot: transaction, sandbox: transaction }
		);

		expect(service.create).toHaveBeenCalledWith(expect.any(Object), 'CustomReportStepKeyResult', {
			kinebot: transaction,
			sandbox: transaction
		});

		expect(sandbox_repository.db.CustomReportStepKeyResult.destroy).toHaveBeenCalledWith({
			where: { id: { [Op.in]: [step_key_result_id] } },
			transaction: transaction,
			force: true
		});

		expect(sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.destroy).toHaveBeenCalledWith({
			where: { id: { [Op.in]: [additional_item_result_id] } },
			transaction: transaction,
			force: true
		});

		expect(sandbox_repository.db.CustomReportResultActionLog.destroy).toHaveBeenCalledWith({
			where: { id: { [Op.in]: [action_log_id] } },
			transaction: transaction,
			force: true
		});

		expect(sandbox_repository.db.CustomReportResult.destroy).toHaveBeenCalledWith({
			where: { id: { [Op.in]: ['new-pea-1-id', 'new-pea-2-id'] } },
			transaction: transaction,
			force: true
		});

		expect(sandbox_repository.db.PEAToSuperPEA.destroy).toHaveBeenCalledWith({
			where: { super_pea_id: super_pea_report_id },
			transaction: transaction,
			force: true
		});

		expect(sandbox_repository.db.SuperPeaReport.destroy).toHaveBeenCalledWith({
			where: { id: super_pea_report_id },
			transaction: transaction,
			force: true
		});

		expect(transaction.commit).toHaveBeenCalledTimes(2);
	});

	it('should rollback on error', async () => {
		sandbox_repository.db.SuperPeaReport.findByPk.mockRejectedValue(new Error('Database error'));

		await expect(service.sendReport('any-id')).rejects.toThrow('Database error');

		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should handle empty related data', async () => {
		const super_pea_report_id = 'super-pea-report-uuid';
		const super_pea_report = {
			id: super_pea_report_id,
			pea_ids: [],
			name: 'Empty Super PEA Report'
		};

		sandbox_repository.db.SuperPeaReport.findByPk.mockResolvedValue(super_pea_report);
		sandbox_repository.db.CustomReportResult.findAll.mockResolvedValue([]);
		sandbox_repository.db.CustomReportStepKeyResult.findAll.mockResolvedValue([]);
		sandbox_repository.db.CustomReportResultActionLog.findAll.mockResolvedValue([]);
		sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.findAll.mockResolvedValue([]);

		service.create = jest.fn().mockResolvedValue({ id: 'new-super-pea-id' });
		kinebot_repository.db.PEAToSuperPEA.bulkCreate.mockResolvedValue([]);

		sandbox_repository.db.CustomReportStepKeyResult.destroy.mockResolvedValue(0);
		sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.destroy.mockResolvedValue(0);
		sandbox_repository.db.CustomReportResultActionLog.destroy.mockResolvedValue(0);
		sandbox_repository.db.CustomReportResult.destroy.mockResolvedValue(0);
		sandbox_repository.db.PEAToSuperPEA.destroy.mockResolvedValue(0);
		sandbox_repository.db.SuperPeaReport.destroy.mockResolvedValue(1);

		await service.sendReport(super_pea_report_id);

		expect(kinebot_repository.db.PEAToSuperPEA.bulkCreate).toHaveBeenCalledWith([], { transaction: transaction });
		expect(transaction.commit).toHaveBeenCalledTimes(2);
	});

	it('should handle missing custom_report_step_key_additional_item_id', async () => {
		const super_pea_report_id = 'super-pea-report-uuid';
		const pea_id = 'pea-uuid';
		const super_pea_report = { id: super_pea_report_id, pea_ids: [pea_id] };
		const custom_report_results = [{ id: pea_id, file_id: 'file-id', evaluator_id: 'evaluator-id' }];
		const custom_report_step_key_additional_item_results = [
			{
				id: 'additional-item-result-id',
				custom_report_result_id: pea_id,
				custom_report_step_key_additional_item_id: null
			}
		];

		sandbox_repository.db.SuperPeaReport.findByPk.mockResolvedValue(super_pea_report);
		sandbox_repository.db.CustomReportResult.findAll.mockResolvedValue(custom_report_results);
		sandbox_repository.db.CustomReportStepKeyResult.findAll.mockResolvedValue([]);
		sandbox_repository.db.CustomReportResultActionLog.findAll.mockResolvedValue([]);
		sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.findAll.mockResolvedValue(
			custom_report_step_key_additional_item_results
		);

		service.getOrCreateFile = jest.fn().mockResolvedValue({ id: 'file-id' });
		service.getOrCreateSimpleDependency = jest.fn().mockResolvedValue({ id: 'evaluator-id' });
		service.create = jest
			.fn()
			.mockResolvedValueOnce({ id: 'new-pea-id' })
			.mockResolvedValueOnce({ id: 'new-super-pea-id' })
			.mockResolvedValue({ id: 'new-id' });

		kinebot_repository.db.PEAToSuperPEA.bulkCreate.mockResolvedValue([]);

		sandbox_repository.db.CustomReportStepKeyResult.destroy.mockResolvedValue(0);
		sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.destroy.mockResolvedValue(1);
		sandbox_repository.db.CustomReportResultActionLog.destroy.mockResolvedValue(0);
		sandbox_repository.db.CustomReportResult.destroy.mockResolvedValue(1);
		sandbox_repository.db.PEAToSuperPEA.destroy.mockResolvedValue(1);
		sandbox_repository.db.SuperPeaReport.destroy.mockResolvedValue(1);

		await service.sendReport(super_pea_report_id);

		expect(service.getOrCreateSimpleDependency).not.toHaveBeenCalledWith(
			null,
			'CustomReportStepKeysAdditionalItem',
			expect.any(Object),
			expect.any(Object)
		);

		expect(transaction.commit).toHaveBeenCalledTimes(2);
	});
});
