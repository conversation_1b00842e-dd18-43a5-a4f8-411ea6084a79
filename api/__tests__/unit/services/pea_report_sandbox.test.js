import Sequelize from 'sequelize';
import { jest } from '@jest/globals';
import { PeaReportSandboxService } from '@src/app/service/sandbox/pea_report_sandbox.js';
import {
	mockedCustomReportResultForSandbox,
	mockedCustomReportStepKeyResultsListForSandbox,
	mockedCustomReportResultActionLogsListForSandbox,
	mockedCustomReportStepKeyAdditionalItemResultsListForSandbox,
	mockedFileForSandbox,
	mockedEvaluatorForSandbox,
	mockedCustomReportStepKeysAdditionalItemForSandbox,
	mockedTransaction
} from '../fixtures';

const { Op } = Sequelize;

describe('[PeaReportSandboxService] - service', () => {
	let kinebot_repository;
	let sandbox_repository;
	let service;
	let transaction;

	beforeEach(() => {
		transaction = {
			commit: jest.fn(),
			rollback: jest.fn()
		};

		kinebot_repository = {
			db: {
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) },
				File: { findOne: jest.fn(), findOrCreate: jest.fn(), create: jest.fn(), findByPk: jest.fn() },
				Sector: { findOrCreate: jest.fn(), create: jest.fn() },
				Line: { findOrCreate: jest.fn(), create: jest.fn() },
				Workstation: { findOrCreate: jest.fn(), create: jest.fn() },
				Evaluator: { findOrCreate: jest.fn(), create: jest.fn() },
				CustomReportResult: { findOrCreate: jest.fn(), create: jest.fn(), findOne: jest.fn() },
				CustomReportStepKeyResult: { findOrCreate: jest.fn(), create: jest.fn() },
				CustomReportResultActionLog: { findOrCreate: jest.fn(), create: jest.fn() },
				CustomReportStepKeyAdditionalItemResult: { findOrCreate: jest.fn(), create: jest.fn() },
				CustomReportStepKeysAdditionalItem: { findOrCreate: jest.fn(), create: jest.fn() }
			}
		};

		sandbox_repository = {
			db: {
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) },
				CustomReportResult: {
					findByPk: jest.fn(),
					destroy: jest.fn()
				},
				CustomReportStepKeyResult: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				CustomReportResultActionLog: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				CustomReportStepKeyAdditionalItemResult: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				File: {
					findByPk: jest.fn()
				},
				Evaluator: {
					findByPk: jest.fn()
				},
				CustomReportStepKeysAdditionalItem: {
					findByPk: jest.fn()
				},
				ActionPlanOrigin: {
					findOne: jest.fn(),
					update: jest.fn()
				},
				PEAToSuperPEA: {
					findOne: jest.fn()
				},
				CustomReportSubStepKeyResult: {
					findAll: jest.fn(),
					destroy: jest.fn()
				}
			}
		};

		service = new PeaReportSandboxService({ kinebot_repository, sandbox_repository });
	});

	it('should send report and destroy sandbox data', async () => {
		const custom_report_result = { ...mockedCustomReportResultForSandbox };
		const custom_report_step_key_results = [...mockedCustomReportStepKeyResultsListForSandbox];
		const custom_report_result_actions_logs = [...mockedCustomReportResultActionLogsListForSandbox];
		const custom_report_step_key_additional_item_results = [
			...mockedCustomReportStepKeyAdditionalItemResultsListForSandbox
		];

		sandbox_repository.db.CustomReportResult.findByPk.mockResolvedValue(custom_report_result);
		sandbox_repository.db.CustomReportStepKeyResult.findAll.mockResolvedValue(custom_report_step_key_results);
		sandbox_repository.db.CustomReportResultActionLog.findAll.mockResolvedValue(custom_report_result_actions_logs);
		sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.findAll.mockResolvedValue(
			custom_report_step_key_additional_item_results
		);
		sandbox_repository.db.ActionPlanOrigin.findOne.mockResolvedValue(null);
		sandbox_repository.db.PEAToSuperPEA.findOne.mockResolvedValue(null);
		sandbox_repository.db.CustomReportSubStepKeyResult.findAll.mockResolvedValue([]);

		const mockFile = { ...mockedFileForSandbox, id: 'new-pea-file-id' };
		const mockEvaluator = { ...mockedEvaluatorForSandbox, id: 'new-pea-evaluator-id' };
		const mockAdditionalItem = {
			...mockedCustomReportStepKeysAdditionalItemForSandbox,
			id: 'new-pea-additional-item-id'
		};

		service.getOrCreateFile = jest.fn().mockResolvedValue(mockFile);
		service.getOrCreateSimpleDependency = jest.fn().mockImplementation((id, model) => {
			if (model === 'Evaluator') return Promise.resolve(mockEvaluator);
			if (model === 'CustomReportStepKeysAdditionalItem') return Promise.resolve(mockAdditionalItem);
			return Promise.resolve({ id: 'new-id' });
		});

		service.create = jest.fn().mockResolvedValue({ id: 'new-pea-created-id' });

		sandbox_repository.db.CustomReportStepKeyResult.destroy.mockResolvedValue(1);
		sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.destroy.mockResolvedValue(1);
		sandbox_repository.db.CustomReportResultActionLog.destroy.mockResolvedValue(1);
		sandbox_repository.db.CustomReportResult.destroy.mockResolvedValue(1);

		await service.sendReport(custom_report_result.id);

		expect(sandbox_repository.db.CustomReportResult.findByPk).toHaveBeenCalledWith(custom_report_result.id);

		expect(sandbox_repository.db.CustomReportStepKeyResult.findAll).toHaveBeenCalledWith({
			where: { custom_report_result_id: custom_report_result.id },
			transaction: transaction
		});

		expect(sandbox_repository.db.CustomReportResultActionLog.findAll).toHaveBeenCalledWith({
			where: { custom_report_result_id: custom_report_result.id },
			transaction: transaction
		});

		expect(sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.findAll).toHaveBeenCalledWith({
			where: { custom_report_result_id: custom_report_result.id },
			transaction: transaction
		});

		expect(service.create).toHaveBeenCalledWith(
			expect.objectContaining({ file_id: mockFile.id, evaluator_id: mockEvaluator.id }),
			'CustomReportResult',
			{ kinebot: transaction, sandbox: transaction }
		);

		expect(service.create).toHaveBeenCalledWith(expect.any(Object), 'CustomReportResultActionLog', {
			kinebot: transaction,
			sandbox: transaction
		});

		expect(service.create).toHaveBeenCalledWith(
			expect.objectContaining({ custom_report_step_key_additional_item_id: mockAdditionalItem.id }),
			'CustomReportStepKeyAdditionalItemResult',
			{ kinebot: transaction, sandbox: transaction }
		);

		expect(service.create).toHaveBeenCalledWith(expect.any(Object), 'CustomReportStepKeyResult', {
			kinebot: transaction,
			sandbox: transaction
		});

		expect(sandbox_repository.db.CustomReportStepKeyResult.destroy).toHaveBeenCalledWith({
			where: { id: { [Op.in]: [custom_report_step_key_results[0].id] } },
			transaction: transaction,
			force: true
		});

		expect(sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.destroy).toHaveBeenCalledWith({
			where: { id: { [Op.in]: [custom_report_step_key_additional_item_results[0].id] } },
			transaction: transaction,
			force: true
		});

		expect(sandbox_repository.db.CustomReportResultActionLog.destroy).toHaveBeenCalledWith({
			where: { id: { [Op.in]: [custom_report_result_actions_logs[0].id] } },
			transaction: transaction,
			force: true
		});

		expect(sandbox_repository.db.CustomReportResult.destroy).toHaveBeenCalledWith({
			where: { id: custom_report_result.id },
			transaction: transaction,
			force: true
		});

		expect(transaction.commit).toHaveBeenCalledTimes(2);
	});

	it('should rollback on error', async () => {
		sandbox_repository.db.CustomReportResult.findByPk.mockRejectedValue(new Error('Database error'));

		await expect(service.sendReport('any-id')).rejects.toThrow('Database error');

		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should handle empty related data', async () => {
		const custom_report_result = { ...mockedCustomReportResultForSandbox };

		sandbox_repository.db.CustomReportResult.findByPk.mockResolvedValue(custom_report_result);
		sandbox_repository.db.CustomReportStepKeyResult.findAll.mockResolvedValue([]);
		sandbox_repository.db.CustomReportResultActionLog.findAll.mockResolvedValue([]);
		sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.findAll.mockResolvedValue([]);
		sandbox_repository.db.CustomReportSubStepKeyResult.findAll.mockResolvedValue([]);

		service.getOrCreateFile = jest.fn().mockResolvedValue({ id: 'new-pea-file-id' });
		service.getOrCreateSimpleDependency = jest.fn().mockResolvedValue({ id: 'new-pea-evaluator-id' });
		service.create = jest.fn().mockResolvedValue({ id: 'new-pea-id' });

		sandbox_repository.db.CustomReportStepKeyResult.destroy.mockResolvedValue(0);
		sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.destroy.mockResolvedValue(0);
		sandbox_repository.db.CustomReportResultActionLog.destroy.mockResolvedValue(0);
		sandbox_repository.db.CustomReportResult.destroy.mockResolvedValue(1);

		await service.sendReport(custom_report_result.id);

		expect(transaction.commit).toHaveBeenCalledTimes(2);
	});

	it('should handle missing custom_report_step_key_additional_item_id', async () => {
		const custom_report_result = { ...mockedCustomReportResultForSandbox };
		const custom_report_step_key_additional_item_results = [
			{
				id: 'pea-additional-item-result-null-id-123',
				custom_report_result_id: custom_report_result.id,
				custom_report_step_key_additional_item_id: null
			}
		];

		sandbox_repository.db.CustomReportResult.findByPk.mockResolvedValue(custom_report_result);
		sandbox_repository.db.CustomReportStepKeyResult.findAll.mockResolvedValue([]);
		sandbox_repository.db.CustomReportResultActionLog.findAll.mockResolvedValue([]);
		sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.findAll.mockResolvedValue(
			custom_report_step_key_additional_item_results
		);
		sandbox_repository.db.CustomReportSubStepKeyResult.findAll.mockResolvedValue([]);

		service.getOrCreateFile = jest.fn().mockResolvedValue({ id: 'new-pea-file-id' });
		service.getOrCreateSimpleDependency = jest.fn().mockResolvedValue({ id: 'new-pea-evaluator-id' });
		service.create = jest.fn().mockResolvedValue({ id: 'new-pea-id' });

		sandbox_repository.db.CustomReportStepKeyResult.destroy.mockResolvedValue(0);
		sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.destroy.mockResolvedValue(1);
		sandbox_repository.db.CustomReportResultActionLog.destroy.mockResolvedValue(0);
		sandbox_repository.db.CustomReportResult.destroy.mockResolvedValue(1);

		await service.sendReport(custom_report_result.id);

		expect(service.getOrCreateSimpleDependency).not.toHaveBeenCalledWith(
			null,
			'CustomReportStepKeysAdditionalItem',
			expect.any(Object),
			expect.any(Object)
		);

		expect(transaction.commit).toHaveBeenCalledTimes(2);
	});
});
