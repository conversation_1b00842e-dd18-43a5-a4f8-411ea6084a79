import Sequelize from 'sequelize';
import { jest } from '@jest/globals';
import { BeraReportSandboxService } from '@src/app/service/sandbox/bera_report_sandbox.js';
import {
	mockedBeraJobSummaryForSandbox,
	mockedFileForSandbox,
	mockedCycleForSandbox,
	mockedTaskForSandbox,
	mockedEvaluatorForSandbox
} from '../fixtures';

const { Op } = Sequelize;

describe('[BeraReportSandboxService] - service', () => {
	let kinebot_repository;
	let sandbox_repository;
	let service;
	let transaction;

	beforeEach(() => {
		transaction = {
			commit: jest.fn(),
			rollback: jest.fn()
		};

		kinebot_repository = {
			db: {
				File: { findOne: jest.fn(), findOrCreate: jest.fn(), create: jest.fn() },
				Sector: { findOrCreate: jest.fn(), create: jest.fn() },
				Line: { findOrCreate: jest.fn(), create: jest.fn() },
				Workstation: { findOrCreate: jest.fn(), create: jest.fn() },
				BeraJobSummary: { findOrCreate: jest.fn(), create: jest.fn() },
				BeraReport: { findOrCreate: jest.fn(), create: jest.fn() },
				BeraJobSummaryFiles: { findOrCreate: jest.fn(), create: jest.fn() },
				Task: { findOrCreate: jest.fn(), create: jest.fn() },
				Evaluator: { findOrCreate: jest.fn(), create: jest.fn() },
				Cycle: { findOrCreate: jest.fn(), create: jest.fn() },
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) }
			}
		};

		sandbox_repository = {
			db: {
				BeraJobSummary: {
					findByPk: jest.fn(),
					destroy: jest.fn()
				},
				BeraReport: {
					destroy: jest.fn()
				},
				BeraJobSummaryFiles: {
					destroy: jest.fn()
				},
				File: {
					findByPk: jest.fn()
				},
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) }
			}
		};

		service = new BeraReportSandboxService({ kinebot_repository, sandbox_repository });

		jest.clearAllMocks();
	});

	it('should send report and destroy sandbox data', async () => {
		const bera_job_summary = { ...mockedBeraJobSummaryForSandbox };
		const mockFile = {
			...mockedFileForSandbox,
			setTask: jest.fn().mockResolvedValue(undefined)
		};
		const mockCycle = {
			...mockedCycleForSandbox,
			addTask: jest.fn().mockResolvedValue(undefined)
		};
		const mockTask = { ...mockedTaskForSandbox };
		const mockEvaluator = { ...mockedEvaluatorForSandbox };
		const new_summary_id = 'new-summary-id';

		sandbox_repository.db.BeraJobSummary.findByPk.mockResolvedValue(bera_job_summary);

		service.getOrCreateFile = jest.fn().mockResolvedValue(mockFile);
		service.getOrCreateSimpleDependency = jest.fn().mockImplementation((id, model) => {
			if (model === 'Cycle') return Promise.resolve(mockCycle);
			if (model === 'Task') return Promise.resolve(mockTask);
			if (model === 'Evaluator') return Promise.resolve(mockEvaluator);
			return Promise.resolve({ id: new_summary_id });
		});
		service.create = jest.fn().mockResolvedValue({ id: new_summary_id });

		sandbox_repository.db.BeraReport.destroy.mockResolvedValue(1);
		sandbox_repository.db.BeraJobSummaryFiles.destroy.mockResolvedValue(1);
		sandbox_repository.db.BeraJobSummary.destroy.mockResolvedValue(1);

		await service.sendReport('bera-job-summary-id-123');

		expect(sandbox_repository.db.BeraJobSummary.findByPk).toHaveBeenCalledWith(
			'bera-job-summary-id-123',
			expect.objectContaining({
				include: expect.any(Array)
			})
		);
		expect(service.getOrCreateFile).toHaveBeenCalled();
		expect(service.getOrCreateSimpleDependency).toHaveBeenCalledWith(
			bera_job_summary.cycle_id,
			'Cycle',
			expect.any(Object)
		);
		expect(service.getOrCreateSimpleDependency).toHaveBeenCalledWith(
			bera_job_summary.bera_report[0].task_id,
			'Task',
			expect.any(Object)
		);
		expect(service.getOrCreateSimpleDependency).toHaveBeenCalledWith(
			bera_job_summary.bera_report[0].evaluator_id,
			'Evaluator',
			expect.any(Object)
		);
		expect(service.create).toHaveBeenCalled();
		expect(mockFile.setTask).toHaveBeenCalledWith([mockTask], { transaction: transaction });
		expect(sandbox_repository.db.BeraReport.destroy).toHaveBeenCalledWith({
			where: {
				id: {
					[Op.in]: [bera_job_summary.bera_report[0].id]
				}
			},
			force: true,
			transaction
		});
		expect(sandbox_repository.db.BeraJobSummaryFiles.destroy).toHaveBeenCalledWith({
			where: {
				id: {
					[Op.in]: [bera_job_summary.bera_job_summary_files[0].id]
				}
			},
			force: true,
			transaction
		});
		expect(sandbox_repository.db.BeraJobSummary.destroy).toHaveBeenCalledWith({
			where: {
				id: bera_job_summary.id
			},
			force: true,
			transaction
		});
		expect(transaction.commit).toHaveBeenCalled();
	});

	it('should rollback on error', async () => {
		const error = new Error('Database operation failed');
		sandbox_repository.db.BeraJobSummary.findByPk.mockRejectedValue(error);

		await expect(service.sendReport('any-id')).rejects.toThrow('Database operation failed');

		expect(transaction.rollback).toHaveBeenCalled();
	});

	it('should handle empty bera_job_summary_files', async () => {
		const bera_job_summary = {
			...mockedBeraJobSummaryForSandbox,
			bera_job_summary_files: [],
			bera_report: []
		};

		sandbox_repository.db.BeraJobSummary.findByPk.mockResolvedValue(bera_job_summary);
		service.getOrCreateFile = jest.fn().mockResolvedValue({
			...mockedFileForSandbox,
			setTask: jest.fn().mockResolvedValue(undefined)
		});
		service.getOrCreateSimpleDependency = jest.fn().mockResolvedValue({ id: 'new-id' });
		service.create = jest.fn().mockResolvedValue({ id: 'new-summary-id' });

		sandbox_repository.db.BeraReport.destroy.mockResolvedValue(0);
		sandbox_repository.db.BeraJobSummaryFiles.destroy.mockResolvedValue(0);
		sandbox_repository.db.BeraJobSummary.destroy.mockResolvedValue(1);

		await service.sendReport(bera_job_summary.id);

		expect(sandbox_repository.db.BeraReport.destroy).toHaveBeenCalledWith({
			where: { id: { [Op.in]: [] } },
			force: true,
			transaction
		});
		expect(sandbox_repository.db.BeraJobSummaryFiles.destroy).toHaveBeenCalledWith({
			where: { id: { [Op.in]: [] } },
			force: true,
			transaction
		});
		expect(transaction.commit).toHaveBeenCalled();
	});
});
