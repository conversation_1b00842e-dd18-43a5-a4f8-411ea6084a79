import Sequelize from 'sequelize';
import { jest } from '@jest/globals';
import { CustomReportSandboxService } from '@src/app/service/sandbox/custom_report_sandbox.js';
import {
	mockedCustomReportReviewsListForSandbox,
	mockedCustomReportResultsListForSandbox,
	mockedCustomReportStepKeyResultsListForSandbox,
	mockedCustomReportResultActionLogsListForSandbox,
	mockedCustomReportStepKeyAdditionalItemResultsListForSandbox,
	mockedCustomReportStepKeyAdditionalItemResultWithNullIdForSandbox,
	mockedFileForSandbox,
	mockedFileForCustomReportSandbox,
	mockedEvaluatorForSandbox,
	mockedCustomReportStepKeysAdditionalItemForSandbox,
	mockedCustomReportNewCreatedDataForSandbox
} from '../fixtures';

const { Op } = Sequelize;

describe('[CustomReportSandboxService] - service', () => {
	let kinebot_repository;
	let sandbox_repository;
	let service;
	let transaction;

	beforeEach(() => {
		transaction = {
			commit: jest.fn(),
			rollback: jest.fn()
		};

		kinebot_repository = {
			db: {
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) },
				File: { findOne: jest.fn(), findOrCreate: jest.fn(), create: jest.fn(), findByPk: jest.fn() },
				Sector: { findOrCreate: jest.fn(), create: jest.fn() },
				Line: { findOrCreate: jest.fn(), create: jest.fn() },
				Workstation: { findOrCreate: jest.fn(), create: jest.fn() },
				Evaluator: { findOrCreate: jest.fn(), create: jest.fn() },
				CustomReportResult: { findOrCreate: jest.fn(), create: jest.fn() },
				CustomReportReview: { findOrCreate: jest.fn(), create: jest.fn() },
				CustomReportStepKeyResult: { findOrCreate: jest.fn(), create: jest.fn() },
				CustomReportResultActionLog: { findOrCreate: jest.fn(), create: jest.fn() },
				CustomReportStepKeyAdditionalItemResult: { findOrCreate: jest.fn(), create: jest.fn() },
				CustomReportStepKeysAdditionalItem: { findOrCreate: jest.fn(), create: jest.fn() },
				CustomReportSubStepKeyResult: { findOrCreate: jest.fn(), create: jest.fn() }
			}
		};

		sandbox_repository = {
			db: {
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) },
				CustomReportReview: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				CustomReportResult: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				CustomReportStepKeyResult: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				CustomReportResultActionLog: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				CustomReportStepKeyAdditionalItemResult: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				CustomReportSubStepKeyResult: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				File: {
					findByPk: jest.fn()
				},
				Evaluator: {
					findByPk: jest.fn()
				},
				CustomReportStepKeysAdditionalItem: {
					findByPk: jest.fn()
				},
				ActionPlanOrigin: {
					findOne: jest.fn(),
					update: jest.fn()
				}
			}
		};

		service = new CustomReportSandboxService({ kinebot_repository, sandbox_repository });

		jest.clearAllMocks();
	});

	describe('[sendReport]', () => {
		it('should send report and destroy sandbox data', async () => {
			const report_id = mockedCustomReportReviewsListForSandbox[0].original_custom_report_result_id;
			const custom_report_reviews = [...mockedCustomReportReviewsListForSandbox];
			const custom_report_results = [...mockedCustomReportResultsListForSandbox];
			const custom_report_step_key_results = [...mockedCustomReportStepKeyResultsListForSandbox];
			const custom_report_result_actions_logs = [...mockedCustomReportResultActionLogsListForSandbox];
			const custom_report_step_key_additional_item_results = [
				...mockedCustomReportStepKeyAdditionalItemResultsListForSandbox
			];

			custom_report_reviews[0].original_custom_report_result_id = report_id;
			custom_report_reviews[0].custom_report_result_id = custom_report_results[0].id;
			custom_report_step_key_results[0].custom_report_result_id = custom_report_results[0].id;
			custom_report_result_actions_logs[0].custom_report_result_id = custom_report_results[0].id;
			custom_report_step_key_additional_item_results[0].custom_report_result_id = custom_report_results[0].id;

			const mockFile = { ...mockedFileForCustomReportSandbox };
			const mockEvaluator = { ...mockedEvaluatorForSandbox };
			const mockAdditionalItem = { ...mockedCustomReportStepKeysAdditionalItemForSandbox };
			const newCreatedData = { ...mockedCustomReportNewCreatedDataForSandbox };

			sandbox_repository.db.CustomReportReview.findAll.mockResolvedValue(custom_report_reviews);
			sandbox_repository.db.CustomReportResult.findAll.mockResolvedValue(custom_report_results);
			sandbox_repository.db.CustomReportStepKeyResult.findAll.mockResolvedValue(custom_report_step_key_results);
			sandbox_repository.db.CustomReportResultActionLog.findAll.mockResolvedValue(
				custom_report_result_actions_logs
			);
			sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.findAll.mockResolvedValue(
				custom_report_step_key_additional_item_results
			);
			sandbox_repository.db.CustomReportSubStepKeyResult.findAll.mockResolvedValue([]);

			kinebot_repository.db.CustomReportResult.create.mockResolvedValue(newCreatedData);
			kinebot_repository.db.CustomReportReview.create.mockResolvedValue({ id: 'new-review-id' });
			kinebot_repository.db.CustomReportStepKeyResult.create.mockResolvedValue({ id: 'new-step-key-result-id' });
			kinebot_repository.db.CustomReportResultActionLog.create.mockResolvedValue({ id: 'new-action-log-id' });
			kinebot_repository.db.CustomReportStepKeyAdditionalItemResult.create.mockResolvedValue({
				id: 'new-additional-item-result-id'
			});

			service.getOrCreateFile = jest.fn().mockResolvedValue(mockFile);
			service.getOrCreateSimpleDependency = jest.fn().mockImplementation((id, model) => {
				if (model === 'Evaluator') return Promise.resolve(mockEvaluator);
				if (model === 'CustomReportStepKeysAdditionalItem') return Promise.resolve(mockAdditionalItem);
				return Promise.resolve({ id: 'new-id' });
			});

			service.create = jest.fn().mockResolvedValue(newCreatedData);

			sandbox_repository.db.CustomReportStepKeyResult.destroy.mockResolvedValue(1);
			sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.destroy.mockResolvedValue(1);
			sandbox_repository.db.CustomReportResultActionLog.destroy.mockResolvedValue(1);
			sandbox_repository.db.CustomReportReview.destroy.mockResolvedValue(1);
			sandbox_repository.db.CustomReportResult.destroy.mockResolvedValue(1);

			await service.sendReport(report_id);

			expect(sandbox_repository.db.CustomReportReview.findAll).toHaveBeenCalledWith({
				where: { original_custom_report_result_id: report_id },
				transaction: transaction
			});

			expect(sandbox_repository.db.CustomReportResult.findAll).toHaveBeenCalledWith({
				where: { id: { [Op.in]: [custom_report_results[0].id] } },
				transaction: transaction
			});

			expect(service.create).toHaveBeenCalledWith(
				expect.objectContaining({
					file_id: mockFile.id,
					evaluator_id: mockEvaluator.id
				}),
				'CustomReportResult',
				{ kinebot: transaction, sandbox: transaction }
			);

			expect(service.create).toHaveBeenCalledWith(
				expect.objectContaining({
					custom_report_step_key_additional_item_id: mockAdditionalItem.id
				}),
				'CustomReportStepKeyAdditionalItemResult',
				{ kinebot: transaction, sandbox: transaction }
			);

			expect(sandbox_repository.db.CustomReportResult.destroy).toHaveBeenCalledWith({
				where: { id: { [Op.in]: [custom_report_results[0].id] } },
				transaction: transaction,
				force: true
			});

			expect(transaction.commit).toHaveBeenCalledTimes(2);
		});

		it('should rollback on error', async () => {
			const error = new Error('Database error');
			sandbox_repository.db.CustomReportReview.findAll.mockRejectedValue(error);

			await expect(service.sendReport('any-id')).rejects.toThrow('Database error');

			expect(transaction.rollback).toHaveBeenCalledTimes(2);
		});

		it('should handle empty reviews', async () => {
			sandbox_repository.db.CustomReportReview.findAll.mockResolvedValue([]);
			sandbox_repository.db.CustomReportResult.findAll.mockResolvedValue([]);
			sandbox_repository.db.CustomReportStepKeyResult.findAll.mockResolvedValue([]);
			sandbox_repository.db.CustomReportResultActionLog.findAll.mockResolvedValue([]);
			sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.findAll.mockResolvedValue([]);
			sandbox_repository.db.CustomReportSubStepKeyResult.findAll.mockResolvedValue([]);

			await service.sendReport('report-id');

			expect(transaction.commit).toHaveBeenCalledTimes(2);
		});

		it('should handle missing custom_report_step_key_additional_item_id', async () => {
			const report_id = 'report-uuid';
			const custom_report_reviews = [
				{
					...mockedCustomReportReviewsListForSandbox[0],
					original_custom_report_result_id: report_id,
					custom_report_result_id: 'result-id'
				}
			];
			const custom_report_results = [
				{
					...mockedCustomReportResultsListForSandbox[0],
					id: 'result-id'
				}
			];
			const custom_report_step_key_additional_item_results = [
				...mockedCustomReportStepKeyAdditionalItemResultWithNullIdForSandbox
			];
			custom_report_step_key_additional_item_results[0].custom_report_result_id = 'result-id';

			sandbox_repository.db.CustomReportReview.findAll.mockResolvedValue(custom_report_reviews);
			sandbox_repository.db.CustomReportResult.findAll.mockResolvedValue(custom_report_results);
			sandbox_repository.db.CustomReportStepKeyResult.findAll.mockResolvedValue([]);
			sandbox_repository.db.CustomReportResultActionLog.findAll.mockResolvedValue([]);
			sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.findAll.mockResolvedValue(
				custom_report_step_key_additional_item_results
			);
			sandbox_repository.db.CustomReportSubStepKeyResult.findAll.mockResolvedValue([]);

			service.getOrCreateFile = jest.fn().mockResolvedValue(mockedFileForSandbox);
			service.getOrCreateSimpleDependency = jest.fn().mockResolvedValue(mockedEvaluatorForSandbox);
			service.create = jest.fn().mockResolvedValue({ id: 'new-id' });

			sandbox_repository.db.CustomReportStepKeyResult.destroy.mockResolvedValue(0);
			sandbox_repository.db.CustomReportStepKeyAdditionalItemResult.destroy.mockResolvedValue(1);
			sandbox_repository.db.CustomReportResultActionLog.destroy.mockResolvedValue(0);
			sandbox_repository.db.CustomReportReview.destroy.mockResolvedValue(1);
			sandbox_repository.db.CustomReportResult.destroy.mockResolvedValue(1);

			await service.sendReport(report_id);

			expect(service.getOrCreateSimpleDependency).not.toHaveBeenCalledWith(
				null,
				'CustomReportStepKeysAdditionalItem',
				expect.any(Object),
				expect.any(Object)
			);

			expect(transaction.commit).toHaveBeenCalledTimes(2);
		});
	});
});
