import Sequelize from 'sequelize';
import { jest } from '@jest/globals';
import { KimMhoReportSandboxService } from '@src/app/service/sandbox/kim_mho_report_sandbox.js';
import { mockedKimMhoReportForSandbox, mockedFileForSandbox, mockedTransaction } from '../fixtures';

const { Op } = Sequelize;

describe('[KimMhoReportSandboxService] - service', () => {
	let kinebot_repository;
	let sandbox_repository;
	let service;
	let transaction;

	beforeEach(() => {
		transaction = {
			commit: jest.fn(),
			rollback: jest.fn()
		};

		kinebot_repository = {
			db: {
				File: { findOne: jest.fn(), findOrCreate: jest.fn(), create: jest.fn() },
				KimManualHandlingReport: { findOrCreate: jest.fn(), create: jest.fn(), findOne: jest.fn() },
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) }
			}
		};

		sandbox_repository = {
			db: {
				KimManualHandlingReport: {
					findByPk: jest.fn(),
					destroy: jest.fn()
				},
				File: {
					findByPk: jest.fn()
				},
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) },
				ActionPlanRelatedReport: {
					findOne: jest.fn()
				}
			}
		};

		service = new KimMhoReportSandboxService({ kinebot_repository, sandbox_repository });
	});

	it('should send report and destroy sandbox data', async () => {
		const kim_mho_report = { ...mockedKimMhoReportForSandbox };
		const mockFile = {
			...mockedFileForSandbox,
			id: 'new-kim-mho-file-id'
		};

		sandbox_repository.db.KimManualHandlingReport.findByPk.mockResolvedValue(kim_mho_report);
		sandbox_repository.db.ActionPlanRelatedReport.findOne.mockResolvedValue(null);

		service.getOrCreateFile = jest.fn().mockResolvedValue(mockFile);
		service.create = jest.fn().mockResolvedValue({ id: 'new-kim-mho-report-id' });

		sandbox_repository.db.KimManualHandlingReport.destroy.mockResolvedValue(1);

		await service.sendReport(kim_mho_report.id);

		expect(sandbox_repository.db.KimManualHandlingReport.findByPk).toHaveBeenCalledWith(kim_mho_report.id, {
			transaction: transaction
		});

		expect(service.create).toHaveBeenCalledWith(
			{ ...kim_mho_report, file_id: mockFile.id },
			'KimManualHandlingReport',
			{
				kinebot: transaction,
				sandbox: transaction
			}
		);
		expect(sandbox_repository.db.KimManualHandlingReport.destroy).toHaveBeenCalledWith({
			where: { id: kim_mho_report.id },
			transaction: transaction,
			force: true
		});
		expect(transaction.commit).toHaveBeenCalledTimes(2);
	});

	it('should throw error when Kim Mho Report not found', async () => {
		const report_id = 'non-existent-id';

		sandbox_repository.db.KimManualHandlingReport.findByPk.mockResolvedValue(null);

		await expect(service.sendReport(report_id)).rejects.toThrow('Kim mho report not found');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should rollback on error', async () => {
		const report_id = 'kim-mho-report-id-123';

		sandbox_repository.db.KimManualHandlingReport.findByPk.mockRejectedValue(new Error('Database error'));

		await expect(service.sendReport(report_id)).rejects.toThrow('Database error');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should rollback on file creation error', async () => {
		const kim_mho_report = { ...mockedKimMhoReportForSandbox };

		sandbox_repository.db.KimManualHandlingReport.findByPk.mockResolvedValue(kim_mho_report);
		service.getOrCreateFile = jest.fn().mockRejectedValue(new Error('File creation failed'));

		await expect(service.sendReport(kim_mho_report.id)).rejects.toThrow('File creation failed');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});
});
