import { jest } from '@jest/globals';
import { faker } from '@faker-js/faker';
import { UploadService } from '@src/app/service/upload.js';
import * as mocksUpload from '@tests/unit/fixtures/upload.js';

const { mockFindAndCountAllRawQueryResult, mockFindAllWithHierarchyResult, mockIndexResult } = mocksUpload;

describe('Upload service', () => {
	let service;

	let repository = {
		findAllWithHierarchy: jest.fn(),
		findAndCountAllRawQuery: jest.fn()
	};

	beforeAll(() => {
		service = new UploadService(repository);
	});

	beforeEach(() => {
		jest.clearAllMocks();
	});

	describe('[index]', () => {
		const organization_id = faker.datatype.uuid();
		const company_id = faker.datatype.uuid();
		const sector_id = faker.datatype.uuid();
		const line_id = faker.datatype.uuid();
		const workstation_id = faker.datatype.uuid();
		const original_name = 'file.mp4';
		const searching_organization_id = faker.datatype.uuid();
		const created_at_start = faker.datatype.datetime();
		const created_at_end = faker.datatype.datetime();
		const limit = 10;
		const offset = 0;
		const user = { id: faker.datatype.uuid(), role: 'USER' };
		const file_ids = [faker.datatype.uuid(), faker.datatype.uuid(), faker.datatype.uuid()];

		const parameters = {
			user,
			limit,
			offset,
			line_id,
			sector_id,
			company_id,
			original_name,
			created_at_end,
			workstation_id,
			organization_id,
			created_at_start,
			searching_organization_id,
			file_ids
		};

		it('should check if call findAllWithHierarchy correct parameters', async () => {
			jest.spyOn(repository, 'findAllWithHierarchy').mockResolvedValueOnce([]);
			await service.index(parameters);
			expect(repository.findAllWithHierarchy).toHaveBeenCalledWith(parameters);
		});

		it('should return empty list if data is not found ', async () => {
			jest.spyOn(repository, 'findAllWithHierarchy').mockResolvedValueOnce([]);
			const result = await service.index(parameters);
			expect(result).toStrictEqual({ count: 0, rows: [] });
		});

		it('should check if call findAndCountAllRawQuery correct parameters', async () => {
			const [upload] = mockFindAllWithHierarchyResult;
			jest.spyOn(repository, 'findAllWithHierarchy').mockResolvedValueOnce(mockFindAllWithHierarchyResult);
			jest.spyOn(repository, 'findAndCountAllRawQuery').mockResolvedValueOnce(mockFindAndCountAllRawQueryResult);
			const expected = { limit, offset: limit * offset, file_ids: `(\"${upload.id}\")`, user_id: null };
			await service.index(parameters);
			expect(repository.findAndCountAllRawQuery).toHaveBeenCalledWith(expected);
		});

		it('should return a list uploads paginated', async () => {
			jest.spyOn(repository, 'findAllWithHierarchy').mockResolvedValueOnce(mockFindAllWithHierarchyResult);
			jest.spyOn(repository, 'findAndCountAllRawQuery').mockResolvedValueOnce(mockFindAndCountAllRawQueryResult);
			const result = await service.index(parameters);
			expect(result).toStrictEqual(mockIndexResult);
		});
	});
});
