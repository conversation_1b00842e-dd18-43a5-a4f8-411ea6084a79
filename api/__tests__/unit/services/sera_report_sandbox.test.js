import Sequelize from 'sequelize';
import { jest } from '@jest/globals';
import { SeraReportSandboxService } from '@src/app/service/sandbox/sera_report_sandbox.js';
import {
	mockedSeraReportForSandbox,
	********************************,
	mockedSeraSummaryReviewsForSandbox,
	mockedSeraReportsForSandbox,
	mockedSeraReviewTasksResultsForSandbox,
	mockedSeraReportUpdatedsForSandbox,
	mockedSeraReviewSelectorsForSandbox,
	mockedFileForSandbox,
	mockedEvaluatorForSandbox,
	mockedCycleForSandbox,
	mockedTaskForSandbox,
	mockedTransaction
} from '../fixtures';

const { Op } = Sequelize;

describe('[SeraReportSandboxService] - service', () => {
	let kinebot_repository;
	let sandbox_repository;
	let service;
	let transaction;

	beforeEach(() => {
		transaction = {
			commit: jest.fn(),
			rollback: jest.fn()
		};

		kinebot_repository = {
			db: {
				File: { findOne: jest.fn(), findOrCreate: jest.fn(), create: jest.fn() },
				Evaluator: { findOne: jest.fn(), findOrCreate: jest.fn(), create: jest.fn() },
				Cycle: { findOne: jest.fn(), findOrCreate: jest.fn(), create: jest.fn() },
				Task: { findOne: jest.fn(), findOrCreate: jest.fn(), create: jest.fn() },
				SeraSummary: { findOrCreate: jest.fn(), create: jest.fn() },
				SeraSummaryFile: { findOrCreate: jest.fn(), create: jest.fn() },
				SeraSummaryReview: { findOrCreate: jest.fn(), create: jest.fn() },
				SeraReport: { findOrCreate: jest.fn(), create: jest.fn() },
				SeraReviewTasksResult: { findOrCreate: jest.fn(), create: jest.fn() },
				SeraReportUpdated: { findOrCreate: jest.fn(), create: jest.fn() },
				SeraReviewSelector: { findOrCreate: jest.fn(), create: jest.fn() },
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) }
			}
		};

		sandbox_repository = {
			db: {
				SeraSummary: {
					findByPk: jest.fn(),
					destroy: jest.fn()
				},
				SeraSummaryFiles: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				SeraSummaryReview: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				SeraReport: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				SeraReviewTasksResult: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				SeraReportUpdated: {
					findAll: jest.fn(),
					destroy: jest.fn()
				},
				SeraReviewSelector: {
					findAll: jest.fn()
				},
				File: {
					findByPk: jest.fn()
				},
				Evaluator: {
					findByPk: jest.fn()
				},
				Cycle: {
					findByPk: jest.fn()
				},
				Task: {
					findByPk: jest.fn()
				},
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) },
				ActionPlanOrigin: {
					findOne: jest.fn(),
					update: jest.fn()
				}
			}
		};

		service = new SeraReportSandboxService({ kinebot_repository, sandbox_repository });
	});

	it('should send sera report with all related entities', async () => {
		const sera_summary = { ...mockedSeraReportForSandbox };
		const sera_summary_files = [...********************************];
		const sera_summary_reviews = [...mockedSeraSummaryReviewsForSandbox];
		const sera_reports = [...mockedSeraReportsForSandbox];
		const sera_review_tasks_results = [...mockedSeraReviewTasksResultsForSandbox];
		const sera_report_updateds = [...mockedSeraReportUpdatedsForSandbox];
		const sera_review_selectors = [...mockedSeraReviewSelectorsForSandbox];

		sandbox_repository.db.SeraSummary.findByPk.mockResolvedValue(sera_summary);
		sandbox_repository.db.SeraSummaryFiles.findAll.mockResolvedValue(sera_summary_files);
		sandbox_repository.db.SeraSummaryReview.findAll.mockResolvedValue(sera_summary_reviews);
		sandbox_repository.db.SeraReport.findAll.mockResolvedValue(sera_reports);
		sandbox_repository.db.SeraReviewTasksResult.findAll.mockResolvedValue(sera_review_tasks_results);
		sandbox_repository.db.SeraReportUpdated.findAll.mockResolvedValue(sera_report_updateds);
		sandbox_repository.db.SeraReviewSelector.findAll.mockResolvedValue(sera_review_selectors);
		sandbox_repository.db.ActionPlanOrigin.findOne.mockResolvedValue(null);

		service.getOrCreateSimpleDependency = jest
			.fn()
			.mockResolvedValueOnce({ ...mockedEvaluatorForSandbox, id: 'new-sera-evaluator-id' })
			.mockResolvedValueOnce({ ...mockedCycleForSandbox, id: 'new-sera-cycle-id' })
			.mockResolvedValueOnce({ ...mockedEvaluatorForSandbox, id: 'new-sera-evaluator-id' })
			.mockResolvedValueOnce({ ...mockedTaskForSandbox, id: 'new-sera-task-id' })
			.mockResolvedValueOnce({ ...mockedTaskForSandbox, id: 'new-sera-task-id' });

		service.getOrCreateFile = jest.fn().mockResolvedValue({ ...mockedFileForSandbox, id: 'new-sera-file-id' });
		service.create = jest
			.fn()
			.mockResolvedValueOnce({ id: 'new-sera-summary-id' })
			.mockResolvedValueOnce({ id: 'new-sera-file-link-id' })
			.mockResolvedValueOnce({ id: 'new-sera-review-id' })
			.mockResolvedValueOnce({ id: 'new-sera-report-id' })
			.mockResolvedValueOnce({ id: 'new-sera-task-result-id' })
			.mockResolvedValueOnce({ id: 'new-sera-updated-id' })
			.mockResolvedValueOnce({ id: 'new-sera-selector-id' });

		sandbox_repository.db.SeraReportUpdated.destroy.mockResolvedValue(1);
		sandbox_repository.db.SeraReviewTasksResult.destroy.mockResolvedValue(1);
		sandbox_repository.db.SeraReport.destroy.mockResolvedValue(1);
		sandbox_repository.db.SeraSummaryReview.destroy.mockResolvedValue(1);
		sandbox_repository.db.SeraSummaryFiles.destroy.mockResolvedValue(1);
		sandbox_repository.db.SeraSummary.destroy.mockResolvedValue(1);

		await service.sendReport(sera_summary.id);

		expect(sandbox_repository.db.SeraSummary.findByPk).toHaveBeenCalledWith(sera_summary.id, {
			transaction: transaction
		});

		expect(sandbox_repository.db.SeraSummaryFiles.findAll).toHaveBeenCalled();
		expect(sandbox_repository.db.SeraSummaryReview.findAll).toHaveBeenCalled();
		expect(sandbox_repository.db.SeraReport.findAll).toHaveBeenCalled();
		expect(sandbox_repository.db.SeraReviewTasksResult.findAll).toHaveBeenCalled();
		expect(sandbox_repository.db.SeraReportUpdated.findAll).toHaveBeenCalled();
		expect(sandbox_repository.db.SeraReviewSelector.findAll).toHaveBeenCalled();

		expect(service.create).toHaveBeenCalledWith(
			expect.objectContaining({ cycle_id: 'new-sera-cycle-id' }),
			'SeraSummary',
			expect.any(Object)
		);
		expect(service.create).toHaveBeenCalledWith(expect.any(Object), 'SeraSummaryFiles', expect.any(Object));
		expect(service.create).toHaveBeenCalledWith(expect.any(Object), 'SeraSummaryReview', expect.any(Object));
		expect(service.create).toHaveBeenCalledWith(expect.any(Object), 'SeraReport', expect.any(Object));

		expect(sandbox_repository.db.SeraSummary.destroy).toHaveBeenCalled();
		expect(transaction.commit).toHaveBeenCalledTimes(2);
	});

	it('should handle error when Sera Summary not found', async () => {
		const sera_summary_id = 'non-existent-id';

		sandbox_repository.db.SeraSummary.findByPk.mockResolvedValue(null);

		await service.sendReport(sera_summary_id);

		expect(transaction.rollback).toHaveBeenCalledTimes(2);
		expect(transaction.commit).not.toHaveBeenCalled();
	});

	it('should rollback on database error', async () => {
		const sera_summary_id = 'sera-summary-id-123';

		sandbox_repository.db.SeraSummary.findByPk.mockRejectedValue(new Error('Database error'));

		await service.sendReport(sera_summary_id);

		expect(transaction.rollback).toHaveBeenCalledTimes(2);
		expect(transaction.commit).not.toHaveBeenCalled();
	});

	it('should handle sera summary without evaluator', async () => {
		const sera_summary = {
			...mockedSeraReportForSandbox,
			evaluator_id: null
		};

		sandbox_repository.db.SeraSummary.findByPk.mockResolvedValue(sera_summary);
		sandbox_repository.db.SeraSummaryFiles.findAll.mockResolvedValue([]);
		sandbox_repository.db.SeraSummaryReview.findAll.mockResolvedValue([]);
		sandbox_repository.db.SeraReport.findAll.mockResolvedValue([]);
		sandbox_repository.db.SeraReviewTasksResult.findAll.mockResolvedValue([]);
		sandbox_repository.db.SeraReportUpdated.findAll.mockResolvedValue([]);
		sandbox_repository.db.SeraReviewSelector.findAll.mockResolvedValue([]);

		service.getOrCreateSimpleDependency = jest
			.fn()
			.mockResolvedValue({ ...mockedCycleForSandbox, id: 'new-sera-cycle-id' });
		service.create = jest.fn().mockResolvedValue({ id: 'new-sera-summary-id' });

		sandbox_repository.db.SeraReportUpdated.destroy.mockResolvedValue(0);
		sandbox_repository.db.SeraReviewTasksResult.destroy.mockResolvedValue(0);
		sandbox_repository.db.SeraReport.destroy.mockResolvedValue(0);
		sandbox_repository.db.SeraSummaryReview.destroy.mockResolvedValue(0);
		sandbox_repository.db.SeraSummaryFiles.destroy.mockResolvedValue(0);
		sandbox_repository.db.SeraSummary.destroy.mockResolvedValue(1);

		await service.sendReport(sera_summary.id);

		expect(transaction.commit).toHaveBeenCalledTimes(2);
	});
});
