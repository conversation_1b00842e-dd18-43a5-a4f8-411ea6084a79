import Sequelize from 'sequelize';
import { jest } from '@jest/globals';
import { RebaReportSandboxService } from '@src/app/service/sandbox/reba_report_sandbox.js';
import { mockedRebaReportForSandbox, mockedFileForSandbox, mockedTransaction } from '../fixtures';

const { Op } = Sequelize;

describe('[RebaReportSandboxService] - service', () => {
	let kinebot_repository;
	let sandbox_repository;
	let service;
	let transaction;

	beforeEach(() => {
		transaction = {
			commit: jest.fn(),
			rollback: jest.fn()
		};

		kinebot_repository = {
			db: {
				File: { findOne: jest.fn(), findOrCreate: jest.fn(), create: jest.fn() },
				RebaReport: { findOrCreate: jest.fn(), create: jest.fn(), findOne: jest.fn() },
				sequelize: {
					transaction: jest.fn().mockResolvedValue(transaction)
				}
			}
		};

		sandbox_repository = {
			db: {
				RebaReport: {
					findByPk: jest.fn(),
					destroy: jest.fn()
				},
				File: {
					findByPk: jest.fn()
				},
				sequelize: {
					transaction: jest.fn().mockResolvedValue(transaction)
				},
				ActionPlanRelatedReport: {
					findOne: jest.fn()
				}
			}
		};

		service = new RebaReportSandboxService({ kinebot_repository, sandbox_repository });
	});

	it('should send report and destroy sandbox data', async () => {
		const reba_report = { ...mockedRebaReportForSandbox };
		const mockFile = {
			...mockedFileForSandbox,
			id: 'new-reba-file-id'
		};

		sandbox_repository.db.RebaReport.findByPk.mockResolvedValue(reba_report);
		sandbox_repository.db.ActionPlanRelatedReport.findOne.mockResolvedValue(null);

		service.getOrCreateFile = jest.fn().mockResolvedValue(mockFile);
		service.create = jest.fn().mockResolvedValue({ id: 'new-reba-report-id' });

		sandbox_repository.db.RebaReport.destroy.mockResolvedValue(1);

		await service.sendReport(reba_report.id);

		expect(sandbox_repository.db.RebaReport.findByPk).toHaveBeenCalledWith(reba_report.id, {
			transaction: transaction
		});

		expect(service.create).toHaveBeenCalledWith({ ...reba_report, file_id: mockFile.id }, 'RebaReport', {
			kinebot: transaction,
			sandbox: transaction
		});
		expect(sandbox_repository.db.RebaReport.destroy).toHaveBeenCalledWith({
			where: { id: reba_report.id },
			transaction: transaction,
			force: true
		});
		expect(transaction.commit).toHaveBeenCalledTimes(2);
	});

	it('should throw error when Reba Report not found', async () => {
		const report_id = 'non-existent-id';

		sandbox_repository.db.RebaReport.findByPk.mockResolvedValue(null);

		await expect(service.sendReport(report_id)).rejects.toThrow('Reba report not found');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should rollback on error', async () => {
		const report_id = 'reba-report-id-123';

		sandbox_repository.db.RebaReport.findByPk.mockRejectedValue(new Error('Database error'));

		await expect(service.sendReport(report_id)).rejects.toThrow('Database error');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should rollback on file creation error', async () => {
		const reba_report = { ...mockedRebaReportForSandbox };

		sandbox_repository.db.RebaReport.findByPk.mockResolvedValue(reba_report);
		service.getOrCreateFile = jest.fn().mockRejectedValue(new Error('File creation failed'));

		await expect(service.sendReport(reba_report.id)).rejects.toThrow('File creation failed');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should create report with updated file_id', async () => {
		const reba_report = { ...mockedRebaReportForSandbox };
		const mockFile = {
			id: 'new-reba-file-id'
		};

		sandbox_repository.db.RebaReport.findByPk.mockResolvedValue(reba_report);

		service.getOrCreateFile = jest.fn().mockResolvedValue(mockFile);
		service.create = jest.fn().mockResolvedValue({ id: 'new-reba-report-id' });
		sandbox_repository.db.RebaReport.destroy.mockResolvedValue(1);

		await service.sendReport(reba_report.id);

		expect(service.create).toHaveBeenCalledWith(
			expect.objectContaining({
				file_id: mockFile.id
			}),
			'RebaReport',
			{
				kinebot: transaction,
				sandbox: transaction
			}
		);
	});
});
