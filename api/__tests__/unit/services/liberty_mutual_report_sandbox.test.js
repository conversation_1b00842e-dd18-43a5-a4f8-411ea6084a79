import Sequelize from 'sequelize';
import { jest } from '@jest/globals';
import { LibertyMutualReportSandboxService } from '@src/app/service/sandbox/liberty_mutual_report_sandbox.js';
import { mockedLibertyMutualReportForSandbox, mockedFileForSandbox, mockedTransaction } from '../fixtures';

const { Op } = Sequelize;

describe('[LibertyMutualReportSandboxService] - service', () => {
	let kinebot_repository;
	let sandbox_repository;
	let service;
	let transaction;

	beforeEach(() => {
		transaction = {
			commit: jest.fn(),
			rollback: jest.fn()
		};

		kinebot_repository = {
			db: {
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) },
				File: { findOne: jest.fn(), findOrCreate: jest.fn(), create: jest.fn() },
				Sector: { findOrCreate: jest.fn(), create: jest.fn() },
				Line: { findOrCreate: jest.fn(), create: jest.fn() },
				Workstation: { findOrCreate: jest.fn(), create: jest.fn() },
				Task: { findOrCreate: jest.fn(), create: jest.fn() },
				LibertyMutualReport: { findOrCreate: jest.fn(), create: jest.fn(), findOne: jest.fn() },
				LibertyMutualReportInput: { findOrCreate: jest.fn(), create: jest.fn() }
			}
		};

		sandbox_repository = {
			db: {
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) },
				LibertyMutualReport: {
					findByPk: jest.fn(),
					destroy: jest.fn()
				},
				LibertyMutualReportInput: {
					destroy: jest.fn()
				},
				File: {
					findByPk: jest.fn()
				},
				Task: {
					findByPk: jest.fn()
				},
				ActionPlanRelatedReport: {
					findOne: jest.fn()
				}
			}
		};

		service = new LibertyMutualReportSandboxService({ kinebot_repository, sandbox_repository });
	});

	it('should send report and destroy sandbox data', async () => {
		const liberty_mutual_report = { ...mockedLibertyMutualReportForSandbox };
		const mockFile = {
			...mockedFileForSandbox,
			id: 'new-liberty-mutual-file-id'
		};

		sandbox_repository.db.LibertyMutualReport.findByPk.mockResolvedValue(liberty_mutual_report);
		sandbox_repository.db.ActionPlanRelatedReport.findOne.mockResolvedValue(null);

		service.getOrCreateFile = jest.fn().mockResolvedValue(mockFile);

		service.create = jest
			.fn()
			.mockResolvedValueOnce({ id: 'new-liberty-mutual-report-id' })
			.mockResolvedValue({ id: 'new-created-id' });

		sandbox_repository.db.LibertyMutualReportInput.destroy.mockResolvedValue(2);
		sandbox_repository.db.LibertyMutualReport.destroy.mockResolvedValue(1);

		await service.sendReport(liberty_mutual_report.id);

		expect(sandbox_repository.db.LibertyMutualReport.findByPk).toHaveBeenCalledWith(liberty_mutual_report.id, {
			include: [{ association: 'report_inputs' }],
			transaction: transaction
		});

		expect(service.create).toHaveBeenCalledWith(
			expect.objectContaining({ file_id: mockFile.id }),
			'LibertyMutualReport',
			{ kinebot: transaction, sandbox: transaction }
		);

		expect(service.create).toHaveBeenCalledWith(
			expect.objectContaining({
				liberty_mutual_report_id: 'new-liberty-mutual-report-id'
			}),
			'LibertyMutualReportInput',
			{ kinebot: transaction, sandbox: transaction }
		);

		expect(service.create).toHaveBeenCalledWith(
			expect.objectContaining({
				liberty_mutual_report_id: 'new-liberty-mutual-report-id'
			}),
			'LibertyMutualReportInput',
			{ kinebot: transaction, sandbox: transaction }
		);

		expect(sandbox_repository.db.LibertyMutualReportInput.destroy).toHaveBeenCalledWith({
			where: { liberty_mutual_report_id: liberty_mutual_report.id },
			transaction: transaction,
			force: true
		});

		expect(sandbox_repository.db.LibertyMutualReport.destroy).toHaveBeenCalledWith({
			where: { id: liberty_mutual_report.id },
			transaction: transaction,
			force: true
		});

		expect(transaction.commit).toHaveBeenCalledTimes(2);
	});

	it('should rollback on error', async () => {
		sandbox_repository.db.LibertyMutualReport.findByPk.mockRejectedValue(new Error('Database error'));

		await expect(service.sendReport('any-id')).rejects.toThrow('Database error');

		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should handle empty report inputs', async () => {
		const liberty_mutual_report = {
			...mockedLibertyMutualReportForSandbox,
			report_inputs: []
		};

		sandbox_repository.db.LibertyMutualReport.findByPk.mockResolvedValue(liberty_mutual_report);

		service.getOrCreateFile = jest.fn().mockResolvedValue({ id: 'new-liberty-mutual-file-id' });
		service.create = jest.fn().mockResolvedValue({ id: 'new-liberty-mutual-report-id' });

		sandbox_repository.db.LibertyMutualReportInput.destroy.mockResolvedValue(0);
		sandbox_repository.db.LibertyMutualReport.destroy.mockResolvedValue(1);

		await service.sendReport(liberty_mutual_report.id);

		expect(transaction.commit).toHaveBeenCalledTimes(2);
	});

	it('should handle missing task_id in report inputs', async () => {
		const liberty_mutual_report = {
			...mockedLibertyMutualReportForSandbox,
			report_inputs: [
				{
					id: 'liberty-mutual-input-null-task-id-123',
					liberty_mutual_report_id: 'liberty-mutual-report-id-123',
					task_id: null,
					value: 5.0
				}
			]
		};

		sandbox_repository.db.LibertyMutualReport.findByPk.mockResolvedValue(liberty_mutual_report);

		service.getOrCreateFile = jest.fn().mockResolvedValue({ id: 'new-liberty-mutual-file-id' });
		service.create = jest
			.fn()
			.mockResolvedValueOnce({ id: 'new-liberty-mutual-report-id' })
			.mockResolvedValue({ id: 'new-input-id' });

		sandbox_repository.db.LibertyMutualReportInput.destroy.mockResolvedValue(1);
		sandbox_repository.db.LibertyMutualReport.destroy.mockResolvedValue(1);

		await service.sendReport(liberty_mutual_report.id);

		expect(service.create).toHaveBeenCalledWith(
			expect.objectContaining({
				liberty_mutual_report_id: 'new-liberty-mutual-report-id',
				task_id: null
			}),
			'LibertyMutualReportInput',
			{ kinebot: transaction, sandbox: transaction }
		);

		expect(transaction.commit).toHaveBeenCalledTimes(2);
	});
});
