import Sequelize from 'sequelize';
import { jest } from '@jest/globals';
import { BackCompressiveReportSandboxService } from '@src/app/service/sandbox/back_compressive_report_sandbox.js';
import { mockedBackCompressiveReportForSandbox, mockedFileForSandbox, mockedTransaction } from '../fixtures';

const { Op } = Sequelize;

describe('[BackCompressiveReportSandboxService] - service', () => {
	let kinebot_repository;
	let sandbox_repository;
	let service;
	let transaction;

	beforeEach(() => {
		transaction = {
			commit: jest.fn(),
			rollback: jest.fn()
		};

		kinebot_repository = {
			db: {
				File: { findOne: jest.fn(), findOrCreate: jest.fn(), create: jest.fn() },
				BackCompressiveForceEstimationReport: {
					findOrCreate: jest.fn(),
					create: jest.fn(),
					findOne: jest.fn()
				},
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) }
			}
		};

		sandbox_repository = {
			db: {
				BackCompressiveForceEstimationReport: {
					findByPk: jest.fn(),
					destroy: jest.fn()
				},
				File: {
					findByPk: jest.fn()
				},
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) },
				ActionPlanRelatedReport: {
					findOne: jest.fn()
				}
			}
		};

		service = new BackCompressiveReportSandboxService({ kinebot_repository, sandbox_repository });
	});

	it('should send report and destroy sandbox data', async () => {
		const back_compressive_report = { ...mockedBackCompressiveReportForSandbox };
		const mockFile = {
			...mockedFileForSandbox,
			id: 'new-back-compressive-file-id'
		};

		sandbox_repository.db.BackCompressiveForceEstimationReport.findByPk.mockResolvedValue(back_compressive_report);
		sandbox_repository.db.ActionPlanRelatedReport.findOne.mockResolvedValue(null);

		service.getOrCreateFile = jest.fn().mockResolvedValue(mockFile);
		service.create = jest.fn().mockResolvedValue({ id: 'new-back-compressive-report-id' });

		sandbox_repository.db.BackCompressiveForceEstimationReport.destroy.mockResolvedValue(1);

		await service.sendReport(back_compressive_report.id);

		expect(sandbox_repository.db.BackCompressiveForceEstimationReport.findByPk).toHaveBeenCalledWith(
			back_compressive_report.id,
			{
				transaction: transaction
			}
		);

		expect(service.create).toHaveBeenCalledWith(
			{ ...back_compressive_report, file_id: mockFile.id },
			'BackCompressiveForceEstimationReport',
			{
				kinebot: transaction,
				sandbox: transaction
			}
		);
		expect(sandbox_repository.db.BackCompressiveForceEstimationReport.destroy).toHaveBeenCalledWith({
			where: { id: back_compressive_report.id },
			transaction: transaction,
			force: true
		});
		expect(transaction.commit).toHaveBeenCalledTimes(2);
	});

	it('should throw error when Back Compressive Report not found', async () => {
		const report_id = 'non-existent-id';

		sandbox_repository.db.BackCompressiveForceEstimationReport.findByPk.mockResolvedValue(null);

		await expect(service.sendReport(report_id)).rejects.toThrow('Back compressive report not found');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should rollback on error', async () => {
		const report_id = 'back-compressive-report-id-123';

		sandbox_repository.db.BackCompressiveForceEstimationReport.findByPk.mockRejectedValue(
			new Error('Database error')
		);

		await expect(service.sendReport(report_id)).rejects.toThrow('Database error');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should rollback on file creation error', async () => {
		const back_compressive_report = { ...mockedBackCompressiveReportForSandbox };

		sandbox_repository.db.BackCompressiveForceEstimationReport.findByPk.mockResolvedValue(back_compressive_report);
		service.getOrCreateFile = jest.fn().mockRejectedValue(new Error('File creation failed'));

		await expect(service.sendReport(back_compressive_report.id)).rejects.toThrow('File creation failed');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});
});
