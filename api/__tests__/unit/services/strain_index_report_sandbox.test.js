import Sequelize from 'sequelize';
import { jest } from '@jest/globals';
import { StrainIndexReportSandboxService } from '@src/app/service/sandbox/strain_index_report_sandbox.js';
import { mockedStrainIndexReportForSandbox, mockedFileForSandbox, mockedTransaction } from '../fixtures';

const { Op } = Sequelize;

describe('[StrainIndexReportSandboxService] - service', () => {
	let kinebot_repository;
	let sandbox_repository;
	let service;
	let transaction;

	beforeEach(() => {
		transaction = {
			commit: jest.fn(),
			rollback: jest.fn()
		};

		kinebot_repository = {
			db: {
				File: { findOne: jest.fn(), findOrCreate: jest.fn(), create: jest.fn() },
				StrainIndexReport: { findOrCreate: jest.fn(), create: jest.fn(), findOne: jest.fn() },
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) }
			}
		};

		sandbox_repository = {
			db: {
				StrainIndexReport: {
					findByPk: jest.fn(),
					destroy: jest.fn()
				},
				File: {
					findByPk: jest.fn()
				},
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) },
				ActionPlanRelatedReport: {
					findOne: jest.fn()
				}
			}
		};

		service = new StrainIndexReportSandboxService({ kinebot_repository, sandbox_repository });
	});

	it('should send report and destroy sandbox data', async () => {
		const strain_index_report = { ...mockedStrainIndexReportForSandbox };
		const mockFile = {
			...mockedFileForSandbox,
			id: 'new-strain-index-file-id'
		};

		sandbox_repository.db.StrainIndexReport.findByPk.mockResolvedValue(strain_index_report);
		sandbox_repository.db.ActionPlanRelatedReport.findOne.mockResolvedValue(null);

		service.getOrCreateFile = jest.fn().mockResolvedValue(mockFile);
		service.create = jest.fn().mockResolvedValue({ id: 'new-strain-index-report-id' });

		sandbox_repository.db.StrainIndexReport.destroy.mockResolvedValue(1);

		await service.sendReport(strain_index_report.id);

		expect(sandbox_repository.db.StrainIndexReport.findByPk).toHaveBeenCalledWith(strain_index_report.id, {
			transaction: transaction
		});

		expect(service.create).toHaveBeenCalledWith(
			{ ...strain_index_report, file_id: mockFile.id },
			'StrainIndexReport',
			{
				kinebot: transaction,
				sandbox: transaction
			}
		);
		expect(sandbox_repository.db.StrainIndexReport.destroy).toHaveBeenCalledWith({
			where: { id: strain_index_report.id },
			transaction: transaction,
			force: true
		});
		expect(transaction.commit).toHaveBeenCalledTimes(2);
	});

	it('should throw error when Strain Index Report not found', async () => {
		const report_id = 'non-existent-id';

		sandbox_repository.db.StrainIndexReport.findByPk.mockResolvedValue(null);

		await expect(service.sendReport(report_id)).rejects.toThrow('Strain index report not found');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should rollback on error', async () => {
		const report_id = 'strain-index-report-id-123';

		sandbox_repository.db.StrainIndexReport.findByPk.mockRejectedValue(new Error('Database error'));

		await expect(service.sendReport(report_id)).rejects.toThrow('Database error');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should rollback on file creation error', async () => {
		const strain_index_report = { ...mockedStrainIndexReportForSandbox };

		sandbox_repository.db.StrainIndexReport.findByPk.mockResolvedValue(strain_index_report);
		service.getOrCreateFile = jest.fn().mockRejectedValue(new Error('File creation failed'));

		await expect(service.sendReport(strain_index_report.id)).rejects.toThrow('File creation failed');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});
});
