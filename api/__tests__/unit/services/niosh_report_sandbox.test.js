import Sequelize from 'sequelize';
import { jest } from '@jest/globals';
import { NioshReportSandboxService } from '@src/app/service/sandbox/niosh_report_sandbox.js';
import { mockedNioshReportForSandbox, mockedFileForSandbox, mockedTransaction } from '../fixtures';

const { Op } = Sequelize;

describe('[NioshReportSandboxService] - service', () => {
	let kinebot_repository;
	let sandbox_repository;
	let service;
	let transaction;

	beforeEach(() => {
		transaction = {
			commit: jest.fn(),
			rollback: jest.fn()
		};

		kinebot_repository = {
			db: {
				File: { findOne: jest.fn(), findOrCreate: jest.fn(), create: jest.fn() },
				NioshReport: { findOrCreate: jest.fn(), create: jest.fn(), findOne: jest.fn() },
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) }
			}
		};

		sandbox_repository = {
			db: {
				NioshReport: {
					findByPk: jest.fn(),
					destroy: jest.fn()
				},
				File: {
					findByPk: jest.fn()
				},
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) },
				ActionPlanRelatedReport: {
					findOne: jest.fn()
				}
			}
		};

		service = new NioshReportSandboxService({ kinebot_repository, sandbox_repository });
	});

	it('should send report and destroy sandbox data', async () => {
		const niosh_report = { ...mockedNioshReportForSandbox };
		const mockFile = {
			...mockedFileForSandbox,
			id: 'new-niosh-file-id'
		};

		sandbox_repository.db.NioshReport.findByPk.mockResolvedValue(niosh_report);
		sandbox_repository.db.ActionPlanRelatedReport.findOne.mockResolvedValue(null);

		service.getOrCreateFile = jest.fn().mockResolvedValue(mockFile);
		service.create = jest.fn().mockResolvedValue({ id: 'new-niosh-report-id' });

		sandbox_repository.db.NioshReport.destroy.mockResolvedValue(1);

		await service.sendReport(niosh_report.id);

		expect(sandbox_repository.db.NioshReport.findByPk).toHaveBeenCalledWith(niosh_report.id, {
			transaction: transaction
		});

		expect(service.create).toHaveBeenCalledWith({ ...niosh_report, file_id: mockFile.id }, 'NioshReport', {
			kinebot: transaction,
			sandbox: transaction
		});
		expect(sandbox_repository.db.NioshReport.destroy).toHaveBeenCalledWith({
			where: { id: niosh_report.id },
			transaction: transaction,
			force: true
		});
		expect(transaction.commit).toHaveBeenCalledTimes(2);
	});

	it('should throw error when NIOSH report not found', async () => {
		const report_id = 'non-existent-id';

		sandbox_repository.db.NioshReport.findByPk.mockResolvedValue(null);

		await expect(service.sendReport(report_id)).rejects.toThrow('Niosh report not found');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should rollback on error', async () => {
		const report_id = 'niosh-report-id-123';

		sandbox_repository.db.NioshReport.findByPk.mockRejectedValue(new Error('Database error'));

		await expect(service.sendReport(report_id)).rejects.toThrow('Database error');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should rollback on file creation error', async () => {
		const niosh_report = { ...mockedNioshReportForSandbox };

		sandbox_repository.db.NioshReport.findByPk.mockResolvedValue(niosh_report);
		service.getOrCreateFile = jest.fn().mockRejectedValue(new Error('File creation failed'));

		await expect(service.sendReport(niosh_report.id)).rejects.toThrow('File creation failed');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});
});
