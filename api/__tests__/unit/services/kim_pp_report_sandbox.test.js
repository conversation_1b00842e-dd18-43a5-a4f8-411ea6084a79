import Sequelize from 'sequelize';
import { jest } from '@jest/globals';
import { KimPpReportSandboxService } from '@src/app/service/sandbox/kim_pp_report_sandbox.js';
import {
	mockedKimPpReportForSandbox,
	mockedFileForSandbox,
	mockedSectorForSandbox,
	mockedTransaction
} from '../fixtures';

const { Op } = Sequelize;

describe('[KimPpReportSandboxService] - service', () => {
	let kinebot_repository;
	let sandbox_repository;
	let service;
	let transaction;

	beforeEach(() => {
		transaction = {
			commit: jest.fn(),
			rollback: jest.fn()
		};

		kinebot_repository = {
			db: {
				File: { findOne: jest.fn(), findOrCreate: jest.fn(), create: jest.fn() },
				Sector: { findOne: jest.fn(), findOrCreate: jest.fn(), create: jest.fn() },
				KimPushPullReport: { findOrCreate: jest.fn(), create: jest.fn(), findOne: jest.fn() },
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) }
			}
		};

		sandbox_repository = {
			db: {
				KimPushPullReport: {
					findByPk: jest.fn(),
					destroy: jest.fn()
				},
				File: {
					findByPk: jest.fn()
				},
				Sector: {
					findByPk: jest.fn()
				},
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) },
				ActionPlanRelatedReport: {
					findOne: jest.fn()
				}
			}
		};

		service = new KimPpReportSandboxService({ kinebot_repository, sandbox_repository });
	});

	it('should send report and destroy sandbox data', async () => {
		const kim_pp_report = { ...mockedKimPpReportForSandbox };
		const mockFile = {
			...mockedFileForSandbox,
			id: 'new-kim-pp-file-id'
		};
		const mockSector = {
			...mockedSectorForSandbox,
			id: 'new-kim-pp-sector-id'
		};

		sandbox_repository.db.KimPushPullReport.findByPk.mockResolvedValue(kim_pp_report);
		sandbox_repository.db.ActionPlanRelatedReport.findOne.mockResolvedValue(null);

		service.getOrCreateFile = jest.fn().mockResolvedValue(mockFile);
		service.getOrCreateSimpleDependency = jest.fn().mockResolvedValue(mockSector);
		service.create = jest.fn().mockResolvedValue({ id: 'new-kim-pp-report-id' });

		sandbox_repository.db.KimPushPullReport.destroy.mockResolvedValue(1);

		await service.sendReport(kim_pp_report.id);

		expect(sandbox_repository.db.KimPushPullReport.findByPk).toHaveBeenCalledWith(kim_pp_report.id, {
			transaction: transaction
		});

		expect(service.create).toHaveBeenCalledWith(
			{ ...kim_pp_report, file_id: mockFile.id, sector_id: mockSector.id },
			'KimPushPullReport',
			{
				kinebot: transaction,
				sandbox: transaction
			}
		);
		expect(sandbox_repository.db.KimPushPullReport.destroy).toHaveBeenCalledWith({
			where: { id: kim_pp_report.id },
			transaction: transaction,
			force: true
		});
		expect(transaction.commit).toHaveBeenCalledTimes(2);
	});

	it('should throw error when Kim Pp Report not found', async () => {
		const report_id = 'non-existent-id';

		sandbox_repository.db.KimPushPullReport.findByPk.mockResolvedValue(null);

		await expect(service.sendReport(report_id)).rejects.toThrow('Kim pp report not found');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should rollback on error', async () => {
		const report_id = 'kim-pp-report-id-123';

		sandbox_repository.db.KimPushPullReport.findByPk.mockRejectedValue(new Error('Database error'));

		await expect(service.sendReport(report_id)).rejects.toThrow('Database error');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should rollback on file creation error', async () => {
		const kim_pp_report = { ...mockedKimPpReportForSandbox };

		sandbox_repository.db.KimPushPullReport.findByPk.mockResolvedValue(kim_pp_report);
		service.getOrCreateFile = jest.fn().mockRejectedValue(new Error('File creation failed'));

		await expect(service.sendReport(kim_pp_report.id)).rejects.toThrow('File creation failed');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should rollback on sector creation error', async () => {
		const kim_pp_report = { ...mockedKimPpReportForSandbox };
		const mockFile = {
			id: 'new-kim-pp-file-id'
		};

		sandbox_repository.db.KimPushPullReport.findByPk.mockResolvedValue(kim_pp_report);
		service.getOrCreateFile = jest.fn().mockResolvedValue(mockFile);
		service.getOrCreateSimpleDependency = jest.fn().mockRejectedValue(new Error('Sector creation failed'));

		await expect(service.sendReport(kim_pp_report.id)).rejects.toThrow('Sector creation failed');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});
});
