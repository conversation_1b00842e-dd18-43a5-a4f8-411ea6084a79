import Sequelize from 'sequelize';
import { jest } from '@jest/globals';
import { AngleTimeReportSandboxService } from '@src/app/service/sandbox/angle_time_report_sandbox.js';
import { mockedAngleTimeReportForSandbox, mockedFileForSandbox, mockedTransaction } from '../fixtures';

const { Op } = Sequelize;

describe('[AngleTimeReportSandboxService] - service', () => {
	let kinebot_repository;
	let sandbox_repository;
	let service;
	let transaction;

	beforeEach(() => {
		transaction = {
			commit: jest.fn(),
			rollback: jest.fn()
		};

		kinebot_repository = {
			db: {
				File: { findOne: jest.fn(), findOrCreate: jest.fn(), create: jest.fn() },
				AngleTimeReport: { findOrCreate: jest.fn(), create: jest.fn(), findOne: jest.fn() },
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) }
			}
		};

		sandbox_repository = {
			db: {
				AngleTimeReport: {
					findByPk: jest.fn(),
					destroy: jest.fn()
				},
				File: {
					findByPk: jest.fn()
				},
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) },
				ActionPlanRelatedReport: {
					findOne: jest.fn()
				}
			}
		};

		service = new AngleTimeReportSandboxService({ kinebot_repository, sandbox_repository });
	});

	it('should send report and destroy sandbox data', async () => {
		const angle_time_report = { ...mockedAngleTimeReportForSandbox };
		const mockFile = {
			...mockedFileForSandbox,
			id: 'new-angle-time-file-id'
		};

		sandbox_repository.db.AngleTimeReport.findByPk.mockResolvedValue(angle_time_report);

		service.getOrCreateFile = jest.fn().mockResolvedValue(mockFile);
		service.create = jest.fn().mockResolvedValue({ id: 'new-angle-time-report-id' });

		sandbox_repository.db.AngleTimeReport.destroy.mockResolvedValue(1);

		await service.sendReport(angle_time_report.id);

		expect(sandbox_repository.db.AngleTimeReport.findByPk).toHaveBeenCalledWith(angle_time_report.id, {
			transaction: transaction
		});

		expect(service.create).toHaveBeenCalledWith({ ...angle_time_report, file_id: mockFile.id }, 'AngleTimeReport', {
			kinebot: transaction,
			sandbox: transaction
		});
		expect(sandbox_repository.db.AngleTimeReport.destroy).toHaveBeenCalledWith({
			where: { id: angle_time_report.id },
			transaction: transaction,
			force: true
		});
		expect(transaction.commit).toHaveBeenCalledTimes(2);
	});

	it('should throw error when Angle Time Reports not found', async () => {
		const report_id = 'non-existent-id';

		sandbox_repository.db.AngleTimeReport.findByPk.mockResolvedValue(null);

		await expect(service.sendReport(report_id)).rejects.toThrow('Angle time report not found');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should rollback on error', async () => {
		const report_id = 'angle-time-report-id-123';

		sandbox_repository.db.AngleTimeReport.findByPk.mockRejectedValue(new Error('Database error'));

		await expect(service.sendReport(report_id)).rejects.toThrow('Database error');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should rollback on file creation error', async () => {
		const angle_time_report = { ...mockedAngleTimeReportForSandbox };

		sandbox_repository.db.AngleTimeReport.findByPk.mockResolvedValue(angle_time_report);
		service.getOrCreateFile = jest.fn().mockRejectedValue(new Error('File creation failed'));

		await expect(service.sendReport(angle_time_report.id)).rejects.toThrow('File creation failed');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});
});
