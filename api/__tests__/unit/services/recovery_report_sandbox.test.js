import Sequelize from 'sequelize';
import { jest } from '@jest/globals';
import { RecoveryReportSandboxService } from '@src/app/service/sandbox/recovery_report_sandbox.js';
import {
	mockedRecoveryReportForSandbox,
	mockedFileForSandbox,
	mockedSectorForSandbox,
	mockedTransaction
} from '../fixtures';

const { Op } = Sequelize;

describe('[RecoveryReportSandboxService] - service', () => {
	let kinebot_repository;
	let sandbox_repository;
	let service;
	let transaction;

	beforeEach(() => {
		transaction = {
			commit: jest.fn(),
			rollback: jest.fn()
		};

		kinebot_repository = {
			db: {
				File: { findOne: jest.fn(), findOrCreate: jest.fn(), create: jest.fn() },
				Sector: { findOne: jest.fn(), findOrCreate: jest.fn(), create: jest.fn() },
				RecoveryReport: { findOrCreate: jest.fn(), create: jest.fn(), findOne: jest.fn() },
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) }
			}
		};

		sandbox_repository = {
			db: {
				RecoveryReport: {
					findByPk: jest.fn(),
					destroy: jest.fn()
				},
				File: {
					findByPk: jest.fn()
				},
				Sector: {
					findByPk: jest.fn()
				},
				sequelize: { transaction: jest.fn().mockResolvedValue(transaction) },
				ActionPlanRelatedReport: {
					findOne: jest.fn()
				}
			}
		};

		service = new RecoveryReportSandboxService({ kinebot_repository, sandbox_repository });
	});

	it('should send report and destroy sandbox data', async () => {
		const recovery_report = { ...mockedRecoveryReportForSandbox };
		const mockFile = {
			...mockedFileForSandbox,
			id: 'new-recovery-file-id'
		};
		const mockSector = {
			...mockedSectorForSandbox,
			id: 'new-recovery-sector-id'
		};

		sandbox_repository.db.RecoveryReport.findByPk.mockResolvedValue(recovery_report);
		sandbox_repository.db.ActionPlanRelatedReport.findOne.mockResolvedValue(null);

		service.getOrCreateFile = jest.fn().mockResolvedValue(mockFile);
		service.getOrCreateSimpleDependency = jest.fn().mockResolvedValue(mockSector);
		service.create = jest.fn().mockResolvedValue({ id: 'new-recovery-report-id' });

		sandbox_repository.db.RecoveryReport.destroy.mockResolvedValue(1);

		await service.sendReport(recovery_report.id);

		expect(sandbox_repository.db.RecoveryReport.findByPk).toHaveBeenCalledWith(recovery_report.id, {
			transaction: transaction
		});

		expect(service.create).toHaveBeenCalledWith(
			{ ...recovery_report, file_id: mockFile.id, sector_id: mockSector.id },
			'RecoveryReport',
			{
				kinebot: transaction,
				sandbox: transaction
			}
		);
		expect(sandbox_repository.db.RecoveryReport.destroy).toHaveBeenCalledWith({
			where: { id: recovery_report.id },
			transaction: transaction,
			force: true
		});
		expect(transaction.commit).toHaveBeenCalledTimes(2);
	});

	it('should throw error when Recovery Report not found', async () => {
		const report_id = 'non-existent-id';

		sandbox_repository.db.RecoveryReport.findByPk.mockResolvedValue(null);

		await expect(service.sendReport(report_id)).rejects.toThrow('Report data not found');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should rollback on error', async () => {
		const report_id = 'recovery-report-id-123';

		sandbox_repository.db.RecoveryReport.findByPk.mockRejectedValue(new Error('Database error'));

		await expect(service.sendReport(report_id)).rejects.toThrow('Database error');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should rollback on file creation error', async () => {
		const recovery_report = { ...mockedRecoveryReportForSandbox };

		sandbox_repository.db.RecoveryReport.findByPk.mockResolvedValue(recovery_report);
		service.getOrCreateFile = jest.fn().mockRejectedValue(new Error('File creation failed'));

		await expect(service.sendReport(recovery_report.id)).rejects.toThrow('File creation failed');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});

	it('should rollback on sector creation error', async () => {
		const recovery_report = { ...mockedRecoveryReportForSandbox };
		const mockFile = {
			id: 'new-recovery-file-id'
		};

		sandbox_repository.db.RecoveryReport.findByPk.mockResolvedValue(recovery_report);
		service.getOrCreateFile = jest.fn().mockResolvedValue(mockFile);
		service.getOrCreateSimpleDependency = jest.fn().mockRejectedValue(new Error('Sector creation failed'));

		await expect(service.sendReport(recovery_report.id)).rejects.toThrow('Sector creation failed');
		expect(transaction.rollback).toHaveBeenCalledTimes(2);
	});
});
