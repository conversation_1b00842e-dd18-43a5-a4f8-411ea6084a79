import { jest } from '@jest/globals';
import { faker } from '@faker-js/faker';
import { AppError } from '@src/app/helpers/errors.js';
import { RESPONSE_ERROR_STATUS } from '@src/app/helpers/index.js';
import { SeraReportService } from '@src/app/service/sera_report.js';
import { mockedHierarchySumRPN, mockedSumRPN, mockedIncidenceCategories } from '@tests/unit/fixtures/index.js';

const { datatype } = faker;
const { DATABASE_FAILED_PERFORM_QUERY } = RESPONSE_ERROR_STATUS;

describe('Sera report service', () => {
	let service;

	let repository = {
		sumRPN: jest.fn(),
		hierarchySumRPN: jest.fn(),
		incidenceCategories: jest.fn(),
		db: {
			sequelize: {
				transaction: jest.fn()
			}
		}
	};

	const transaction = {
		commit: () => {},
		rollback: () => {}
	};

	beforeAll(() => {
		service = new SeraReportService({ repository });
		jest.spyOn(repository.db.sequelize, 'transaction').mockResolvedValue(transaction);
	});

	beforeEach(() => {
		jest.clearAllMocks();
	});

	describe('[sumRPN]', () => {
		const payload = {
			organization_id: datatype.uuid(),
			company_id: datatype.uuid()
		};

		it('should return the sum of RPN', async () => {
			jest.spyOn(repository, 'sumRPN').mockResolvedValue([mockedSumRPN]);
			const result = await service.sumRPN(payload);
			expect(result).toStrictEqual(mockedSumRPN);
		});

		it('should verify repository methods call', async () => {
			jest.spyOn(repository, 'sumRPN').mockResolvedValue([mockedSumRPN]);
			await service.sumRPN(payload);
			expect(repository.sumRPN).toHaveBeenCalledWith({
				companies_ids: undefined,
				company_id: payload.company_id,
				end_date: undefined,
				line_id: undefined,
				organization_id: payload.organization_id,
				sector_id: undefined,
				start_date: undefined,
				user_id: '',
				workstation_id: undefined
			});
		});

		it('should throw database error', async () => {
			jest.spyOn(repository, 'sumRPN').mockResolvedValue([null, new Error('Database error')]);
			await expect(service.sumRPN(payload)).rejects.toThrow(new AppError(DATABASE_FAILED_PERFORM_QUERY));
		});
	});

	describe('[hierarchySumRPN]', () => {
		const payload = {
			organization_id: datatype.uuid(),
			company_id: datatype.uuid()
		};

		it('should return the total RPN from hierarchy', async () => {
			jest.spyOn(repository, 'hierarchySumRPN').mockResolvedValue([mockedHierarchySumRPN]);
			const result = await service.hierarchySumRPN(payload);
			expect(result).toStrictEqual(mockedHierarchySumRPN);
		});

		it('should verify repository methods call', async () => {
			jest.spyOn(repository, 'hierarchySumRPN').mockResolvedValue([mockedHierarchySumRPN]);
			await service.hierarchySumRPN(payload);
			expect(repository.hierarchySumRPN).toHaveBeenCalledWith({
				companies_ids: undefined,
				company_id: payload.company_id,
				end_date: undefined,
				line_id: undefined,
				organization_id: payload.organization_id,
				sector_id: undefined,
				start_date: undefined,
				user_id: '',
				workstation_id: undefined
			});
		});

		it('should throw database error', async () => {
			jest.spyOn(repository, 'hierarchySumRPN').mockResolvedValue([null, new Error('Database error')]);
			await expect(service.hierarchySumRPN(payload)).rejects.toThrow(new AppError(DATABASE_FAILED_PERFORM_QUERY));
		});
	});

	describe('[incidenceCategories]', () => {
		const payload = {
			organization_id: datatype.uuid(),
			company_id: datatype.uuid()
		};

		it('should return the incidence categories', async () => {
			const expectedResponse = [
				{
					risk_category_id: expect.any(String),
					description: expect.any(String),
					percentage: expect.any(Number),
					count: expect.any(Number)
				},
				{
					risk_category_id: expect.any(String),
					description: expect.any(String),
					percentage: expect.any(Number),
					count: expect.any(Number)
				}
			];
			jest.spyOn(repository, 'incidenceCategories').mockResolvedValue([mockedIncidenceCategories]);
			const result = await service.incidenceCategories(payload);
			expect(result).toStrictEqual(expectedResponse);
		});

		it('should verify repository methods call', async () => {
			jest.spyOn(repository, 'incidenceCategories').mockResolvedValue([mockedIncidenceCategories]);
			await service.incidenceCategories(payload);
			expect(repository.incidenceCategories).toHaveBeenCalledWith({
				companies_ids: undefined,
				company_id: payload.company_id,
				end_date: undefined,
				line_id: undefined,
				organization_id: payload.organization_id,
				sector_id: undefined,
				start_date: undefined,
				user_id: '',
				workstation_id: undefined
			});
		});

		it('should throw database error', async () => {
			jest.spyOn(repository, 'incidenceCategories').mockResolvedValue([null, new Error('Database error')]);
			await expect(service.incidenceCategories(payload)).rejects.toThrow(
				new AppError(DATABASE_FAILED_PERFORM_QUERY)
			);
		});
	});
});
