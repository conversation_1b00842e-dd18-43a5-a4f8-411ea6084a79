import { jest } from '@jest/globals';
import { faker } from '@faker-js/faker';

import { ERROR_RESPONSE_ENUM, ERROR_RESPONSE_ENTITIES_ENUM } from '../../../src/app/util/enum.js';
import { StressLevelService } from '../../../src/app/service/stress_level.js';
import { mockedCustomReportStepKey, mockedStressLevel } from '../fixtures';
import { AppError } from '../../../src/app/helpers/errors.js';

const { datatype } = faker;

describe('Stress level service', () => {
	let customReportStepKeyRepository;
	let stressLevelRepository;
	let stressLevelService;

	beforeAll(() => {
		customReportStepKeyRepository = {
			findByPk: jest.fn()
		};
		stressLevelRepository = {
			findByPk: jest.fn(),
			findAllByForeignKey: jest.fn(),
			db: {
				sequelize: {
					transaction: jest.fn()
				}
			}
		};
		stressLevelService = new StressLevelService({
			repository: stressLevelRepository,
			custom_report_step_key_repository: customReportStepKeyRepository
		});
	});

	beforeEach(() => {
		jest.clearAllMocks();
	});

	describe('[findById]', () => {
		const request = {
			id: datatype.uuid()
		};
		it('should find stress level by id', async () => {
			jest.spyOn(stressLevelRepository, 'findByPk').mockResolvedValue(mockedStressLevel);

			const result = await stressLevelService.findById(request.id);

			const response = expect.objectContaining({
				name: expect.any(String),
				description: expect.any(String),
				score: expect.any(Number)
			});

			expect(result).toStrictEqual(response);
		});

		it('should return not found error', async () => {
			const stressLevel = expect(async () => {
				jest.spyOn(stressLevelRepository, 'findByPk').mockResolvedValue(null);

				await stressLevelService.findById(request.id);
			});

			stressLevel.rejects.toStrictEqual(new AppError(ERROR_RESPONSE_ENTITIES_ENUM.STRESS_LEVEL.NOT_FOUND));
		});
	});

	describe('[findAllByStepKeyId]', () => {
		const request = datatype.uuid();
		it('should find stress levels by step key id', async () => {
			jest.spyOn(customReportStepKeyRepository, 'findByPk').mockResolvedValue(mockedCustomReportStepKey);
			jest.spyOn(stressLevelRepository, 'findAllByForeignKey').mockResolvedValueOnce([mockedStressLevel]);

			const result = await stressLevelService.findAllByStepKeyId(request);

			const response = expect.arrayContaining([
				expect.objectContaining({
					name: expect.any(String),
					description: expect.any(String),
					score: expect.any(Number),
					custom_report_step_key_id: expect.any(String)
				})
			]);

			expect(result).toHaveLength(1);
			expect(result).toStrictEqual(response);
		});

		it('should find stress levels by step key id for lifting step key', async () => {
			jest.spyOn(customReportStepKeyRepository, 'findByPk').mockResolvedValue({
				...mockedCustomReportStepKey,
				name: 'lifting'
			});
			jest.spyOn(stressLevelRepository, 'findAllByForeignKey').mockResolvedValueOnce([mockedStressLevel]);

			const result = await stressLevelService.findAllByStepKeyId(request);

			const response = expect.arrayContaining([
				expect.objectContaining({
					name: expect.any(String),
					description: expect.any(String),
					score: expect.any(Number),
					custom_report_step_key_id: expect.any(String)
				})
			]);

			expect(result).toHaveLength(1);
			expect(result).toStrictEqual(response);
		});
	});
});
