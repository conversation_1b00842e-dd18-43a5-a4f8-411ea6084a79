import { jest } from '@jest/globals';
import { SandboxServiceStrategy } from '@src/app/strategy/sandbox_service.js';
import { BeraReportSandboxService } from '@src/app/service/sandbox/bera_report_sandbox.js';
import { mockedSandboxStrategy, createMockedKinebotRepository, createMockedSandboxRepository } from '@fixtures';

describe('[SandboxServiceStrategy] - strategy', () => {
	let kinebot_repository;
	let sandbox_repository;
	let strategy;

	beforeEach(() => {
		kinebot_repository = createMockedKinebotRepository();
		sandbox_repository = createMockedSandboxRepository();

		strategy = new SandboxServiceStrategy({
			kinebot_repository,
			sandbox_repository
		});

		jest.clearAllMocks();
	});

	describe('[setStrategy]', () => {
		it('should set BERA strategy', () => {
			strategy.setStrategy('BERA');

			expect(strategy.strategy).toBeInstanceOf(BeraReportSandboxService);
		});

		it('should set CUSTOM_REPORT strategy', () => {
			strategy.setStrategy('CUSTOM_REPORT');

			expect(strategy.strategy).toBeDefined();
		});

		it('should handle unknown strategy type', () => {
			expect(() => {
				strategy.setStrategy('UNKNOWN_TYPE');
			}).toThrow();
		});
	});

	describe('[sendReport]', () => {
		it('should send report using selected strategy', async () => {
			const mockStrategy = { ...mockedSandboxStrategy };
			const testId = 'test-report-id';

			strategy.strategy = mockStrategy;

			await strategy.sendReport(testId);

			expect(mockStrategy.sendReport).toHaveBeenCalledWith(testId);
		});

		it('should handle strategy sendReport error', async () => {
			const error = new Error('Strategy sendReport failed');
			const mockStrategy = {
				sendReport: jest.fn().mockRejectedValue(error)
			};
			const testId = 'test-report-id';

			strategy.strategy = mockStrategy;

			await expect(strategy.sendReport(testId)).rejects.toThrow('Strategy sendReport failed');

			expect(mockStrategy.sendReport).toHaveBeenCalledWith(testId);
		});

		it('should throw error if no strategy is set', async () => {
			strategy.strategy = null;

			await expect(strategy.sendReport('test-id')).rejects.toThrow();
		});
	});
});
