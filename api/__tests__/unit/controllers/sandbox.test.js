import { jest } from '@jest/globals';
import { mockedSandboxSendReportRequest } from '../fixtures';
import { SandboxController } from '@src/app/controllers/sandbox.js';

describe('[Sandbox] - Controller', () => {
	let controller;

	let service = {
		sendReport: jest.fn(),
		logout: jest.fn()
	};

	let res = {
		status: jest.fn().mockReturnThis(),
		send: jest.fn()
	};

	let next = jest.fn();

	beforeEach(() => {
		controller = new SandboxController(service);
	});

	beforeEach(() => {
		jest.clearAllMocks();
	});

	describe('[sendReport]', () => {
		const req = { ...mockedSandboxSendReportRequest };

		it('should return throw error', async () => {
			jest.spyOn(service, 'sendReport').mockRejectedValueOnce(new Error('Service error'));
			await controller.sendReport(req, res, next);
			expect(next).toHaveBeenCalledWith(expect.any(Error));
		});

		it('should throw error and not return status', async () => {
			jest.spyOn(service, 'sendReport').mockRejectedValueOnce(new Error('Service error'));
			await controller.sendReport(req, res, next);
			expect(res.status).not.toHaveBeenCalled();
		});

		it('should throw error and not return send', async () => {
			jest.spyOn(service, 'sendReport').mockRejectedValueOnce(new Error('Service error'));
			await controller.sendReport(req, res, next);
			expect(res.send).not.toHaveBeenCalled();
		});

		it('should throw error and invoked next callback', async () => {
			jest.spyOn(service, 'sendReport').mockRejectedValueOnce(new Error('Service error'));
			await controller.sendReport(req, res, next);
			expect(next).toHaveBeenCalled();
		});

		it('should invoke service layer with method sendReport', async () => {
			jest.spyOn(service, 'sendReport').mockResolvedValueOnce(undefined);
			await controller.sendReport(req, res, next);
			expect(service.sendReport).toHaveBeenCalledWith({ ...mockedSandboxSendReportRequest.payload });
		});

		it('should return status 200', async () => {
			jest.spyOn(service, 'sendReport').mockResolvedValueOnce(undefined);
			await controller.sendReport(req, res, next);
			expect(res.status).toHaveBeenCalledWith(200);
		});

		it('should return successful response', async () => {
			jest.spyOn(service, 'sendReport').mockResolvedValueOnce(undefined);
			await controller.sendReport(req, res, next);
			expect(res.send).toHaveBeenCalled();
		});

		it('should not invoke next callback on success', async () => {
			jest.spyOn(service, 'sendReport').mockResolvedValueOnce(undefined);
			await controller.sendReport(req, res, next);
			expect(next).not.toHaveBeenCalled();
		});
	});

	describe('[logout]', () => {
		const req = {};

		it('should return throw error', async () => {
			jest.spyOn(service, 'logout').mockRejectedValueOnce(new Error('Service error'));
			await controller.logout(req, res, next);
			expect(next).toHaveBeenCalledWith(expect.any(Error));
		});

		it('should throw error and not return status', async () => {
			jest.spyOn(service, 'logout').mockRejectedValueOnce(new Error('Service error'));
			await controller.logout(req, res, next);
			expect(res.status).not.toHaveBeenCalled();
		});

		it('should throw error and not return send', async () => {
			jest.spyOn(service, 'logout').mockRejectedValueOnce(new Error('Service error'));
			await controller.logout(req, res, next);
			expect(res.send).not.toHaveBeenCalled();
		});

		it('should throw error and invoked next callback', async () => {
			jest.spyOn(service, 'logout').mockRejectedValueOnce(new Error('Service error'));
			await controller.logout(req, res, next);
			expect(next).toHaveBeenCalled();
		});

		it('should invoke service layer with method logout', async () => {
			jest.spyOn(service, 'logout').mockResolvedValueOnce(undefined);
			await controller.logout(req, res, next);
			expect(service.logout).toHaveBeenCalled();
		});

		it('should return status 200', async () => {
			jest.spyOn(service, 'logout').mockResolvedValueOnce(undefined);
			await controller.logout(req, res, next);
			expect(res.status).toHaveBeenCalledWith(200);
		});

		it('should return successful response', async () => {
			jest.spyOn(service, 'logout').mockResolvedValueOnce(undefined);
			await controller.logout(req, res, next);
			expect(res.send).toHaveBeenCalled();
		});

		it('should not invoke next callback on success', async () => {
			jest.spyOn(service, 'logout').mockResolvedValueOnce(undefined);
			await controller.logout(req, res, next);
			expect(next).not.toHaveBeenCalled();
		});
	});
});
