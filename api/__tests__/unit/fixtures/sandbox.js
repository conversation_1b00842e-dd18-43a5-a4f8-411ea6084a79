import { faker } from '@faker-js/faker';
import { jest } from '@jest/globals';

const { datatype } = faker;

const sandboxReportId = datatype.uuid();
const beraJobSummaryId = datatype.uuid();
const cycleId = datatype.uuid();
const beraFileId = datatype.uuid();
const beraJobSummaryFileId = datatype.uuid();
const beraReportFileId = datatype.uuid();
const beraReportId = datatype.uuid();
const taskId = datatype.uuid();
const evaluatorId = datatype.uuid();
const fileId = datatype.uuid();
const customReportFileListId = datatype.uuid();
const customReportResultId = datatype.uuid();
const customReportFileId = datatype.uuid();
const customReportEvaluatorId = datatype.uuid();
const customReportReviewId = datatype.uuid();
const originalCustomReportResultId = datatype.uuid();
const customReportStepKeyResultId = datatype.uuid();
const customReportResultActionLogId = datatype.uuid();
const customReportStepKeyAdditionalItemResultId = datatype.uuid();
const customReportStepKeyAdditionalItemId = datatype.uuid();
const customReportStepKeysAdditionalItemId = datatype.uuid();
const customReportReviewListId = datatype.uuid();
const originalCustomReportResultListId = datatype.uuid();
const customReportResultListId = datatype.uuid();
const customReportFileListIdForList = datatype.uuid();
const customReportEvaluatorListId = datatype.uuid();
const customReportStepKeyResultListId = datatype.uuid();
const customReportResultActionLogListId = datatype.uuid();
const customReportStepKeyAdditionalItemResultListId = datatype.uuid();
const customReportStepKeyAdditionalItemListId = datatype.uuid();
const customReportStepKeyAdditionalItemResultNullId = datatype.uuid();
const customReportResultNullListId = datatype.uuid();
const customReportNewCreatedId = datatype.uuid();
const customReportNewFileId = datatype.uuid();
const customReportNewEvaluatorId = datatype.uuid();
const testId = datatype.uuid();
const customReportServiceId = datatype.uuid();

// Additional IDs for new mock objects
const sectorId = datatype.uuid();
const rebaReportId = datatype.uuid();
const rebaFileId = datatype.uuid();
const nioshReportId = datatype.uuid();
const nioshFileId = datatype.uuid();
const kimMhoReportId = datatype.uuid();
const kimMhoFileId = datatype.uuid();
const kimPpReportId = datatype.uuid();
const kimPpFileId = datatype.uuid();
const kimPpSectorId = datatype.uuid();
const angleTimeReportId = datatype.uuid();
const angleTimeFileId = datatype.uuid();
const backCompressiveReportId = datatype.uuid();
const backCompressiveFileId = datatype.uuid();
const libertyMutualReportId = datatype.uuid();
const libertyMutualFileId = datatype.uuid();
const libertyMutualInput1Id = datatype.uuid();
const libertyMutualInput2Id = datatype.uuid();
const libertyMutualTaskId = datatype.uuid();
const seraSummaryId = datatype.uuid();
const seraEvaluatorId = datatype.uuid();
const seraCycleId = datatype.uuid();
const seraSummaryFile1Id = datatype.uuid();
const seraFile1Id = datatype.uuid();
const seraSummaryReview1Id = datatype.uuid();
const seraReport1Id = datatype.uuid();
const seraTaskId = datatype.uuid();
const seraReviewTasksResult1Id = datatype.uuid();
const seraReportUpdated1Id = datatype.uuid();
const seraReviewSelector1Id = datatype.uuid();
const strainIndexReportId = datatype.uuid();
const strainIndexFileId = datatype.uuid();
const recoveryReportId = datatype.uuid();
const recoveryFileId = datatype.uuid();
const recoverySectorId = datatype.uuid();

const actionPlanId = datatype.uuid();
const actionPlanOriginId = datatype.uuid();
const actionPlanOriginProductionId = datatype.uuid();
const actionPlanActivityId = datatype.uuid();
const actionPlanWorkstationId = datatype.uuid();
const actionPlanLineId = datatype.uuid();
const actionPlanSectorId = datatype.uuid();
const actionPlanFileId = datatype.uuid();
const actionPlanEvaluatorId = datatype.uuid();
const actionPlanTask1Id = datatype.uuid();
const actionPlanTask2Id = datatype.uuid();
const actionPlanUser1Id = datatype.uuid();
const actionPlanUser2Id = datatype.uuid();
const actionPlanAttachment1Id = datatype.uuid();
const actionPlanTaskAttachment1Id = datatype.uuid();
const actionPlanHistory1Id = datatype.uuid();
const actionPlanComment1Id = datatype.uuid();
const actionPlanRebaReportId = datatype.uuid();
const actionPlanRebaFileId = datatype.uuid();
const actionPlanRebaEvaluatorId = datatype.uuid();
const customReportColumnId = datatype.uuid();
const seraColumnId = datatype.uuid();

export const mockedSandboxSendReportRequest = {
	payload: {
		report_type: 'BERA',
		id: sandboxReportId
	}
};

export const mockedSandboxSendReportResponse = {
	status: 'mockFunction',
	send: 'mockFunction'
};

export const mockedBeraJobSummaryForSandbox = {
	id: beraJobSummaryId,
	cycle_id: cycleId,
	bera_job_summary_files: [
		{
			file_id: beraFileId,
			id: beraJobSummaryFileId
		}
	],
	bera_report: [
		{
			file_id: beraReportFileId,
			id: beraReportId,
			task_id: taskId,
			evaluator_id: evaluatorId
		}
	]
};

export const mockedFileForSandbox = {
	id: fileId,
	setTask: 'mockFunction'
};

export const mockedFileForCustomReportSandbox = {
	id: customReportFileListId,
	setTask: 'mockFunction'
};

export const mockedCycleForSandbox = {
	id: cycleId,
	addTask: 'mockFunction'
};

export const mockedTaskForSandbox = {
	id: taskId
};

export const mockedEvaluatorForSandbox = {
	id: evaluatorId
};

export const mockedSectorForSandbox = {
	id: sectorId,
	name: 'Test Sector'
};

export const mockedCustomReportResultForSandbox = {
	id: customReportResultId,
	file_id: customReportFileId,
	evaluator_id: customReportEvaluatorId,
	collection_date: '2024-11-07T17:14:41.000Z'
};

export const mockedCustomReportReviewForSandbox = {
	id: customReportReviewId,
	original_custom_report_result_id: originalCustomReportResultId,
	custom_report_result_id: customReportResultId
};

export const mockedCustomReportStepKeyResultForSandbox = {
	id: customReportStepKeyResultId,
	custom_report_result_id: customReportResultId
};

export const mockedCustomReportResultActionLogForSandbox = {
	id: customReportResultActionLogId,
	custom_report_result_id: customReportResultId
};

export const mockedCustomReportStepKeyAdditionalItemResultForSandbox = {
	id: customReportStepKeyAdditionalItemResultId,
	custom_report_result_id: customReportResultId,
	custom_report_step_key_additional_item_id: customReportStepKeyAdditionalItemId
};

export const mockedCustomReportStepKeysAdditionalItemForSandbox = {
	id: customReportStepKeysAdditionalItemId
};

export const mockedCustomReportReviewsListForSandbox = [
	{
		id: customReportReviewListId,
		original_custom_report_result_id: originalCustomReportResultListId,
		custom_report_result_id: customReportResultListId
	}
];

export const mockedCustomReportResultsListForSandbox = [
	{
		id: customReportResultListId,
		file_id: customReportFileListIdForList,
		evaluator_id: customReportEvaluatorListId,
		collection_date: '2024-11-07T17:14:41.000Z'
	}
];

export const mockedCustomReportStepKeyResultsListForSandbox = [
	{
		id: customReportStepKeyResultListId,
		custom_report_result_id: customReportResultListId
	}
];

export const mockedCustomReportResultActionLogsListForSandbox = [
	{
		id: customReportResultActionLogListId,
		custom_report_result_id: customReportResultListId
	}
];

export const mockedCustomReportStepKeyAdditionalItemResultsListForSandbox = [
	{
		id: customReportStepKeyAdditionalItemResultListId,
		custom_report_result_id: customReportResultListId,
		custom_report_step_key_additional_item_id: customReportStepKeyAdditionalItemListId
	}
];

export const mockedCustomReportStepKeyAdditionalItemResultWithNullIdForSandbox = [
	{
		id: customReportStepKeyAdditionalItemResultNullId,
		custom_report_result_id: customReportResultNullListId,
		custom_report_step_key_additional_item_id: null
	}
];

export const mockedCustomReportNewCreatedDataForSandbox = {
	id: customReportNewCreatedId,
	file_id: customReportNewFileId,
	evaluator_id: customReportNewEvaluatorId
};

export const mockedSandboxServiceParams = {
	report_type: 'BERA',
	id: testId
};

export const mockedSandboxServiceParamsCustomReport = {
	report_type: 'CUSTOM_REPORT',
	id: customReportServiceId
};

export const mockedSandboxStrategy = {
	sendReport: () => {}
};

export const mockedTransaction = {
	commit: jest.fn(),
	rollback: jest.fn()
};

// Action Plan Fixtures
export const mockedActionPlanV2ForSandbox = {
	id: actionPlanId,
	action_plan_origin_id: actionPlanOriginId,
	activity_id: actionPlanActivityId,
	workstation_id: actionPlanWorkstationId,
	file_id: actionPlanFileId,
	name: faker.lorem.words(3),
	title: faker.lorem.sentence(),
	status: faker.helpers.arrayElement(['TO DO', 'IN PROGRESS', 'DONE']),
	priority: datatype.number({ min: 1, max: 5 }),
	due_date: datatype.datetime()
};

export const mockedActionPlanOriginForSandbox = {
	id: actionPlanOriginId,
	origin_name: 'PEA',
	table_name: 'custom_reports',
	column_id: customReportColumnId
};

export const mockedActionPlanOriginProductionForSandbox = {
	id: actionPlanOriginProductionId,
	origin_name: 'SERA',
	table_name: 'production:sera_summaries',
	column_id: seraColumnId
};

export const mockedActivityForSandbox = {
	id: actionPlanActivityId,
	name: faker.lorem.words(2),
	workstation_id: actionPlanWorkstationId,
	dataValues: {
		name: faker.lorem.words(2),
		workstation_id: actionPlanWorkstationId
	}
};

export const mockedActionPlanWorkstationForSandbox = {
	id: actionPlanWorkstationId,
	name: faker.lorem.words(2),
	line_id: actionPlanLineId
};

export const mockedActionPlanLineForSandbox = {
	id: actionPlanLineId,
	name: faker.lorem.words(2),
	sector_id: actionPlanSectorId
};

export const mockedActionPlanSectorForSandbox = {
	id: actionPlanSectorId,
	name: faker.lorem.words(2)
};

export const mockedActionPlanFileForSandbox = {
	id: actionPlanFileId,
	original_name: faker.system.fileName(),
	generated_name: faker.system.fileName(),
	sector_id: actionPlanSectorId,
	workstation_id: actionPlanWorkstationId,
	size: datatype.number({ min: 1024, max: 1048576 }),
	url: faker.internet.url(),
	workstations: {
		line_id: actionPlanLineId
	}
};

export const mockedActionPlanEvaluatorForSandbox = {
	id: actionPlanEvaluatorId,
	name: datatype.string()
};

export const mockedActionPlanTasksForSandbox = [
	{
		id: actionPlanTask1Id,
		action_plan_id: actionPlanId,
		name: faker.lorem.words(2),
		user_id: actionPlanUser1Id
	},
	{
		id: actionPlanTask2Id,
		action_plan_id: actionPlanId,
		name: faker.lorem.words(2),
		user_id: actionPlanUser2Id
	}
];

export const mockedActionPlanAttachmentsForSandbox = [
	{
		id: actionPlanAttachment1Id,
		action_plan_id: actionPlanId,
		description: faker.lorem.sentence()
	}
];

export const mockedActionPlanTaskAttachmentsForSandbox = [
	{
		id: actionPlanTaskAttachment1Id,
		action_plan_task_id: actionPlanTask1Id,
		description: faker.lorem.sentence()
	}
];

export const mockedActionPlanRelatedReportsForSandbox = [];

export const mockedActionPlanHistoriesForSandbox = [
	{
		id: actionPlanHistory1Id,
		action_plan_id: actionPlanId,
		type: 'created',
		description: faker.lorem.sentence()
	}
];

export const mockedActionPlanCommentsForSandbox = [
	{
		id: actionPlanComment1Id,
		action_plan_id: actionPlanId,
		comment: faker.lorem.sentence()
	}
];

export const mockedRebaReportForActionPlanSandbox = {
	id: actionPlanRebaReportId,
	file_id: actionPlanRebaFileId,
	evaluator_id: actionPlanRebaEvaluatorId
};

export const createMockedKinebotRepository = () => ({
	db: {
		File: { findOne: jest.fn(), findOrCreate: jest.fn(), create: jest.fn() },
		Sector: { findOrCreate: jest.fn(), create: jest.fn() },
		Line: { findOrCreate: jest.fn(), create: jest.fn() },
		Workstation: { findOrCreate: jest.fn(), create: jest.fn() },
		BeraJobSummary: { findOrCreate: jest.fn(), create: jest.fn() },
		BeraReport: { findOrCreate: jest.fn(), create: jest.fn() },
		BeraJobSummaryFiles: { findOrCreate: jest.fn(), create: jest.fn() },
		Task: { findOrCreate: jest.fn(), create: jest.fn() },
		Evaluator: { findOrCreate: jest.fn(), create: jest.fn() },
		Cycle: { findOrCreate: jest.fn(), create: jest.fn() },
		CustomReportResult: { findOrCreate: jest.fn(), create: jest.fn() },
		CustomReportReview: { findOrCreate: jest.fn(), create: jest.fn() },
		CustomReportStepKeyResult: { findOrCreate: jest.fn(), create: jest.fn() },
		CustomReportResultActionLog: { findOrCreate: jest.fn(), create: jest.fn() },
		CustomReportStepKeyAdditionalItemResult: { findOrCreate: jest.fn(), create: jest.fn() },
		CustomReportStepKeysAdditionalItem: { findOrCreate: jest.fn(), create: jest.fn() },
		sequelize: { transaction: jest.fn() }
	}
});

export const createMockedSandboxRepository = () => ({
	db: {
		BeraJobSummary: {
			findByPk: jest.fn(),
			destroy: jest.fn()
		},
		BeraReport: {
			destroy: jest.fn()
		},
		BeraJobSummaryFiles: {
			destroy: jest.fn()
		},
		CustomReportReview: {
			findAll: jest.fn(),
			destroy: jest.fn()
		},
		CustomReportResult: {
			findAll: jest.fn(),
			destroy: jest.fn()
		},
		CustomReportStepKeyResult: {
			findAll: jest.fn(),
			destroy: jest.fn()
		},
		CustomReportResultActionLog: {
			findAll: jest.fn(),
			destroy: jest.fn()
		},
		CustomReportStepKeyAdditionalItemResult: {
			findAll: jest.fn(),
			destroy: jest.fn()
		},
		File: {
			findByPk: jest.fn()
		},
		Evaluator: {
			findByPk: jest.fn()
		},
		CustomReportStepKeysAdditionalItem: {
			findByPk: jest.fn()
		},
		sequelize: { transaction: jest.fn() }
	}
});

export const mockedRebaReportForSandbox = {
	id: rebaReportId,
	file_id: rebaFileId,
	trunk: datatype.number(),
	neck: datatype.number(),
	score_seconds: datatype.number(),
	left_lower_arm: datatype.number(),
	right_lower_arm: datatype.number(),
	left_upper_arm: datatype.number(),
	right_upper_arm: datatype.number(),
	left_knee: datatype.number(),
	right_knee: datatype.number(),
	left_ankle: datatype.number(),
	right_ankle: datatype.number(),
	hip: datatype.number(),
	repetition: datatype.number(),
	force: datatype.number(),
	coupling: datatype.number(),
	collection_date: datatype.datetime(),
	comment: datatype.string()
};

export const mockedNioshReportForSandbox = {
	id: nioshReportId,
	file_id: nioshFileId,
	gender: datatype.string(),
	age: datatype.string(),
	workers: datatype.number(),
	hands: datatype.number(),
	coupling: datatype.string(),
	frequency: datatype.number(),
	duration: datatype.string(),
	mass_m: datatype.number(),
	distance_dc: datatype.number(),
	dc_factor: datatype.number(),
	distance_vc: datatype.number(),
	vc_factor: datatype.number(),
	distance_h: datatype.float(),
	h_factor: datatype.number(),
	angle_a: datatype.float(),
	a_factor: datatype.float(),
	coupling_factor: datatype.float(),
	frequency_factor: datatype.number(),
	one_handed_factor: datatype.number(),
	multiple_workers_factor: datatype.number(),
	reference_weight: datatype.number(),
	recommended_weight_limit: datatype.number(),
	lifting_index: datatype.number(),
	risk: 'low',
	collection_date: datatype.datetime(),
	report_type: datatype.string(),
	comment: datatype.string()
};

export const mockedKimMhoReportForSandbox = {
	id: kimMhoReportId,
	file_id: kimMhoFileId,
	duration: datatype.number(),
	left_force_type: 'power',
	left_force_frequency: datatype.number(),
	left_force_intensity: 'light',
	right_force_type: 'power',
	right_force_frequency: datatype.number(),
	right_force_intensity: 'light',
	force_transfer: 'good',
	arm_posture: 'good',
	work_conditions: 'good',
	temporal_distribution: 'good',
	body_posture: 'good',
	risk_score: datatype.number(),
	risk_load: datatype.string(),
	collection_date: datatype.datetime(),
	comment: datatype.string()
};

export const mockedKimPpReportForSandbox = {
	id: kimPpReportId,
	file_id: kimPpFileId,
	sector_id: kimPpSectorId,
	duration: datatype.number(),
	force_frequency: datatype.number(),
	force_intensity: 'light',
	work_height: 'good',
	trunk_posture: 'good',
	work_conditions: 'good',
	temporal_distribution: 'good',
	risk_score: datatype.number(),
	risk_load: datatype.string(),
	collection_date: datatype.datetime(),
	comment: datatype.string()
};

export const mockedAngleTimeReportForSandbox = {
	id: angleTimeReportId,
	file_id: angleTimeFileId,
	collection_date: datatype.datetime(),
	total_seconds: datatype.number(),
	comment: datatype.string()
};

export const mockedBackCompressiveReportForSandbox = {
	id: backCompressiveReportId,
	file_id: backCompressiveFileId,
	collection_date: datatype.datetime(),
	comment: datatype.string()
};

export const mockedLibertyMutualReportForSandbox = {
	id: libertyMutualReportId,
	file_id: libertyMutualFileId,
	name: faker.lorem.words(3),
	collection_date: datatype.datetime(),
	comment: datatype.string(),
	report_inputs: [
		{
			id: libertyMutualInput1Id,
			liberty_mutual_report_id: libertyMutualReportId,
			task_id: libertyMutualTaskId,
			value: datatype.float()
		},
		{
			id: libertyMutualInput2Id,
			liberty_mutual_report_id: libertyMutualReportId,
			task_id: libertyMutualTaskId,
			value: datatype.float()
		}
	]
};

export const mockedSeraReportForSandbox = {
	id: seraSummaryId,
	evaluator_id: seraEvaluatorId,
	cycle_id: seraCycleId,
	collection_date: '2024-11-07T17:14:41.000Z'
};

export const mockedSeraSummaryFilesForSandbox = [
	{
		id: seraSummaryFile1Id,
		sera_summary_id: seraSummaryId,
		file_id: seraFile1Id
	}
];

export const mockedSeraSummaryReviewsForSandbox = [
	{
		id: seraSummaryReview1Id,
		sera_summary_id: seraSummaryId
	}
];

export const mockedSeraReportsForSandbox = [
	{
		id: seraReport1Id,
		sera_summary_review_id: seraSummaryReview1Id,
		evaluator_id: seraEvaluatorId,
		task_id: seraTaskId
	}
];

export const mockedSeraReviewTasksResultsForSandbox = [
	{
		id: seraReviewTasksResult1Id,
		sera_summary_review_id: seraSummaryReview1Id,
		task_id: seraTaskId
	}
];

export const mockedSeraReportUpdatedsForSandbox = [
	{
		id: seraReportUpdated1Id,
		sera_report_id: seraReport1Id
	}
];

export const mockedSeraReviewSelectorsForSandbox = [
	{
		id: seraReviewSelector1Id,
		sera_summary_review_id: seraSummaryReview1Id
	}
];

export const mockedStrainIndexReportForSandbox = {
	id: strainIndexReportId,
	file_id: strainIndexFileId,
	collection_date: datatype.datetime(),
	comment: datatype.string()
};

export const mockedRecoveryReportForSandbox = {
	id: recoveryReportId,
	file_id: recoveryFileId,
	sector_id: recoverySectorId,
	collection_date: datatype.datetime(),
	comment: datatype.string()
};
