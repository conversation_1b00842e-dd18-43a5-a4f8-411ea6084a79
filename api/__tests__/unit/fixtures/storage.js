import { jest } from '@jest/globals';
import { faker } from '@faker-js/faker';

export const mockedS3Service = {
	headObject: jest.fn().mockReturnValue({ promise: jest.fn() }),
	putObject: jest.fn().mockReturnValue({ promise: jest.fn() }),
	getObject: jest.fn().mockReturnValue({ promise: jest.fn() }),
	headBucket: jest.fn().mockReturnValue({ promise: jest.fn() }),
	listObjects: jest.fn().mockReturnValue({ promise: jest.fn() }),
	deleteObjects: jest.fn().mockReturnValue({ promise: jest.fn() }),
	deleteBucket: jest.fn().mockReturnValue({ promise: jest.fn() }),
	createBucket: jest.fn().mockReturnValue({ promise: jest.fn() }),
	getSignedUrlPromise: jest.fn(),
	getSignedUrl: jest.fn(),
	listObjectsV2: jest.fn().mockReturnValue({ promise: jest.fn() }),
	deleteObject: jest.fn().mockReturnValue({ promise: jest.fn() })
};

export const mockedExistsObjectParams = {
	Bucket: faker.lorem.word(),
	Key: faker.lorem.word()
};

export const mockedPutObjectParams = {
	Bucket: faker.lorem.word(),
	Key: faker.lorem.word(),
	Body: faker.lorem.text(),
	ContentType: 'text/plain'
};

export const mockedGetObjectParams = {
	Bucket: faker.lorem.word(),
	Key: faker.lorem.word()
};

export const mockedBucketName = faker.lorem.word();

export const mockedObjects = [{ Key: 'file1.txt' }, { Key: 'file2.txt' }];

export const mockedSignatureUploadParams = {
	fileName: faker.system.fileName(),
	contentType: 'image/jpeg',
	bucket: faker.lorem.word()
};

export const mockedSignedUrlParams = {
	Key: faker.lorem.word(),
	Bucket: faker.lorem.word(),
	Expires: 3600
};

export const mockedSignatureDownloadParams = {
	Key: faker.lorem.word(),
	Bucket: faker.lorem.word(),
	Expires: 60,
	fileName: faker.system.fileName()
};

export const mockedListObjectsParams = {
	Bucket: faker.lorem.word(),
	Prefix: faker.lorem.word()
};

export const mockedRemoveObjectParams = {
	Bucket: faker.lorem.word(),
	Key: faker.lorem.word()
};

export const mockedOrganizationId = faker.datatype.uuid();
export const mockedCompanyId = faker.datatype.uuid();
