import { jest } from '@jest/globals';
import { faker } from '@faker-js/faker';
import { SeraReportRepository } from '@src/app/repository/sera_report.js';
import {
	mockedSumRPN,
	mockedSeraReport,
	mockedHierarchySumRPN,
	mockedIncidenceCategories
} from '@tests/unit/fixtures/index.js';

const { datatype, lorem } = faker;

describe('Sera report repository', () => {
	let repository;

	let db = {
		SeraReport: {
			create: jest.fn(),
			update: jest.fn(),
			findAll: jest.fn(),
			destroy: jest.fn(),
			findByPk: jest.fn(),
			bulkCreate: jest.fn()
		},
		sequelize: {
			query: jest.fn(),
			QueryTypes: { SELECT: 'SELECT' }
		}
	};

	beforeAll(() => {
		repository = new SeraReportRepository(db);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	describe('[create]', () => {
		it('should create sera report', async () => {
			const request = {
				data: {
					specifications: lorem.sentences(2),
					existing_prevention_measures: lorem.sentences(2),
					rpn: datatype.number(),
					evaluator_id: datatype.string(),
					severity_id: datatype.string(),
					exposure_id: datatype.string(),
					vulnerability_id: datatype.string(),
					risk_description_id: datatype.string(),
					risk_damage_id: datatype.string(),
					risk_category_id: datatype.string(),
					task_id: datatype.string(),
					sera_summary_review_id: datatype.string()
				},
				transaction: {}
			};

			jest.spyOn(db.SeraReport, 'create').mockResolvedValue(mockedSeraReport);

			const result = await repository.create(request.data, request.transaction);

			const response = expect.objectContaining({
				id: expect.any(String),
				deleted_at: null
			});

			expect(result).toStrictEqual(response);
		});
	});

	describe('[bulkCreate]', () => {
		it('should bulk create sera report', async () => {
			const request = {
				data: [
					{
						specifications: lorem.sentences(2),
						existing_prevention_measures: lorem.sentences(2),
						rpn: datatype.number(),
						evaluator_id: datatype.string(),
						severity_id: datatype.string(),
						exposure_id: datatype.string(),
						vulnerability_id: datatype.string(),
						risk_description_id: datatype.string(),
						risk_damage_id: datatype.string(),
						risk_category_id: datatype.string(),
						task_id: datatype.string(),
						sera_summary_review_id: datatype.string()
					}
				],
				transaction: {}
			};

			jest.spyOn(db.SeraReport, 'bulkCreate').mockResolvedValue(mockedSeraReport);

			const result = await repository.bulkCreate(request.data, request.transaction);

			const response = expect.objectContaining({
				id: expect.any(String),
				deleted_at: null
			});

			expect(result).toStrictEqual(response);
		});
	});

	describe('[update]', () => {
		it('should update sera report', async () => {
			const request = {
				data: {
					specifications: lorem.sentences(2),
					existing_prevention_measures: lorem.sentences(2),
					rpn: datatype.number(),
					evaluator_id: datatype.string(),
					severity_id: datatype.string(),
					exposure_id: datatype.string(),
					vulnerability_id: datatype.string(),
					risk_description_id: datatype.string(),
					risk_damage_id: datatype.string(),
					risk_category_id: datatype.string(),
					task_id: datatype.string(),
					sera_summary_review_id: datatype.string()
				},
				params: {
					where: {
						id: datatype.uuid()
					},
					transaction: {}
				}
			};

			jest.spyOn(db.SeraReport, 'update').mockResolvedValue(mockedSeraReport);

			const result = await repository.update(request.data, request.transaction);

			const response = expect.objectContaining({
				id: expect.any(String),
				deleted_at: null
			});

			expect(result).toStrictEqual(response);
		});
	});

	describe('[findByPk]', () => {
		it('should find by sera report id', async () => {
			const request = {
				id: datatype.uuid()
			};

			jest.spyOn(db.SeraReport, 'findByPk').mockResolvedValue(mockedSeraReport);

			const result = await repository.findByPk(request.id);

			const response = expect.objectContaining({
				specifications: expect.any(String),
				existing_prevention_measures: expect.any(String),
				rpn: expect.any(Number),
				evaluator_id: expect.any(String),
				severity_id: expect.any(String),
				exposure_id: expect.any(String),
				vulnerability_id: expect.any(String),
				risk_description_id: expect.any(String),
				risk_damage_id: expect.any(String),
				risk_category_id: expect.any(String),
				task_id: expect.any(String),
				sera_summary_review_id: expect.any(String),
				deleted_at: null
			});

			expect(result).toStrictEqual(response);
		});
	});

	describe('[findAll]', () => {
		it('should find by sera report id', async () => {
			const request = {};

			jest.spyOn(db.SeraReport, 'findAll').mockResolvedValue([mockedSeraReport]);

			const result = await repository.findAll(request);

			const response = expect.arrayContaining([
				expect.objectContaining({
					specifications: expect.any(String),
					existing_prevention_measures: expect.any(String),
					rpn: expect.any(Number),
					evaluator_id: expect.any(String),
					severity_id: expect.any(String),
					exposure_id: expect.any(String),
					vulnerability_id: expect.any(String),
					risk_description_id: expect.any(String),
					risk_damage_id: expect.any(String),
					risk_category_id: expect.any(String),
					task_id: expect.any(String),
					sera_summary_review_id: expect.any(String),
					deleted_at: null
				})
			]);

			expect(result).toStrictEqual(response);
		});
	});

	describe('[delete]', () => {
		it('should delete sera report', async () => {
			const request = {
				data: {
					id: datatype.uuid()
				},
				transaction: {}
			};

			jest.spyOn(db.SeraReport, 'destroy').mockResolvedValue([]);

			const result = await repository.delete(request.data, request.transaction);

			const response = [];

			expect(result).toStrictEqual(response);
		});
	});

	describe('[findAllByForeignKey]', () => {
		it('should find all sera reports', async () => {
			const request = {
				where: {
					task_id: datatype.uuid()
				}
			};

			jest.spyOn(db.SeraReport, 'findAll').mockResolvedValue([mockedSeraReport]);

			const result = await repository.findAllByForeignKey(request);

			const response = expect.arrayContaining([
				expect.objectContaining({
					id: expect.any(String)
				})
			]);

			expect(result).toStrictEqual(response);
		});
	});

	describe('[sumRPN]', () => {
		const payload = {
			companies_ids: [datatype.uuid()]
		};

		it('should return array containing rpn sum and error null', async () => {
			jest.spyOn(db.sequelize, 'query').mockResolvedValue(mockedSumRPN);
			const result = await repository.sumRPN(payload);
			expect(result).toStrictEqual([mockedSumRPN, null]);
		});

		it('should verify called properties', async () => {
			jest.spyOn(db.sequelize, 'query').mockResolvedValue(mockedSumRPN);
			await repository.sumRPN(payload);
			expect(db.sequelize.query).toHaveBeenCalledWith(
				expect.stringContaining('COALESCE(SUM(sera.sum_rpn), 0) as sum_rpn'),
				{
					plain: true,
					replacements: payload,
					type: 'SELECT'
				}
			);
		});

		it('should return array with data null and error', async () => {
			jest.spyOn(db.sequelize, 'query').mockRejectedValue(new Error('SequelizeDatabaseError'));
			const result = await repository.sumRPN(payload);
			expect(result).toStrictEqual([null, new Error('SequelizeDatabaseError')]);
		});
	});

	describe('[hierarchySumRPN]', () => {
		const payload = {
			companies_ids: [datatype.uuid()],
			limit: 10,
			offset: 1
		};

		it('should return array containing rpn sum and error null', async () => {
			jest.spyOn(db.sequelize, 'query').mockResolvedValue(mockedHierarchySumRPN);
			const result = await repository.hierarchySumRPN(payload);
			expect(result).toStrictEqual([mockedHierarchySumRPN, null]);
		});

		it('should verify called properties', async () => {
			jest.spyOn(db.sequelize, 'query').mockResolvedValue(mockedHierarchySumRPN);
			await repository.hierarchySumRPN(payload);
			expect(db.sequelize.query).toHaveBeenCalledWith(
				expect.stringContaining('COALESCE(SUM(sera.sum_rpn), 0) as total_rpn'),
				{
					replacements: {
						...payload,
						limit: 10,
						offset: 0
					},
					type: 'SELECT'
				}
			);
		});

		it('should return array with data null and error', async () => {
			jest.spyOn(db.sequelize, 'query').mockRejectedValue(new Error('SequelizeDatabaseError'));
			const result = await repository.hierarchySumRPN(payload);
			expect(result).toStrictEqual([null, new Error('SequelizeDatabaseError')]);
		});
	});

	describe('[incidenceCategories]', () => {
		const payload = {
			companies_ids: [datatype.uuid()]
		};

		it('should return array containing rpn sum and error null', async () => {
			jest.spyOn(db.sequelize, 'query').mockResolvedValue(mockedIncidenceCategories);
			const result = await repository.incidenceCategories(payload);
			expect(result).toStrictEqual([mockedIncidenceCategories, null]);
		});

		it('should verify called properties', async () => {
			jest.spyOn(db.sequelize, 'query').mockResolvedValue(mockedIncidenceCategories);
			await repository.incidenceCategories(payload);
			expect(db.sequelize.query).toHaveBeenCalledWith(
				expect.stringContaining('COUNT(sera_report.risk_category_id) as count'),
				{
					replacements: payload,
					type: 'SELECT'
				}
			);
		});

		it('should return array with data null and error', async () => {
			jest.spyOn(db.sequelize, 'query').mockRejectedValue(new Error('SequelizeDatabaseError'));
			const result = await repository.incidenceCategories(payload);
			expect(result).toStrictEqual([null, new Error('SequelizeDatabaseError')]);
		});
	});

	describe('[getHierarchyFields]', () => {
		let join;
		let fields;

		it('should return hierarchy fields for workstation_id', () => {
			const parameters = {
				workstation_id: datatype.uuid()
			};
			fields = 'tasks.name, tasks.id';
			join = 'JOIN tasks ON tasks.id = max_review.task_id';
			const result = repository.getHierarchyFields(parameters);
			expect(result).toStrictEqual({ fields, join });
		});

		it('should return hierarchy fields for line_id', () => {
			const parameters = {
				line_id: datatype.uuid()
			};
			fields = 'workstations.name, workstations.id';
			join = 'JOIN workstations ON workstations.id = max_review.workstation_id';
			const result = repository.getHierarchyFields(parameters);
			expect(result).toStrictEqual({ fields, join });
		});

		it('should return hierarchy fields for sector_id', () => {
			const parameters = {
				sector_id: datatype.uuid()
			};
			fields = 'lines.name, lines.id';
			join = 'JOIN lines ON lines.id = max_review.line_id';
			const result = repository.getHierarchyFields(parameters);
			expect(result).toStrictEqual({ fields, join });
		});

		it('should return hierarchy fields for company_id', () => {
			const parameters = {
				company_id: datatype.uuid()
			};
			fields = 'sectors.name, sectors.id';
			join = 'JOIN sectors ON sectors.id = max_review.sector_id';
			const result = repository.getHierarchyFields(parameters);
			expect(result).toStrictEqual({ fields, join });
		});

		it('should return hierarchy fields for organization_id', () => {
			const parameters = {
				organization_id: datatype.uuid()
			};
			fields = 'companies.name, companies.id';
			join = 'JOIN companies ON companies.id = max_review.company_id';
			const result = repository.getHierarchyFields(parameters);
			expect(result).toStrictEqual({ fields, join });
		});
	});

	describe('[getQueryLastReviews]', () => {
		const payload = {
			start_date: datatype.datetime(),
			end_date: datatype.datetime(),
			organization_id: datatype.uuid(),
			workstation_id: datatype.uuid(),
			company_id: datatype.uuid(),
			sector_id: datatype.uuid(),
			line_id: datatype.uuid()
		};

		it('should return query with all filters and full hierarchy', () => {
			const parameters = {
				...payload,
				full_hierarchy: true
			};
			const response = `
				SELECT
					sera_summary_id,
					MAX(review) as last_review ,
					t.id as task_id,
					f.organization_id,
					c.id as company_id,
					f.sector_id,
					w.line_id,
					f.workstation_id
				FROM
					sera_summary_reviews
					INNER JOIN sera_summaries summary ON summary.id = sera_summary_reviews.sera_summary_id and summary.deleted_at IS NULL
  					INNER JOIN sera_review_tasks_results tr ON tr.sera_summary_review_id = sera_summary_reviews.id
  					INNER JOIN tasks t ON t.id = tr.task_id
  					INNER JOIN tasks_files tf ON tf.task_id = t.id
  					INNER JOIN files f ON tf.file_id = f.id AND f.is_active = 1
					INNER JOIN workstations w ON w.id = f.workstation_id  AND w.deleted_at IS NULL
					INNER JOIN lines AS l ON l.id = w.line_id AND l.deleted_at IS NULL
					INNER JOIN sectors s ON s.id = l.sector_id AND s.is_active = 1
					INNER JOIN companies c ON c.id = s.company_id AND c.is_active = 1
				WHERE
					sera_summary_reviews.deleted_at IS NULL
					AND DATE(sera_summary_reviews.created_at) BETWEEN DATE(:start_date) AND DATE(:end_date)
					AND t.company_id = :company_id
					AND f.organization_id = :organization_id
					AND c.id = :company_id
					AND w.line_id = :line_id
					AND s.id = :sector_id
					AND f.workstation_id = :workstation_id
				GROUP BY sera_summary_id
			`
				.replace(/\s+/g, ' ')
				.trim();
			const result = repository.getQueryLastReviews(parameters).replace(/\s+/g, ' ').trim();
			expect(result).toStrictEqual(response);
		});

		it('should return query with all filters without full hierarchy', () => {
			const response = `
				SELECT
					sera_summary_id,
					MAX(review) as last_review
				FROM
					sera_summary_reviews
					INNER JOIN sera_summaries summary ON summary.id = sera_summary_reviews.sera_summary_id and summary.deleted_at IS NULL
  					INNER JOIN sera_review_tasks_results tr ON tr.sera_summary_review_id = sera_summary_reviews.id
  					INNER JOIN tasks t ON t.id = tr.task_id
  					INNER JOIN tasks_files tf ON tf.task_id = t.id
  					INNER JOIN files f ON tf.file_id = f.id AND f.is_active = 1
					INNER JOIN workstations w ON w.id = f.workstation_id  AND w.deleted_at IS NULL
					INNER JOIN lines AS l ON l.id = w.line_id AND l.deleted_at IS NULL
					INNER JOIN sectors s ON s.id = l.sector_id AND s.is_active = 1
					INNER JOIN companies c ON c.id = s.company_id AND c.is_active = 1
				WHERE
					sera_summary_reviews.deleted_at IS NULL
					AND DATE(sera_summary_reviews.created_at) BETWEEN DATE(:start_date) AND DATE(:end_date)
					AND t.company_id = :company_id
					AND f.organization_id = :organization_id
					AND c.id = :company_id
					AND w.line_id = :line_id
					AND s.id = :sector_id
					AND f.workstation_id = :workstation_id
				GROUP BY sera_summary_id
			`
				.replace(/\s+/g, ' ')
				.trim();
			const result = repository.getQueryLastReviews(payload).replace(/\s+/g, ' ').trim();
			expect(result).toStrictEqual(response);
		});

		it('should return query with all companies id only', () => {
			const parameters = {
				companies_ids: [datatype.uuid()]
			};
			const response = `
				SELECT
					sera_summary_id,
					MAX(review) as last_review
				FROM
					sera_summary_reviews
					INNER JOIN sera_summaries summary ON summary.id = sera_summary_reviews.sera_summary_id and summary.deleted_at IS NULL
  					INNER JOIN sera_review_tasks_results tr ON tr.sera_summary_review_id = sera_summary_reviews.id
  					INNER JOIN tasks t ON t.id = tr.task_id
  					INNER JOIN tasks_files tf ON tf.task_id = t.id
  					INNER JOIN files f ON tf.file_id = f.id AND f.is_active = 1
					INNER JOIN workstations w ON w.id = f.workstation_id  AND w.deleted_at IS NULL
					INNER JOIN lines AS l ON l.id = w.line_id AND l.deleted_at IS NULL
					INNER JOIN sectors s ON s.id = l.sector_id AND s.is_active = 1
					INNER JOIN companies c ON c.id = s.company_id AND c.is_active = 1
				WHERE
					sera_summary_reviews.deleted_at IS NULL
					AND t.company_id IN(:companies_ids)
					AND c.id IN(:companies_ids)
				GROUP BY sera_summary_id
			`
				.replace(/\s+/g, ' ')
				.trim();
			const result = repository.getQueryLastReviews(parameters).replace(/\s+/g, ' ').trim();
			expect(result).toStrictEqual(response);
		});
	});
});
