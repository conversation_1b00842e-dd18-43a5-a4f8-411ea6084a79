import { jest } from '@jest/globals';
import { faker } from '@faker-js/faker';
import { StrainIndexReportRepository } from '@src/app/repository/strain_index.js';
import { mockedStrainIndexReportGreatestScoreRsi } from '../fixtures/strain_index.js';

const { datatype } = faker;

describe('Strain Index report repository', () => {
	let repository;

	let db = {
		sequelize: {
			query: jest.fn(),
			QueryTypes: { SELECT: 'SELECT' }
		}
	};

	beforeAll(() => {
		repository = new StrainIndexReportRepository(db);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	describe('[getGreatestScoreRsiFilterParams]', () => {
		const organization_id = datatype.uuid();
		const company_id = datatype.uuid();
		const companies_ids = [datatype.uuid()];
		const sector_id = datatype.uuid();
		const line_id = datatype.uuid();
		const workstation_id = datatype.uuid();
		const start_date = datatype.datetime();
		const end_date = datatype.datetime();

		let parameters = {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date,
			companies_ids
		};

		it('should return correct filters when all params are provided', () => {
			const expectedFilters = {
				organization_id,
				company_id,
				sector_id,
				line_id,
				workstation_id,
				start_date,
				end_date,
				companies_ids
			};

			const result = repository.getGreatestScoreRsiFilterParams(parameters);
			expect(result).toStrictEqual(expectedFilters);
		});

		it('should return others filters when optional sector_id are missing', () => {
			const expectedFilters = {
				organization_id,
				company_id,
				line_id,
				workstation_id,
				start_date,
				end_date
			};

			parameters = {
				organization_id,
				company_id,
				line_id,
				workstation_id,
				start_date,
				end_date
			};

			const result = repository.getGreatestScoreRsiFilterParams(parameters);
			expect(result).toStrictEqual(expectedFilters);
		});

		it('should return others filters when optional line_id are missing', () => {
			const expectedFilters = {
				organization_id,
				company_id,
				sector_id,
				workstation_id,
				start_date,
				end_date
			};

			parameters = {
				organization_id,
				company_id,
				sector_id,
				workstation_id,
				start_date,
				end_date
			};

			const result = repository.getGreatestScoreRsiFilterParams(parameters);
			expect(result).toStrictEqual(expectedFilters);
		});

		it('should return others filters when optional workstation_id are missing', () => {
			const expectedFilters = {
				organization_id,
				company_id,
				sector_id,
				line_id,
				start_date,
				end_date
			};

			parameters = {
				organization_id,
				company_id,
				sector_id,
				line_id,
				start_date,
				end_date
			};

			const result = repository.getGreatestScoreRsiFilterParams(parameters);
			expect(result).toStrictEqual(expectedFilters);
		});

		it('should return others filters when optional start_date and end_date are missing', () => {
			const expectedFilters = {
				organization_id,
				company_id,
				sector_id,
				line_id,
				workstation_id
			};

			parameters = {
				organization_id,
				company_id,
				sector_id,
				line_id,
				workstation_id
			};

			const result = repository.getGreatestScoreRsiFilterParams(parameters);
			expect(result).toStrictEqual(expectedFilters);
		});

		it('should return only required filters when optional params are missing', () => {
			const expectedFilters = { organization_id, company_id };
			parameters = { organization_id, company_id };
			const result = repository.getGreatestScoreRsiFilterParams(parameters);
			expect(result).toStrictEqual(expectedFilters);
		});
	});

	describe('[getGreatestScoreRsiFilterQuery]', () => {
		const organization_id = datatype.uuid();
		const company_id = datatype.uuid();
		const sector_id = datatype.uuid();
		const line_id = datatype.uuid();
		const workstation_id = datatype.uuid();
		const start_date = datatype.datetime();
		const end_date = datatype.datetime();

		it('should return correct query when all params are provided', () => {
			const params = {
				organization_id,
				company_id,
				sector_id,
				line_id,
				workstation_id,
				start_date,
				end_date
			};

			const expectedQuery = `
				SELECT
					COUNT(CASE WHEN greatest_score <= 10 THEN 1 END) AS less_or_equal_than_ten,
					COUNT(CASE WHEN greatest_score > 10 THEN 1 END) AS greater_than_ten
				FROM (
					SELECT
						report.id,
						COALESCE(GREATEST(MAX(report.score_left_rsi), MAX(report.score_right_rsi)), 0) AS greatest_score
					FROM strain_index_reports AS report
						INNER JOIN files AS file ON file.id = report.file_id
						INNER JOIN workstations AS workstation ON workstation.id = file.workstation_id
						INNER JOIN lines AS line ON line.id = workstation.line_id
						INNER JOIN sectors AS sector ON sector.id = line.sector_id
					WHERE
						report.is_active = 1
						AND file.organization_id = :organization_id
						AND file.is_active = 1
						AND sector.is_active = 1
						AND line.deleted_at IS NULL
						AND workstation.deleted_at IS NULL
						AND file.company_id = :company_id
						AND workstation.id = :workstation_id
						AND file.id = :sector_id
						AND line.id = :line_id
						AND DATE(report.created_at) BETWEEN DATE (:start_date) AND DATE(:end_date)
					GROUP BY report.id
				) AS rsi_risk
			`;

			const result = repository.getGreatestScoreRsiFilterQuery(params);
			expect(result.replace(/\s+/g, ' ').trim()).toBe(expectedQuery.replace(/\s+/g, ' ').trim());
		});

		it('should return query with only required conditions when optional params are missing', () => {
			const params = {
				sector_id: null,
				line_id: null,
				workstation_id: null,
				start_date: null,
				end_date: null,
				companies_ids: [datatype.uuid()]
			};

			const expectedQuery = `
				SELECT
					COUNT(CASE WHEN greatest_score <= 10 THEN 1 END) AS less_or_equal_than_ten,
					COUNT(CASE WHEN greatest_score > 10 THEN 1 END) AS greater_than_ten
				FROM (
					SELECT report.id, COALESCE(GREATEST(MAX(report.score_left_rsi), MAX(report.score_right_rsi)), 0) AS greatest_score
					FROM strain_index_reports AS report
						INNER JOIN files AS file ON file.id = report.file_id
						INNER JOIN workstations AS workstation ON workstation.id = file.workstation_id
						INNER JOIN lines AS line ON line.id = workstation.line_id
						INNER JOIN sectors AS sector ON sector.id = line.sector_id
					WHERE report.is_active = 1
						AND file.organization_id = :organization_id
						AND file.is_active = 1
						AND sector.is_active = 1
						AND line.deleted_at IS NULL
						AND workstation.deleted_at IS NULL
						AND file.company_id IN(:companies_ids)
					GROUP BY report.id
				) AS rsi_risk
			`;

			const result = repository.getGreatestScoreRsiFilterQuery(params);
			expect(result.replace(/\s+/g, ' ').trim()).toBe(expectedQuery.replace(/\s+/g, ' ').trim());
		});
	});

	describe('[getGreatestScoreRsi]', () => {
		const organization_id = datatype.uuid();
		const company_id = datatype.uuid();
		const sector_id = datatype.uuid();
		const line_id = datatype.uuid();
		const workstation_id = datatype.uuid();
		const start_date = datatype.datetime();
		const end_date = datatype.datetime();

		const params = {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date
		};

		it('should certificate the call method with parameters correct', async () => {
			const config = {
				plain: true,
				replacements: params,
				type: db.sequelize.QueryTypes.SELECT
			};

			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(null);
			await repository.getGreatestScoreRsi(params);
			expect(db.sequelize.query).toHaveBeenCalledWith(expect.stringContaining('GREATEST'), config);
		});

		it('should return the greatest score rsi value', async () => {
			const mockExpect = [mockedStrainIndexReportGreatestScoreRsi, null];
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(mockedStrainIndexReportGreatestScoreRsi);
			const result = await repository.getGreatestScoreRsi(params);
			expect(result).toStrictEqual(mockExpect);
		});

		it('should return error if the database found some error', async () => {
			const throwError = new Error('StackOverflowError');
			const expected = [null, throwError];
			jest.spyOn(db.sequelize, 'query').mockRejectedValueOnce(throwError);
			const result = await repository.getGreatestScoreRsi(params);
			expect(result).toStrictEqual(expected);
		});
	});
});
