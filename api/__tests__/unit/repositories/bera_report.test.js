import { jest } from '@jest/globals';
import { faker } from '@faker-js/faker';
import { BeraReportRepository } from '@src/app/repository/bera_report.js';
import { DASHBOARD_GRANULARITY_VALUES } from '@src/app/utils/constants.js';
import {
	mockedSumRPN,
	mockedBeraReport,
	mockedHierarchySumRPN,
	mockedCommonDashboardPayloadRequest
} from '@tests/unit/fixtures/index.js';

const { datatype } = faker;

describe('[BeraReport] - Repository', () => {
	let repository;

	let db = {
		BeraReport: {
			create: jest.fn(),
			update: jest.fn(),
			destroy: jest.fn(),
			findOne: jest.fn(),
			findAll: jest.fn(),
			findByPk: jest.fn()
		},
		sequelize: {
			query: jest.fn(),
			QueryTypes: {
				SELECT: 'SELECT'
			}
		}
	};

	beforeAll(() => {
		repository = new BeraReportRepository(db);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	describe('[create]', () => {
		it('should create bera bera report', async () => {
			const request = {
				data: {
					operator_evaluated: datatype.uuid(),
					work_center: datatype.string(),
					comment: datatype.string(),
					evaluator_id: datatype.uuid(),
					task_time: datatype.number(),
					task_time_format: datatype.string(),
					has_known_injury: datatype.boolean(),
					collection_date: new Date(),
					severity: datatype.number(),
					exposure: datatype.number(),
					vulnerability: datatype.number(),
					rpn: datatype.number(),
					task_id: datatype.uuid(),
					file_id: datatype.uuid(),
					workstation_id: datatype.uuid(),
					bera_job_summary_id: datatype.uuid()
				},
				transaction: {}
			};

			jest.spyOn(db.BeraReport, 'create').mockResolvedValue(mockedBeraReport);

			const result = await repository.create(request.data, request.transaction);

			const response = expect.objectContaining({
				id: expect.any(String),
				operator_evaluated: expect.any(String),
				work_center: expect.any(String)
			});

			expect(result).toStrictEqual(response);
		});
	});

	describe('[update]', () => {
		it('should update bera bera report', async () => {
			const request = {
				data: {
					operator_evaluated: datatype.uuid(),
					work_center: datatype.string(),
					comment: datatype.string(),
					evaluator_id: datatype.uuid(),
					task_time: datatype.number(),
					task_time_format: datatype.string(),
					has_known_injury: datatype.boolean(),
					collection_date: new Date(),
					severity: datatype.number(),
					exposure: datatype.number(),
					vulnerability: datatype.number(),
					rpn: datatype.number(),
					task_id: datatype.uuid(),
					file_id: datatype.uuid(),
					workstation_id: datatype.uuid(),
					bera_job_summary_id: datatype.uuid()
				},
				params: {
					where: {
						id: datatype.uuid()
					},
					transaction: {}
				}
			};

			jest.spyOn(db.BeraReport, 'update').mockResolvedValue([1]);

			const result = await repository.update(request.data, request.params);

			expect(result).toStrictEqual([1]);
		});
	});

	describe('[findByPk]', () => {
		it('should find bera report by id', async () => {
			const request = {
				id: datatype.string(),
				params: {}
			};

			jest.spyOn(db.BeraReport, 'findByPk').mockResolvedValueOnce(mockedBeraReport);

			const result = await repository.findByPk(request.id, request.params);

			const response = expect.objectContaining({
				id: expect.any(String),
				operator_evaluated: expect.any(String),
				work_center: expect.any(String)
			});

			expect(result).toStrictEqual(response);
		});
	});

	describe('[delete]', () => {
		it('should delete bera bera report', async () => {
			const request = {
				data: {
					id: datatype.uuid()
				},
				transaction: {}
			};

			jest.spyOn(db.BeraReport, 'destroy').mockResolvedValue([1]);

			const result = await repository.delete(request.data, request.transaction);

			expect(result).toStrictEqual([1]);
		});
	});

	describe('[findOne]', () => {
		it('should find bera report for specific task id', async () => {
			const request = {
				where: {
					task_id: datatype.uuid()
				}
			};

			jest.spyOn(db.BeraReport, 'findOne').mockResolvedValueOnce(mockedBeraReport);

			const result = await repository.findOne(request);

			const response = expect.objectContaining({
				id: expect.any(String),
				severity: expect.any(Number),
				exposure: expect.any(Number),
				vulnerability: expect.any(Number),
				rpn: expect.any(Number),
				task_id: expect.any(String)
			});

			expect(result).toStrictEqual(response);
		});
	});

	describe('[findAllByForeignKey]', () => {
		it('should find all bera report for specific step id', async () => {
			const request = {
				where: {
					bera_job_summary_id: datatype.uuid()
				}
			};

			jest.spyOn(db.BeraReport, 'findAll').mockResolvedValueOnce([mockedBeraReport]);

			const result = await repository.findAllByForeignKey(request);

			const response = expect.arrayContaining([mockedBeraReport]);

			expect(result).toHaveLength(1);
			expect(result).toStrictEqual(response);
		});
	});

	describe('[genderNeutralCount]', () => {
		const payload = {};
		const mockGenderNeutralCount = {
			month: datatype.number(),
			year: datatype.number(),
			count: datatype.number()
		};

		it('should verify repository invoked params for gender neutral false', () => {
			const expectedCalledParams = {
				replacements: payload,
				type: expect.any(String)
			};
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(mockGenderNeutralCount);
			repository.genderNeutralCount(payload);
			expect(db.sequelize.query).toHaveBeenCalledWith(
				expect.stringContaining('bera.id IN'),
				expectedCalledParams
			);
		});

		it('should verify repository invoked params for gender neutral true', () => {
			const parameters = {
				is_gender_neutral: true
			};
			const expectedCalledParams = {
				replacements: parameters,
				type: expect.any(String)
			};
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(mockGenderNeutralCount);
			repository.genderNeutralCount(parameters);
			expect(db.sequelize.query).toHaveBeenCalledWith(
				expect.stringContaining('bera.id NOT IN'),
				expectedCalledParams
			);
		});

		it('should return array with items and error null if success', async () => {
			const response = [mockGenderNeutralCount, null];
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(response[0]);
			const result = await repository.genderNeutralCount(payload);
			expect(result).toStrictEqual(response);
		});

		it('should return array with items null and error', async () => {
			const response = [null, new Error()];
			jest.spyOn(db.sequelize, 'query').mockRejectedValue(new Error());
			const result = await repository.genderNeutralCount(payload);
			expect(result).toStrictEqual(response);
		});
	});

	describe('[sumRPN]', () => {
		const payload = {};

		it('should verify repository invoked params', () => {
			const expectedCalledParams = {
				plain: true,
				replacements: payload,
				type: expect.any(String)
			};
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(mockedSumRPN);
			repository.sumRPN(payload);
			expect(db.sequelize.query).toHaveBeenCalledWith(expect.any(String), expectedCalledParams);
		});

		it('should return array with items and error null if success', async () => {
			const response = [mockedSumRPN, null];
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(response[0]);
			const result = await repository.sumRPN(payload);
			expect(result).toStrictEqual(response);
		});

		it('should return array with items null and error', async () => {
			const response = [null, new Error()];
			jest.spyOn(db.sequelize, 'query').mockRejectedValue(new Error());
			const result = await repository.sumRPN(payload);
			expect(result).toStrictEqual(response);
		});
	});

	describe('[hierarchySumRPN]', () => {
		const payload = { limit: 10, offset: 1 };

		it('should verify repository invoked params', () => {
			const expectedCalledParams = {
				type: expect.any(String),
				replacements: { limit: 10, offset: 0 }
			};
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce([]);
			repository.hierarchySumRPN(payload);
			expect(db.sequelize.query).toHaveBeenCalledWith(expect.any(String), expectedCalledParams);
		});

		it('should return array with items and error null if success', async () => {
			const response = [mockedHierarchySumRPN, null];
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(response[0]);
			const result = await repository.hierarchySumRPN(payload);
			expect(result).toStrictEqual(response);
		});

		it('should return array with items null and error', async () => {
			const response = [null, new Error()];
			jest.spyOn(db.sequelize, 'query').mockRejectedValue(new Error());
			const result = await repository.hierarchySumRPN(payload);
			expect(result).toStrictEqual(response);
		});
	});

	describe('[sixTooSumRPN]', () => {
		const payload = {};

		it('should verify repository invoked params', () => {
			const expectedCalledParams = { replacements: payload, type: expect.any(String) };
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(mockedSumRPN);
			repository.sixTooSumRPN(payload);
			expect(db.sequelize.query).toHaveBeenCalledWith(expect.any(String), expectedCalledParams);
		});

		it('should return array with items and error null if success', async () => {
			const response = [mockedSumRPN, null];
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(response[0]);
			const result = await repository.sixTooSumRPN(payload);
			expect(result).toStrictEqual(response);
		});

		it('should return array with items null and error', async () => {
			const response = [null, new Error()];
			jest.spyOn(db.sequelize, 'query').mockRejectedValue(new Error());
			const result = await repository.sixTooSumRPN(payload);
			expect(result).toStrictEqual(response);
		});
	});

	describe('[getHierarchySumRPNQuery]', () => {
		const payload = {
			line_id: datatype.uuid(),
			sector_id: datatype.uuid(),
			company_id: datatype.uuid(),
			workstation_id: datatype.uuid(),
			organization_id: datatype.uuid()
		};

		it('should return query for tasks hierarchy', () => {
			const response = expect.stringContaining('INNER JOIN bera_last_reviews blr ON tasks.id = blr.task_id');
			const result = repository.getHierarchySumRPNQuery(payload);
			expect(result).toStrictEqual(response);
		});

		it('should return query for workstation hierarchy', () => {
			const parameters = { ...payload, workstation_id: null };
			const response = expect.stringContaining(
				'INNER JOIN bera_last_reviews blr ON workstations.id = blr.workstation_id'
			);
			const result = repository.getHierarchySumRPNQuery(parameters);
			expect(result).toStrictEqual(response);
		});

		it('should return query for workstation line', () => {
			const parameters = {
				...payload,
				line_id: null,
				workstation_id: null
			};
			const response = expect.stringContaining('INNER JOIN bera_last_reviews blr ON lines.id = blr.line_id');
			const result = repository.getHierarchySumRPNQuery(parameters);
			expect(result).toStrictEqual(response);
		});

		it('should return query for workstation sector', () => {
			const parameters = {
				...payload,
				sector_id: null,
				line_id: null,
				workstation_id: null
			};
			const response = expect.stringContaining('INNER JOIN bera_last_reviews blr ON sectors.id = blr.sector_id');
			const result = repository.getHierarchySumRPNQuery(parameters);
			expect(result).toStrictEqual(response);
		});

		it('should return query for workstation company', () => {
			const parameters = {
				...payload,
				line_id: null,
				sector_id: null,
				company_id: null,
				workstation_id: null
			};
			const response = expect.stringContaining(
				'INNER JOIN bera_last_reviews blr ON companies.id = blr.company_id'
			);
			const result = repository.getHierarchySumRPNQuery(parameters);
			expect(result).toStrictEqual(response);
		});
	});

	describe('[getQueryLastReviews]', () => {
		const payload = {
			...mockedCommonDashboardPayloadRequest,
			select_with_full_hierarchy: true
		};

		it('should return query with all filters and full hierarchy', () => {
			const expectedQuery = `
				SELECT
					bera_reports.bera_job_summary_id,
					bera_reports.rpn,
    				bera_reports.task_id,
    				files.organization_id,
    				c.id as company_id,
    				files.sector_id,
    				workstations.line_id,
    				files.workstation_id,
					MAX(bera_reports.created_at) AS last_created_at
				FROM
					bera_reports
					INNER JOIN files ON files.id = bera_reports.file_id
					INNER JOIN workstations ON workstations.id = files.workstation_id  AND workstations.deleted_at IS NULL
					INNER JOIN lines AS l ON l.id = workstations.line_id AND l.deleted_at IS NULL
					INNER JOIN sectors s ON s.id = l.sector_id AND s.is_active = 1
					INNER JOIN bera_job_summaries bjs ON bjs.id = bera_reports.bera_job_summary_id
					INNER JOIN companies c ON c.id = s.company_id AND c.is_active = 1
    			WHERE
    				files.is_active = 1
    				AND bera_reports.deleted_at IS NULL
    				AND bera_reports.consolidated = 1
    				AND DATE(bera_reports.created_at) BETWEEN DATE(:start_date) AND DATE(:end_date)
    				AND files.organization_id = :organization_id
    				AND c.id = :company_id
    				AND workstations.line_id = :line_id
    				AND s.id = :sector_id
    				AND files.workstation_id = :workstation_id
    			GROUP BY bera_reports.bera_job_summary_id
			`
				.replace(/\s+/g, ' ')
				.trim();
			const result = repository.getQueryLastReviews(payload).replace(/\s+/g, ' ').trim();
			expect(result).toStrictEqual(expectedQuery);
		});

		it('should return query without line_id filters and full hierarchy', () => {
			const parameters = {
				...payload,
				line_id: null,
				sector_id: null
			};
			const result = repository.getQueryLastReviews(parameters);
			expect(result).not.toContain('workstations.line_id = :line_id');
		});

		it('should return query without filters and without full hierarchy', () => {
			const parameters = {
				companies_ids: []
			};
			const response = `
				SELECT
					bera_reports.bera_job_summary_id,
					MAX(bera_reports.created_at) AS last_created_at
				FROM
					bera_reports
				INNER JOIN files ON files.id = bera_reports.file_id
				INNER JOIN workstations ON workstations.id = files.workstation_id AND workstations.deleted_at IS NULL
				INNER JOIN lines AS l ON l.id = workstations.line_id AND l.deleted_at IS NULL
				INNER JOIN sectors s ON s.id = l.sector_id AND s.is_active = 1
				INNER JOIN bera_job_summaries bjs ON bjs.id = bera_reports.bera_job_summary_id
				INNER JOIN companies c ON c.id = s.company_id AND c.is_active = 1
				WHERE
					files.is_active = 1
					AND bera_reports.deleted_at IS NULL
					AND bera_reports.consolidated = 1
					AND c.id IN(:companies_ids)
				GROUP BY bera_reports.bera_job_summary_id
			`
				.replace(/\s+/g, ' ')
				.trim();
			const result = repository.getQueryLastReviews(parameters).replace(/\s+/g, ' ').trim();
			expect(result).toStrictEqual(response);
		});
	});

	describe('[getPeriodGroupQuery]', () => {
		const payload = {
			year_start_month: 1
		};

		it('should return period group for day', () => {
			const parameters = {
				...payload,
				period_group: DASHBOARD_GRANULARITY_VALUES.DAY
			};
			const response = {
				fields: expect.stringContaining('DAY(bera.created_at) as day'),
				group_by: 'day, month, year',
				order_by: 'year, month, day'
			};
			const result = repository.getPeriodGroupQuery(parameters, 'day');
			expect(result).toStrictEqual(response);
		});

		it('should return period group for week', () => {
			const parameters = {
				...payload,
				period_group: DASHBOARD_GRANULARITY_VALUES.WEEK
			};
			const response = {
				group_by: 'week, month, year',
				order_by: 'year, month, week',
				fields: expect.stringContaining('WEEK(bera.created_at) as week')
			};
			const result = repository.getPeriodGroupQuery(parameters, 'week');
			expect(result).toStrictEqual(response);
		});

		it('should return period group for month', () => {
			const parameters = {
				...payload,
				period_group: DASHBOARD_GRANULARITY_VALUES.MONTH
			};
			const response = {
				group_by: 'month, year',
				order_by: 'year, month',
				fields: expect.stringContaining('MONTH(bera.created_at) as month')
			};
			const result = repository.getPeriodGroupQuery(parameters);
			expect(result).toStrictEqual(response);
		});

		it('should return period group for year', () => {
			const parameters = {
				...payload,
				period_group: DASHBOARD_GRANULARITY_VALUES.YEAR
			};
			const response = {
				group_by: 'year',
				order_by: 'year',
				fields: expect.stringContaining('YEAR(bera.created_at) as year')
			};
			const result = repository.getPeriodGroupQuery(parameters);
			expect(result).toStrictEqual(response);
		});

		it('should return period group for quarter', () => {
			const parameters = {
				...payload,
				period_group: DASHBOARD_GRANULARITY_VALUES.QUARTER
			};
			const response = {
				group_by: 'quarter, year',
				order_by: 'year, quarter',
				fields: expect.stringContaining('YEAR(bera.created_at) as year')
			};
			const result = repository.getPeriodGroupQuery(parameters);
			expect(result).toStrictEqual(response);
		});

		it('should return period group for semester', () => {
			const parameters = {
				...payload,
				period_group: DASHBOARD_GRANULARITY_VALUES.SEMESTER
			};
			const response = {
				group_by: 'semester, year',
				order_by: 'year, semester',
				fields: expect.stringContaining('YEAR(bera.created_at) as year')
			};
			const result = repository.getPeriodGroupQuery(parameters);
			expect(result).toStrictEqual(response);
		});
	});

	describe('[getYearExpression]', () => {
		let chunkQuery;

		it('should return simple year expression when year_start_month is 1', () => {
			chunkQuery = 'YEAR(bera.created_at) as year';
			const result = repository.getYearExpression(1);
			expect(result).toBe(chunkQuery);
		});

		it('should return complex year expression when year_start_month is not 1', () => {
			chunkQuery = 'IF(MONTH(bera.created_at) >= 4, 1, 0) as year';
			const result = repository.getYearExpression(4);
			expect(result).toContain(chunkQuery);
		});
	});

	describe('[getMonthExpression]', () => {
		let chunkQuery;

		it('should return simple month expression when year_start_month is 1', () => {
			chunkQuery = 'MONTH(bera.created_at) as month';
			const result = repository.getMonthExpression(1);
			expect(result).toBe(chunkQuery);
		});

		it('should return simple month expression when year_start_month is undefined', () => {
			chunkQuery = 'MONTH(bera.created_at) as month';
			const result = repository.getMonthExpression();
			expect(result).toBe(chunkQuery);
		});

		it('should return complex month expression when year_start_month is not 1', () => {
			chunkQuery = '1 + MOD(MONTH(bera.created_at) - 4 + 12, 12) as month';
			const result = repository.getMonthExpression(4);
			expect(result).toBe(chunkQuery);
		});
	});

	describe('[getQuarterExpression]', () => {
		let chunkQuery;

		it('should return quarter expression with year_start_month 1', () => {
			chunkQuery = 'CEIL((MONTH(bera.created_at) ) / 3) as quarter';
			const result = repository.getQuarterExpression(1);
			expect(result).toBe(chunkQuery);
		});

		it('should return quarter expression with year_start_month not 1', () => {
			chunkQuery = 'CEIL((1 + MOD(MONTH(bera.created_at) - 4 + 12, 12) ) / 3) as quarter';
			const result = repository.getQuarterExpression(4);
			expect(result).toBe(chunkQuery);
		});
	});

	describe('[getSemesterExpression]', () => {
		let chunkQuery;

		it('should return semester expression with year_start_month 1', () => {
			chunkQuery = 'CEIL((MONTH(bera.created_at) ) / 6) as semester';
			const result = repository.getSemesterExpression(1);
			expect(result).toBe(chunkQuery);
		});

		it('should return semester expression with year_start_month not 1', () => {
			chunkQuery = 'CEIL((1 + MOD(MONTH(bera.created_at) - 7 + 12, 12) ) / 6) as semester';
			const result = repository.getSemesterExpression(7);
			expect(result).toBe(chunkQuery);
		});
	});
});
