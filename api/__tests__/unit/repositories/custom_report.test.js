import { jest } from '@jest/globals';
import { faker } from '@faker-js/faker';
import { CustomReportRepository } from '@src/app/repository/custom_report.js';
import { GetAllBeraReportCountSQL } from '@src/app/mappers/bera_reports/GetAllBeraReportCountSQL.js';
import { GetAllSeraReportCountSQL } from '@src/app/mappers/sera_reports/GetAllSeraReportCountSQL.js';
import { mockedCustomReport, mockedFile, mockedFindAllReportTypes } from '@tests/unit/fixtures/index.js';
import { GetAllCustomReportTypesSQL } from '@src/app/mappers/custom_reports/GetAllCustomReportTypesSQL.js';

const { datatype } = faker;

describe('Custom report repository', () => {
	let customReportRepository;
	let db = {
		sequelize: {
			query: jest.fn(),
			QueryTypes: {
				SELECT: 'SELECT'
			}
		},
		Sequelize: {
			Op: {
				ne: 'NOT_EQUAL'
			}
		},
		CustomReport: {
			findAll: jest.fn(),
			findOne: jest.fn(),
			findByPk: jest.fn()
		},
		UserAccess: {
			findAll: jest.fn()
		}
	};
	beforeAll(() => {
		customReportRepository = new CustomReportRepository(db);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	describe('[findByPk]', () => {
		it('should find custom report by id', async () => {
			const request = {
				id: datatype.uuid()
			};

			jest.spyOn(db.CustomReport, 'findByPk').mockResolvedValueOnce(mockedCustomReport);

			const result = await customReportRepository.findByPk(request.id);

			const response = expect.objectContaining({
				id: expect.any(String),
				name: expect.any(String),
				description: expect.any(String),
				acronym: expect.any(String)
			});

			expect(result).toStrictEqual(response);
		});
	});

	describe('[findOne]', () => {
		it('should find custom report by id', async () => {
			const request = {
				where: {
					name: datatype.string()
				}
			};

			jest.spyOn(db.CustomReport, 'findOne').mockResolvedValueOnce(mockedCustomReport);

			const result = await customReportRepository.findOne(request);

			const response = expect.objectContaining({
				id: expect.any(String),
				name: expect.any(String),
				description: expect.any(String),
				acronym: expect.any(String)
			});

			expect(result).toStrictEqual(response);
		});
	});

	describe('[findAll]', () => {
		it('should find all custom reports', async () => {
			jest.spyOn(db.CustomReport, 'findAll').mockResolvedValueOnce([mockedCustomReport]);

			const result = await customReportRepository.findAll();

			const response = expect.arrayContaining([
				expect.objectContaining({
					id: expect.any(String),
					name: expect.any(String),
					description: expect.any(String),
					acronym: expect.any(String)
				})
			]);

			expect(result).toHaveLength(1);
			expect(result).toStrictEqual(response);
		});
	});

	describe('[findAllByForeignKey]', () => {
		it('should find all custom reports for specific foreign key', async () => {
			const request = {
				where: {
					file_id: datatype.uuid()
				}
			};

			const mockedCustomReportResponse = {
				...mockedCustomReport,
				file: mockedFile
			};

			jest.spyOn(db.CustomReport, 'findAll').mockResolvedValueOnce([mockedCustomReportResponse]);

			const result = await customReportRepository.findAllByForeignKey(request);

			const response = expect.arrayContaining([
				expect.objectContaining({
					id: expect.any(String),
					name: expect.any(String),
					description: expect.any(String),
					acronym: expect.any(String),
					file: expect.objectContaining({
						id: expect.any(String)
					})
				})
			]);

			expect(result).toHaveLength(1);
			expect(result).toStrictEqual(response);
		});
	});

	describe('[getHierarchyWorstScore]', () => {
		const params = {
			custom_report_name: datatype.string(),
			custom_report_id: datatype.uuid(),
			organization_id: datatype.uuid(),
			workstation_id: datatype.uuid(),
			company_id: datatype.uuid(),
			sector_id: datatype.uuid(),
			line_id: datatype.uuid(),
			limit: 10,
			offset: 1
		};

		const mockRows = [
			{
				score: datatype.number(),
				unit_id: datatype.uuid(),
				unit_name: datatype.string()
			}
		];

		it('should return the hierarchy with the worst score for a custom report', async () => {
			const mockedResult = {
				rows: mockRows,
				entity: 'file',
				total: 1
			};

			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(mockedResult.rows);
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce({ total: mockedResult.total });
			const result = await customReportRepository.getHierarchyWorstScore(params);
			expect(result).toEqual([{ ...mockedResult, limit: params.limit, offset: params.offset }, null]);
		});

		it('should return the hierarchy for tasks with the worst score for a custom report', async () => {
			const payload = {
				...params,
				custom_report_name: 'bera'
			};
			const mockedResult = {
				rows: mockRows,
				entity: 'task',
				total: 1
			};
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(mockedResult.rows);
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce({ total: mockedResult.total });
			const result = await customReportRepository.getHierarchyWorstScore(payload);
			expect(result).toEqual([{ ...mockedResult, limit: params.limit, offset: params.offset }, null]);
		});

		it('should return the hierarchy for workstations', async () => {
			const payload = {
				...params,
				workstation_id: null
			};
			const mockedResult = {
				rows: mockRows,
				entity: 'workstation',
				total: 1
			};
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(mockedResult.rows);
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce({ total: mockedResult.total });
			const result = await customReportRepository.getHierarchyWorstScore(payload);
			expect(result).toEqual([{ ...mockedResult, limit: params.limit, offset: params.offset }, null]);
		});

		it('should return the hierarchy for line', async () => {
			const payload = {
				...params,
				line_id: null,
				workstation_id: null
			};
			const mockedResult = {
				rows: mockRows,
				entity: 'line',
				total: 1
			};
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(mockedResult.rows);
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce({ total: mockedResult.total });
			const result = await customReportRepository.getHierarchyWorstScore(payload);
			expect(result).toEqual([{ ...mockedResult, limit: params.limit, offset: params.offset }, null]);
		});

		it('should return the hierarchy for sector', async () => {
			const payload = {
				...params,
				sector_id: null,
				line_id: null,
				workstation_id: null
			};
			const mockedResult = {
				rows: mockRows,
				entity: 'sector',
				total: 1
			};
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(mockedResult.rows);
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce({ total: mockedResult.total });
			const result = await customReportRepository.getHierarchyWorstScore(payload);
			expect(result).toEqual([{ ...mockedResult, limit: params.limit, offset: params.offset }, null]);
		});

		it('should return the hierarchy for company', async () => {
			const payload = {
				...params,
				company_id: null,
				sector_id: null,
				line_id: null,
				workstation_id: null
			};
			const mockedResult = {
				rows: mockRows,
				entity: 'company',
				total: 1
			};
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(mockedResult.rows);
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce({ total: mockedResult.total });
			const result = await customReportRepository.getHierarchyWorstScore(payload);
			expect(result).toEqual([{ ...mockedResult, limit: params.limit, offset: params.offset }, null]);
		});

		it('should call sequelize query method twice', async () => {
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce([]);
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce({ total: 0 });
			await customReportRepository.getHierarchyWorstScore(params);
			expect(db.sequelize.query).toHaveBeenCalledTimes(2);
		});

		it('should return error if sequelize query method fails', async () => {
			jest.spyOn(db.sequelize, 'query').mockRejectedValueOnce(new Error('Sequelize query failed'));
			const result = await customReportRepository.getHierarchyWorstScore(params);
			expect(result).toEqual([null, new Error('Sequelize query failed')]);
		});
	});

	describe('[getWorstScoreQuery]', () => {
		it('should return query for workstation with task table', () => {
			const params = {
				filters: { workstation_id: datatype.uuid() },
				task_table_name: datatype.string(),
				score_field: datatype.string(),
				report_joins: datatype.string()
			};
			const expected_query = `
				FROM
					tasks
			`;
			const result = customReportRepository.getWorstScoreQuery(params);
			expect(result).toContain(expected_query);
		});

		it('should return query for workstation without task table', () => {
			const params = {
				filters: { workstation_id: datatype.uuid() },
				task_table_name: null,
				score_field: datatype.string(),
				report_joins: datatype.string()
			};
			const expected_query = `
				FROM
					files
			`;
			const result = customReportRepository.getWorstScoreQuery(params);
			expect(result).toContain(expected_query);
		});

		it('should return query for line', () => {
			const params = {
				filters: { line_id: datatype.uuid() },
				task_table_name: null,
				score_field: datatype.string(),
				report_joins: datatype.string()
			};
			const expected_query = `
				FROM
					workstations
			`;
			const result = customReportRepository.getWorstScoreQuery(params);
			expect(result).toContain(expected_query);
		});

		it('should return query for sector', () => {
			const params = {
				filters: { sector_id: datatype.uuid() },
				task_table_name: null,
				score_field: datatype.string(),
				report_joins: datatype.string()
			};
			const expected_query = `
				FROM
					lines
			`;
			const result = customReportRepository.getWorstScoreQuery(params);
			expect(result).toContain(expected_query);
		});

		it('should return query for company', () => {
			const params = {
				filters: { company_id: datatype.uuid() },
				task_table_name: null,
				score_field: datatype.string(),
				report_joins: datatype.string()
			};
			const expected_query = `
				FROM
					sectors
			`;
			const result = customReportRepository.getWorstScoreQuery(params);
			expect(result).toContain(expected_query);
		});

		it('should return query for organization', () => {
			const params = {
				filters: { organization_id: datatype.uuid() },
				task_table_name: null,
				score_field: datatype.string(),
				report_joins: datatype.string()
			};
			const expected_query = `
				FROM
					companies
			`;

			const result = customReportRepository.getWorstScoreQuery(params);

			expect(result).toContain(expected_query);
		});
	});

	describe('[getWorstScoreQueryCount]', () => {
		it('should return count query for workstation with task table', () => {
			const params = {
				filters: { workstation_id: datatype.uuid() },
				task_table_name: datatype.string(),
				report_joins: datatype.string()
			};
			const expected_query = `COUNT(DISTINCT tasks.id) as total`;
			const result = customReportRepository.getWorstScoreQueryCount(params);
			expect(result).toContain(expected_query);
		});

		it('should return count query for workstation without task table', () => {
			const params = {
				filters: { workstation_id: datatype.uuid() },
				task_table_name: null,
				report_joins: datatype.string()
			};
			const expected_query = `COUNT(DISTINCT files.id) as total`;
			const result = customReportRepository.getWorstScoreQueryCount(params);
			expect(result).toContain(expected_query);
		});

		it('should return count query for line', () => {
			const params = {
				filters: { line_id: datatype.uuid() },
				task_table_name: null,
				report_joins: datatype.string()
			};
			const expected_query = `COUNT(DISTINCT workstations.id) as total`;
			const result = customReportRepository.getWorstScoreQueryCount(params);
			expect(result).toContain(expected_query);
		});

		it('should return count query for sector', () => {
			const params = {
				filters: { sector_id: datatype.uuid() },
				task_table_name: null,
				report_joins: datatype.string()
			};
			const expected_query = `COUNT(DISTINCT lines.id) as total`;
			const result = customReportRepository.getWorstScoreQueryCount(params);
			expect(result).toContain(expected_query);
		});

		it('should return count query for company', () => {
			const params = {
				filters: { company_id: datatype.uuid() },
				task_table_name: null,
				report_joins: datatype.string()
			};
			const expected_query = `COUNT(DISTINCT sectors.id) as total`;
			const result = customReportRepository.getWorstScoreQueryCount(params);
			expect(result).toContain(expected_query);
		});

		it('should return count query for organization', () => {
			const params = {
				filters: { organization_id: datatype.uuid() },
				task_table_name: null,
				report_joins: datatype.string()
			};
			const expected_query = `COUNT(DISTINCT companies.id) as total`;
			const result = customReportRepository.getWorstScoreQueryCount(params);
			expect(result).toContain(expected_query);
		});
	});

	describe('[getReportJoins]', () => {
		it('should return joins for D86 report', () => {
			const params = {
				custom_report_name: 'jds_d86',
				task_table_name: null,
				filters: {}
			};
			const expected_query = `
				LEFT JOIN (
					SELECT crr.*
					FROM custom_report_reviews crr
					INNER JOIN (
			`;
			const result = customReportRepository.getReportJoins(params);
			expect(result).toContain(expected_query);
		});

		it('should return joins for report with task table', () => {
			const params = {
				custom_report_name: datatype.string(),
				task_table_name: datatype.string(),
				filters: {}
			};
			const expected_query = `LEFT JOIN tasks_files`;
			const result = customReportRepository.getReportJoins(params);
			expect(result).toContain(expected_query);
		});

		it('should return joins for report with date filters', () => {
			const params = {
				custom_report_name: datatype.string(),
				task_table_name: null,
				filters: {
					created_at_start: datatype.datetime().toISOString(),
					created_at_end: datatype.datetime().toISOString()
				}
			};
			const expected_query = `DATE(custom_report_results.created_at) BETWEEN`;
			const result = customReportRepository.getReportJoins(params);
			expect(result).toContain(expected_query);
		});

		it('should return joins for report tasks with date filters', () => {
			const params = {
				custom_report_name: datatype.string(),
				task_table_name: 'task_table',
				filters: {
					created_at_start: datatype.datetime().toISOString(),
					created_at_end: datatype.datetime().toISOString()
				}
			};
			const expected_query = `DATE(task_table.created_at) BETWEEN`;
			const result = customReportRepository.getReportJoins(params);
			expect(result).toContain(expected_query);
		});
	});

	describe('[getReportTaskTableName]', () => {
		it('should return table name for bera', () => {
			const result = customReportRepository.getReportTaskTableName('bera');
			expect(result).toBe('bera_reports');
		});

		it('should return table name for sera', () => {
			const result = customReportRepository.getReportTaskTableName('sera');
			expect(result).toBe('sera_reports');
		});

		it('should return null for other reports', () => {
			const result = customReportRepository.getReportTaskTableName(datatype.string());
			expect(result).toBeNull();
		});
	});

	describe('[getWorstScoreField]', () => {
		const parameters = {
			task_table_name: null,
			custom_report_name: 'bera'
		};
		it('should return score field for report without task table', () => {
			const result = customReportRepository.getWorstScoreField(parameters);
			const expected_query = `MAX(custom_report_results.result) AS score`;
			expect(result).toContain(expected_query);
		});

		it('should return score field for report with task table', () => {
			const result = customReportRepository.getWorstScoreField({
				...parameters,
				task_table_name: datatype.string()
			});
			const expected_query = `WHEN EXISTS(SELECT 1 FROM`;
			expect(result).toContain(expected_query);
		});
	});

	describe('[getReportTaskExistField]', () => {
		const parameters = {
			task_table_name: 'bera_reports',
			custom_report_name: 'bera'
		};
		it('should return existence field for bera table', () => {
			const result = customReportRepository.getReportTaskExistField(parameters);
			const expected_query = `WHEN EXISTS(SELECT 1 FROM ${parameters.task_table_name} WHERE ${parameters.task_table_name}.task_id = tasks.id AND bera_reports.consolidated = 1)`;
			expect(result).toContain(expected_query);
		});

		it('should return existence field for sera table', () => {
			let payload = {
				task_table_name: 'sera_reports'
			};
			const result = customReportRepository.getReportTaskExistField(payload);
			const expected_query = `WHEN EXISTS(SELECT 1 FROM ${payload.task_table_name} WHERE ${payload.task_table_name}.task_id = tasks.id )`;
			expect(result).toContain(expected_query);
		});
	});

	describe('[findAllTypes]', () => {
		const organization_id = datatype.uuid();
		const selected_company_id = datatype.uuid();
		const sector_id = datatype.uuid();
		const line_id = datatype.uuid();
		const workstation_id = datatype.uuid();
		const start_date = datatype.datetime();
		const end_date = datatype.datetime();
		const companies_with_user_access = [selected_company_id];

		const params = {
			organization_id,
			selected_company_id,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date,
			companies_with_user_access
		};

		it('should return data and null error when query is successful', async () => {
			const beraReportCount = { total: datatype.number() };
			const seraReportCount = { total: datatype.number() };
			jest.spyOn(db.sequelize, 'query')
				.mockResolvedValueOnce(mockedFindAllReportTypes)
				.mockResolvedValueOnce([beraReportCount])
				.mockResolvedValueOnce([seraReportCount]);
			const [result, error] = await customReportRepository.findAllTypes(params);
			expect(error).toBeNull();
			expect(result).toEqual(
				expect.arrayContaining([
					expect.objectContaining({
						name: 'sera',
						total_reports: seraReportCount.total
					}),
					expect.objectContaining({
						name: 'bera',
						total_reports: beraReportCount.total
					}),
					expect.objectContaining({
						name: 'sera',
						total_reports: seraReportCount.total
					})
				])
			);
		});

		it('should return null data and error when query fails', async () => {
			jest.spyOn(db.sequelize, 'query').mockRejectedValueOnce(new Error());
			const [result, returnedError] = await customReportRepository.findAllTypes(params);
			expect(result).toBeNull();
			expect(returnedError).toBeInstanceOf(Error);
		});

		it('should call sequelize query with the correct parameters', async () => {
			jest.spyOn(db.sequelize, 'query')
				.mockResolvedValueOnce([])
				.mockResolvedValueOnce([{ total: 0 }])
				.mockResolvedValueOnce([{ total: 0 }]);
			await customReportRepository.findAllTypes(params);
			expect(db.sequelize.query).toHaveBeenCalledTimes(3);
			expect(db.sequelize.query.mock.calls[0][1]).toEqual(
				expect.objectContaining({
					type: db.sequelize.QueryTypes.SELECT,
					replacements: expect.any(Object)
				})
			);
		});

		it('should handle empty report counts', async () => {
			jest.spyOn(db.sequelize, 'query')
				.mockResolvedValueOnce(mockedFindAllReportTypes)
				.mockResolvedValueOnce([])
				.mockResolvedValueOnce([]);
			const [result] = await customReportRepository.findAllTypes(params);
			expect(result).toEqual(
				expect.arrayContaining([
					expect.objectContaining({
						name: 'sera',
						total_reports: 0
					}),
					expect.objectContaining({
						name: 'bera',
						total_reports: 0
					})
				])
			);
		});

		it('should call all mappers', async () => {
			const get_all_custom_report_types_sql_spy = jest.spyOn(GetAllCustomReportTypesSQL.prototype, 'getQuery');
			const get_all_bera_report_count_sql_spy = jest.spyOn(GetAllBeraReportCountSQL.prototype, 'getQuery');
			const get_all_sera_report_count_sql_spy = jest.spyOn(GetAllSeraReportCountSQL.prototype, 'getQuery');
			jest.spyOn(db.sequelize, 'query')
				.mockResolvedValueOnce(mockedFindAllReportTypes)
				.mockResolvedValueOnce([{ total: 0 }])
				.mockResolvedValueOnce([{ total: 0 }]);
			await customReportRepository.findAllTypes(params);
			expect(get_all_custom_report_types_sql_spy).toHaveBeenCalled();
			expect(get_all_bera_report_count_sql_spy).toHaveBeenCalled();
			expect(get_all_sera_report_count_sql_spy).toHaveBeenCalled();
		});
	});
});
