import { jest } from '@jest/globals';
import { faker } from '@faker-js/faker';
import { RebaReportRepository } from '@src/app/repository/reba.js';
import { mockedRebaExposureScore } from '@tests/unit/fixtures/reba_report.js';

const { datatype } = faker;

describe('Reba repository', () => {
	let repository;

	let db = {
		sequelize: {
			query: jest.fn(),
			QueryTypes: {
				SELECT: 'SELECT'
			}
		}
	};

	beforeAll(() => {
		repository = new RebaReportRepository(db);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	describe('[getExposuresScore]', () => {
		const parameters = {
			organization_id: datatype.uuid(),
			company_id: datatype.uuid(),
			sector_id: datatype.uuid(),
			line_id: datatype.uuid(),
			workstation_id: datatype.uuid(),
			companies_ids: [datatype.uuid()],
			start_date: datatype.datetime(),
			end_date: datatype.datetime(),
			range_risk_id: datatype.uuid()
		};

		it('should get exposures score', async () => {
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(mockedRebaExposureScore);
			const result = await repository.getExposuresScore(parameters);
			expect(result).toStrictEqual([mockedRebaExposureScore, null]);
		});

		it('should verify query params', async () => {
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(mockedRebaExposureScore);
			await repository.getExposuresScore(parameters);
			expect(db.sequelize.query).toHaveBeenCalledWith(
				expect.stringContaining('SUM(percentage * files.duration) / 100 as seconds'),
				{
					replacements: {
						end_date: parameters.end_date,
						start_date: parameters.start_date,
						organization_id: parameters.organization_id,
						company_id: parameters.company_id,
						companies_ids: parameters.companies_ids,
						sector_id: parameters.sector_id,
						line_id: parameters.line_id,
						workstation_id: parameters.workstation_id,
						range_risk_id: parameters.range_risk_id,
						user_id: undefined
					},
					type: db.sequelize.QueryTypes.SELECT
				}
			);
		});

		it('should verify query params', async () => {
			jest.spyOn(repository, 'getExposureScoreFilter').mockResolvedValueOnce(null);
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(mockedRebaExposureScore);
			await repository.getExposuresScore(parameters);
			expect(repository.getExposureScoreFilter).toHaveBeenCalledWith({
				end_date: parameters.end_date,
				start_date: parameters.start_date,
				organization_id: parameters.organization_id,
				company_id: parameters.company_id,
				companies_ids: parameters.companies_ids,
				sector_id: parameters.sector_id,
				line_id: parameters.line_id,
				workstation_id: parameters.workstation_id,
				user_id: ''
			});
		});

		it('should throw error in query', async () => {
			jest.spyOn(db.sequelize, 'query').mockRejectedValueOnce(new Error());
			const result = await repository.getExposuresScore(parameters);
			expect(result).toStrictEqual([null, expect.any(Error)]);
		});
	});

	describe('[getBodySideExposuresScore]', () => {
		const parameters = {
			organization_id: datatype.uuid(),
			company_id: datatype.uuid(),
			sector_id: datatype.uuid(),
			line_id: datatype.uuid(),
			workstation_id: datatype.uuid(),
			companies_ids: [datatype.uuid()],
			start_date: datatype.datetime(),
			end_date: datatype.datetime(),
			range_risk_id: datatype.uuid()
		};

		it('should get exposures score', async () => {
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(mockedRebaExposureScore);
			const result = await repository.getBodySideExposuresScore(parameters);
			expect(result).toStrictEqual([mockedRebaExposureScore, null]);
		});

		it('should verify query params', async () => {
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(mockedRebaExposureScore);
			await repository.getBodySideExposuresScore(parameters);
			expect(db.sequelize.query).toHaveBeenCalledWith(
				expect.stringContaining('SUM(percentage * files.duration) / 100 as seconds'),
				{
					replacements: {
						end_date: parameters.end_date,
						start_date: parameters.start_date,
						organization_id: parameters.organization_id,
						company_id: parameters.company_id,
						companies_ids: parameters.companies_ids,
						sector_id: parameters.sector_id,
						line_id: parameters.line_id,
						workstation_id: parameters.workstation_id,
						range_risk_id: parameters.range_risk_id,
						user_id: undefined
					},
					type: db.sequelize.QueryTypes.SELECT
				}
			);
		});

		it('should verify query params', async () => {
			jest.spyOn(repository, 'getExposureScoreFilter').mockResolvedValueOnce(null);
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(mockedRebaExposureScore);
			await repository.getBodySideExposuresScore(parameters);
			expect(repository.getExposureScoreFilter).toHaveBeenCalledWith({
				end_date: parameters.end_date,
				start_date: parameters.start_date,
				organization_id: parameters.organization_id,
				company_id: parameters.company_id,
				companies_ids: parameters.companies_ids,
				sector_id: parameters.sector_id,
				line_id: parameters.line_id,
				workstation_id: parameters.workstation_id,
				user_id: ''
			});
		});

		it('should get query with all filters', () => {
			const expectedQuery = `
				AND DATE(r.created_at) BETWEEN DATE(:start_date)
				AND DATE(:end_date)
				AND files.organization_id = :organization_id
				AND files.company_id = :company_id
				AND w.line_id = :line_id
				AND files.sector_id = :sector_id
				AND files.workstation_id = :workstation_id
			`;
			const result = repository.getExposureScoreFilter(parameters);
			expect(result.replace(/\s+/g, ' ').trim()).toStrictEqual(expectedQuery.replace(/\s+/g, ' ').trim());
		});

		it('should throw error in query', async () => {
			jest.spyOn(db.sequelize, 'query').mockRejectedValueOnce(new Error());
			const result = await repository.getBodySideExposuresScore(parameters);
			expect(result).toStrictEqual([null, expect.any(Error)]);
		});
	});

	describe('[getExposureScoreFilter]', () => {
		const parameters = {
			organization_id: datatype.uuid(),
			company_id: datatype.uuid(),
			sector_id: datatype.uuid(),
			line_id: datatype.uuid(),
			workstation_id: datatype.uuid(),
			companies_ids: [datatype.uuid()],
			start_date: datatype.datetime(),
			end_date: datatype.datetime(),
			range_risk_id: datatype.uuid()
		};

		it('should get query with all filters', () => {
			const expectedQuery = `
				AND DATE(r.created_at) BETWEEN DATE(:start_date) AND DATE(:end_date)
				AND files.organization_id = :organization_id
				AND files.company_id = :company_id
				AND w.line_id = :line_id
				AND files.sector_id = :sector_id
				AND files.workstation_id = :workstation_id
			`;
			const result = repository.getExposureScoreFilter(parameters);
			expect(result.replace(/\s+/g, ' ').trim()).toStrictEqual(expectedQuery.replace(/\s+/g, ' ').trim());
		});

		it('should get query with companies_id only', () => {
			const expectedQuery = `AND files.company_id IN(:companies_ids)`;
			const result = repository.getExposureScoreFilter({ companies_ids: parameters.companies_ids });
			expect(result.replace(/\s+/g, ' ').trim()).toStrictEqual(expectedQuery);
		});

		it('should return empty if does not have filters', () => {
			const expectedQuery = ``;
			const result = repository.getExposureScoreFilter({});
			expect(result.replace(/\s+/g, ' ').trim()).toStrictEqual(expectedQuery);
		});
	});
});
