import { jest } from '@jest/globals';
import { faker } from '@faker-js/faker';
import { LibertyMutualReportRepository } from '@src/app/repository/liberty_mutual_report.js';
import { mockedLibertyMutualReport, mockedLibertyMutualReportPercentileByGender } from '../fixtures/index.js';

const { datatype } = faker;

describe('Liberty Mutual report repository', () => {
	let repository;

	let db = {
		LibertyMutualReport: {
			create: jest.fn(),
			update: jest.fn(),
			findAll: jest.fn(),
			findOne: jest.fn(),
			findByPk: jest.fn(),
			getPercentileByGender: jest.fn(),
			getPercentileByGenderFilterQuery: jest.fn(),
			getPercentileByGenderFilterParams: jest.fn()
		},
		sequelize: {
			query: jest.fn(),
			QueryTypes: {
				SELECT: 'SELECT'
			}
		}
	};

	beforeAll(() => {
		repository = new LibertyMutualReportRepository(db);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	describe('[create]', () => {
		it('should create liberty mutual report', async () => {
			const request = {
				data: {
					file_id: datatype.uuid(),
					report_user_id: datatype.uuid(),
					system_of_units_id: datatype.uuid()
				},
				transaction: {}
			};

			jest.spyOn(db.LibertyMutualReport, 'create').mockResolvedValueOnce(mockedLibertyMutualReport);

			const result = await repository.create(request.data, request.transaction);

			const response = expect.objectContaining({
				id: expect.any(String),
				report_user_id: expect.any(String),
				file_id: expect.any(String),
				system_of_units_id: expect.any(String)
			});

			expect(result).toStrictEqual(response);
		});
	});

	describe('[update]', () => {
		it('should update liberty mutual report', async () => {
			const request = {
				data: {
					file_id: datatype.uuid(),
					task_id: datatype.uuid(),
					frequency: datatype.float(),
					hand_coupling: datatype.float(),
					object_weight: datatype.float(),
					end_hand_height: datatype.float(),
					system_of_units_id: datatype.uuid(),
					start_hand_height: datatype.float(),
					end_hand_distance: datatype.float(),
					start_hand_distance: datatype.float(),
					frequency_time_format: datatype.string()
				},
				params: {
					where: {
						id: datatype.uuid()
					},
					transaction: {}
				}
			};

			jest.spyOn(db.LibertyMutualReport, 'update').mockResolvedValueOnce([1]);

			const result = await repository.update(request.data, request.params);

			expect(result).toStrictEqual([1]);
		});
	});

	describe('[findByPk]', () => {
		it('should find liberty mutual report by id', async () => {
			const request = {
				params: {},
				id: datatype.string()
			};

			jest.spyOn(db.LibertyMutualReport, 'findByPk').mockResolvedValueOnce(mockedLibertyMutualReport);

			const result = await repository.findByPk(request.id, request.params);

			const response = expect.objectContaining({
				id: expect.any(String),
				file_id: expect.any(String),
				report_user_id: expect.any(String),
				system_of_units_id: expect.any(String)
			});

			expect(result).toStrictEqual(response);
		});
	});

	describe('[findOne]', () => {
		it('should find liberty mutual report for specific file id', async () => {
			const request = {
				where: {
					file_id: datatype.uuid()
				}
			};

			jest.spyOn(db.LibertyMutualReport, 'findOne').mockResolvedValueOnce(mockedLibertyMutualReport);

			const result = await repository.findOne(request);

			const response = expect.objectContaining({
				id: expect.any(String),
				file_id: expect.any(String),
				report_user_id: expect.any(String),
				system_of_units_id: expect.any(String)
			});

			expect(result).toStrictEqual(response);
		});
	});

	describe('[findAllByForeignKey]', () => {
		it('should find all liberty mutual reports for specific task id', async () => {
			const request = {
				where: {
					task_id: datatype.uuid()
				}
			};

			jest.spyOn(db.LibertyMutualReport, 'findAll').mockResolvedValueOnce([mockedLibertyMutualReport]);

			const result = await repository.findAllByForeignKey(request);

			const response = expect.arrayContaining([
				expect.objectContaining({
					id: expect.any(String),
					file_id: expect.any(String),
					report_user_id: expect.any(String),
					system_of_units_id: expect.any(String)
				})
			]);

			expect(result).toHaveLength(1);
			expect(result).toStrictEqual(response);
		});
	});

	describe('[getPercentileByGenderFilterParams]', () => {
		const organization_id = datatype.uuid();
		const company_id = datatype.uuid();
		const sector_id = datatype.uuid();
		const line_id = datatype.uuid();
		const workstation_id = datatype.uuid();
		const start_date = datatype.datetime();
		const end_date = datatype.datetime();

		let parameters = {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date
		};

		it('should return correct filters when all params are provided', () => {
			const expectedFilters = {
				organization_id,
				company_id,
				sector_id,
				line_id,
				workstation_id,
				start_date,
				end_date
			};
			const result = repository.getPercentileByGenderFilterParams(parameters);
			expect(result).toStrictEqual(expectedFilters);
		});

		it('should return others filters when optional sector_id are missing', () => {
			const expectedFilters = {
				organization_id,
				company_id,
				line_id,
				workstation_id,
				start_date,
				end_date
			};

			parameters = {
				organization_id,
				company_id,
				line_id,
				workstation_id,
				start_date,
				end_date
			};

			const result = repository.getPercentileByGenderFilterParams(parameters);
			expect(result).toStrictEqual(expectedFilters);
		});

		it('should return others filters when optional line_id are missing', () => {
			const expectedFilters = {
				organization_id,
				company_id,
				sector_id,
				workstation_id,
				start_date,
				end_date
			};

			parameters = {
				organization_id,
				company_id,
				sector_id,
				workstation_id,
				start_date,
				end_date
			};

			const result = repository.getPercentileByGenderFilterParams(parameters);
			expect(result).toStrictEqual(expectedFilters);
		});

		it('should return others filters when optional workstation_id are missing', () => {
			const expectedFilters = {
				organization_id,
				company_id,
				sector_id,
				line_id,
				start_date,
				end_date
			};

			parameters = {
				organization_id,
				company_id,
				sector_id,
				line_id,
				start_date,
				end_date
			};

			const result = repository.getPercentileByGenderFilterParams(parameters);
			expect(result).toStrictEqual(expectedFilters);
		});

		it('should return others filters when optional start_date and end_date are missing', () => {
			const expectedFilters = {
				organization_id,
				company_id,
				sector_id,
				line_id,
				workstation_id
			};

			parameters = {
				organization_id,
				company_id,
				sector_id,
				line_id,
				workstation_id
			};

			const result = repository.getPercentileByGenderFilterParams(parameters);
			expect(result).toStrictEqual(expectedFilters);
		});

		it('should return only required filters when optional params are missing', () => {
			const expectedFilters = { organization_id, company_id };
			parameters = { organization_id, company_id };
			const result = repository.getPercentileByGenderFilterParams(parameters);
			expect(result).toStrictEqual(expectedFilters);
		});
	});

	describe('[getPercentileByGenderFilterQuery]', () => {
		const organization_id = datatype.uuid();
		const company_id = datatype.uuid();
		const sector_id = datatype.uuid();
		const line_id = datatype.uuid();
		const workstation_id = datatype.uuid();
		const start_date = datatype.datetime();
		const end_date = datatype.datetime();

		it('should return correct query when all params are provided', () => {
			const parameters = {
				organization_id,
				company_id,
				sector_id,
				line_id,
				workstation_id,
				start_date,
				end_date
			};

			const expectedQuery = `
				SELECT
					COALESCE(MIN(percentile_man), 0) as man,
					COALESCE(MIN(percentile_woman), 0) as woman
				FROM liberty_mutual_reports as report
				INNER JOIN files as file on file.id = report.file_id
				INNER JOIN workstations as workstation on workstation.id = file.workstation_id
				INNER JOIN lines as line on line.id = workstation.line_id
				INNER JOIN sectors as sector on sector.id = line.sector_id
				INNER JOIN liberty_mutual_report_inputs as report_inputs on report_inputs.liberty_mutual_report_id = report.id
				WHERE (
					report.deleted_at IS NULL
					AND file.is_active = 1
					AND sector.is_active = 1
					AND line.deleted_at IS NULL
					AND workstation.deleted_at IS NULL
					AND file.organization_id = :organization_id
					AND file.company_id = :company_id
					AND file.sector_id = :sector_id
					AND line.id = :line_id
					AND file.workstation_id = :workstation_id
					AND DATE(report_inputs.created_at) BETWEEN DATE (:start_date) AND DATE(:end_date)
				)
			`;

			const result = repository.getPercentileByGenderFilterQuery(parameters);
			expect(result.replace(/\s+/g, ' ').trim()).toBe(expectedQuery.replace(/\s+/g, ' ').trim());
		});

		it('should return query with only required conditions when optional params are missing', () => {
			const parameters = {
				companies_ids: [datatype.uuid()],
				sector_id: null,
				line_id: null,
				workstation_id: null,
				start_date: null,
				end_date: null
			};

			const expectedQuery = `
				SELECT
					COALESCE(MIN(percentile_man), 0) as man,
					COALESCE(MIN(percentile_woman), 0) as woman
				FROM liberty_mutual_reports as report
					INNER JOIN files as file on file.id = report.file_id
					INNER JOIN workstations as workstation on workstation.id = file.workstation_id
					INNER JOIN lines as line on line.id = workstation.line_id
					INNER JOIN sectors as sector on sector.id = line.sector_id
					INNER JOIN liberty_mutual_report_inputs as report_inputs on report_inputs.liberty_mutual_report_id = report.id
				WHERE (
					report.deleted_at IS NULL
					AND file.is_active = 1
					AND sector.is_active = 1
					AND line.deleted_at IS NULL
					AND workstation.deleted_at IS NULL
					AND file.company_id IN (:companies_ids)
				)
			`;

			const result = repository.getPercentileByGenderFilterQuery(parameters);
			expect(result.replace(/\s+/g, ' ').trim()).toBe(expectedQuery.replace(/\s+/g, ' ').trim());
		});
	});

	describe('[getPercentileByGender]', () => {
		const organization_id = datatype.uuid();
		const company_id = datatype.uuid();
		const sector_id = datatype.uuid();
		const line_id = datatype.uuid();
		const workstation_id = datatype.uuid();
		const start_date = datatype.datetime();
		const end_date = datatype.datetime();

		const parameters = {
			organization_id,
			company_id,
			sector_id,
			line_id,
			workstation_id,
			start_date,
			end_date
		};

		it('should certificate the call method with parameters correct', async () => {
			jest.spyOn(repository, 'getPercentileByGenderFilterParams').mockResolvedValueOnce(null);
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(null);
			await repository.getPercentileByGender(parameters);
			expect(repository.getPercentileByGenderFilterParams).toHaveBeenCalledWith(parameters);
		});

		it('should certificate the call method with query correct', async () => {
			jest.spyOn(repository, 'getPercentileByGenderFilterQuery').mockResolvedValueOnce(null);
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(null);
			await repository.getPercentileByGender(parameters);
			expect(repository.getPercentileByGenderFilterQuery).toHaveBeenCalledWith(parameters);
		});

		it('should return the minimum percentile value for man and woman', async () => {
			const mockExpect = [mockedLibertyMutualReportPercentileByGender, null];
			jest.spyOn(db.sequelize, 'query').mockResolvedValueOnce(mockedLibertyMutualReportPercentileByGender);
			const result = await repository.getPercentileByGender(parameters);
			expect(result).toStrictEqual(mockExpect);
		});

		it('should return error if the database found some error', async () => {
			const throwError = new Error('StackOverflowError');
			const expected = [null, throwError];
			jest.spyOn(db.sequelize, 'query').mockRejectedValueOnce(throwError);
			const result = await repository.getPercentileByGender(parameters);
			expect(result).toStrictEqual(expected);
		});
	});
});
