import { FileSandboxSyncSQL } from '@src/app/mappers/FileSandboxSyncSQL.js';

describe('[FileSandboxSyncSQL] - Mapper', () => {
	let mapper;

	beforeEach(() => {
		mapper = new FileSandboxSyncSQL();
	});

	describe('[createInsertStatement]', () => {
		it('should create a valid INSERT query with all columns and deleted_at', () => {
			const tableName = 'files';
			const columns = ['id', 'original_name', 'generated_name', 'size', 'url'];

			const { query } = mapper.createInsertStatement(tableName, columns);

			expect(query).toBe(`
            INSERT INTO \`files\` 
            (\`id\`, \`original_name\`, \`generated_name\`, \`size\`, \`url\`)
            VALUES (?, ?, ?, ?, ?)
        `);
		});
	});

	describe('[createUpdateStatement]', () => {
		it('should create a valid UPDATE query excluding id column', () => {
			const tableName = 'files';
			const columns = ['id', 'original_name', 'generated_name', 'size', 'url'];

			const { query } = mapper.createUpdateStatement(tableName, columns);

			expect(query).toBe(`
            UPDATE \`files\`
            SET \`original_name\` = ?, \`generated_name\` = ?, \`size\` = ?, \`url\` = ?
            WHERE \`id\` = ?
        `);
		});

		it('should handle columns array with only id', () => {
			const tableName = 'files';
			const columns = ['id'];

			const { query } = mapper.createUpdateStatement(tableName, columns);

			expect(query).toBe(`
            UPDATE \`files\`
            SET 
            WHERE \`id\` = ?
        `);
		});
	});
});
