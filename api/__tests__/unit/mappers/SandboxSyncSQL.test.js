import { SandboxSyncSQL } from '@src/app/mappers/SandboxSyncSQL.js';

describe('[SandboxSyncSQL] - Mapper', () => {
	let mapper;

	beforeEach(() => {
		mapper = new SandboxSyncSQL();
	});

	describe('[createInsertStatement]', () => {
		it('should create a valid INSERT query with all columns', () => {
			const tableName = 'users';
			const columns = ['id', 'name', 'email'];
			const { query } = mapper.createInsertStatement(tableName, columns);
			expect(query.replace(/\s+/g, ' ').trim()).toBe(
				'INSERT INTO `users` (`id`, `name`, `email`) VALUES (?, ?, ?)'
			);
		});

		it('should handle empty columns array', () => {
			const tableName = 'users';
			const columns = [];
			const { query } = mapper.createInsertStatement(tableName, columns);
			expect(query.replace(/\s+/g, ' ').trim()).toBe('INSERT INTO `users` (``) VALUES ()');
		});
	});

	describe('[createUpdateStatement]', () => {
		it('should create a valid UPDATE query excluding id column', () => {
			const tableName = 'users';
			const columns = ['id', 'name', 'email'];
			const { query } = mapper.createUpdateStatement(tableName, columns);
			expect(query.replace(/\s+/g, ' ').trim()).toBe('UPDATE `users` SET `name` = ?, `email` = ? WHERE `id` = ?');
		});

		it('should handle columns array with only id', () => {
			const tableName = 'users';
			const columns = ['id'];
			const { query } = mapper.createUpdateStatement(tableName, columns);
			expect(query.replace(/\s+/g, ' ').trim()).toBe('UPDATE `users` SET WHERE `id` = ?');
		});
	});

	describe('[createDeleteStatement]', () => {
		it('should create a valid DELETE query', () => {
			const tableName = 'users';
			const { query } = mapper.createDeleteStatement(tableName);
			expect(query.replace(/\s+/g, ' ').trim()).toBe('DELETE FROM `users` WHERE `id` = ?');
		});

		it('should handle table name with special characters', () => {
			const tableName = 'user_profiles';
			const { query } = mapper.createDeleteStatement(tableName);
			expect(query.replace(/\s+/g, ' ').trim()).toBe('DELETE FROM `user_profiles` WHERE `id` = ?');
		});
	});
});
