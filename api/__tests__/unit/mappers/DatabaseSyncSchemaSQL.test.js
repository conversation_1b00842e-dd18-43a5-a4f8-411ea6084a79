import { DatabaseSyncSchemaSQL } from '@src/app/mappers/DatabaseSyncSchemaSQL.js';

describe('[DatabaseSyncSchemaSQL] - Mapper', () => {
	let mapper;

	beforeEach(() => {
		mapper = new DatabaseSyncSchemaSQL();
	});

	describe('[getTableCollumns]', () => {
		it('should generate correct query for table columns', () => {
			const table_name = 'test_table';
			const database = 'test_db';

			const { query, replacements } = mapper.getTableCollumns(table_name, database);

			expect(query).toContain('SELECT');
			expect(query).toContain('FROM INFORMATION_SCHEMA.COLUMNS');
			expect(query).toContain(`TABLE_SCHEMA = '${database}'`);
			expect(query).toContain(`TABLE_NAME = '${table_name}'`);
			expect(replacements).toEqual({ table_name, database });
		});
	});

	describe('[getTableForeignKeys]', () => {
		it('should generate correct query for foreign keys', () => {
			const table_name = 'test_table';
			const database = 'test_db';

			const { query, replacements } = mapper.getTableForeignKeys(table_name, database);

			expect(query).toContain('SELECT');
			expect(query).toContain('FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE');
			expect(query).toContain(`TABLE_SCHEMA = '${database}'`);
			expect(query).toContain(`TABLE_NAME = '${table_name}'`);
			expect(query).toContain('REFERENCED_TABLE_NAME IS NOT NULL');
			expect(replacements).toEqual({ table_name, database });
		});
	});

	describe('[getTableIndexes]', () => {
		it('should generate correct query for indexes', () => {
			const table_name = 'test_table';

			const { query, replacements } = mapper.getTableIndexes(table_name);

			expect(query).toContain('SHOW INDEX FROM');
			expect(query).toContain(`\`${table_name}\``);
			expect(replacements).toEqual({ table_name });
		});
	});

	describe('[getColumnInfo]', () => {
		it('should generate correct query for column info', () => {
			const table_name = 'test_table';
			const column_name = 'test_column';
			const database = 'test_db';

			const { query, replacements } = mapper.getColumnInfo(table_name, column_name, database);

			expect(query).toContain('SELECT');
			expect(query).toContain('FROM INFORMATION_SCHEMA.COLUMNS');
			expect(query).toContain(`TABLE_SCHEMA = '${database}'`);
			expect(query).toContain(`TABLE_NAME = '${table_name}'`);
			expect(query).toContain(`COLUMN_NAME = '${column_name}'`);
			expect(replacements).toEqual({ table_name, column_name, database });
		});
	});

	describe('[alterTableStatement]', () => {
		it('should generate correct ALTER TABLE statement', () => {
			const table_name = 'test_table';
			const column_name = 'test_column';
			const target_column = {
				COLUMN_TYPE: 'VARCHAR(255)',
				CHARACTER_SET_NAME: 'utf8mb4',
				COLLATION_NAME: 'utf8mb4_unicode_ci',
				IS_NULLABLE: 'NO',
				COLUMN_DEFAULT: 'test',
				EXTRA: 'auto_increment'
			};

			const { query, replacements } = mapper.alterTableStatement(table_name, column_name, target_column);

			expect(query).toContain('ALTER TABLE');
			expect(query).toContain(`\`${table_name}\``);
			expect(query).toContain('MODIFY');
			expect(query).toContain(`\`${column_name}\``);
			expect(query).toContain(target_column.COLUMN_TYPE);
			expect(query).toContain(`CHARACTER SET ${target_column.CHARACTER_SET_NAME}`);
			expect(query).toContain(`COLLATE ${target_column.COLLATION_NAME}`);
			expect(query).toContain('NOT NULL');
			expect(query).toContain(`DEFAULT ${target_column.COLUMN_DEFAULT}`);
			expect(query).toContain('AUTO_INCREMENT');
			expect(replacements).toHaveProperty('collumn_type');
		});
	});

	describe('[addColumnStatement]', () => {
		it('should generate correct ADD COLUMN statement', () => {
			const table_name = 'test_table';
			const column_info = {
				COLUMN_NAME: 'test_column',
				COLUMN_TYPE: 'VARCHAR(255)',
				DATA_TYPE: 'varchar',
				CHARACTER_SET_NAME: 'utf8mb4',
				COLLATION_NAME: 'utf8mb4_unicode_ci',
				IS_NULLABLE: 'NO',
				COLUMN_DEFAULT: 'test',
				EXTRA: 'auto_increment'
			};

			const { query, replacements } = mapper.addColumnStatement(table_name, column_info);

			expect(query).toContain('ALTER TABLE');
			expect(query).toContain(`\`${table_name}\``);
			expect(query).toContain('ADD COLUMN');
			expect(query).toContain(`\`${column_info.COLUMN_NAME}\``);
			expect(query).toContain(column_info.COLUMN_TYPE);
			expect(query).toContain(`CHARACTER SET ${column_info.CHARACTER_SET_NAME}`);
			expect(query).toContain(`COLLATE ${column_info.COLLATION_NAME}`);
			expect(query).toContain('NOT NULL');
			expect(query).toContain(`DEFAULT '${column_info.COLUMN_DEFAULT}'`);
			expect(query).toContain('AUTO_INCREMENT');
			expect(replacements).toHaveProperty('collumn_type');
		});
	});

	describe('[getAllTables]', () => {
		it('should generate correct query for all tables', () => {
			const database = 'test_db';
			const db_filters = { table_name: 'test_table' };

			const { query, replacements } = mapper.getAllTables(database, db_filters);

			expect(query).toContain('SELECT table_name');
			expect(query).toContain('FROM information_schema.tables');
			expect(query).toContain(`table_schema = '${database}'`);
			expect(query).toContain("table_type = 'BASE TABLE'");
			expect(query).toContain(`table_name = '${db_filters.table_name}'`);
			expect(replacements).toEqual({ database, table_name: db_filters.table_name });
		});

		it('should generate query without table filter when not provided', () => {
			const database = 'test_db';

			const { query, replacements } = mapper.getAllTables(database);

			expect(query).not.toContain('table_name =');
			expect(replacements).toEqual({ database });
		});
	});

	describe('[addForeignKey]', () => {
		it('should generate correct ADD FOREIGN KEY statement', () => {
			const table_name = 'test_table';
			const foreign_key_info = {
				CONSTRAINT_NAME: 'fk_test',
				COLUMN_NAME: 'test_column',
				REFERENCED_TABLE_NAME: 'referenced_table',
				REFERENCED_COLUMN_NAME: 'referenced_column'
			};

			const { query, replacements } = mapper.addForeignKey(table_name, foreign_key_info);

			expect(query).toContain('ALTER TABLE');
			expect(query).toContain(`\`${table_name}\``);
			expect(query).toContain('ADD CONSTRAINT');
			expect(query).toContain(`\`${foreign_key_info.CONSTRAINT_NAME}\``);
			expect(query).toContain('FOREIGN KEY');
			expect(query).toContain(`\`${foreign_key_info.COLUMN_NAME}\``);
			expect(query).toContain('REFERENCES');
			expect(query).toContain(`\`${foreign_key_info.REFERENCED_TABLE_NAME}\``);
			expect(query).toContain(`\`${foreign_key_info.REFERENCED_COLUMN_NAME}\``);
			expect(replacements).toEqual({
				table_name,
				constraint_name: foreign_key_info.CONSTRAINT_NAME,
				column_name: foreign_key_info.COLUMN_NAME,
				referenced_table: foreign_key_info.REFERENCED_TABLE_NAME,
				referenced_column: foreign_key_info.REFERENCED_COLUMN_NAME
			});
		});
	});

	describe('[createTableStatement]', () => {
		it('should generate correct CREATE TABLE statement', () => {
			const table_name = 'test_table';
			const column_defs = ['id INT', 'name VARCHAR(255)'];
			const charset = 'utf8mb4';
			const collation = 'utf8mb4_unicode_ci';

			const { query, replacements } = mapper.createTableStatement(table_name, column_defs, charset, collation);

			expect(query).toContain('CREATE TABLE');
			expect(query).toContain(`\`${table_name}\``);
			expect(query).toContain(column_defs.join(',\n                '));
			expect(query).toContain(`CHARSET=${charset}`);
			expect(query).toContain(`COLLATE=${collation}`);
			expect(replacements).toEqual({});
		});
	});

	describe('[createIndexStatement]', () => {
		it('should generate correct CREATE INDEX statement', () => {
			const table_name = 'test_table';
			const index_name = 'idx_test';
			const column_name = 'test_column';

			const { query, replacements } = mapper.createIndexStatement(table_name, index_name, column_name);

			expect(query).toContain('CREATE INDEX');
			expect(query).toContain(`\`${index_name}\``);
			expect(query).toContain(`ON \`${table_name}\``);
			expect(query).toContain(`\`${column_name}\``);
			expect(replacements).toEqual({});
		});
	});

	describe('[dropTableStatement]', () => {
		it('should generate correct DROP TABLE statement', () => {
			const table_name = 'test_table';

			const { query, replacements } = mapper.dropTableStatement(table_name);

			expect(query).toBe(`DROP TABLE \`${table_name}\``);
			expect(replacements).toEqual({});
		});
	});

	describe('[checkForeignKeyExists]', () => {
		it('should generate correct query to check foreign key existence', () => {
			const table_name = 'test_table';
			const constraint_name = 'fk_test';
			const database = 'test_db';

			const { query, replacements } = mapper.checkForeignKeyExists(table_name, constraint_name, database);

			expect(query).toContain('SELECT COUNT(*) as count');
			expect(query).toContain('FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS');
			expect(query).toContain(`TABLE_SCHEMA = '${database}'`);
			expect(query).toContain(`TABLE_NAME = '${table_name}'`);
			expect(query).toContain(`CONSTRAINT_NAME = '${constraint_name}'`);
			expect(query).toContain("CONSTRAINT_TYPE = 'FOREIGN KEY'");
			expect(replacements).toEqual({ table_name, constraint_name, database });
		});
	});
});
