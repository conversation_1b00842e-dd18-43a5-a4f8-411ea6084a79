import { jest } from '@jest/globals';
import { faker } from '@faker-js/faker';

import { GetEwaCustomReportResultsSQL } from '@src/app/mappers/getEwaCustomReportResultsSQL.js';

const { datatype, helpers } = faker;

describe('GetEwaCustomReportResultsSQL mappers', () => {
	const parameters = {
		organization_id: datatype.uuid(),
		company_id: datatype.uuid(),
		sector_id: datatype.uuid(),
		line_id: datatype.uuid(),
		workstation_id: datatype.uuid(),
		evaluator_id: datatype.uuid(),
		file_name: datatype.string(),
		collection_date_start: datatype.datetime(),
		collection_date_end: datatype.datetime(),
		created_at_start: datatype.datetime(),
		created_at_end: datatype.datetime(),
		limit: datatype.number(),
		offset: datatype.number()
	};

	beforeEach(() => {
		jest.clearAllMocks();
	});

	describe('[getIndexQuery]', () => {
		it('should get query to find all for table', async () => {
			const hierarcy_filter = datatype.string();
			const custom_report_filter = datatype.string();

			jest.spyOn(GetEwaCustomReportResultsSQL.prototype, 'getHierarchyFilter').mockReturnValueOnce(
				hierarcy_filter
			);
			jest.spyOn(GetEwaCustomReportResultsSQL.prototype, 'getCustomReportFilters').mockReturnValueOnce(
				custom_report_filter
			);

			const query = `
			SELECT crr.id, crr.name, crr.result, f.original_name as file_name, e.name as analyst_name, crr.collection_date, crr.created_at, crr.updated_at,
                JSON_OBJECT('id', cr.id, 'name', cr.name, 'description', cr.description, 'acronym', cr.acronym) AS custom_report
            FROM custom_report_results crr
                INNER JOIN custom_reports cr ON cr.id = crr.custom_report_id AND cr.deleted_at IS NULL
                INNER JOIN files f ON crr.file_id = f.id AND f.is_active IS TRUE
                INNER JOIN evaluators e ON e.id = crr.evaluator_id AND e.deleted_at IS NULL
                INNER JOIN workstations w ON w.id = f.workstation_id AND w.deleted_at IS NULL
                INNER JOIN lines l ON l.id = w.line_id AND l.deleted_at IS NULL
                INNER JOIN sectors s ON s.id = l.sector_id AND s.is_active IS TRUE
                INNER JOIN companies c ON c.id = s.company_id AND c.is_active IS TRUE
            WHERE crr.deleted_at IS NULL
                AND (cr.name = 'ewa' OR cr.name = 'ewa_d86')
                AND crr.consolidated IS TRUE
                AND c.organization_id = :organization_id
                ${hierarcy_filter}
                ${custom_report_filter}
			LIMIT :limit
			OFFSET :offset
		`;

			const mapper = new GetEwaCustomReportResultsSQL(parameters);
			const result = await mapper.getIndexQuery();
			expect(result.trim()).toStrictEqual(query.trim());
		});
	});

	describe('[getCountAllQuery]', () => {
		it('should get query to find all for table', async () => {
			const hierarcy_filter = datatype.string();
			const custom_report_filter = datatype.string();

			jest.spyOn(GetEwaCustomReportResultsSQL.prototype, 'getHierarchyFilter').mockReturnValueOnce(
				hierarcy_filter
			);
			jest.spyOn(GetEwaCustomReportResultsSQL.prototype, 'getCustomReportFilters').mockReturnValueOnce(
				custom_report_filter
			);

			const query = `
				SELECT COUNT(DISTINCT crr.id) AS total
            FROM custom_report_results crr 
                INNER JOIN custom_reports cr ON cr.id = crr.custom_report_id AND cr.deleted_at IS NULL
                INNER JOIN files f ON crr.file_id = f.id AND f.is_active IS TRUE
                INNER JOIN evaluators e ON e.id = crr.evaluator_id AND e.deleted_at IS NULL
                INNER JOIN workstations w ON w.id = f.workstation_id AND w.deleted_at IS NULL
                INNER JOIN lines l ON l.id = w.line_id AND l.deleted_at IS NULL
                INNER JOIN sectors s ON s.id = l.sector_id AND s.is_active IS TRUE
                INNER JOIN companies c ON c.id = s.company_id AND c.is_active IS TRUE
            WHERE crr.deleted_at IS NULL
                AND (cr.name = 'ewa' OR cr.name = 'ewa_d86')
                AND crr.consolidated IS TRUE
                AND c.organization_id = :organization_id
                ${hierarcy_filter}
                ${custom_report_filter}
			`;

			const mapper = new GetEwaCustomReportResultsSQL(parameters);
			const result = await mapper.getCountAllQuery();

			expect(result.trim()).toStrictEqual(query.trim());
		});
	});

	describe('[getCustomReportFilters]', () => {
		it('should set query to filter action plans without action plan filter', async () => {
			const mapper = new GetEwaCustomReportResultsSQL({});
			const result = await mapper.getCustomReportFilters();
			expect(result).toStrictEqual('');
		});

		it('should set query to filter action plans by evaluator_id', async () => {
			const mapper = new GetEwaCustomReportResultsSQL({ evaluator_id: parameters.evaluator_id });
			const result = await mapper.getCustomReportFilters();
			expect(result).toStrictEqual('AND e.id = :evaluator_id\n');
		});

		it('should set query to filter action plans by collection_date_start', async () => {
			const mapper = new GetEwaCustomReportResultsSQL({
				collection_date_start: parameters.collection_date_start
			});
			const result = await mapper.getCustomReportFilters();
			expect(result).toStrictEqual(`AND DATE(crr.collection_date) >= DATE(:collection_date_start)\n`);
		});

		it('should set query to filter action plans by collection_date_end', async () => {
			const mapper = new GetEwaCustomReportResultsSQL({
				collection_date_end: parameters.collection_date_end
			});
			const result = await mapper.getCustomReportFilters();
			expect(result).toStrictEqual(`AND DATE(crr.collection_date) <= DATE(:collection_date_end)\n`);
		});

		it('should set query to filter action plans by created_at_start', async () => {
			const mapper = new GetEwaCustomReportResultsSQL({ created_at_start: parameters.created_at_start });
			const result = await mapper.getCustomReportFilters();
			expect(result).toStrictEqual(`AND DATE(crr.created_at) >= DATE(:created_at_start)\n`);
		});

		it('should set query to filter action plans by created_at_end', async () => {
			const mapper = new GetEwaCustomReportResultsSQL({ created_at_end: parameters.created_at_end });
			const result = await mapper.getCustomReportFilters();
			expect(result).toStrictEqual(`AND DATE(crr.created_at) <= DATE(:created_at_end)\n`);
		});

		it('should set query to filter action plans by file_name', async () => {
			const mapper = new GetEwaCustomReportResultsSQL({ file_name: parameters.file_name });
			const result = await mapper.getCustomReportFilters();
			expect(result).toStrictEqual(`AND f.original_name LIKE '%${parameters.file_name}%'\n`);
		});
	});

	describe('[getHierarchyFilter]', () => {
		it('should set query to filter action plans by organization', async () => {
			const mapper = new GetEwaCustomReportResultsSQL({
				organization_id: parameters.organization_id
			});
			const result = await mapper.getHierarchyFilter();
			expect(result).toContain('AND c.id IN(:companies_with_user_access)\n');
		});

		it('should set query to filter action plans by company', async () => {
			const mapper = new GetEwaCustomReportResultsSQL({
				organization_id: parameters.organization_id,
				company_id: parameters.company_id
			});
			const result = await mapper.getHierarchyFilter();
			expect(result).toStrictEqual('AND c.id = :company_id\n');
		});

		it('should set query to filter action plans by sector', async () => {
			const mapper = new GetEwaCustomReportResultsSQL({
				organization_id: parameters.organization_id,
				company_id: parameters.company_id,
				sector_id: parameters.sector_id
			});
			const result = await mapper.getHierarchyFilter();
			expect(result).toContain(`AND s.id = :sector_id\n`);
		});

		it('should set query to filter action plans by workstation', async () => {
			const mapper = new GetEwaCustomReportResultsSQL({
				organization_id: parameters.organization_id,
				company_id: parameters.company_id,
				sector_id: parameters.sector_id,
				line_id: parameters.line_id,
				workstation_id: parameters.workstation_id
			});
			const result = await mapper.getHierarchyFilter();
			expect(result).toContain(`AND w.id = :workstation_id\n`);
		});
	});
});
