import { FileSandboxSyncHook } from '@src/app/models/hooks/file_sandbox_sync.js';
import { FileSandboxSyncSQL } from '@src/app/mappers/FileSandboxSyncSQL.js';

// Mock the database sandbox
const mockDbSandbox = {
	sequelize: {
		query: jest.fn()
	}
};

// Mock the logger
const mockLogger = {
	info: jest.fn(),
	error: jest.fn()
};

jest.mock('@src/app/models/sandbox.js', () => mockDbSandbox);
jest.mock('@src/app/helpers/index.js', () => ({
	logger: mockLogger
}));

describe('[FileSandboxSyncHook] - Hook', () => {
	let mockInstance;
	let mockOptions;

	beforeEach(() => {
		jest.clearAllMocks();

		mockInstance = {
			constructor: {
				name: 'File',
				tableName: 'files'
			},
			toJSON: jest.fn(() => ({
				id: 'test-id',
				original_name: 'test.mp4',
				generated_name: 'generated.mp4',
				size: 1024,
				url: 'https://test.com/file.mp4'
			})),
			_previousDataValues: {}
		};

		mockOptions = {};
	});

	describe('[beforeCreate]', () => {
		it('should capture CREATE operation for files table', async () => {
			await FileSandboxSyncHook.beforeCreate(mockInstance, mockOptions);

			expect(mockOptions.fileSandboxSync).toEqual({
				operation: 'CREATE',
				tableName: 'files',
				data: mockInstance.toJSON()
			});

			expect(mockLogger.info).toHaveBeenCalledWith('[FileSandboxSync] Capturing CREATE operation for model File');
		});

		it('should not capture operation for non-files table', async () => {
			mockInstance.constructor.tableName = 'users';

			await FileSandboxSyncHook.beforeCreate(mockInstance, mockOptions);

			expect(mockOptions.fileSandboxSync).toBeUndefined();
		});

		it('should handle errors gracefully', async () => {
			mockInstance.toJSON.mockImplementation(() => {
				throw new Error('Test error');
			});

			await expect(FileSandboxSyncHook.beforeCreate(mockInstance, mockOptions)).resolves.not.toThrow();
			expect(mockLogger.error).toHaveBeenCalledWith(
				'[FileSandboxSync] Error in beforeCreate:',
				expect.any(Error)
			);
		});
	});

	describe('[afterCreate]', () => {
		beforeEach(() => {
			mockOptions.fileSandboxSync = {
				operation: 'CREATE',
				tableName: 'files',
				data: mockInstance.toJSON()
			};
		});

		it('should sync CREATE operation to sandbox', async () => {
			await FileSandboxSyncHook.afterCreate(mockInstance, mockOptions);

			expect(mockDbSandbox.sequelize.query).toHaveBeenCalledWith(expect.stringContaining('INSERT INTO `files`'), {
				replacements: Object.values(mockInstance.toJSON()),
				type: 'INSERT'
			});

			expect(mockLogger.info).toHaveBeenCalledWith(
				'[FileSandboxSync] Successfully synced CREATE operation to sandbox'
			);
		});

		it('should not sync if no syncInfo', async () => {
			mockOptions.fileSandboxSync = null;

			await FileSandboxSyncHook.afterCreate(mockInstance, mockOptions);

			expect(mockDbSandbox.sequelize.query).not.toHaveBeenCalled();
		});
	});

	describe('[beforeDestroy]', () => {
		it('should capture SOFT_DELETE operation for files table', async () => {
			await FileSandboxSyncHook.beforeDestroy(mockInstance, mockOptions);

			expect(mockOptions.fileSandboxSync).toEqual({
				operation: 'SOFT_DELETE',
				tableName: 'files',
				data: mockInstance.toJSON()
			});

			expect(mockLogger.info).toHaveBeenCalledWith('[FileSandboxSync] Capturing DELETE operation for model File');
		});

		it('should not capture operation for non-files table', async () => {
			mockInstance.constructor.tableName = 'users';

			await FileSandboxSyncHook.beforeDestroy(mockInstance, mockOptions);

			expect(mockOptions.fileSandboxSync).toBeUndefined();
		});
	});

	describe('[afterDestroy]', () => {
		beforeEach(() => {
			mockOptions.fileSandboxSync = {
				operation: 'SOFT_DELETE',
				tableName: 'files',
				data: mockInstance.toJSON()
			};
		});

		it('should sync SOFT DELETE operation to sandbox', async () => {
			await FileSandboxSyncHook.afterDestroy(mockInstance, mockOptions);

			expect(mockDbSandbox.sequelize.query).toHaveBeenCalledWith(expect.stringContaining('UPDATE `files`'), {
				replacements: ['test-id'],
				type: 'UPDATE'
			});

			expect(mockLogger.info).toHaveBeenCalledWith(
				'[FileSandboxSync] Successfully synced SOFT DELETE operation to sandbox'
			);
		});

		it('should not sync if no syncInfo', async () => {
			mockOptions.fileSandboxSync = null;

			await FileSandboxSyncHook.afterDestroy(mockInstance, mockOptions);

			expect(mockDbSandbox.sequelize.query).not.toHaveBeenCalled();
		});
	});

	describe('[beforeUpdate]', () => {
		it('should capture UPDATE operation for files table', async () => {
			await FileSandboxSyncHook.beforeUpdate(mockInstance, mockOptions);

			expect(mockOptions.fileSandboxSync).toEqual({
				operation: 'UPDATE',
				tableName: 'files',
				data: mockInstance.toJSON(),
				previousData: mockInstance._previousDataValues
			});

			expect(mockLogger.info).toHaveBeenCalledWith('[FileSandboxSync] Capturing UPDATE operation for model File');
		});
	});

	describe('[afterUpdate]', () => {
		beforeEach(() => {
			mockOptions.fileSandboxSync = {
				operation: 'UPDATE',
				tableName: 'files',
				data: mockInstance.toJSON(),
				previousData: {}
			};
		});

		it('should sync UPDATE operation to sandbox', async () => {
			await FileSandboxSyncHook.afterUpdate(mockInstance, mockOptions);

			expect(mockDbSandbox.sequelize.query).toHaveBeenCalledWith(expect.stringContaining('UPDATE `files`'), {
				replacements: expect.arrayContaining(['test-id']),
				type: 'UPDATE'
			});

			expect(mockLogger.info).toHaveBeenCalledWith(
				'[FileSandboxSync] Successfully synced UPDATE operation to sandbox'
			);
		});
	});

	describe('[beforeBulkDestroy]', () => {
		it('should capture BULK_SOFT_DELETE operation for files table', async () => {
			const mockOptionsWithModel = {
				model: {
					tableName: 'files'
				},
				where: { id: 'test-id' }
			};

			await FileSandboxSyncHook.beforeBulkDestroy(mockOptionsWithModel);

			expect(mockOptionsWithModel.fileSandboxSync).toEqual({
				operation: 'BULK_SOFT_DELETE',
				tableName: 'files',
				where: { id: 'test-id' }
			});

			expect(mockLogger.info).toHaveBeenCalledWith(
				'[FileSandboxSync] Capturing BULK DELETE operation for table files',
				{ where: { id: 'test-id' } }
			);
		});

		it('should not capture operation for non-files table', async () => {
			const mockOptionsWithModel = {
				model: {
					tableName: 'users'
				},
				where: { id: 'test-id' }
			};

			await FileSandboxSyncHook.beforeBulkDestroy(mockOptionsWithModel);

			expect(mockOptionsWithModel.fileSandboxSync).toBeUndefined();
		});
	});

	describe('[afterBulkDestroy]', () => {
		it('should sync BULK SOFT DELETE operation to sandbox', async () => {
			const mockModel = {
				findAll: jest.fn().mockResolvedValue([{ id: 'test-id-1' }, { id: 'test-id-2' }])
			};

			const mockOptionsWithSync = {
				model: mockModel,
				fileSandboxSync: {
					operation: 'BULK_SOFT_DELETE',
					tableName: 'files',
					where: { id: 'test-id' }
				}
			};

			await FileSandboxSyncHook.afterBulkDestroy(mockOptionsWithSync);

			expect(mockModel.findAll).toHaveBeenCalledWith({
				where: { id: 'test-id' },
				paranoid: false,
				attributes: ['id']
			});

			expect(mockDbSandbox.sequelize.query).toHaveBeenCalledTimes(2);
			expect(mockLogger.info).toHaveBeenCalledWith(
				'[FileSandboxSync] Successfully synced BULK SOFT DELETE operation to sandbox for 2 files'
			);
		});

		it('should not sync if no instances found', async () => {
			const mockModel = {
				findAll: jest.fn().mockResolvedValue([])
			};

			const mockOptionsWithSync = {
				model: mockModel,
				fileSandboxSync: {
					operation: 'BULK_SOFT_DELETE',
					tableName: 'files',
					where: { id: 'test-id' }
				}
			};

			await FileSandboxSyncHook.afterBulkDestroy(mockOptionsWithSync);

			expect(mockDbSandbox.sequelize.query).not.toHaveBeenCalled();
		});
	});
});
