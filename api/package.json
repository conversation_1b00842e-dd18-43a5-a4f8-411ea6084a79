{"name": "kinebot-api", "version": "1.0.0", "main": "app.js", "repository": "https://github.com/pixfy/kinebot-api.git", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "type": "module", "engines": {"node": ">=14.15.0"}, "scripts": {"format": "prettier --write .", "format-check": "prettier --check .", "dev": "nodemon --inspect=0.0.0.0:9229 --experimental-modules ./src/bin/server.mjs | pino-pretty -c", "start": "node build/index.js", "start:prod": "node --experimental-modules ./src/bin/server.js", "test:unit:dev": "cross-env NODE_OPTIONS=--experimental-vm-modules jest --max_old_space_size=5120 --bail=true", "test:unit": "yarn test:unit:controllers && yarn test:unit:services && yarn test:unit:repositories && yarn test:unit:entities && yarn test:unit:helpers && yarn test:unit:jobs && yarn test:unit:mappers", "test:unit:coverage": "node --expose-gc --no-compilation-cache --experimental-vm-modules --max_old_space_size=6144 --optimize-for-size --gc-interval=100 node_modules/jest/bin/jest.js --logHeapUsage --runInBand --detectOpenHandles --coverage --coverageDirectory=./coverage --coverageReporters=\"json-summary\" --forceExit --bail /unit/", "test:unit:controllers": "node --expose-gc --no-compilation-cache --experimental-vm-modules --max_old_space_size=5120 node_modules/jest/bin/jest.js --logHeapUsage --runInBand --detectOpenHandles --verbose --silent /unit/controllers", "test:unit:services": "node --expose-gc --no-compilation-cache --experimental-vm-modules --max_old_space_size=5120 node_modules/jest/bin/jest.js --logHeapUsage --runInBand --detectOpenHandles --verbose --silent /unit/services", "test:unit:repositories": "node --expose-gc --no-compilation-cache --experimental-vm-modules --max_old_space_size=5120 node_modules/jest/bin/jest.js --logHeapUsage --runInBand --detectOpenHandles --verbose --silent /unit/repositories", "test:unit:entities": "node --expose-gc --no-compilation-cache --experimental-vm-modules --max_old_space_size=5120 node_modules/jest/bin/jest.js --logHeapUsage --runInBand --detectOpenHandles --verbose --silent /unit/entities", "test:unit:helpers": "node --expose-gc --no-compilation-cache --experimental-vm-modules --max_old_space_size=5120 node_modules/jest/bin/jest.js --logHeapUsage --runInBand --detectOpenHandles --verbose --silent /unit/helpers", "test:unit:jobs": "node --expose-gc --no-compilation-cache --experimental-vm-modules --max_old_space_size=5120 node_modules/jest/bin/jest.js --logHeapUsage --runInBand --detectOpenHandles --verbose --silent /unit/jobs", "test:unit:mappers": "node --expose-gc --no-compilation-cache --experimental-vm-modules --max_old_space_size=5120 node_modules/jest/bin/jest.js --logHeapUsage --runInBand --detectOpenHandles --verbose --silent /unit/mappers", "migration:stage": "cross-env NODE_ENV=migration_stage_john-deere npx sequelize db:migrate", "migration:production": "cross-env NODE_ENV=migration_production_john-deere npx sequelize db:migrate", "migration:dev": "cross-env NODE_ENV=migration_development npx sequelize db:migrate", "migration:sandbox": "cross-env NODE_ENV=migration_sandbox npx sequelize db:migrate", "rollback": "cross-env NODE_ENV=migration_development sequelize db:migrate:undo --name ", "rollback:from": "cross-env NODE_ENV=migration_development sequelize db:migrate:undo:all --to", "rollback:from:stage": "cross-env NODE_ENV=migration_stage_john-deere sequelize db:migrate:undo:all --to", "seed:rollback": "cross-env NODE_ENV=migration_development npx sequelize db:seed:undo --seed", "seed:all": "cross-env NODE_ENV=migration_development npx sequelize db:seed:all", "seed:all:stage": "cross-env NODE_ENV=migration_stage_john-deere npx sequelize db:seed:all", "seed:stage": "cross-env NODE_ENV=migration_stage npx sequelize db:seed:all", "seed:all:prod": "cross-env NODE_ENV=migration_production_john-deere npx sequelize db:seed:all", "seed:rollback:all": "cross-env NODE_ENV=migration_development npx sequelize db:seed:undo:all", "seed:create": "npx sequelize seed:generate --name ", "seed:unique": "cross-env NODE_ENV=migration_development npx sequelize db:seed --seed ", "sync:sandbox:dev": "cross-env NODE_ENV=migration_development node src/commands/sync-schema.js && cross-env NODE_ENV=migration_development node src/commands/transfer-data.js", "sync:sandbox:stage": "cross-env NODE_ENV=migration_stage_john-deere node src/commands/sync-schema.js && cross-env NODE_ENV=migration_stage_john-deere node src/commands/transfer-data.js", "sync:sandbox:production": "cross-env NODE_ENV=migration_production_john-deere node src/commands/sync-schema.js && cross-env NODE_ENV=migration_production_john-deere node src/commands/transfer-data.js", "test:transfer:bera": "node src/commands/test-transfer-bera.js", "test:transfer:custom": "node src/commands/test-transfer-custom.js", "test:transfer:pea": "node src/commands/test-transfer-pea.js", "test:transfer:superPea": "node src/commands/test-transfer-superPea.js", "test:transfer:libertyMutual": "node src/commands/test-transfer-libertyMutual.js", "test:transfer:niosh": "node src/commands/test-transfer-niosh.js", "test:transfer:backCompressive": "node src/commands/test-transfer-backCompressive.js", "test:transfer:angle": "node src/commands/test-transfer-angle.js", "test:transfer:kimMho": "node src/commands/test-transfer-kimMho.js", "test:transfer:kimPp": "node src/commands/test-transfer-kimPp.js", "test:transfer:reba": "node src/commands/test-transfer-reba.js", "test:transfer:recovery": "node src/commands/test-transfer-recovery.js", "test:transfer:strain": "node src/commands/test-transfer-strain.js", "test:transfer:sera": "node src/commands/test-transfer-sera.js", "test:transfer:actionPlan": "node src/commands/test-transfer-actionPlan.js"}, "dependencies": {"@aws-sdk/client-cloudwatch-logs": "^3.435.0", "@ladjs/country-language": "^1.0.2", "aws-sdk": "^2.676.0", "axios": "^1.7.9", "babel-register": "^6.26.0", "bcryptjs": "^2.4.3", "body-parser": "^1.19.0", "config": "^3.3.7", "cookie-session": "^2.0.0", "cors": "^2.8.5", "cross-env": "^7.0.3", "express": "^4.21.2", "express-acl": "^2.0.8", "express-rate-limit": "^6.7.0", "express-session": "^1.18.1", "express-validator": "^6.6.1", "fastest-validator": "^1.11.1", "handlebars": "^4.7.7", "helmet": "^4.1.0", "i18next": "^21.9.1", "i18next-fs-backend": "^1.1.5", "joi": "^17.4.0", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "macaddress": "^0.5.3", "moment": "^2.29.4", "multer": "^2.0.0", "mysql2": "^3.9.8", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "otplib": "^12.0.1", "passport": "^0.6.0", "passport-saml": "^3.2.4", "prettier": "^3.5.3", "puppeteer": "^13.4.0", "qrcode": "^1.5.3", "redis": "^3.1.2", "sequelize": "^6.35.0", "sequelize-cli": "^6.6.2", "socket.io": "^4.3.1", "sqs-consumer": "^5.4.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "util": "^0.12.4", "uuid": "^8.3.2", "winston": "^3.11.0", "winston-cloudwatch": "^6.2.0"}, "devDependencies": {"@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@faker-js/faker": "^7.6.0", "faker": "^4.1.0", "husky": "^4.3.0", "jest": "^29.5.0", "nodemon": "2.0.15", "pino-pretty": "^10.2.3", "sqlite3": "^5.1.6", "sucrase": "3.20.2", "supertest": "^6.3.3"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "husky": {"hooks": {"pre-commit": "bash hooks/pre-commit.sh", "pre-push": "git diff HEAD --quiet && yarn test:unit", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}}