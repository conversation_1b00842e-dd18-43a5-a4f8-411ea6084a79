const path = require('path');
require('babel-register');

const environment = process.env.NODE_ENV !== 'migration_sandbox' ? 'database' : 'sandbox';
const config_path = path.resolve('src', 'config', `${environment}.cjs`);

module.exports = {
	config: config_path,
	'models-path': path.resolve('src', 'app', 'models'),
	'seeders-path': path.resolve('src', 'database', 'seeders'),
	'migrations-path': path.resolve('src', 'database', 'migrations')
};
