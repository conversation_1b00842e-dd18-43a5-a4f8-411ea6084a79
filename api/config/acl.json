[{"group": "ADMIN", "permissions": [{"resource": "*", "methods": "*", "action": "allow"}]}, {"group": "USER", "permissions": [{"resource": "action_plan/*", "methods": "*", "action": "allow"}, {"resource": "2fa/*", "methods": "*", "action": "allow"}, {"resource": "dashboard/*", "methods": "*", "action": "allow"}, {"resource": "line/*", "methods": ["GET"], "action": "allow"}, {"resource": "workstation/*", "methods": ["GET"], "action": "allow"}, {"resource": "sector/mosaic/*", "methods": "*", "action": "allow"}, {"resource": "super-pea/*", "methods": "*", "action": "allow"}, {"resource": "organization/*", "methods": ["GET"], "action": "allow"}, {"resource": "company/*", "methods": ["GET"], "action": "allow"}, {"resource": "upload/*", "methods": "*", "action": "allow"}, {"resource": "ergonomic-tool/*", "methods": "*", "action": "allow"}, {"resource": "system-of-units/*", "methods": "*", "action": "allow"}, {"resource": "task/*", "methods": ["GET", "PUT", "POST"], "action": "allow"}, {"resource": "bera/*", "methods": ["GET", "PUT", "POST"], "action": "allow"}, {"resource": "sera/*", "methods": ["GET", "PUT", "POST"], "action": "allow"}, {"resource": "cycle/*", "methods": ["GET", "PUT", "POST"], "action": "allow"}, {"resource": "evaluator/*", "methods": ["GET", "PUT", "POST"], "action": "allow"}, {"resource": "activity/*", "methods": "*", "action": "allow"}, {"resource": "custom-report/*", "methods": ["GET", "PUT", "POST"], "action": "allow"}, {"resource": "worker-self-evaluation/*", "methods": ["GET", "PUT", "POST"], "action": "allow"}, {"resource": "plan/*", "methods": "*", "action": "allow"}, {"resource": "user/*", "methods": ["GET", "PUT"], "action": "allow"}, {"resource": "/customer/info", "methods": ["GET"], "action": "allow"}, {"resource": "recovery_report/*", "methods": "*", "action": "allow"}, {"resource": "customization/*", "methods": "*", "action": "allow"}, {"resource": "sector/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "/plan/checking-limits", "methods": ["POST"], "action": "allow"}, {"resource": "report/set_parameters", "methods": ["POST"], "action": "allow"}, {"resource": "auth/sign", "methods": ["post"], "action": "allow"}, {"resource": "organization/user_list", "methods": ["GET"], "action": "allow"}, {"resource": "/organization/", "methods": ["GET"], "action": "allow"}, {"resource": "user/create", "methods": ["POST"], "action": "deny"}, {"resource": "file/*", "methods": "*", "action": "allow"}, {"resource": "report/*", "methods": "*", "action": "allow"}, {"resource": "file/video_counter/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "file/duration_total/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "file/percent_risk/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "file/sector/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "file/risk_by_time/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "report/score_rula/:organization_id/:company_id/:upload_id", "methods": ["GET"], "action": "allow"}, {"resource": "report/score_rula/movement/file_data/:organization_id/:company_id/:upload_id", "methods": ["GET"], "action": "allow"}, {"resource": "report/score_rula/angle_time/:organization_id/:company_id/:upload_id", "methods": ["GET"], "action": "allow"}, {"resource": "report/extracted/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "company/:org/:cmp/sector", "methods": ["GET"], "action": "allow"}, {"resource": "company/sector/create", "methods": ["POST"], "action": "allow"}, {"resource": "sector/usage_check", "methods": ["POST"], "action": "allow"}, {"resource": "sector/total_critical/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "sector/total/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "sector/critical_sector/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "recovery_report/create", "methods": ["POST"], "action": "allow"}, {"resource": "recovery_report/:file_id/:type", "methods": ["GET"], "action": "allow"}, {"resource": "company/sector/:organization_id/:company_id/:id", "methods": ["DELETE"], "action": "allow"}, {"resource": "company/risk_status/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "company/list_user", "methods": ["POST"], "action": "allow"}, {"resource": "company/user_list/:organization_id", "methods": ["GET"], "action": "allow"}, {"resource": "user_access/*", "methods": "*", "action": "allow"}, {"resource": "user/change-password", "methods": ["PUT"], "action": "allow"}, {"resource": "niosh/*", "methods": ["GET"], "action": "allow"}, {"resource": "data-extraction/*", "methods": "*", "action": "allow"}]}, {"group": "MASTER", "permissions": [{"resource": "action_plan/*", "methods": "*", "action": "allow"}, {"resource": "2fa/*", "methods": "*", "action": "allow"}, {"resource": "dashboard/*", "methods": "*", "action": "allow"}, {"resource": "line/*", "methods": "*", "action": "allow"}, {"resource": "workstation/*", "methods": "*", "action": "allow"}, {"resource": "super-pea/*", "methods": "*", "action": "allow"}, {"resource": "organization/*", "methods": "*", "action": "allow"}, {"resource": "company/*", "methods": "*", "action": "allow"}, {"resource": "upload/*", "methods": "*", "action": "allow"}, {"resource": "ergonomic-tool/*", "methods": "*", "action": "allow"}, {"resource": "system-of-units/*", "methods": "*", "action": "allow"}, {"resource": "task/*", "methods": "*", "action": "allow"}, {"resource": "bera/*", "methods": "*", "action": "allow"}, {"resource": "sera/*", "methods": "*", "action": "allow"}, {"resource": "cycle/*", "methods": "*", "action": "allow"}, {"resource": "evaluator/*", "methods": "*", "action": "allow"}, {"resource": "activity/*", "methods": "*", "action": "allow"}, {"resource": "custom-report/*", "methods": "*", "action": "allow"}, {"resource": "worker-self-evaluation/*", "methods": "*", "action": "allow"}, {"resource": "user/*", "methods": ["GET", "PUT"], "action": "allow"}, {"resource": "plan/*", "methods": "*", "action": "allow"}, {"resource": "customer", "methods": "*", "action": "allow"}, {"resource": "recovery_report/*", "methods": "*", "action": "allow"}, {"resource": "customization/*", "methods": "*", "action": "allow"}, {"resource": "sector/*", "methods": "*", "action": "allow"}, {"resource": "sector/create", "methods": ["POST"], "action": "allow"}, {"resource": "customization/*", "methods": "*", "action": "allow"}, {"resource": "/organization/", "methods": ["GET"], "action": "allow"}, {"resource": "/customer/info", "methods": ["GET"], "action": "allow"}, {"resource": "/customer/update", "methods": ["PUT"], "action": "allow"}, {"resource": "/file/delete", "methods": ["POST"], "action": "allow"}, {"resource": "/plan/checking-limits", "methods": ["POST"], "action": "allow"}, {"resource": "/plan/calculate-difference", "methods": ["POST"], "action": "allow"}, {"resource": "/plan/renovate", "methods": ["POST"], "action": "allow"}, {"resource": "user_management/*", "methods": "*", "action": "allow"}, {"resource": "/plan/cancel", "methods": ["POST"], "action": "allow"}, {"resource": "/plan/user/update_creditcard", "methods": ["PUT"], "action": "allow"}, {"resource": "sector/usage_check", "methods": ["POST"], "action": "allow"}, {"resource": "company/sector/create", "methods": ["POST"], "action": "allow"}, {"resource": "report/*", "methods": "*", "action": "allow"}, {"resource": "company/:org/:cmp/sector", "methods": ["GET"], "action": "allow"}, {"resource": "company/sector/:organization_id/:company_id/:id", "methods": ["DELETE"], "action": "allow"}, {"resource": "company/risk_status/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "company/list_user", "methods": ["POST"], "action": "allow"}, {"resource": "recovery_report/create", "methods": ["POST"], "action": "allow"}, {"resource": "recovery_report/:file_id/:type", "methods": ["GET"], "action": "allow"}, {"resource": "sector/total_critical/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "report/extracted/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "sector/critical_sector/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "sector/total/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "sector/critical_sector/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "file/*", "methods": "*", "action": "allow"}, {"resource": "company/create", "methods": ["POST"], "action": "allow"}, {"resource": "company/risk_status/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "report/set_parameters", "methods": ["POST"], "action": "allow"}, {"resource": "user/*", "methods": "*", "action": "allow"}, {"resource": "organization/user_list", "methods": ["GET"], "action": "allow"}, {"resource": "organization/create", "methods": ["POST"], "action": "allow"}, {"resource": "company/user_list/:organization_id", "methods": ["GET"], "action": "allow"}, {"resource": "niosh/*", "methods": ["GET"], "action": "allow"}, {"resource": "sandbox/*", "methods": ["POST"], "action": "allow"}, {"resource": "data-extraction/*", "methods": "*", "action": "allow"}]}, {"group": "SUPERVISOR", "permissions": [{"resource": "action_plan/*", "methods": "*", "action": "allow"}, {"resource": "2fa/*", "methods": "*", "action": "allow"}, {"resource": "dashboard/*", "methods": "*", "action": "allow"}, {"resource": "line/*", "methods": "*", "action": "allow"}, {"resource": "workstation/*", "methods": "*", "action": "allow"}, {"resource": "super-pea/*", "methods": "*", "action": "allow"}, {"resource": "organization/*", "methods": ["GET"], "action": "allow"}, {"resource": "company/*", "methods": ["GET"], "action": "allow"}, {"resource": "user/*", "methods": ["GET", "PUT"], "action": "allow"}, {"resource": "customer", "methods": ["GET"], "action": "allow"}, {"resource": "/customer/info", "methods": ["GET"], "action": "allow"}, {"resource": "upload/*", "methods": "*", "action": "allow"}, {"resource": "ergonomic-tool/*", "methods": "*", "action": "allow"}, {"resource": "system-of-units/*", "methods": "*", "action": "allow"}, {"resource": "task/*", "methods": "*", "action": "allow"}, {"resource": "bera/*", "methods": "*", "action": "allow"}, {"resource": "sera/*", "methods": "*", "action": "allow"}, {"resource": "cycle/*", "methods": "*", "action": "allow"}, {"resource": "evaluator/*", "methods": "*", "action": "allow"}, {"resource": "activity/*", "methods": "*", "action": "allow"}, {"resource": "custom-report/*", "methods": "*", "action": "allow"}, {"resource": "worker-self-evaluation/*", "methods": "*", "action": "allow"}, {"resource": "plan/*", "methods": "*", "action": "allow"}, {"resource": "customization/*", "methods": "*", "action": "allow"}, {"resource": "recovery_report/*", "methods": "*", "action": "allow"}, {"resource": "sector/create", "methods": ["POST"], "action": "allow"}, {"resource": "sector/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "organization/*", "methods": ["GET"], "action": "allow"}, {"resource": "/plan/checking-limits", "methods": ["POST"], "action": "allow"}, {"resource": "sector/usage_check", "methods": ["POST"], "action": "allow"}, {"resource": "company/sector/create", "methods": ["POST"], "action": "allow"}, {"resource": "report/*", "methods": "*", "action": "allow"}, {"resource": "company/:org/:cmp/sector", "methods": ["GET"], "action": "allow"}, {"resource": "company/sector/:organization_id/:company_id/:id", "methods": ["DELETE"], "action": "allow"}, {"resource": "company/risk_status/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "company/list_user", "methods": ["POST"], "action": "allow"}, {"resource": "recovery_report/create", "methods": ["POST"], "action": "allow"}, {"resource": "recovery_report/:file_id/:type", "methods": ["GET"], "action": "allow"}, {"resource": "sector/total_critical/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "report/extracted/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "sector/critical_sector/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "sector/total/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "sector/critical_sector/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "file/*", "methods": "*", "action": "allow"}, {"resource": "company/risk_status/:organization_id/:company_id", "methods": ["GET"], "action": "allow"}, {"resource": "report/set_parameters", "methods": ["POST"], "action": "allow"}, {"resource": "organization/user_list", "methods": ["GET"], "action": "allow"}, {"resource": "company/user_list/:organization_id", "methods": ["GET"], "action": "allow"}, {"resource": "niosh/*", "methods": ["GET"], "action": "allow"}, {"resource": "sandbox/*", "methods": ["POST"], "action": "allow"}, {"resource": "data-extraction/*", "methods": "*", "action": "allow"}]}]