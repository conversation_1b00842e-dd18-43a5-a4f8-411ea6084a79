import { useMutation } from '@tanstack/react-query';
import { message } from 'antd';
import { I18n } from '@aws-amplify/core';
import { DataExtractionService } from '@/infra/services/data-extraction';

interface CreateDataExtractionCSVToDownloadParams {
    organization_id: string;
    company_id: string;
    data_extraction_id: string;
}

const Service = DataExtractionService.getInstance();

export const useCreateDataExtractionCSVToDownload = () => {
    return useMutation({
        mutationFn: async (params: CreateDataExtractionCSVToDownloadParams) => Service.createCSVToDownload(params),
        onSuccess: () => {
            message.success(I18n.get('Data extraction CSV to download created successfully'));
        },
        onError: () => {
            message.error(I18n.get('Error creating data extraction CSV to download'));
        }
    });
};
