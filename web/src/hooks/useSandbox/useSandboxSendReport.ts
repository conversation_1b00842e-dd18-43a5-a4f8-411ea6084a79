import { useMutation } from '@tanstack/react-query';
import { Service } from '@/infra/services/sandbox';
import { message } from 'antd';
import { I18n } from '@aws-amplify/core';

type SandboxSendReportParams = {
    report_type: string;
    id: string;
    company_id: string;
    organization_id: string;
};

export const useSandboxSendReport = () => {
    return useMutation((params: SandboxSendReportParams) => Service.sendReport(params), {
        onError: () => {
            message.error(I18n.get('Error on send report'));
        },
        onSuccess: () => {
            message.success(I18n.get('Report sent successfully'));
        }
    });
};