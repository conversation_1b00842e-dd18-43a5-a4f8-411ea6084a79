import { BaseService } from './base-service';
import { SandboxSendReportDTO } from '@/core/dto/sandbox';

class SandboxService extends BaseService<void> {
    constructor(public readonly basePath: string = '/sandbox') {
        super();
    }

    async sendReport(params: SandboxSendReportDTO): Promise<void> {
        await this.getInstance().post<void>(this.basePath + `/send-report?report_type=${params.report_type}&organization_id=${params.organization_id}&company_id=${params.company_id}&id=${params.id}`);
    }

    async logout(): Promise<void> {
        await this.getInstance().post<void>(this.basePath + '/logout');
    }
}

const Service = Object.freeze(new SandboxService());
export { Service };
