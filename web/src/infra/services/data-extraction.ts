import { BaseService } from './base-service';
import {
	GetDataExtractionRequestDTO,
	GetDataExtractionResponseDTO,
	CreateDataExtractionRequestDTO,
	CreateDataExtractionResponseDTO,
	UpdateDataExtractionRequestDTO,
	UpdateDataExtractionResponseDTO,
	CreateDataExtractionModelRequestDTO,
	CreateDataExtractionModelResponseDTO
} from '@/core/dto/data-extraction';

import { GetErgonomicToolListMapper } from '@/core/mapper/ergonomic-tool/get-ergonomic-tool-list';
import type {
	GetDataExtractionListRequestDTO,
	GetDataExtractionListResponseDTO,
	DeleteDataExtractionRequestDTO,
	DeleteDataExtractionResponseDTO,
	DeleteDataExtractionsRequestDTO,
	DeleteDataExtractionsResponseDTO,
	GetActionPlanColumnsRequestDTO,
	GetActionPlanColumnsResponseDTO,
	GetAllDataExtractionIdsResponseDTO,
	GetAllDataExtractionIdsRequestDTO
} from '@/core/dto/data-extraction';
import {
	CreateDataExtractionQueueRequestDTO,
	CreateDataExtractionQueueResponseDTO
} from '@/core/dto/data-extraction/create-data-extraction-queue';

export class DataExtractionService extends BaseService<CreateDataExtractionResponseDTO> {
	private static instance: DataExtractionService;

	private constructor(public readonly basePath: string = '/data-extraction') {
		super();
	}

	public static getInstance(): DataExtractionService {
		if (!DataExtractionService.instance) {
			DataExtractionService.instance = new DataExtractionService();
		}
		return DataExtractionService.instance;
	}

	public async create(params: CreateDataExtractionRequestDTO): Promise<CreateDataExtractionResponseDTO> {
		const url = this.basePath + '/';
		const { data } = await this.getInstance().post<CreateDataExtractionResponseDTO>(url, params);
		return data;
	}

	public async update(params: UpdateDataExtractionRequestDTO): Promise<UpdateDataExtractionResponseDTO> {
		const url = `${this.basePath}/${params.id}`;
		const { data } = await this.getInstance().put<UpdateDataExtractionResponseDTO>(url, params);
		return data;
	}

	public async findOne(params: GetDataExtractionRequestDTO): Promise<GetDataExtractionResponseDTO> {
		const { id } = params;
		const url = this.basePath + `/${id}`;
		const { data } = await this.getInstance().get<GetDataExtractionResponseDTO>(url, { params });
		return data;
	}

	public async destroy(params: DeleteDataExtractionRequestDTO): Promise<DeleteDataExtractionResponseDTO> {
		const url = `${this.basePath}/${params.id}`;
		const { data } = await this.getInstance().delete<DeleteDataExtractionResponseDTO>(url);
		return data;
	}

	public async findAll(parameters: GetDataExtractionListRequestDTO): Promise<GetDataExtractionListResponseDTO> {
		const url = this.basePath + '/';
		const params = GetErgonomicToolListMapper.toDTO(parameters);
		const { data } = await this.getInstance().get<GetDataExtractionListResponseDTO>(url, { params });
		return data;
	}

	public async deleteDataExtractions(
		params: DeleteDataExtractionsRequestDTO
	): Promise<DeleteDataExtractionsResponseDTO> {
		const url = `${this.basePath}`;
		const { data } = await this.getInstance().delete<DeleteDataExtractionsResponseDTO>(url, {
			data: params
		});
		return data;
	}

	public async getAllExtractionIds(
		params: GetAllDataExtractionIdsRequestDTO
	): Promise<GetAllDataExtractionIdsResponseDTO> {
		const url = `${this.basePath}/ids`;
		const { data } = await this.getInstance().get<GetAllDataExtractionIdsResponseDTO>(url, { params });
		return data;
	}

	public async getActionPlanColumns(
		params: GetActionPlanColumnsRequestDTO
	): Promise<GetActionPlanColumnsResponseDTO> {
		const url = `${this.basePath}/${params.data_extraction_id}/action-plan-columns`;
		const { data } = await this.getInstance().get<GetActionPlanColumnsResponseDTO>(url);
		return data;
	}

	public async createModel(
		params: CreateDataExtractionModelRequestDTO
	): Promise<CreateDataExtractionModelResponseDTO> {
		const url = `${this.basePath}/models`;
		const { data } = await this.getInstance().post<CreateDataExtractionModelResponseDTO>(url, params);
		return data;
	}

	public async createDataExtractionQueue(
		params: CreateDataExtractionQueueRequestDTO
	): Promise<CreateDataExtractionQueueResponseDTO> {
		const url = `${this.basePath}/${params.data_extraction_id}/send-to-queue`;
		const { data } = await this.getInstance().post<CreateDataExtractionQueueResponseDTO>(url, params);
		return data;
	}

	public async createCSVToDownload(
		params: CreateDataExtractionCSVToDownloadRequestDTO
	): Promise<CreateDataExtractionCSVToDownloadResponseDTO> {
		const url = `${this.basePath}/${params.data_extraction_id}/download-file`;
		const { data } = await this.getInstance().post<CreateDataExtractionCSVToDownloadResponseDTO>(url, params);
		return data;
	}
}
