import styled from 'styled-components';
import { Layout } from 'antd';
export const ChildrenContainer = styled.div`
	background: #fff;
	position: relative;
	margin-top: 15px;
	min-height: 70vh;
	padding: 10px;
`;

export const ContainerChildren = styled.div`
	position: relative;
	margin-top: 15px;
	min-height: 70vh;
	padding: 10px;
`;

export const SiderMenuContainer = styled.div`
	display: flex;
	flex-direction: column;
	background-color: #fff;
	height: calc(100% - 80px);
	position: relative;
	margin-bottom: 50px;
`;

export const SiderMenuContainerDivider = styled.div`
	flex: 1;
	height: 95%;
	overflow-y: auto;
`;

export const SiderMenuContainerDividerSandbox = styled.div`
	margin-top: auto;
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 10px;
	height: 5%;
`;

export const LayoutContent = styled(Layout)`
	transition: all 0.2s;
	margin-top: 80px;
	min-height: 100vh;
	height: 100%;
`;

export const CustomSider = styled(Layout.Sider)`
	position: fixed;
	height: 100vh;
	overflow: auto;
	margin-top: 75px;
	transition: all 0.2s;
`;