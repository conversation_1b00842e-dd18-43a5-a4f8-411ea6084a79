import React from 'react';
import { Col } from 'antd';
import { Logo } from '../Logo';
import { Popover } from '@/components/Layout/Popover';
import { useApplicationContext } from '@/context/v1/Application/context';
import { SelectProject } from '@/components/SelectProject';
import { ProjectSelect } from '@/components/Layout/ProjectSelect';
import { Container, ContainerRow, ContainerDiv, ContainerText } from './styles';

export const Header: React.FC = () => {
	const { isSandboxEnabled } = useApplicationContext();

	const styles = {
		border: 'none',
		color: isSandboxEnabled ? 'white' : '',
		backgroundColor: isSandboxEnabled ? '#002FBB' : ''
	};

	return (
		<Container style={styles}>
			<ContainerRow gutter={14} align="middle">
				<ContainerDiv>
					<Col>
						<Logo />
					</Col>
				</ContainerDiv>
				<ContainerDiv>
					{isSandboxEnabled && (
						<Col>
							<ContainerText> Modo Sandbox </ContainerText>
						</Col>
					)}
				</ContainerDiv>
				<ContainerDiv>
					<Col>
						<ProjectSelect />
					</Col>
					<Col>
						<Popover />
					</Col>
					<Col>
						<SelectProject />
					</Col>
				</ContainerDiv>
			</ContainerRow>
		</Container>
	);
};
