import { Layout, Row } from 'antd';
import styled from 'styled-components';

export const Container = styled(Layout.Header)`
	padding: 0;
	background: #fff;
	position: fixed;
	width: 100%;
	z-index: 1000;
	height: 80px;
	transition: background 0.3s, width 0.3s cubic-bezier(0.2, 0, 0, 1) 0s;
`;

export const ContainerRow = styled(Row)`
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 100%;
`;

export const ContainerDiv = styled.div`
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
`;

export const ContainerText = styled.p`
	font-size: 31.25px;
	font-weight: 600;
	margin-bottom: 0;
`;
