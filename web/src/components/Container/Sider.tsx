import React from 'react';
import { useApplicationContext } from '@/context/v1/Application/context';
import { CustomSider } from './styles';

interface SiderProps {
	children: React.ReactNode;
}

export function Sider({ children }: Readonly<SiderProps>) {
	const { siderCollapsed, handleSiderCollapse } = useApplicationContext();


	return (
		<CustomSider
			id="sider"
			width={250}
			collapsible
			theme="light"
			collapsed={siderCollapsed}
			onCollapse={handleSiderCollapse}
		>
			{children}
		</CustomSider>
	);
};
