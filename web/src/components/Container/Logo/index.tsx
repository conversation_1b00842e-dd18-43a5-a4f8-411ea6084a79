import React from 'react';
import { Img } from './styles';
import logo from '@/assets/img/logo.svg';
import logoSandbox from '@/assets/img/logo_white.svg';
import { useApplicationContext } from '@/context/v1/Application/context';

export const URL_LOGO = logo;
export const URL_LOGO_SANDBOX = logoSandbox;

export const Logo: React.FC = () => {
	const { isSandboxEnabled } = useApplicationContext();

	return (
		<Img
			data-testid="container-logo-id"
			preview={{ visible: false, mask: false }}
			src={isSandboxEnabled ? URL_LOGO_SANDBOX : URL_LOGO}
			style={{ width: '180px' }}
		/>
	);
};
