import React, { useCallback, useLayoutEffect, useEffect, useState } from 'react';
import { I18n } from '@aws-amplify/core';
import { useSelector, useDispatch } from 'react-redux';
import { Layout, notification, Switch, Tooltip } from 'antd';

import { Header } from './Header';
import { Content } from './Content';
import { MenuOptions } from './Menu';
import {
	SiderMenuContainer,
	SiderMenuContainerDivider,
	SiderMenuContainerDividerSandbox,
	LayoutContent
} from './styles';

import Api from '@/services/api';
import { Sider } from './Sider';
import { setPlanDetails } from '@/redux/plan/actions';
import { ModalSandbox } from '@/components/ui/Modals/ModalSandbox';
import { useApplicationContext } from '@/context/v1/Application/context';

interface ContainerProps {
	children: () => JSX.Element;
}

const MODAL_MESSAGE_EXIT_ENVIRONMENT = {
	production: I18n.get('You are activating sandbox mode. From now on, no actions will be recorded in the official environment - use the area for simulations.')
	,
	sandbox: I18n.get('You are exiting sandbox mode. All actions that have not been applied will be lost.')
};

export function Container({ children }: Readonly<ContainerProps>) {
	const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

	const dispatch = useDispatch();
	const isExpired = useSelector((state: any) => state.plan.expired);
	const isCanceled = useSelector((state: any) => state.plan.canceled);
	const { isSandboxEnabled, siderCollapsed, handleSiderCollapse, toggleSandbox } = useApplicationContext();

	const getPlan = useCallback(() => {
		Api.get('/plan/customer/details')
			.then((res) => {
				dispatch(setPlanDetails(res.data));
			})
			.catch((err) => {
				console.log(err, 'data');
			});
	}, [dispatch]);

	const alertAboutPlan = useCallback(() => {
		if (isCanceled) {
			const message = I18n.get('Your plan has been canceled');
			const description = I18n.get('Upgrade your plan and continue using one platform');
			notification.error({ message, description });
		}

		if (isExpired) {
			const message = I18n.get('Your plan has expired');
			const description = I18n.get('Renew it to continue using');
			notification.warning({ message, description });
		}
	}, [isExpired, isCanceled]);

	useLayoutEffect(() => {
		getPlan();
	}, [getPlan]);

	useEffect(() => {
		alertAboutPlan();
		handleSiderCollapse(window.innerWidth < 1200);
	}, [alertAboutPlan]);

	function handleOpenModal() {
		const message = isSandboxEnabled
			? MODAL_MESSAGE_EXIT_ENVIRONMENT.sandbox
			: MODAL_MESSAGE_EXIT_ENVIRONMENT.production;
		const primaryButton = {
			style: {
				backgroundColor: isSandboxEnabled ? '#FAEBEC' : '#2f54eb',
				border: isSandboxEnabled ? '1px solid #D13342' : '1px solid #2f54eb',
				color: isSandboxEnabled ? '#D13342' : '#fff'
			},
			text: isSandboxEnabled ? I18n.get('Exit without applying') : I18n.get('Continue')
		};

		const secondaryButton = {
			style: {
				border: '1px solid #e6e6e6'
			},
			text: I18n.get('Cancel')
		};

		const onCancel = () => {
			setIsModalOpen(false);
		};

		const onConfirm = () => {
			toggleSandbox(!isSandboxEnabled);
			setIsModalOpen(false);
		};
		return (
			<ModalSandbox
				open={isModalOpen}
				onCancel={onCancel}
				onConfirm={onConfirm}
				primaryButton={primaryButton}
				secondaryButton={secondaryButton}
			>
				{message}
			</ModalSandbox>
		);
	}

	function handleSandboxMode() {
		setIsModalOpen(true);
	}

	const tooltipMessage = isSandboxEnabled ? I18n.get('To production') : I18n.get('To sandbox');

	return (
		<>
			<Header />
			<Layout style={{ minHeight: 'calc(100vh - 80px)' }}>
				<Sider>
					<SiderMenuContainer>
						<SiderMenuContainerDivider>
							<MenuOptions />
						</SiderMenuContainerDivider>
						<SiderMenuContainerDividerSandbox>
							<Tooltip title={tooltipMessage}>
								<Switch checked={isSandboxEnabled} onChange={() => handleSandboxMode()} />{' '}
								{!siderCollapsed && 'Sandbox'}
							</Tooltip>
						</SiderMenuContainerDividerSandbox>
					</SiderMenuContainer>
				</Sider>
				<LayoutContent style={{ marginLeft: siderCollapsed ? '50px' : '250px' }}>
					<Content>{children}</Content>
				</LayoutContent>
			</Layout>
			{handleOpenModal()}
		</>
	);
}
