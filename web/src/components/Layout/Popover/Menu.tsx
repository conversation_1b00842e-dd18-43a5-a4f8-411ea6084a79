import React from 'react';
import { Menu } from 'antd';
import { I18n } from '@aws-amplify/core';
import { LogoutOutlined, InfoCircleOutlined } from '@ant-design/icons';

import { AvatarUser } from './AvatarUser';
import { useApplicationContext } from '@/context/v1/Application/context';
import { CustomMenu } from './styles';

interface MenuPopoverProps {
	onLogout: () => void;
	onMyAccount: () => void;
}

export function MenuPopover({ onMyAccount, onLogout }: Readonly<MenuPopoverProps>) {
	const { isSandboxEnabled } = useApplicationContext();

	const styles = {
		color: isSandboxEnabled ? 'white' : '',
		backgroundColor: isSandboxEnabled ? '#002FBB' : ''
	};

	return (
		<CustomMenu key="user" mode="horizontal" style={styles}>
			<Menu.SubMenu title={<AvatarUser size="large" />}>
				<Menu.Item key="account" icon={<InfoCircleOutlined />} onClick={onMyAccount}>
					{I18n.get('My account')}
				</Menu.Item>
				<Menu.Item key="logout" icon={<LogoutOutlined />} onClick={onLogout}>
					{I18n.get('Logout')}
				</Menu.Item>
			</Menu.SubMenu>
		</CustomMenu>
	);
}
