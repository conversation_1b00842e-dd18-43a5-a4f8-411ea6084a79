import React, { useState } from 'react';
import { Row, Col, Divider } from 'antd';

import { useLibertyMutualSubStepsContext } from '../context';
import { ReportInformation } from './ReportInformation';
import { Text, Title } from '@/components/Typography';
import { ResultsContainer } from './styles';
import { DownloadPDF } from './DownloadPDF';
import { Summary } from './Summary';
import { Header } from './Header';
import { SendReportModal } from '@/components/ui/Modals/SendReportModal';
import { useApplicationContext } from '@/context/v1/Application/context';
import { SendReportButton } from '@/components/ui/Buttons/SendReportButton';
import { SendReportModalProps, Report } from '@/components/ui/Modals/SendReportModal/types';
import type { SendReportRequest } from '@/types/Sandbox';

export function Result() {
	const [isEditing, setIsEditing] = useState<boolean>(false);
	const [isSendReportModalOpen, setIsSendReportModalOpen] = useState<boolean>(false);

	const { libertyMutualResult } = useLibertyMutualSubStepsContext();
	const { organization, company, isSandboxEnabled, sendReport } = useApplicationContext();

	if (!libertyMutualResult?.id) {
		return (
			<Row justify="center">
				<Col>
					<Text>Oops... Something went wrong!</Text>
				</Col>
			</Row>
		);
	}

	function handleDisableFormEdition(editing: boolean) {
		setIsEditing(editing);
	}

	function handleSendReport(): void {
		const request: SendReportRequest = {
			report_type: 'LIBERTY_MUTUAL',
			id: libertyMutualResult.id || '',
			company_id: company?.id,
			organization_id: organization?.id
		};
		setIsSendReportModalOpen(false);
		sendReport(request, '/custom-reports');
	}

	function mountSendReportModalProps(): SendReportModalProps {
		const report: Report = {
			id: libertyMutualResult.id || '',
			workstation_name: libertyMutualResult.informations.workstation_name,
			tools_to_show: [],
			action_plans: [],
			files_names: [libertyMutualResult.informations.original_name]
		};
		return {
			isOpen: isSendReportModalOpen,
			onCancel: () => setIsSendReportModalOpen(false),
			onConfirm: handleSendReport,
			report: report
		};
	}

	return (
		<Row justify="center">
			<Col span={24} style={{ marginBottom: '2rem', padding: '0 1rem' }}>
				<Title level={4}>Result jjjj</Title>
				{isSandboxEnabled && <SendReportButton onClick={() => setIsSendReportModalOpen(true)} />}
			</Col>
			<Col style={{ padding: '1rem' }}>
				<ResultsContainer>
					<Header />
					<ReportInformation />
					<Divider type="horizontal" />
					<Summary />
					<Divider type="horizontal" />
					<DownloadPDF onDownloadPDF={handleDisableFormEdition} disableDownload={isEditing} />
				</ResultsContainer>
			</Col>
			{isSandboxEnabled && <SendReportModal {...mountSendReportModalProps()} />}
		</Row>
	);
}
