import React from 'react';
import { I18n } from '@aws-amplify/core';

import { DetailsTable } from './DetailsTable/DetailsTable';
import { Conclusion } from './Conclusion';
import { ReportTemplate } from '@/components/views/Report/ReportTemplate';

export const Result = ({
	file,
	report,
	sectors,
	formatReport,
	onDownloadPDF,
	isLoadingPDF,
	onChangeComment
}) => {
	const formattedReport = formatReport(report.data);

	const sections = [
		{
			title: I18n.get('Conclusion'),
			component: <Conclusion formattedReport={formattedReport} />
		},
		{
			title: I18n.get('Details'),
			component: <DetailsTable formattedReport={formattedReport} />
		}
	];

	const isLoading = report?.isLoading || sectors?.isLoading || !formattedReport;
	const selectedSector = sectors.data.find((sector) => sector.id === file.data.sector_id);

	return (
		<ReportTemplate
			title={I18n.get('Report Results - Revised Strain Index')}
			sections={sections}
			sector={selectedSector}
			fileData={file.data}
			reportData={report.data}
			isLoading={isLoading}
			onDownloadPDF={onDownloadPDF}
			isLoadingPDF={isLoadingPDF}
			onChangeComment={onChangeComment}
			reportType='STRAIN_INDEX'
		/>
	);
};
