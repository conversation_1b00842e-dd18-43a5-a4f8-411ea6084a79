import React, { useState } from 'react';
import { Row, Col, Divider } from 'antd';

import { Text, Title } from '@/components/Typography';
import { ReportInformation } from './ReportInformation';
import { Header } from '../../../ReportTemplate/Header';
import { DownloadPDF } from './DownloadPDF';
import { ResultForce } from './ResultForce';
import { Summary } from './Summary';
import { Comment } from './Comment';

import { useBackCompressiveForceEstimationContext } from '../../context';
import { SendReportModal } from '@/components/ui/Modals/SendReportModal';
import { useApplicationContext } from '@/context/v1/Application/context';
import { SendReportButton } from '@/components/ui/Buttons/SendReportButton';
import { SendReportModalProps, Report } from '@/components/ui/Modals/SendReportModal/types';
import type { SendReportRequest } from '@/types/Sandbox';
import { ResultsContainer } from './styles';

export function Result() {
	const [isEditing, setIsEditing] = useState<boolean>(false);
	const [isSendReportModalOpen, setIsSendReportModalOpen] = useState<boolean>(false);

	const { organization, company, isSandboxEnabled, sendReport } = useApplicationContext();
	const { backCompressiveForceEstimationResult } = useBackCompressiveForceEstimationContext();

	if (!backCompressiveForceEstimationResult?.id) {
		return (
			<Row justify="center">
				<Col>
					<Text>Oops... Something went wrong!</Text>
				</Col>
			</Row>
		);
	}

	function handleDisableFormEdition(editing: boolean): void {
		setIsEditing(editing);
	}

	function handleSendReport(): void {
		const request: SendReportRequest = {
			report_type: 'BACK_COMPRESSIVE',
			id: backCompressiveForceEstimationResult.id || '',
			company_id: company?.id,
			organization_id: organization?.id
		};
		setIsSendReportModalOpen(false);
		sendReport(request, '/reporting');
	}

	function mountSendReportModalProps(): SendReportModalProps {
		const report: Report = {
			id: backCompressiveForceEstimationResult.id || '',
			workstation_name: backCompressiveForceEstimationResult.informations.workstation_name,
			tools_to_show: [],
			action_plans: [],
			files_names: [backCompressiveForceEstimationResult.informations.original_name]
		};
		return {
			isOpen: isSendReportModalOpen,
			onCancel: () => setIsSendReportModalOpen(false),
			onConfirm: handleSendReport,
			report: report
		};
	}

	return (
		<Row justify="center">
			<Col
				span={24}
				style={{ marginBottom: '2rem', padding: '0 1rem', display: 'flex', justifyContent: 'space-between' }}
			>
				<Title level={4}>Result</Title>
				{isSandboxEnabled && <SendReportButton onClick={() => setIsSendReportModalOpen(true)} />}
			</Col>
			<Col style={{ padding: '1rem' }}>
				<ResultsContainer>
					<Header title="Back compressive force estimation" />
					<ReportInformation />
					<Divider type="horizontal" />
					<ResultForce />
					<Divider type="horizontal" />
					<Summary />
					<Divider type="horizontal" />
					<Comment onCommentChange={handleDisableFormEdition} disableEditComment={isEditing} />
					<DownloadPDF onDownloadPDF={handleDisableFormEdition} disableDownload={isEditing} />
				</ResultsContainer>
			</Col>
			{isSandboxEnabled && <SendReportModal {...mountSendReportModalProps()} />}
		</Row>
	);
}
