import React, { useState } from 'react';
import moment from 'moment';
import { I18n } from '@aws-amplify/core';
import { Row, Col, Checkbox, Space, Tooltip } from 'antd';

import { Text } from '@/components/Typography';
import { Actions } from '../List/Actions/Actions';

import { Filter } from '../../Filter';
import { Container } from './Container';
import { FloatingBar } from '../List/SelectionBar';
import { PaginationCustom } from '@/components/ui/Pagination';
import { useDataExtractionContext } from '@/context/DataExtraction';
import { useDataExtractionTable } from '../hooks/useDataExtractionTable';
import { useGetDataExtractionList } from '@/hooks/useGetDataExtractionList';
import { FormFields, DataExtractionListItem } from '@/types/entities/DataExtraction';
import { DestroyDataExtractionModal } from '@/components/Settings/DataExtraction/components/Modal';
import type { TableRowSelection } from 'antd/es/table/interface';
import { TableWrapper, TruncatedText } from './styles';

type TableDataItem = DataExtractionListItem & { key: string };

export function TableList() {
	const [filters] = useState<FormFields>({});
	const { filter } = useDataExtractionContext();
	const { isLoading: isLoadingList, data } = useGetDataExtractionList(filter);

	const {
		isLoading,
		tableData,
		changePage,
		changeLimit,
		deleteData,
		modalOpen,
		handleDeleteAllAcrossPages,
		handleConfirmDelete,
		closeModal,
		selectedIds,
		selectedRowKeys,
		isAllSelected,
		isIndeterminate,
		handleMainCheckboxChange,
		handleRowCheckboxChange,
		totalSelectedCount,
		isSelectAllMode,
		triggerClearSelection
	} = useDataExtractionTable({ filters });

	function handleRowSelect(record: TableDataItem, selected: boolean) {
		handleRowCheckboxChange(record.id, selected);
	}

	function handleSelectAll(
		selected: boolean,
		_selectedRows?: readonly TableDataItem[],
		changeRows?: readonly TableDataItem[]
	) {
		if (changeRows) {
			const pageRowIds = changeRows.map((row) => row.id);
			handleRowCheckboxChange(pageRowIds.join(','), selected);
		}
	}

	// function handleDeleteItem(item: TableDataItem) {
	// 	handleDelete([item]);
	// }

	function getRowClassName(record: TableDataItem) {
		if (!record || typeof record.id !== 'string') return '';
		return selectedIds.includes(record.id) ? 'selected-row' : '';
	}

	const columns: any = [
		{
			key: 'name',
			dataIndex: 'extraction_name',
			title: <Text strong>{I18n.get('Name')}</Text>,
			ellipsis: true,
			align: 'center'
		},
		{
			key: 'content',
			dataIndex: 'content',
			title: <Text strong>{I18n.get('Content')}</Text>,
			ellipsis: true,
			align: 'center',
			responsive: ['lg'],
			render: (text: string[]) => <TruncatedText>{text.join(', ')}</TruncatedText>
		},
		{
			key: 'createdAt',
			dataIndex: 'created_at',
			title: <Text strong>{I18n.get('Date')}</Text>,
			ellipsis: true,
			align: 'center',
			responsive: ['lg'],
			render: (text: string) => (text ? moment(text).format('L') : '--/--/----')
		},
		{
			key: 'author',
			dataIndex: 'author_name',
			title: <Text strong>{I18n.get('Author')}</Text>,
			ellipsis: true,
			align: 'center',
			responsive: ['lg'],
			render: (text: string) => text ?? '-'
		},
		{
			key: 'status',
			title: <Text strong>{I18n.get('Status')}</Text>,
			dataIndex: 'status',
			ellipsis: true,
			align: 'center',
			responsive: ['lg'],
			render: (text: string) => text ?? '-'
		},
		{
			key: 'actions',
			title: <Text strong>{I18n.get('Actions')}</Text>,
			align: 'center',
			width: 160,
			render: (_: any, row: TableDataItem) => <Actions rowData={row} onDeleteActionClick={() => {}} />
		}
	];

	const rowSelection: TableRowSelection<TableDataItem> = {
		selectedRowKeys,
		onSelect: handleRowSelect,
		onSelectAll: handleSelectAll,
		columnWidth: 60,
		columnTitle: (
			<Space onClick={(e) => e.stopPropagation()}>
				<Tooltip title={isAllSelected ? I18n.get('Unselect all') : I18n.get('Select all')}>
					<Checkbox
						checked={isAllSelected}
						indeterminate={isIndeterminate}
						onChange={handleMainCheckboxChange}
					/>
				</Tooltip>
			</Space>
		)
	};

	return (
		<>
			<Row gutter={[0, 16]}>
				<Col span={24}>
					<Filter />
				</Col>
				<Col span={24}>
					<Row justify="center">
						<Container>
							<TableWrapper
								rowKey="key"
								columns={columns}
								pagination={false}
								loading={isLoadingList}
								rowSelection={rowSelection}
								rowClassName={getRowClassName}
								dataSource={data?.rows || ([] as any[])}
							/>
							<PaginationCustom
								loading={isLoading}
								maxPage={tableData.limit}
								page={tableData.page}
								setMaxPage={changeLimit}
								setPage={changePage}
								total={tableData.total}
							/>
						</Container>
					</Row>
					<FloatingBar
						totalSelectedCount={totalSelectedCount}
						isSelectAllMode={isSelectAllMode}
						onDeleteAllAcrossPages={handleDeleteAllAcrossPages}
						onClearSelection={triggerClearSelection}
					/>
				</Col>
			</Row>
			<DestroyDataExtractionModal
				open={modalOpen}
				onCancel={closeModal}
				isDeleteAll={deleteData?.all}
				extractions={deleteData?.items}
				onSuccess={handleConfirmDelete}
			/>
		</>
	);
}
