import React from 'react';
import { ModalStyled, PrimaryButton, SecondaryButton, Footer, Header, Body } from './styles';
import dangerIcon from '@/assets/icons/danger.svg';
import { Image } from 'antd';

interface ModalProps {
    open: boolean;
    onCancel: () => void;
    onConfirm: () => void;
    children: React.ReactNode;
    primaryButton?: {
        style: React.CSSProperties,
        text: string;
    };
    secondaryButton?: {
        style: React.CSSProperties,
        text: string;
    };
}

const DANGER_ICON = dangerIcon;

export function ModalSandbox({ open, onCancel, onConfirm, children, primaryButton, secondaryButton }: ModalProps) {
    return (
        <ModalStyled open={open} onCancel={onCancel} onOk={onConfirm} footer={null} style={{ alignSelf: 'center' }}>
            <Header>
                <Image src={DANGER_ICON} alt="danger" />
            </Header>
            <Body>
                {children}
            </Body>
            <Footer>
                <SecondaryButton onClick={onCancel} style={secondaryButton?.style}>{secondaryButton?.text}</SecondaryButton>
                <PrimaryButton onClick={onConfirm} style={primaryButton?.style}>{primaryButton?.text}</PrimaryButton>
            </Footer>
        </ModalStyled>
    );
}