import React from 'react';
import { ModalSandbox } from "@/components/ui/Modals/ModalSandbox";
import { SendReportModalProps } from './types';
import { I18n } from '@aws-amplify/core';

const PrimaryButton = {
    style: {
        backgroundColor: '#2F54EB',
        color: '#ffffff'
    },
    text: I18n.get('Apply_all')
};

const SecondaryButton = {
    style: {
        backgroundColor: '#ffffff',
        color: '#000000'
    },
    text: I18n.get('Cancel')
};

const TOOLS_TO_SHOW_MAPPER: Record<string, string> = {
    kim_pp: "Push and pull (KIM PP)",
    back_compressive_force_estimation: "Back compressive force estimation",
    rula: "Rapid Upper Limb Assessment",
    kim_mho: "Manual Handling (KIM MHO)",
    reba: "Rapid Entire Body Assessment",
    niosh: "Manual lifting (NIOSH)",
    liberty_mutual: "Material handling",
    angle_time: "Angle by time",
    strain_index: "Strain index"
};

function BulletItem({ label, value }: { label: string; value: string }) {
    return (
        <div style={{ display: 'flex', alignItems: 'flex-start', gap: '8px', width: '100%' }}>
            <span style={{ fontSize: '20px', lineHeight: '1' }}>•</span>
            <div style={{ display: 'flex', flexWrap: 'nowrap', alignItems: 'baseline', width: '100%', gap: '4px' }}>
                <strong style={{ whiteSpace: 'nowrap' }}>{label}</strong>
                <span style={{ wordBreak: 'break-word', minWidth: 0, flexShrink: 1 }}>
                    {value}
                </span>
            </div>
        </div>
    );
}


function showNewReports(tools: string[]) {
    return tools.map((tool, index) => (
        <BulletItem
            key={index}
            label={`${I18n.get('New report')}:`}
            value={TOOLS_TO_SHOW_MAPPER[tool] || tool}
        />
    ));
}

function showActionPlans(action_plans: any[]) {
    return action_plans.map((action_plan, index) => (
        <BulletItem
            key={index}
            label={`${I18n.get('Action plan')}:`}
            value={action_plan.title}
        />
    ));
}

function showFilesNames(files_names: string[]) {
    return files_names.map((file_name, index) => (
        <BulletItem
            key={index}
            label={`${I18n.get('Video upload')}:`}
            value={file_name}
        />
    ));
}

export function SendReportModal({ isOpen, onCancel, onConfirm, report }: SendReportModalProps) {
    return (
        <ModalSandbox
            open={isOpen}
            onCancel={onCancel}
            onConfirm={onConfirm}
            primaryButton={PrimaryButton}
            secondaryButton={SecondaryButton}
        >
            <div style={{ marginTop: '20px', textAlign: 'center', fontSize: '18px' }}>
                {I18n.get('For the application of the report in the official environment, the following items will also be applied')}
            </div>

            <div
                style={{
                    marginTop: '40px',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '10px',
                    alignItems: 'flex-start',
                    width: '100%'
                }}
            >
                {report?.files_names && showFilesNames(report.files_names)}
                {report?.workstation_name && (
                    <BulletItem
                        label={`${I18n.get('Workstation')}:`}
                        value={report.workstation_name}
                    />
                )}
                {report?.action_plans && showActionPlans(report.action_plans)}
                {report?.tools_to_show && report.tools_to_show.length > 0 && showNewReports(report.tools_to_show)}
            </div>
        </ModalSandbox>
    );
}
