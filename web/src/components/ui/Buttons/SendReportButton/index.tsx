import { I18n } from "@aws-amplify/core";
import React from "react";
import { SandboxButton } from "../CustomButton/styles";
import { CheckCircleOutlined } from "@ant-design/icons";

type SendReportButtonProps = {
    onClick: () => void;
}

export function SendReportButton({ onClick }: SendReportButtonProps) {
    return (
        <SandboxButton style={{ height: "40px", fontSize: "16px", display: "flex", alignItems: "center", justifyContent: "center" }} type="primary" onClick={onClick}>
            <CheckCircleOutlined style={{ fontSize: "16px", paddingTop: "2=5px" }} />
            <span>{I18n.get('Apply report in the official environment')}</span>
        </SandboxButton>
    );
}