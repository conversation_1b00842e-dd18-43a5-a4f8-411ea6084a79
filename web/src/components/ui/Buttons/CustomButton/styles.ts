import { Button } from 'antd';
import styled from 'styled-components';

export const CustomButton = styled(Button)`
	border-radius: 5px;
	background-color: ${(props) => (props.type === 'primary' ? '#2F54EB' : null)};
`;

export const SandboxButton = styled(Button)`
	border-radius: 5px;
	background-color: #fff;
	color: #2F54EB;
	border: 1px solid #2F54EB;

	&:hover {
		background-color: #2F54EB;
		color: #fff;
	}
`;
