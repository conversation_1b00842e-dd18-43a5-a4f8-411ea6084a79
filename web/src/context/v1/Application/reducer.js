export const Types = {
	CLEAR_PROJECT: 'C<PERSON><PERSON>_PROJECT',
	SET_ORGANIZATION: 'SET_ORGANIZATION',
	SET_COMPANY: 'SET_COMPANY',
	SET_USER: 'SET_USER',
	TOGG<PERSON>_SIDER: 'TOGGLE_SIDER',
	SET_SANDBOX_ENABLED: 'SET_SANDBOX_ENABLED',
	TOGGLE_SANDBOX: 'TOGGLE_SANDBOX'
};

export const AppReducer = (state, action) => {
	switch (action.type) {
		case Types.CLEAR_PROJECT:
			return {
				...state,
				organization: null,
				company: null,
				user: null
			};

		case Types.SET_ORGANIZATION:
			return {
				...state,
				organization: action.payload
			};

		case Types.SET_COMPANY:
			return {
				...state,
				company: action.payload
			};

		case Types.SET_USER:
			return {
				...state,
				user: action.payload
			};

		case Types.TOGGLE_SIDER:
			return {
				...state,
				siderCollapsed: !state.siderCollapsed
			};

		case Types.SET_SANDBOX_ENABLED:
			return {
				...state,
				isSandboxEnabled: action.payload
			};

		case Types.TOGGLE_SANDBOX:
			return {
				...state,
				isSandboxEnabled: !state.isSandboxEnabled
			};

		default:
			return state;
	}
};
