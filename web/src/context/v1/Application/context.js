import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { AppReducer, Types } from './reducer';
import { useHistory } from 'react-router-dom';
import { useSandboxLogout, useSandboxSendReport } from '@/hooks/useSandbox/index';

// https://endertech.com/blog/using-reacts-context-api-for-global-state-managements

const SANDBOX_STORAGE_KEY = 'is_sandbox';

const storage = {
	organization: localStorage.getItem('organization'),
	company: localStorage.getItem('company'),
	user: localStorage.getItem('info_user')
};


const isSandboxEnabled = localStorage.getItem(SANDBOX_STORAGE_KEY) ? JSON.parse(localStorage.getItem(SANDBOX_STORAGE_KEY)) : false;

const defaultState = {
	organization: storage.organization ? JSON.parse(storage.organization) : null,
	company: storage.company ? JSON.parse(storage.company) : null,
	user: storage.user ? JSON.parse(storage.user)?.user : null,
	siderCollapsed: false,
	isSandboxEnabled: isSandboxEnabled,
	setOrganization: (data) => { },
	setCompany: (data) => { },
	clearProject: () => { },
	handleSiderCollapse: (collapsed) => { },
	toggleSandbox: (isSandbox) => { },
	sendReport: (params, url) => { },
	setSandbox: (value) => { }
};

const ApplicationContext = createContext(defaultState);

export const ApplicationProvider = ({ children }) => {
	const [state, dispatch] = useReducer(AppReducer, defaultState);

	const history = useHistory();
	const { mutateAsync: logout } = useSandboxLogout();
	const { mutateAsync: send } = useSandboxSendReport();

	function setOrganization(item) {
		dispatch({ type: Types.SET_ORGANIZATION, payload: item });
	}

	function setCompany(item) {
		localStorage.setItem('organization', JSON.stringify(state.organization));
		localStorage.setItem('company', JSON.stringify(item));
		dispatch({ type: Types.SET_COMPANY, payload: item });
	}

	function setUser(item) {
		localStorage.setItem('user', JSON.stringify(item));
		dispatch({ type: Types.SET_USER, payload: item });
	}

	function clearProject() {
		localStorage.removeItem('organization');
		localStorage.removeItem('company');
		dispatch({ type: Types.CLEAR_PROJECT });
	}

	function handleSiderCollapse() {
		dispatch({ type: Types.TOGGLE_SIDER });
	}

	// Sync sandbox state with localStorage
	useEffect(() => {
		localStorage.setItem(SANDBOX_STORAGE_KEY, JSON.stringify(state.isSandboxEnabled));
	}, [state.isSandboxEnabled]);

	async function toggleSandbox(isSandbox) {
		if (!isSandbox) {
			await logout();
		}
		dispatch({ type: Types.SET_SANDBOX_ENABLED, payload: isSandbox });
	}

	async function sendReport(params, url) {
		await send(params);
		history.push(url);
	}

	function setSandbox() {
		dispatch({ type: Types.TOGGLE_SANDBOX });
	}

	const context = {
		...state,
		setOrganization,
		setCompany,
		setUser,
		clearProject,
		handleSiderCollapse,
		toggleSandbox,
		sendReport,
		setSandbox
	};

	return <ApplicationContext.Provider value={context}>{children}</ApplicationContext.Provider>;
};

export function useApplicationContext() {
	const context = useContext(ApplicationContext);
	return context;
}
